import { PageThemeVariant } from 'store/slices/conf';

export type TemplateTypeRecord = Record<string, PageThemeVariant>;

export const templatePageVariants: Record<string, TemplateTypeRecord> = {
  autumn: {
    'classifieds.html': PageThemeVariant.CLASSIFIEDS,
    'index_page_exploretravel_v2.html': PageThemeVariant.EXPLORE_TRAVEL,
    'index_page_helpcentre.html': PageThemeVariant.HELP_CENTRE,
    'index_page_helpcentre_2nd_level.html': PageThemeVariant.HELP_CENTRE,
    'obituaries.html': PageThemeVariant.CLASSIFIEDS,
    'playhq_match_detail.html': PageThemeVariant.SPORT,
    'playhq_match_index.html': PageThemeVariant.SPORT,
    'playhq_season.html': PageThemeVariant.SPORT,
    'sport_index.html': PageThemeVariant.SPORT,
    'sport_ladder_index.html': PageThemeVariant.SPORT,
    'sport_live_index.html': PageThemeVariant.SPORT,
    'sport_match_detail.html': PageThemeVariant.SPORT,
    'sport_match_index.html': PageThemeVariant.SPORT,
    'story-local-partner.html': PageThemeVariant.LOCAL_PARTNER,
    'subcategory_page_exploretravel_v2.html': PageThemeVariant.EXPLORE_TRAVEL,
    'tributes.html': PageThemeVariant.CLASSIFIEDS,
    'vertical_homepage_exploretravel.html': PageThemeVariant.EXPLORE_TRAVEL,
  },
};

export const storyPageVariants: Record<string, TemplateTypeRecord> = {
  autumn: {
    'story-commercial': PageThemeVariant.SPONSORED,
    'story-helpcentre': PageThemeVariant.HELP_CENTRE,
    'story-local-partner': PageThemeVariant.LOCAL_PARTNER,
    'story-travel': PageThemeVariant.EXPLORE_TRAVEL,
  },
};
