import clsx from 'clsx';
import dynamic from 'next/dynamic';

import { type ZoneItem } from 'types/ZoneItems';

import type React from 'react';

interface GetZoneItemClassNameContext {
  defaultClassName?: string;
  theme: string;
  themeVariant: string;
}

export interface TemplateRecord {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component?: React.ComponentType<any>;
  getClassName?: (
    previous: ZoneItem | undefined,
    current: ZoneItem,
    next: ZoneItem | undefined,
    ctx: GetZoneItemClassNameContext,
  ) => string | undefined;
  globalOnly?: boolean;
  name: string;
}

export type TemplateTypeRecord = Record<string, TemplateRecord>;

const zoneItemTemplates: Record<string, Record<string, TemplateTypeRecord>> = {
  autumn: {
    advertisement: {
      'advertisement1.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/advertisement/Leaderboard'
            ),
          { ssr: true },
        ),
        name: 'Leaderboard',
      },
      'advertisement2.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/advertisement/Mrec'),
          { ssr: true },
        ),
        name: 'Mr<PERSON>',
      },
      'advertisement3.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/advertisement/MrecLeaderboard'
            ),
          { ssr: true },
        ),
        name: 'Mrec or Leaderboard',
      },
      'advertisement4.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/advertisement/LeaderboardOrMobile'
            ),
          { ssr: true },
        ),
        name: 'Leaderboard or Mobile',
      },
      'advertisement5.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/advertisement/BillboardOrMrec'
            ),
          { ssr: true },
        ),
        name: 'Billboard (desktop) & Mrec (mobile)',
      },
      'advertisement6.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/advertisement/Noticeboard'
            ),
          { ssr: true },
        ),
        name: 'Noticeboard',
      },
      'explore.html': {
        component: dynamic(
          () => import('themes/autumn/templates/explore/SearchWidget'),
          { ssr: true },
        ),
        name: 'Explore Travel search widget',
      },
      'explore_banner.html': {
        component: dynamic(
          () => import('themes/autumn/templates/explore/SearchWidget/Banner'),
          { ssr: true },
        ),
        name: 'Explore Travel search widget for banner',
      },
      'farmbuy_featured_properties_carousel.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/advertisement/FarmBuyFeaturedPropertiesCarousel'
            ),
          { ssr: true },
        ),
        name: 'FarmBuy Featured Properties Carousel',
      },
      'farmbuywidget.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/advertisement/FarmBuyWidget'
            ),
          { ssr: true },
        ),
        name: 'FarmBuy Widget',
      },
    },
    authors: {
      'meet_the_team.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/authors/MeetTheTeam'),
          { ssr: true },
        ),
        name: 'Meet the Team',
      },
    },
    banner: {
      'default_banner.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/banner/Default'),
          { ssr: true },
        ),
        name: 'Default Banner',
      },
      'explore_destination_header_banner.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/banner/explore/DestinationHeader'
            ),
          { ssr: true },
        ),
        name: 'Explore Destination Header Banner',
      },
      'explore_homepage_banner.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/banner/explore/Homepage'
            ),
          { ssr: true },
        ),
        name: 'Explore Homepage Banner',
      },
      'parallax_banner.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/banner/Parallax'),
          { ssr: true },
        ),
        name: 'Parallax Banner',
      },
    },
    carousel: {
      'default.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/carousel/Default'),
          { ssr: true },
        ),
        name: 'Default',
      },
    },
    cinematicfeatured: {
      'index.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/cinematicfeatured/Default'
            ),
          { ssr: true },
        ),
        name: 'Default',
      },
    },
    classified: {
      'business_promo.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/classified/LocalAdUnit/LocalAdUnitSidebar'
            ),
          { ssr: true },
        ),
        name: 'Local Ad Unit (Sidebar)',
      },
      'business_promo_main.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/classified/LocalAdUnit/LocalAdUnitMain'
            ),
          { ssr: true },
        ),
        name: 'Local Ad Unit (Main)',
      },
      'ownlocal.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/classified/OwnLocalAds'),
          { ssr: true },
        ),
        name: 'OwnLocal business listings',
      },
    },
    classifiedlist: {
      'default.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/classifiedlist/Default'),
          { ssr: true },
        ),
        name: 'Default',
      },
      'tributes_funeral.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/classifiedlist/TributesFuneralNotices'
            ),
          { ssr: true },
        ),
        name: 'Tributes & Funeral Notices',
      },
    },
    clusteredstorylist: {
      'default.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/clusteredstorylist/Default'
            ),
          { ssr: true },
        ),
        name: 'Default',
      },
      'with_promoted_link.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/clusteredstorylist/WithPromotedLink'
            ),
          { ssr: true },
        ),
        name: 'With promoted cluster site link',
      },
    },
    codesnippet: {
      'default.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/codesnippet/Default'),
          { ssr: true },
        ),
        name: 'Default',
      },
      'exploretravel.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/codesnippet/ExploreTravel'
            ),
          { ssr: true },
        ),
        name: 'Explore Travel',
      },
      'noticeboard-quicklinks.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/codesnippet/NoticeboardQuickLinks'
            ),
          { ssr: true },
        ),
        name: 'Noticeboard Quick Links',
      },
      'puzzle-grid.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/codesnippet/PuzzleGrid'),
          { ssr: true },
        ),
        name: 'Puzzle Grid',
      },
      'share-photos-widget.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/codesnippet/SharePhotosWidget'
            ),
          { ssr: true },
        ),
        name: 'Share Photos Widget',
      },
      'share-story-widget.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/codesnippet/ShareStoryWidget'
            ),
          { ssr: true },
        ),
        name: 'Share Story Widget',
      },
      'standard.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/codesnippet/StandardHtml'
            ),
          { ssr: true },
        ),
        name: 'Standard HTML',
      },
      'terms-and-conditions.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/codesnippet/TermsAndConditions'
            ),
          { ssr: true },
        ),
        name: 'Terms and Conditions',
      },
      'whatson-newsletter-widget.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/codesnippet/WhatsOnNewsletterWidget'
            ),
          { ssr: true },
        ),
        name: "What's On Newsletter Widget",
      },
    },
    comments: {
      'featured_comments.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/comments/FeaturedComments'
            ),
          { ssr: true },
        ),
        name: 'Featured comments',
      },
    },
    dailymotion: {
      'gallery.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/dailyMotion/Gallery'),
          { ssr: true },
        ),
        name: 'Gallery',
      },
      'gallery_weather.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/dailyMotion/GalleryWeather'
            ),
          { ssr: true },
        ),
        name: 'Gallery Weather',
      },
      'video_shorts.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/dailyMotion/VideoShorts'
            ),
          { ssr: true },
        ),
        name: 'Video shorts',
      },
    },
    dpecard: {
      'index.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/dpecard/Default'),
          { ssr: true },
        ),
        name: 'Default',
      },
      'index_mop.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/dpecard/MOP'),
          { ssr: true },
        ),
        name: 'MOP',
      },
    },
    dpelist: {
      'index.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/dpelist/Default'),
          { ssr: true },
        ),
        name: "DPE List - Today's Paper",
      },
      'list_all.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/dpelist/List'),
          { ssr: true },
        ),
        name: 'DPE List - List All',
      },
      'list_no_pagination.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/dpelist/Section'),
          { ssr: true },
        ),
        name: 'DPE List - Section',
      },
    },
    emaglist: {
      'index.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/emaglist/Default'),
          { ssr: true },
        ),
        name: 'E-Mags',
      },
    },
    exploresimplelinkwidget: {
      'deals_widget.html': {
        component: dynamic(
          import(
            'themes/autumn/templates/zoneItems/exploresimplelinkwidget/DealsWidget'
          ),
          { ssr: true },
        ),
        name: 'Deals Widget',
      },
    },
    exploretraveldeallist: {
      'carousel_deals.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/exploretravel/ExploreDealsCarousel'
            ),
          { ssr: true },
        ),
        name: 'Carousel',
      },
      'carousel_deals_v2.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/exploretravel/TravlrDealsCarousel'
            ),
          { ssr: true },
        ),
        name: 'CarouselV2',
      },
      'offer_list_inline.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/exploretravel/ExploreDealsListInline'
            ),
          { ssr: true },
        ),
        name: 'List Inline',
      },
      'sidebar_deal.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/exploretravel/ExploreDealsSidebar'
            ),
          { ssr: true },
        ),
        name: 'Sidebar Deal',
      },
      'two_column_grid_deals.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/exploretravel/ExploreDealsGrid'
            ),
          { ssr: true },
        ),
        name: 'Grid Deals - 2 Columns',
      },
    },
    faq: {
      'default.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/faq/Default'),
          { ssr: true },
        ),
        name: 'Default',
      },
    },
    featureddestination: {
      'index.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/featureddestination/Default'
            ),
          { ssr: true },
        ),
        name: 'Default',
      },
      'inspiring_cinematic_banner.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/featureddestination/InspiringCinematicBanner'
            ),
          { ssr: true },
        ),
        name: 'Inspiring Cinematic Banner',
      },
      'inspiring_static_featured.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/featureddestination/InspiringStaticFeatured'
            ),
          { ssr: true },
        ),
        name: 'Inspiring Static Featured',
      },
    },
    footer: {
      // TODO: These 3 footer entries can be removed once sites have their
      // global zone items removed from Suzuka DB.
      'echidna.html': {
        component: undefined,
        name: 'The Echidna',
      },
      'explore.html': {
        component: undefined,
        name: 'Explore',
      },
      'index.html': {
        component: undefined,
        name: 'Default',
      },
    },
    heading: {
      'allhomes_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/AllhomesStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Allhomes Strap Heading',
      },
      'car_expert_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/CarExpertStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Car Expert Strap Heading',
      },
      'community_photos_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/CommunityPhotosHeading'
            ),
          { ssr: true },
        ),
        name: 'Community Photos Heading',
      },
      'drive_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/DriveStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Drive Strap Heading',
      },
      'explore_centered_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/ExploreTravelCenteredHeading'
            ),
          { ssr: true },
        ),
        name: 'Explore Centered Strap Heading',
      },
      'explore_page_heading_with_subtitle.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/ExplorePageHeadingWithSubtitle'
            ),
          { ssr: true },
        ),
        name: 'Explore Page Heading with subtitle',
      },
      'explore_page_no_link_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/ExplorePageNoLinkHeading'
            ),
          { ssr: true },
        ),
        name: 'Explore Page No Link Heading',
      },
      'explore_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/ExploreStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Explore Strap Heading',
      },
      'explore_travel_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/ExploreTravelStrapHeading'
            ),
          { ssr: true },
        ),
        getClassName(previous, current, next, ctx) {
          if (next?.zoneItemData.template === 'explore-travel-carousel.html') {
            return clsx(ctx.defaultClassName, 'md:mb-12');
          }
          return ctx.defaultClassName;
        },
        name: 'Explore Travel Strap Heading',
      },
      'explore_travel_v2_centered_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/ExploreTravelV2CenteredStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Explore Travel V2 Centered Strap Heading',
      },
      'farm_online_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/FarmOnlineStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Farm Online Strap Heading',
      },
      'farm_weekly_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/FarmWeeklyStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Farm Weekly Strap Heading',
      },
      'footer_nav.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/heading/FooterNav'),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Footer Navigation Heading',
      },
      'good_fruit_and_veg_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/GoodFruitAndVegStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Good Fruit & Veg Strap Heading',
      },
      'heading_without_link.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/HeadingWithoutLink'
            ),
          { ssr: true },
        ),
        name: 'Heading without link',
      },
      'heading1.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/heading/Default'),
          { ssr: true },
        ),
        name: 'Custom Heading',
      },
      'heading2.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/heading/Default'),
          { ssr: true },
        ),
        name: 'Custom Heading with Background',
      },
      'north_queensland_register_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/NorthQueenslandRegisterStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'North Queensland Register Strap Heading',
      },
      'page_heading.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/heading/PageHeading'),
          { ssr: true },
        ),
        name: 'Page Heading',
      },
      'queensland_country_life_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/QueenslandCountryLifeStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Queensland Country Life Strap Heading',
      },
      'rev_page_heading.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/heading/REVPageHeading'),
          { ssr: true },
        ),
        name: 'REV Page Heading',
      },
      'signpost_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/SignpostHeading'
            ),
          { ssr: true },
        ),
        name: 'Signpost Heading',
      },
      'stock_and_land_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/StockAndLandStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Stock & Land Strap Heading',
      },
      'stock_journal_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/StockJournalStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Stock Journal Strap Heading',
      },
      'the_land_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/TheLandStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'The Land Strap Heading',
      },
      'the_senior_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/heading/TheSeniorStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'The Senior Strap Heading',
      },
      'ugc_contact_us.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/heading/UgcContactUs'),
          { ssr: true },
        ),
        name: 'UGC Contact Us',
      },
      'ugc_trade_box.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/heading/UgcTradeBox'),
          { ssr: true },
        ),
        name: 'UGC Trade Box',
      },
    },
    iframe: {
      'default.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/iframe/Default'),
          { ssr: true },
        ),
        name: 'Default',
      },
      'iframe.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/iframe/Default'),
          { ssr: true },
        ),
        name: 'Iframe',
      },
      'jotform.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/iframe/JotForm'),
          { ssr: true },
        ),
        name: 'JotForm',
      },
    },
    image: {
      'explore_travel.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/image/ExploreTravel'),
          { ssr: true },
        ),
        name: 'Explore Travel Image',
      },
      'left_aligned.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/image/LeftAligned'),
          { ssr: true },
        ),
        name: 'Left Aligned Image',
      },
    },
    infowithcta: {
      'default.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/infowithcta/Default'),
          { ssr: true },
        ),
        name: 'Help Centre',
      },
    },
    mailinglist: {
      'default.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/mailinglist/Default'),
          { ssr: true },
        ),
        name: 'Default',
      },
      'echidna.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/mailinglist/Echidna'),
          { ssr: true },
        ),
        name: 'The Echidna',
      },
      'today_paper_alert.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/mailinglist/TodaysPaperAlert'
            ),
          { ssr: true },
        ),
        name: "Today's Paper Alert",
      },
    },
    menulist: {
      'car_expert_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/CarExpertStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Car Expert Strap Heading',
      },
      'echidna_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/EchidnaStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'The Echidna Strap Heading',
      },
      'echidna_view_all.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/EchidnaViewAll'
            ),
          { ssr: true },
        ),
        name: 'The Echidna View All',
      },
      'explore_page_heading_with_subtitle.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/ExplorePageHeadingWithSubtitle'
            ),
          { ssr: true },
        ),
        name: 'Explore Page Heading with subtitle',
      },
      'explore_travel_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/ExploreTravelStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Explore Travel Strap Heading',
      },
      'farmbuy_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/FarmBuyStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'FarmBuy Strap Heading',
      },
      'footer_nav.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/menulist/FooterNav'),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Footer Navigation Heading',
      },
      'mop_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/MOPStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'MOP Heading',
      },
      'page_navigation.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/PageNavigation'
            ),
          { ssr: true },
        ),
        name: 'Page Navigation',
      },
      'rev_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/REVStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'REV Strap Heading',
      },
      'strap_heading.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/menulist/StrapHeading'),
          { ssr: true },
        ),
        name: 'Strap Heading',
      },
      'strap_heading_with_subtitle.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/StrapHeadingWithSubtitle'
            ),
          { ssr: true },
        ),
        name: 'Heading with subtitle (Explore Travel)',
      },
      'video_strap_heading.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/menulist/VideoStrapHeading'
            ),
          { ssr: true },
        ),
        name: 'Video Strap Heading',
      },
    },
    navigation: {
      'footer_nav_subscription.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/footerNavSubscription/Default/'
            ),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Footer Nav Subscription',
      },
      'footer_nav_subscription_ags.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/footerNavSubscription/Default/AgsIndex'
            ),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Footer Nav Subscription Ags',
      },
      'footer_nav_subscription_echidna.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/footerNavSubscription/Default/Echidna'
            ),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Footer Nav Subscription The Echidna',
      },
      'nav_ags.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/navigation/Ags'),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Nav Ags',
      },
      'nav_community_hub.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/navigation/CommunityHub'
            ),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Nav Community Hub',
      },
      'nav_echidna.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/navigation/Echidna'),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Nav Echidna',
      },
      'nav_explore.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/navigation/Explore'),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Nav Explore Travel',
      },
      'nav2.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/navigation/Default'),
          { ssr: true },
        ),
        globalOnly: true,
        name: 'Nav Default',
      },
      'shortcuts_strap.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/navigation/ShortcutsStrap'
            ),
          { ssr: true },
        ),
        name: 'Shortcuts Strap',
      },
    },
    newsletter: {
      'newsletter.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/newsletter/Newsletter'),
          { ssr: true },
        ),
        name: 'Newsletters',
      },
    },
    pagecollection: {
      'card_grid.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/pagecollection/CardGrid'
            ),
          { ssr: true },
        ),
        name: 'Card Grid',
      },
      'collection_carousel.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/pagecollection/CollectionCarousel'
            ),
          { ssr: true },
        ),
        name: 'Collection Carousel',
      },
      'explore-collection-sub-navigation-strip.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/pagecollection/ExploreSubNavigationStrip'
            ),
          { ssr: true },
        ),
        name: 'Explore Collection Sub Navigation Strip',
      },
      'four_column_title_list.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/pagecollection/FourColumnTitleList'
            ),
          { ssr: true },
        ),
        name: 'Four Column Title List',
      },
      'horizontal_strip.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/pagecollection/HorizontalStrip'
            ),
          { ssr: true },
        ),
        getClassName() {
          return 'mb-7';
        },
        name: 'Horizontal Strip',
      },
      'image_card_list.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/pagecollection/ImageCardList'
            ),
          { ssr: true },
        ),
        name: 'Image Card List',
      },
      'pill_page_navigation.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/pagecollection/PillPageNavigation'
            ),
          { ssr: true },
        ),
        name: 'Pill Page Navigation',
      },
    },
    playhq: {
      'grade.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/playhq/Grade'),
          { ssr: true },
        ),
        name: 'Grade',
      },
      'season.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/playhq/Season'),
          { ssr: true },
        ),
        name: 'Season',
      },
      'upcomingMatches.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/playhq/UpcomingMatches'),
          { ssr: true },
        ),
        name: 'Upcoming Matches',
      },
    },
    revwidget: {
      'rev_widget.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/revwidget/Default'),
          { ssr: true },
        ),
        name: 'Default',
      },
    },
    socials: {
      'share_and_bookmark.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/shareAndBookmark/'),
          { ssr: true },
        ),
        name: 'Default',
      },
    },
    sportshub: {
      'upcomingMatches.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/sportsHub/UpcomingMatches/'
            ),
          { ssr: true },
        ),
        name: 'Upcoming Matches',
      },
    },
    storylist: {
      '3ColFeatureArticles.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/3ColFeatureArticles/'
            ),
          { ssr: true },
        ),
        name: '3ColFeatureArticles',
      },
      '3ColListViewLoadMore.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/3ColListViewLoadMore/'
            ),
          { ssr: true },
        ),
        name: '3Col Articles with Load More',
      },
      'allhomes.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/external/AllHomes'
            ),
          { ssr: true },
        ),
        name: 'All Homes',
      },
      'allhomes-featured': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/external/AllHomesFeatured'
            ),
          { ssr: true },
        ),
        name: 'All Homes Featured',
      },
      'author-stories-listview.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/AuthorStoriesListViewV2'
            ),
          { ssr: true },
        ),
        name: 'Author Stories List View',
      },
      'businessfeature.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/BusinessFeature'
            ),
          { ssr: true },
        ),
        name: 'Business Feature (ESOV)',
      },
      'carousel.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/storylist/Carousel'),
          { ssr: true },
        ),
        name: 'Carousel',
      },
      'carousel-center-mode.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/Carousel/CarouselCenterMode'
            ),
          { ssr: true },
        ),
        name: 'Carousel Center Mode',
      },
      'dailymotion-list.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/storylist/Dailymotion'),
          { ssr: true },
        ),
        name: 'Dailymotion - List',
      },
      'domain.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/external/Domain'
            ),
          { ssr: true },
        ),
        name: 'Domain',
      },
      'drive.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/external/Drive'
            ),
          { ssr: true },
        ),
        name: 'Drive',
      },
      'echidna.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/storylist/Echidna'),
          { ssr: true },
        ),
        name: 'The Echidna - Article',
      },
      'echidna_cartoon.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/EchidnaCartoon'
            ),
          { ssr: true },
        ),
        name: 'The Echidna - Cartoon',
      },
      'echidna_cartoon_list.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/EchidnaPaginatedCartoonList'
            ),
          { ssr: true },
        ),
        name: 'The Echidna - Cartoon List',
      },
      'echidna_list.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/storylist/EchidnaList'),
          { ssr: true },
        ),
        name: 'The Echidna - List',
      },
      'echidna_paginated_list.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/EchidnaPaginatedList'
            ),
          { ssr: true },
        ),
        name: 'The Echidna - Paginated list',
      },
      'explore.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/external/Explore'
            ),
          { ssr: true },
        ),
        name: 'Explore',
      },
      'explore-travel-carousel.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/ExploreTravelCarousel'
            ),
          { ssr: true },
        ),
        name: 'Explore Travel Carousel',
      },
      'explore-travel-infinite-scroll.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/ExploreTravelInfiniteScroll'
            ),
          { ssr: true },
        ),
        name: 'Explore Travel Infinite Scroll',
      },
      'explore-travel-latest-and-most-viewed.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/ExploreTravelLatestAndMostViewed'
            ),
          { ssr: true },
        ),
        name: 'Explore Travel Latest and Most Viewed',
      },
      'featuredPhotos.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/FeaturedPhotos'
            ),
          { ssr: true },
        ),
        name: 'Featured Photos (Community Index)',
      },
      'featuredPhotosWidget.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/FeaturedPhotosWidget'
            ),
          { ssr: true },
        ),
        name: 'Featured Photos (Widget)',
      },
      'help-centre-listview.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/HelpCentreListView'
            ),
          { ssr: true },
        ),
        name: 'Help Centre List View',
      },
      'inline.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/storylist/Inline'),
          { ssr: true },
        ),
        name: 'Inline List View',
      },
      'listview.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/storylist/ListView'),
          { ssr: true },
        ),
        name: 'List View',
      },
      'listview-left-images.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/ListViewLeftImage'
            ),
          { ssr: true },
        ),
        name: 'Left images list View',
      },
      'listview-load-more.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/ListViewLoadMore'
            ),
          { ssr: true },
        ),
        name: 'List View with Load More',
      },
      'listview-load-more-left-images.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/ListViewLoadMore'
            ),
          { ssr: true },
        ),
        name: 'List View with Load More - Left Images',
      },
      'local-partner-load-more.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/LocalPartnerLoadMore'
            ),
          { ssr: true },
        ),
        name: 'Local Partner with Load More',
      },
      'local-partner-widget.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/storylist/LocalPartner'),
          { ssr: true },
        ),
        name: 'Local Partner Widget',
      },
      'main.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/storylist/Main'),
          { ssr: true },
        ),
        name: 'Main',
      },
      'most-asked-questions.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/MostAskedQuestions'
            ),
          { ssr: true },
        ),
        name: 'Most Asked Questions',
      },
      'newswell.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/storylist/Newswell'),
          { ssr: true },
        ),
        name: 'Newswell',
      },
      'newswellexplore.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/NewswellExplore'
            ),
          { ssr: true },
        ),
        name: 'Newswell (Explore)',
      },
      'parallax.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/storylist/Parallax'),
          { ssr: true },
        ),
        name: 'Parallax',
      },
      'realestateview.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/external/RealEstateView'
            ),
          { ssr: true },
        ),
        name: 'Real Estate View',
      },
      'scoresanddraws.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylist/ScoresAndDraws'
            ),
          { ssr: true },
        ),
        name: 'Score And Draws',
      },
      'sports_hub.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/storylist/SportsHub'),
          { ssr: true },
        ),
        name: 'Sports Hub',
      },
    },
    storylistcollection: {
      'explore-travel-carousel.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/storylistcollection/ExploreTravelCarousel'
            ),
          { ssr: true },
        ),
        name: 'Explore Travel Collection Carousel',
      },
    },
    textblock: {
      'text1.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/textblock/Default'),
          { ssr: true },
        ),
        name: 'Text',
      },
      'text2.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/textblock/Default'),
          { ssr: true },
        ),
        name: 'Text with background',
      },
    },
    titledstorylist: {
      'default.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/titledstorylist/Default'
            ),
          { ssr: true },
        ),
        name: 'Default',
      },
      'numbered.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/titledstorylist/Numbered'
            ),
          { ssr: true },
        ),
        name: 'Numbered',
      },
    },
    traffic: {
      'rfs.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/traffic/Rfs'),
          { ssr: true },
        ),
        name: 'RFS widget',
      },
      'traffic.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/traffic/Traffic'),
          { ssr: true },
        ),
        name: 'Live Traffic',
      },
    },
    ugclist: {
      'grid-view.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/ugclist/GridView'),
          { ssr: true },
        ),
        name: 'Photos (Grid)',
      },
      'local-expert.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/ugclist/LocalExpert'),
          { ssr: true },
        ),
        name: 'Local Expert',
      },
      'photos-widget.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/ugclist/PhotosWidget'),
          { ssr: true },
        ),
        name: 'Photos Widget',
      },
      'whats-on.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/ugclist/WhatsOn'),
          { ssr: true },
        ),
        name: "What's On (Vertical Calendar)",
      },
      'whats-on-widget.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/ugclist/WhatsOnWidget'),
          { ssr: true },
        ),
        name: "What's On Widget",
      },
    },
    viewjobs: {
      'viewjobs.html': {
        component: dynamic(
          () => import('themes/autumn/templates/zoneItems/viewjobs/Default'),
          { ssr: true },
        ),
        name: 'Default',
      },
    },
    weather: {
      'weathercard.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/weather/WeatherCard'),
          { ssr: true },
        ),
        name: 'Weather card',
      },
      'weathercard-with-border.html': {
        component: dynamic(
          () =>
            import(
              'themes/autumn/templates/zoneItems/weather/WeatherCard/WithBorder'
            ),
          { ssr: true },
        ),
        name: 'Weather card with border',
      },
      'weatherzone.html': {
        component: dynamic(
          () =>
            import('themes/autumn/templates/zoneItems/weather/WeatherZone'),
          { ssr: true },
        ),
        name: 'WeatherZone',
      },
    },
  },
};

const internalZoneItemTemplates: Record<
  string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Record<string, React.ComponentType<any>>
> = {
  autumn: {
    TemplateNotFound: dynamic(
      () => import('themes/autumn/templates/zoneItems/TemplateNotFound'),
    ),
  },
};

export function getZoneItemTemplateComponent(
  theme: string,
  zoneItemType: string,
  templateKey: string,
): React.ComponentType | null | undefined {
  const templateTypeRecord =
    zoneItemTemplates[theme][zoneItemType][templateKey];
  return templateTypeRecord
    ? templateTypeRecord.component
    : internalZoneItemTemplates[theme].TemplateNotFound;
}

export function getZoneItemClassName(
  previous: ZoneItem,
  current: ZoneItem,
  next: ZoneItem,
  ctx: GetZoneItemClassNameContext,
) {
  const templateTypeRecord =
    zoneItemTemplates[ctx.theme]?.[current.zoneItemType]?.[
      current.zoneItemData.template
    ];
  if (!templateTypeRecord || !templateTypeRecord.getClassName) {
    return ctx.defaultClassName;
  }

  return templateTypeRecord.getClassName(previous, current, next, ctx);
}

export default zoneItemTemplates;
