import templates from 'templates/templates';

import type { NextApiRequest, NextApiResponse } from 'next';

export type ThemeTemplate = {
  key: string;
  title: string;
  zones?: Array<string>;
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse,
): void {
  const { theme } = req.query;
  const themeTemplates = templates[<string>theme];

  if (!themeTemplates) {
    res.status(404).json([]);
    return;
  }

  // TODO: Currently filtering out story templates so they won't appear in
  // suzuka dropdowns. Need a better solution.

  const response: Array<ThemeTemplate> = Object.entries(themeTemplates)
    .filter(([, value]) => !value.hide)
    .map(([key, value]) => ({
      key,
      settings: value.settings,
      title: value.name,
      zones: value.zones,
    }));

  res.status(200).json(response);
}
