import templates from 'templates/templates';

import type { NextApiRequest, NextApiResponse } from 'next';

export type ThemeTemplate = {
  key: string;
  title: string;
  zones?: Array<string>;
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse,
): void {
  const { template, theme } = req.query;
  const themeTemplates = templates[<string>theme];

  if (!themeTemplates) {
    res.status(404).json({});
    return;
  }

  const themeTemplate = themeTemplates[<string>template];
  if (!themeTemplate || themeTemplate.hide) {
    res.status(404).json({});
    return;
  }

  res.status(200).json({
    key: template,
    settings: themeTemplate.settings,
    title: themeTemplate.name,
    zones: themeTemplate.zones,
  });
}
