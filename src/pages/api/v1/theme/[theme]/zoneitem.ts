import zoneItemTemplates from 'templates/zoneItemTemplates';

import type { NextApiRequest, NextApiResponse } from 'next';

export type ThemeTemplate = {
  key: string;
  title: string;
};

type Response = { [key: string]: ThemeTemplate[] };

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse,
): void {
  const { theme } = req.query;

  const templates = zoneItemTemplates[<string>theme];

  if (!templates) {
    res.json({});
    return;
  }

  const response: Response = {};

  Object.entries(templates).forEach(([key, value]) => {
    response[key] = Object.entries(value).map(
      ([template_key, template_value]) => ({
        key: template_key,
        title: template_value.name,
      }),
    );
  });

  res.status(200).json(response);
}
