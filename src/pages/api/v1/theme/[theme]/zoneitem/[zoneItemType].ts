import zoneItemTemplates from 'templates/zoneItemTemplates';

import type { ThemeTemplate } from '../zoneitem';
import type { NextApiRequest, NextApiResponse } from 'next';

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse,
): void {
  const { theme, zoneItemType } = req.query;

  const templates = zoneItemTemplates[<string>theme]?.[<string>zoneItemType];

  if (!templates) {
    res.status(404).json([]);
    return;
  }

  const response: Array<ThemeTemplate> = Object.entries(templates).map(
    ([key, value]) => ({ key, title: value.name }),
  );

  res.status(200).json(response);
}
