import { useAppSelector } from 'store/hooks';
import AuthorLink from 'themes/autumn/components/stories/AuthorLink';
import AuthorImage from 'themes/autumn/components/storyElements/common/AuthorImage';
import { getAuthorBioHtml } from 'util/author/author';
import { nameToInitials } from 'util/string';

import type { StoryAuthor } from 'types/Story';

export interface AuthorBioProps {
  authors: StoryAuthor[];
}
const Author: React.FC<{ author: StoryAuthor }> = ({ author }) => {
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);

  return (
    <div className="flex flex-row gap-3 border-t-1 border-gray-300 py-8 last:pb-0">
      {/* AUTHOR MUGSHOT */}
      {author.mugshot !== '' ? (
        <div className="shrink-0 self-start">
          <AuthorImage
            author={author}
            className="size-12 rounded-full md:size-16"
            height={64}
            transformUrl={transformUrl}
            width={64}
          />
        </div>
      ) : (
        <div className="shrink-0 self-start rounded-full bg-gray-500 uppercase ring-2 ring-white">
          <div className="flex size-12 items-center justify-center rounded-full font-inter text-xl font-medium text-white md:size-16">
            <div>{nameToInitials(author.name)}</div>
          </div>
        </div>
      )}
      {/* NAME AND BIO */}
      <div className="flex flex-col gap-2">
        {/* AUTHOR NAME */}
        <div className="flex flex-row items-center">
          <span className="mr-2 text-sm/[16px] font-medium text-gray-400">
            Words by
          </span>
          <span>
            <AuthorLink
              author={author}
              authorClassName="
                text-sm/[16px] font-medium font-inter underline text-gray-900
              "
            >
              {author.name}
            </AuthorLink>
          </span>
        </div>
        {/* AUTHOR BIO */}
        <div
          className="prose line-clamp-4 font-inter text-xs/[150%] font-normal text-gray-500"
          dangerouslySetInnerHTML={{ __html: getAuthorBioHtml(author) }}
        />
      </div>
    </div>
  );
};

const AuthorBio: React.FC<AuthorBioProps> = ({ authors }) => (
  <div className="flex flex-col">
    {authors.map((author) => (
      <Author author={author} key={author.id} />
    ))}
  </div>
);

export default AuthorBio;
