import { faCheck, faPlus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import React, { useEffect } from 'react';

import { useAppSelector } from 'store/hooks';
import { useNewsletterSubscriptionState } from 'util/newsletter';

const EXPLORE_TRAVEL_NEWSLETTER_ID = 'Explore Travel';

interface NewsletterSignupProps {
  onNewswell?: boolean;
}

export default function NewsletterSignup({
  onNewswell = false,
}: NewsletterSignupProps): React.ReactElement | null {
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const user = useAppSelector((state) => state.piano.user);

  const { handleClick, isLoading, isSubscribed, setEnabled } =
    useNewsletterSubscriptionState(EXPLORE_TRAVEL_NEWSLETTER_ID);

  useEffect(() => {
    if (!pianoInitialized) {
      return;
    }

    if (window.localStorage) {
      const queuedGroupId = window.localStorage.getItem('queuedNewsletter');
      if (queuedGroupId !== EXPLORE_TRAVEL_NEWSLETTER_ID) {
        return;
      }

      if (user) {
        setEnabled(EXPLORE_TRAVEL_NEWSLETTER_ID, true);
      }
      window.localStorage.removeItem('queuedNewsletter');
    }
  }, [user, pianoInitialized, setEnabled]);

  return (
    <div
      className={clsx('md:px-0', {
        'mb-6 px-4 md:w-180': !onNewswell,
        'mt-11 md:mt-18': onNewswell,
      })}
    >
      <div
        className={clsx(
          'flex flex-col items-center justify-center gap-4 border-y border-gray-200 py-3.5 md:flex-row',
          {
            'md:justify-between md:gap-5 md:self-stretch': !onNewswell,
            'md:justify-center md:gap-7 md:py-6': onNewswell,
          },
        )}
      >
        <div
          className={clsx(
            'px-7 text-center font-inter text-gray-900 md:px-0 md:text-left',
            {
              'text-base font-medium': onNewswell,
              'text-sm font-semibold': !onNewswell,
            },
          )}
        >
          Get exclusive travel tips, hidden gems &amp; expert insights:
          delivered to your inbox
        </div>
        <button
          className={clsx(
            'h-10 w-[116px] rounded-full border border-solid border-gray-300 px-2 py-1 text-sm font-semibold shadow-sm transition-colors hover:shadow-md focus:outline-none disabled:opacity-90',
            isSubscribed
              ? 'bg-green-600 text-white'
              : 'bg-white text-gray-900',
          )}
          disabled={isLoading || isSubscribed}
          onClick={handleClick}
          type="button"
        >
          <FontAwesomeIcon
            className="mr-2"
            icon={isSubscribed ? faCheck : faPlus}
          />
          {isSubscribed ? 'Selected' : 'Sign Up'}
        </button>
      </div>
    </div>
  );
}
