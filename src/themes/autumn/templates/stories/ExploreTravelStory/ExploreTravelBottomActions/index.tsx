import { useAppSelector } from 'store/hooks';
import { useWindowHref } from 'util/hooks';

import CommentCount from '../../common/CommentCount';
import SaveButton from '../../common/SaveButton';
import ShareButton from '../../common/ShareButton';

interface Props {
  showCommentCount?: boolean;
}

const ExploreTravelBottomActions = ({ showCommentCount = true }: Props) => {
  const story = useAppSelector((state) => state.story);
  const url = useWindowHref({ strip: true });

  return (
    <div className="flex flex-row flex-wrap items-center gap-4">
      <SaveButton />
      <ShareButton url={url} />
      {showCommentCount && <CommentCount story={story} />}
    </div>
  );
};

export default ExploreTravelBottomActions;
