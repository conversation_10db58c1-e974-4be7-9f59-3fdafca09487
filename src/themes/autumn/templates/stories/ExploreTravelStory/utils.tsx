import React, { Fragment } from 'react';

import ViafouraConversationStarter from 'themes/autumn/components/stories/Comments/ViafouraConversationStarter';
import StoryComponentWrapper from 'themes/autumn/components/stories/StoryElements/StoryComponentWrapper';
import { StoryComponentWrapperMaxWidthEnum } from 'themes/autumn/components/stories/StoryElements/enums';
import { StoryElementType } from 'types/Story';

import type { StoryComponentMap } from 'themes/autumn/components/storyElements/storyElementsMap';
import type { StoryElement } from 'types/Story';

/*
 * Process remaining elements after injection point, grouping
 * paragraphs that appear before any non-paragraph element with
 * the recommendation component
 */
export default function ProcessExploreTravelInjection({
  applyPaywallHiding,
  classNameForStoryComponentWrapper,
  component,
  elements,
  hasPaywall,
  hasPianoPaywall,
  index,
  injectComponentToAdd,
  itemsShownBehindPaywall,
  noMaxWidthForStoryElements,
  pianoFeature,
  shouldApplyPaywall,
  showConversationStarter,
  storyComponentMap,
  usePaywall,
}: {
  applyPaywallHiding: (
    component: React.ReactElement | null,
    index: number,
    itemsShownBehindPaywall: { fadeIdx: number },
    hasPaywall: boolean,
  ) => React.ReactElement | null;
  classNameForStoryComponentWrapper?: string;
  component: React.ReactElement | null;
  elements: StoryElement[];
  hasPaywall: boolean;
  hasPianoPaywall: boolean;
  index: number;
  injectComponentToAdd: {
    injectComponent: React.ReactNode;
    injectComponentClassName?: string;
    injectPos?: number;
    skipElementsAfterInjection?: boolean;
  };
  itemsShownBehindPaywall: { fadeIdx: number };
  noMaxWidthForStoryElements?: boolean;
  pianoFeature: { enabled: boolean };
  shouldApplyPaywall: (
    pianoFeature: { enabled: boolean },
    usePaywall: boolean,
    hasPianoPaywall: boolean,
  ) => boolean;
  showConversationStarter?: boolean;
  storyComponentMap: StoryComponentMap;
  usePaywall: boolean;
}): React.ReactElement | null {
  const remainingElements = elements.slice(index);
  const paragraphElements: React.ReactNode[] = [];
  const nonParagraphElements: React.ReactNode[] = [];

  const firstNonParagraphIndex = remainingElements.findIndex(
    (el) => el.type !== StoryElementType.Paragraph,
  );

  remainingElements.forEach((remainingElement, remainingIndex) => {
    const RemainingStoryComponent =
      storyComponentMap[remainingElement.type]?.[0];

    if (!RemainingStoryComponent) {
      return;
    }

    const isParagraph = remainingElement.type === StoryElementType.Paragraph;
    const allParagraphs = firstNonParagraphIndex === -1;
    const beforeFirstNonParagraph = remainingIndex < firstNonParagraphIndex;

    // Determine if this paragraph will be grouped with the recommendation
    const isGroupedWithRecommendation =
      isParagraph && (allParagraphs || beforeFirstNonParagraph);

    const componentToAdd = (
      // eslint-disable-next-line react/no-array-index-key
      <Fragment key={remainingIndex}>
        <StoryComponentWrapper
          className={
            isGroupedWithRecommendation
              ? injectComponentToAdd.injectComponentClassName
              : classNameForStoryComponentWrapper
          }
          maxWidth={
            noMaxWidthForStoryElements || !isParagraph
              ? StoryComponentWrapperMaxWidthEnum.None
              : undefined
          }
          type={remainingElement.type}
        >
          <RemainingStoryComponent
            element={remainingElement}
            index={index + remainingIndex}
          />
        </StoryComponentWrapper>
        {showConversationStarter &&
        index + remainingIndex === Math.floor(elements.length / 2) ? (
          <ViafouraConversationStarter />
        ) : null}
      </Fragment>
    );

    (isGroupedWithRecommendation
      ? paragraphElements
      : nonParagraphElements
    ).push(componentToAdd);
  });

  const shouldWrapRemaining =
    injectComponentToAdd.skipElementsAfterInjection !== false;

  if (shouldWrapRemaining) {
    const recommendationContainer = (
      <div className={injectComponentToAdd.injectComponentClassName}>
        {injectComponentToAdd.injectComponent}
      </div>
    );

    const paragraphsWrapper = (
      <div>
        {recommendationContainer}
        {paragraphElements.length > 0 && paragraphElements}
      </div>
    );

    let wrappedComponent: React.ReactElement | null = (
      <Fragment key={injectComponentToAdd.injectPos}>
        {paragraphsWrapper}
        {nonParagraphElements}
      </Fragment>
    );
    if (shouldApplyPaywall(pianoFeature, usePaywall, hasPianoPaywall)) {
      const lastElementIdx = index + remainingElements.length - 1;
      wrappedComponent = applyPaywallHiding(
        wrappedComponent,
        lastElementIdx,
        itemsShownBehindPaywall,
        hasPaywall,
      );
    }
    return wrappedComponent;
  }

  return (
    <Fragment key={injectComponentToAdd.injectPos}>
      {component}
      <div
        // eslint-disable-next-line react/no-array-index-key
        className={injectComponentToAdd.injectComponentClassName}
        key={injectComponentToAdd.injectPos}
      >
        {injectComponentToAdd.injectComponent}
      </div>
    </Fragment>
  );
}
