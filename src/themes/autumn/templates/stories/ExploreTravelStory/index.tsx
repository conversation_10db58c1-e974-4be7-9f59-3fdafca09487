import React, { useEffect, useMemo, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import Comments from 'themes/autumn/components/stories/Comments';
import Recommendation from 'themes/autumn/components/stories/Recommendation';
import StoryElements from 'themes/autumn/components/stories/StoryElements';
import ExploreTravelPublishingTimes from 'themes/autumn/components/stories/StoryPageHeadline/PublishingTimes/ExploreTravel';
import { exploreTravelStoryComponentMap } from 'themes/autumn/components/storyElements/storyElementsMap';
import ExploreTravelTopics from 'themes/autumn/components/topics/ExploreTravelTopics';
import GlobalZone from 'themes/autumn/components/zone/GlobalZone';
import { ExploreTravelLogo } from 'themes/autumn/templates/zoneItems/navigation/ExploreTravelSubNav';
import { hasH2Headings } from 'themes/autumn/templates/zoneItems/navigation/common/utils';
import {
  type HeadingElement,
  type ParagraphElement,
  StoryElementType,
} from 'types/Story';
import { GlobalZoneName } from 'types/ZoneItems';
import { AdSize } from 'util/ads';
import { useDeviceTypeFromWidth } from 'util/hooks';
import { useNewsletterSubscriptionState } from 'util/newsletter';

import ExploreTravelRecirculationWidget from '../common/ExploreTravelRecirculationWidget';

import AuthorBio from './AuthorBio';
import ExploreTravelBottomActions from './ExploreTravelBottomActions';
import ExploreTravelHeader from './ExploreTravelHeader';
import NewsletterSignup from './NewsletterSignup';

// Following the design, place Read More section 240px
// from the bottom of the story
const READ_MORE_DISTANCE_FROM_BOTTOM_PX = 240;

export default function ExploreTravelStory(): React.ReactElement {
  const story = useAppSelector((state) => state.story);
  const deviceType = useDeviceTypeFromWidth();
  const exploreTravelRecirculationFeature = useAppSelector(
    (state) => state.features.exploreTravelRecirculation,
  );
  const [paragraphReferenceElementIndex, setParagraphReferenceElementIndex] =
    useState<number>(-1);

  const seoTitle = story.seoTitle || story.title;
  const { seoDescription, tags: storyTags } = story;
  const isSponsored = storyTags?.includes('story-sponsored');

  const hasAnchorLinks = story.makeH2AnchorLinks && hasH2Headings(story);

  const EXPLORE_TRAVEL_NEWSLETTER_ID = 'Explore Travel';
  const { isSubscribed } = useNewsletterSubscriptionState(
    EXPLORE_TRAVEL_NEWSLETTER_ID,
  );

  const storyElements = useMemo(() => {
    let elements = story.elements ?? [];
    const hasLeadImage = elements[0]?.type === StoryElementType.Image;
    if (story.makeH2AnchorLinks && hasLeadImage) {
      const headingElements = elements.filter(
        (element): element is HeadingElement =>
          element.type === StoryElementType.Heading && element.level === '2',
      );

      // Insert an anchor box elementtype before the first paragraph
      const firstParagraphIndex = elements.findIndex(
        (element): element is ParagraphElement =>
          element.type === StoryElementType.Paragraph,
      );
      if (
        firstParagraphIndex !== -1 &&
        headingElements &&
        headingElements.length > 0
      ) {
        elements = [
          ...elements.slice(0, firstParagraphIndex),
          {
            elements: headingElements,
            type: StoryElementType.AnchorBox,
          },
          ...elements.slice(firstParagraphIndex),
        ];
      }
    }
    return elements;
  }, [story.elements, story.makeH2AnchorLinks]);

  const totalParagraphCount = useMemo(
    () =>
      storyElements.filter(
        (element) => element.type === StoryElementType.Paragraph,
      ).length,
    [storyElements],
  );
  const addNewsletterSignupComponent =
    !isSubscribed && totalParagraphCount >= 2;

  const findPositionAfterThirdParagraph = () => {
    let paragraphCount = 0;
    for (let i = 0; i < storyElements.length; i += 1) {
      if (storyElements[i].type === StoryElementType.Paragraph) {
        paragraphCount += 1;
        if (paragraphCount === 3) {
          return i;
        }
      }
    }

    // Fallback if there are fewer than 3 paragraphs
    return Math.min(3, storyElements.length - 1);
  };

  const positionAfterThirdParagraph = addNewsletterSignupComponent
    ? findPositionAfterThirdParagraph()
    : -1;

  useEffect(() => {
    if (totalParagraphCount === 0) {
      setParagraphReferenceElementIndex(-1);
      return;
    }

    const findClosestParagraphInReverse = () => {
      let totalHeight = 0;
      let lastParagraphIndex = -1;
      let lastParagraphHeight = 0;

      for (let i = storyElements.length - 1; i >= 0; i--) {
        const element = storyElements[i];
        const estimateHeightFn =
          exploreTravelStoryComponentMap[element.type]?.[1];

        if (estimateHeightFn) {
          const elementHeight = estimateHeightFn({
            deviceType,
            item: { element, index: i },
          });

          if (element.type === StoryElementType.Paragraph) {
            if (totalHeight < READ_MORE_DISTANCE_FROM_BOTTOM_PX) {
              lastParagraphIndex = i;
              lastParagraphHeight = totalHeight;
            } else {
              const currentDifference = Math.abs(
                totalHeight - READ_MORE_DISTANCE_FROM_BOTTOM_PX,
              );
              const lastDifference = Math.abs(
                lastParagraphHeight - READ_MORE_DISTANCE_FROM_BOTTOM_PX,
              );
              if (currentDifference < lastDifference) {
                return i;
              }
              return lastParagraphIndex;
            }
          }

          totalHeight += elementHeight;
        }
      }
      return lastParagraphIndex;
    };

    const paragraphIndex = findClosestParagraphInReverse();
    setParagraphReferenceElementIndex(paragraphIndex);
  }, [storyElements, deviceType, totalParagraphCount]);

  let paragraphIndex = -1;
  if (totalParagraphCount > 0) {
    if (paragraphReferenceElementIndex >= 0) {
      paragraphIndex = paragraphReferenceElementIndex;
    } else {
      // Only compute fallback if needed
      paragraphIndex = Math.floor(storyElements.length * 0.75);
    }
  }

  // Only create recommendation component if we need it
  const recommendationComponent =
    totalParagraphCount > 0 ? (
      <Recommendation className="mb-6 flex justify-center md:float-right md:ml-8 md:mt-0 md:block md:w-56" />
    ) : null;

  const classNameForStoryComponentWrapper =
    '!mx-0 md:!mx-auto flex font-playfair';

  // Only compute newsletter index if we're going to use it
  let newsletterIndex = -1;
  if (addNewsletterSignupComponent) {
    newsletterIndex =
      positionAfterThirdParagraph >= paragraphIndex
        ? paragraphIndex - 1
        : positionAfterThirdParagraph;
  }

  /*
    TODO: As it stands, the inject components are working as expected.
    But adding additional components might introduce some issues.
    Specially if we set skipElementsAfterInjection to true.
    A revamp of the injection logic will be needed in the future in
    src/themes/autumn/components/stories/StoryElements/index.tsx
    to make the code more readable and maintainable.
  */
  const injectComponents = [
    ...(addNewsletterSignupComponent
      ? [
          {
            injectComponent: <NewsletterSignup />,
            injectComponentClassName: classNameForStoryComponentWrapper,
            injectPos: newsletterIndex,
            skipElementsAfterInjection: false,
          },
        ]
      : []),
    ...(totalParagraphCount > 0 && recommendationComponent
      ? [
          {
            injectComponent: recommendationComponent,
            injectComponentClassName: 'mx-auto md:w-180 font-playfair',
            injectPos: paragraphIndex,
            skipElementsAfterInjection: true,
          },
        ]
      : []),
  ];

  return (
    <TemplateWrapper
      navigationStickyMobileContent={<ExploreTravelLogo />}
      navigationStickyWideContent={
        hasAnchorLinks ? (
          <ExploreTravelLogo />
        ) : (
          <GlobalZone name={GlobalZoneName.STORY_TRAVEL_SUB_NAV} />
        )
      }
      seoDescription={seoDescription}
      seoTitle={seoTitle}
      showNavigationAd={false}
      showStickyFooterAd
    >
      <div className="border-b border-gray-300">
        <GlobalZone name={GlobalZoneName.STORY_TRAVEL_SUB_NAV} />
      </div>
      <Container className="flex flex-col pt-5" noGutter>
        <ExploreTravelHeader />
        <StoryElements
          adLgSizes={AdSize.billboard}
          adMdSizes={AdSize.billboard}
          authors={story.authors}
          byline={story.byline}
          classNameForPaywall="w-full md:w-180 justify-center"
          classNameForStoryComponentWrapper={classNameForStoryComponentWrapper}
          customStoryComponentMap={exploreTravelStoryComponentMap}
          elements={storyElements}
          hideRecommendation
          injectComponents={injectComponents}
          isTextToSpeechEnabled={story.isTextToSpeechEnabled}
          noMaxWidthForStoryElements
          publishingTimes={
            <ExploreTravelPublishingTimes
              publishFrom={story.publishFrom}
              updatedOn={story.updatedOn}
            />
          }
          showAds={!isSponsored}
          showByAuthor
          showConversationStarter={false}
          textToSpeechProjectId={story.textToSpeechProjectId}
          usePaywall
        />

        <div className="mx-5 mb-8 md:mx-auto md:w-180">
          <ExploreTravelTopics tags={storyTags} />
        </div>
        <div className="mx-5 border-gray-300 px-0 py-4 md:mx-auto md:w-180 md:border-t-1 md:py-8">
          <ExploreTravelBottomActions />
        </div>
        <div className="mx-5 mb-8 md:mx-auto md:w-180">
          <AuthorBio authors={story.authors} />
        </div>
        <div className="mx-10 md:mx-auto md:w-[821px]">
          <Comments
            allowAds={false}
            id={story.id}
            showTrendingArticles={false}
            state={story.comments}
            title={story.title}
          />
        </div>
        {exploreTravelRecirculationFeature.enabled &&
          exploreTravelRecirculationFeature.data && (
            <div className="mx-5 mt-20 md:mx-auto md:w-[1220px]">
              <ExploreTravelRecirculationWidget
                latestStories={
                  exploreTravelRecirculationFeature.data?.latestStoryWidget
                    .stories ?? []
                }
                latestStoriesLimit={
                  exploreTravelRecirculationFeature.data?.latestStoryWidget
                    .limit ?? 0
                }
                mostPopularStories={
                  exploreTravelRecirculationFeature.data
                    ?.mostPopularStoryWidget.stories ?? []
                }
                mostPopularStoriesLimit={
                  exploreTravelRecirculationFeature.data
                    ?.mostPopularStoryWidget.limit ?? 0
                }
              />
            </div>
          )}
      </Container>
    </TemplateWrapper>
  );
}
