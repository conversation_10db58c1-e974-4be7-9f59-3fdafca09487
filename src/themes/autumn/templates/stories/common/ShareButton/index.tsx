import {
  CloseButton,
  Popover,
  PopoverButton,
  PopoverPanel,
} from '@headlessui/react';
import clsx from 'clsx';
import { useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { twMerge } from 'tailwind-merge';

import CloseIcon from 'themes/autumn/components/icons/CloseIcon';
import ShareIcon from 'themes/autumn/components/icons/ShareIcon';
import { getSocialShareUrls } from 'themes/autumn/components/stories/StorySocials/utils';
import { DeviceType } from 'util/device';
import { useDeviceTypeFromWidth } from 'util/hooks';

import FacebookIcon from '../FacebookIcon';
import { IconButton } from '../IconButton';
import LinkIcon from '../LinkIcon';
import LinkedInIcon from '../LinkedInIcon';
import MailIcon from '../MailIcon';
import ShareLink from '../ShareLink';
import TwitterIcon from '../TwitterIcon';
import WhatsappIcon from '../WhatsappIcon';

interface ShareButtonProps {
  className?: string;
  showTriggerTitle?: boolean;
  url: string;
}

const ShareButton: React.FC<ShareButtonProps> = ({
  className,
  showTriggerTitle = true,
  url,
}) => {
  const [canShare, setCanShare] = useState<boolean>(false);
  const deviceType = useDeviceTypeFromWidth();
  const isMobile = deviceType === DeviceType.MOBILE;

  useEffect(() => {
    setCanShare(!!navigator.share);
  }, []);

  function onMobileShareClick() {
    navigator.share?.({ title: document.title, url }).catch((e) => {
      console.error('Failed to share link', e);
    });
  }

  const encodedUrl = encodeURIComponent(url);
  const socialUrls = getSocialShareUrls(encodedUrl);

  const handleCopyLink = useCallback(
    (e: React.MouseEvent<HTMLAnchorElement>) => {
      e.preventDefault();
      if (!url) {
        return;
      }

      // eslint-disable-next-line compat/compat
      navigator.clipboard
        ?.writeText(url)
        .then(() => {
          toast.success('Link copied to clipboard');
        })
        .catch((err) => {
          console.error('Failed to copy link', err);
        });
    },
    [url],
  );

  return isMobile && canShare ? (
    <IconButton
      Icon={ShareIcon}
      aria-label="Share"
      className={className}
      onClick={() => onMobileShareClick()}
      title="Share"
    >
      {showTriggerTitle && 'Share'}
    </IconButton>
  ) : (
    <Popover className="relative">
      {({ open }) => (
        <>
          <PopoverButton
            aria-label="Share"
            className={twMerge(
              'flex h-9 w-[94px] cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100 focus-visible:outline-none',
              className,
            )}
          >
            <ShareIcon />
            {showTriggerTitle && <span className="text-xs">Share</span>}
          </PopoverButton>
          <div
            className={clsx(
              'absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white',
              {
                hidden: !open,
              },
            )}
          />

          <PopoverPanel className="absolute left-1/2 top-[51px] z-40 flex h-[280px] w-[300px] -translate-x-1/2 flex-col items-center justify-start rounded-md border-1 border-gray-200 bg-white px-8 py-4">
            <CloseButton className="self-end rounded-full p-3 hover:bg-gray-100">
              <CloseIcon />
            </CloseButton>

            <span className="mb-5 self-start text-sm font-semibold text-gray-900">
              Share Options
            </span>
            <div className="flex size-full flex-col">
              <div className="flex h-1/2 w-full flex-row items-center justify-between gap-7">
                <ShareLink
                  Icon={LinkIcon}
                  href="#"
                  onClick={handleCopyLink}
                  text="Copy Link"
                />
                <ShareLink
                  Icon={MailIcon}
                  href={socialUrls.email}
                  text="Email"
                />
                <ShareLink
                  Icon={FacebookIcon}
                  href={socialUrls.facebook}
                  text="Facebook"
                />
              </div>
              <div className="flex h-1/2 w-full flex-row items-center justify-between gap-7">
                <ShareLink
                  Icon={TwitterIcon}
                  href={socialUrls.twitter}
                  text="Twitter"
                />
                <ShareLink
                  Icon={LinkedInIcon}
                  href={socialUrls.linkedin}
                  text="LinkedIn"
                />
                <ShareLink
                  Icon={WhatsappIcon}
                  href={socialUrls.whatsapp}
                  text="WhatsApp"
                />
              </div>
            </div>
          </PopoverPanel>
        </>
      )}
    </Popover>
  );
};

export default ShareButton;
