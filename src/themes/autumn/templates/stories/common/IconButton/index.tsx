import { twMerge } from 'tailwind-merge';

import type React from 'react';

export interface IconButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  Icon: React.FC<{ className: string }>;
  className?: string;
  iconClassName?: string;
  textClassName?: string;
}

export const IconButton: React.FC<
  React.PropsWithChildren<IconButtonProps>
> = ({
  Icon,
  children,
  className,
  iconClassName,
  onClick,
  textClassName,
  ...props
}) => (
  <button
    className={twMerge(
      'flex h-9 w-[94px] cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100',
      className,
    )}
    onClick={onClick}
    // eslint-disable-next-line react/jsx-props-no-spreading
    {...props}
    type="button"
  >
    <Icon className={twMerge('size-4.5', iconClassName)} />
    <span className={twMerge('text-xs', textClassName)}>{children}</span>
  </button>
);
