import Script from 'next/script';
import { useCallback, useEffect, useRef, useState } from 'react';

import OnboardingTooltip from 'components/Onboarding/Tooltip';
import { useAppSelector } from 'store/hooks';

import {
  AUDIO_LOAD_DELAY,
  AUDIO_NATIVE_URL_REQUEST_MESSAGE,
  ONBOARDING_TEXT_TO_SPEECH_DISMISSED_KEY,
} from './constants';

interface Props {
  projectId?: string;
}

function TextToSpeech({ projectId }: Props): React.ReactElement | null {
  const hasPianoPaywall = useAppSelector(
    (state) =>
      state.features.piano.enabled &&
      (state.piano.hasPaywall || state.piano.loadingPaywall),
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [iframeSrc, setIframeSrc] = useState('');
  const [playerLoaded, setPlayerLoaded] = useState(false);

  const checkForIframe = useCallback(() => {
    if (containerRef.current) {
      const iframe = containerRef.current.querySelector('iframe');
      if (iframe) {
        setIframeSrc(iframe.src);
        return true;
      }
    }
    return false;
  }, []);

  useEffect(() => {
    if (!containerRef.current || !scriptLoaded) return () => {};

    const observer = new MutationObserver(() => {
      if (checkForIframe()) {
        observer.disconnect();
      }
    });

    observer.observe(containerRef.current, { childList: true });

    if (checkForIframe()) {
      observer.disconnect();
    }

    return () => {
      observer.disconnect();
    };
  }, [checkForIframe, scriptLoaded]);

  useEffect(() => {
    if (!iframeSrc) return () => {};

    const iframeUrl = new URL(iframeSrc);
    const listener = (event: MessageEvent) => {
      if (event.origin !== iframeUrl.origin) return;

      if (event.data === AUDIO_NATIVE_URL_REQUEST_MESSAGE) {
        // Wait time for the mp3 file to be download
        setTimeout(() => setPlayerLoaded(true), AUDIO_LOAD_DELAY);
      }
    };

    window.addEventListener('message', listener);
    return () => {
      window.removeEventListener('message', listener);
    };
  }, [iframeSrc]);

  if (hasPianoPaywall || !projectId) {
    return null;
  }

  return (
    <>
      <OnboardingTooltip
        action="Listen to this article"
        // eslint-disable-next-line @stylistic/max-len
        description="You have access to audio on this article for a limited time"
        isReady={playerLoaded}
        localStorageKey={ONBOARDING_TEXT_TO_SPEECH_DISMISSED_KEY}
        trigger={
          <div className="w-full md:w-180" ref={containerRef}>
            <div
              data-frameborder="no"
              data-height="90"
              data-playerurl="https://elevenlabs.io/player/index.html"
              data-projectid={projectId}
              // eslint-disable-next-line @stylistic/max-len
              data-publicuserid="82d422b2fa0d7b09699ae5ed97e0c1170e55b273864431dc38de4d529951204f"
              data-scrolling="no"
              data-width="100%"
              id="elevenlabs-audionative-widget"
            >
              Loading the{' '}
              <a
                href="https://elevenlabs.io/text-to-speech"
                rel="noopener"
                target="_blank"
              >
                Text to Speech
              </a>{' '}
              Player...
            </div>
            <p className="text-sm italic text-gray-400">
              Produced by ElevenLabs and ACM using AI narration.
            </p>
          </div>
        }
      />
      <Script
        async
        onLoad={() => setScriptLoaded(true)}
        src="https://elevenlabs.io/player/audioNativeHelper.js"
      />
    </>
  );
}

export default TextToSpeech;
