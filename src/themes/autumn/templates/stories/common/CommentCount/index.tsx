import { twMerge } from 'tailwind-merge';

import CommentIcon from 'themes/autumn/components/icons/CommentIcon';
import Count from 'themes/autumn/components/stories/Comments/Count';

import type { StoryState } from 'store/slices/story';

interface CommentCountProps {
  className?: string;
  containerClassName?: string;
  story: StoryState;
}

const CommentCount: React.FC<CommentCountProps> = ({
  className,
  containerClassName,
  story,
}) => (
  <div className={containerClassName}>
    <Count
      className={twMerge(
        'flex h-[36px] w-[94px] flex-row items-center justify-center rounded-3xl hover:bg-gray-100',
        className,
      )}
      countOnly
      icon={<CommentIcon className="mr-2" />}
      id={story.id}
      state={story.comments}
    />
  </div>
);

export default CommentCount;
