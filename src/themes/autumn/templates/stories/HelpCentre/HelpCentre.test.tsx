import { render, screen } from '@testing-library/react';

import { createStore } from 'store/store';
import { type Story, StoryElementType } from 'types/Story';
import { TestWrapper, genMockStories } from 'util/jest';

import HelpCentreStory from '.';

describe('HelpCentreStory', () => {
  const createMockStore = (storyData: Partial<Story> = {}) => {
    const mockStory = genMockStories({ length: 1 })[0];
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        domain: 'www.example.com',
      },
      story: {
        ...mockStory,
        bodyText: '<p>Test Help Centre body content</p>',
        elements: [],
        seoDescription: 'Test Help Centre description',
        seoTitle: 'SEO Test Help Centre Title',
        title: 'Test Help Centre Title',
        ...storyData,
      },
    }));
    return store;
  };

  it('renders the Help Centre story with title', () => {
    const store = createMockStore();

    render(
      <TestWrapper store={store}>
        <HelpCentreStory />
      </TestWrapper>,
    );

    expect(
      screen.getByRole('heading', { name: 'Test Help Centre Title' }),
    ).toBeInTheDocument();
  });

  it('renders bodyText when no elements are present', () => {
    const store = createMockStore({
      bodyText: '<p>This is the Help Centre body text</p>',
      elements: [],
    });

    render(
      <TestWrapper store={store}>
        <HelpCentreStory />
      </TestWrapper>,
    );

    expect(
      screen.getByText('This is the Help Centre body text'),
    ).toBeInTheDocument();
  });

  it('renders story elements when present', () => {
    const store = createMockStore({
      elements: [
        {
          text: 'Test element content',
          type: StoryElementType.Paragraph,
        },
      ],
    });

    render(
      <TestWrapper store={store}>
        <HelpCentreStory />
      </TestWrapper>,
    );

    expect(
      screen.getByRole('heading', { name: 'Test Help Centre Title' }),
    ).toBeInTheDocument();
  });

  it('handles missing bodyText and elements gracefully', () => {
    const store = createMockStore({
      bodyText: '',
      elements: [],
    });

    render(
      <TestWrapper store={store}>
        <HelpCentreStory />
      </TestWrapper>,
    );

    expect(
      screen.getByRole('heading', { name: 'Test Help Centre Title' }),
    ).toBeInTheDocument();
  });

  it('uses seoTitle when provided', () => {
    const store = createMockStore({
      seoTitle: 'SEO Optimized Title',
      title: 'Regular Title',
    });

    render(
      <TestWrapper store={store}>
        <HelpCentreStory />
      </TestWrapper>,
    );

    expect(
      screen.getByRole('heading', { name: 'Regular Title' }),
    ).toBeInTheDocument();
  });

  it('falls back to regular title when seoTitle is not provided', () => {
    const store = createMockStore({
      seoTitle: '',
      title: 'Regular Title Only',
    });

    render(
      <TestWrapper store={store}>
        <HelpCentreStory />
      </TestWrapper>,
    );

    expect(
      screen.getByRole('heading', { name: 'Regular Title Only' }),
    ).toBeInTheDocument();
  });

  it('matches snapshot', () => {
    const store = createMockStore({
      // eslint-disable-next-line @stylistic/max-len
      bodyText:
        '<p>This is the Help Centre body content with <strong>some HTML</strong></p>',
      seoDescription: 'This is a test Help Centre description for SEO',
      seoTitle: 'SEO Help Centre Test Title',
      title: 'Help Centre Test Title',
    });

    const { container } = render(
      <TestWrapper store={store}>
        <HelpCentreStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
