import React from 'react';

import { useAppSelector } from 'store/hooks';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageBreadcrumb from 'themes/autumn/components/page/PageBreadcrumb';
import StoryComponentWrapper from 'themes/autumn/components/stories/StoryElements/StoryComponentWrapper';
import { helpCentreStoryComponentMap } from 'themes/autumn/components/storyElements/storyElementsMap';
import Zone from 'themes/autumn/components/zone/Zone';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';

import InfoWithCTA from '../../zoneItems/infowithcta/Default';

import type { Story } from 'types/Story';

const renderStoryElements = (story: Story) => {
  if (!story.elements?.length) {
    return story.bodyText ? (
      <div
        className="prose max-w-none prose-p:text-base prose-p:leading-6"
        dangerouslySetInnerHTML={{ __html: story.bodyText }}
      />
    ) : null;
  }

  return (
    <>
      {story.elements.map((element, index) => {
        const StoryComponent = helpCentreStoryComponentMap[element.type]?.[0];

        if (!StoryComponent) {
          return null;
        }

        const elementKey = `${element.type}-${index}`;

        return (
          <StoryComponentWrapper key={elementKey} type={element.type}>
            <StoryComponent element={element} index={index} />
          </StoryComponentWrapper>
        );
      })}
    </>
  );
};
export default function HelpCentreStory(): React.ReactElement {
  const story = useAppSelector((state) => state.story);
  const seoTitle = story.seoTitle || story.title;
  const { seoDescription } = story;

  return (
    <TemplateWrapper
      bgColor="bg-stone-100"
      seoDescription={seoDescription}
      seoTitle={seoTitle}
      showNavigationAd={false}
    >
      <Container noGutter>
        <div className="border-b border-gray-300">
          <Zone itemMargin="m-0" name={ZoneName.NAVIGATION} snapHeading />
        </div>
        <div className="mx-auto mt-8 flex w-full max-w-container flex-col px-4 font-inter md:px-6 xl:px-0">
          <PageBreadcrumb />
          {story?.title && (
            <h1 className="mt-8 font-inter text-3xl font-medium text-gray-900 md:!text-3xl lg:leading-4">
              {story.title}
            </h1>
          )}
        </div>
      </Container>
      <Container className="mt-4 flex flex-col pt-5 font-inter">
        <div className="w-full max-w-3xl rounded-lg border border-gray-200 bg-white px-6 py-9 shadow md:p-14">
          {renderStoryElements(story)}
        </div>
        <div className="mt-8">
          <InfoWithCTA
            elementId={0}
            index={0}
            order={0}
            zoneItemData={{
              buttonText: 'Contact Customer Support',
              description:
                'Can’t find the answer you’re looking for? Feel free to contact our customer support team. Monday to Friday 9am to 5pm or call on 1300 131 095',
              template: 'Default',
              title: 'STILL HAVE QUESTIONS?',
              url: '/help-centre/contact-us/',
            }}
            zoneItemId={0}
            zoneItemType={ZoneItemType.InfoWithCTA}
            zoneName={ZoneName.MAIN}
          />
        </div>
      </Container>
    </TemplateWrapper>
  );
}
