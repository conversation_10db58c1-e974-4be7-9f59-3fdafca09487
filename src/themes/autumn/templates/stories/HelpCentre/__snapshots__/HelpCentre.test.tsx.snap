// Jest <PERSON>napshot v1, https://jestjs.io/docs/snapshot-testing

exports[`HelpCentreStory matches snapshot 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"NewsArticle","alternativeHeadline":"Help Centre Test Title","description":"This is a test Help Centre description for SEO","isPartOf":{"@type":["CreativeWork","Product"],"name":"","productID":"example.com/subscribe/"},"keywords":"domestic","mainEntityOfPage":{"@type":"WebPage","@id":"https://resi.uatz.view.com.au/advice/selling/clean-code-vs-dirty-code-react-best-practices/"},"headline":"SEO Help Centre Test Title","image":[],"articleSection":"","dateCreated":"2021-06-30T00:27:33.248683+00:00","datePublished":"2021-06-30T00:27:33.248683+00:00","dateModified":"2021-06-30T00:27:33.248683+00:00","author":{"@type":"Person","name":"Callum Godde"},"articleBody":"&lt;p&gt;This is the Help Centre body content with &lt;strong&gt;some HTML&lt;/strong&gt;&lt;/p&gt;","isAccessibleForFree":false}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"},{"@type":"ListItem","position":2,"item":"https://story/100000","name":"SEO Help Centre Test Title"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https://www.example.com/"}
  </script>
  <div
    class="bg-stone-100 bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-sm md:max-w-lg"
    >
      <div
        class="border-b border-gray-300"
      />
      <div
        class="mx-auto mt-8 flex w-full max-w-container flex-col px-4 font-inter md:px-6 xl:px-0"
      >
        <h1
          class="mt-8 font-inter text-3xl font-medium text-gray-900 md:!text-3xl lg:leading-4"
        >
          Help Centre Test Title
        </h1>
      </div>
    </div>
    <div
      class="mx-auto w-full max-w-sm md:max-w-lg px-4 md:px-6 xl:px-0 mt-4 flex flex-col pt-5 font-inter"
    >
      <div
        class="w-full max-w-3xl rounded-lg border border-gray-200 bg-white px-6 py-9 shadow md:p-14"
      >
        <div
          class="prose max-w-none prose-p:text-base prose-p:leading-6"
        >
          <p>
            This is the Help Centre body content with 
            <strong>
              some HTML
            </strong>
          </p>
        </div>
      </div>
      <div
        class="mt-8"
      >
        <div
          class="flex flex-col gap-7 bg-stone-200 px-4 py-6 font-inter md:flex-row md:items-center md:gap-6 md:p-8 lg:rounded-xl"
        >
          <div
            class="flex flex-1 flex-col gap-3"
          >
            <h2
              class="text-xs font-bold uppercase tracking-wider text-gray-900"
            >
              STILL HAVE QUESTIONS?
            </h2>
            <p
              class="text-base font-normal leading-6 text-gray-900 lg:whitespace-pre-line"
            >
              Can’t find the answer you’re looking for? Feel free to contact our customer support team. Monday to Friday 9am to 5pm or call on 
              <a
                class="text-blue-600 underline hover:text-blue-800 lg:hidden text-blue-600 underline decoration-blue-400 hover:text-blue-600 hover:decoration-blue-600 visited:text-gray-500 visited:decoration-gray-500"
                href="tel:+611300131095"
              >
                1300 131 095
              </a>
              <span
                class="hidden lg:inline"
              >
                1300 131 095
              </span>
            </p>
          </div>
          <a
            class="flex w-full items-center justify-center gap-2 rounded bg-black px-4 py-3 text-sm font-medium text-white md:w-auto"
            href="https://www.example.com/help-centre/contact-us/"
          >
            Contact Customer Support
          </a>
        </div>
      </div>
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  />
</div>
`;
