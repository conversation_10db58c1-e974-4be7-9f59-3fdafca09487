import React from 'react';

import { useAppSelector } from 'store/hooks';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import StoryElements from 'themes/autumn/components/stories/StoryElements';
import ExploreTravelPublishingTimes from 'themes/autumn/components/stories/StoryPageHeadline/PublishingTimes/ExploreTravel';
import { exploreTravelStoryComponentMap } from 'themes/autumn/components/storyElements/storyElementsMap';

import ExploreTravelBottomActions from '../ExploreTravelStory/ExploreTravelBottomActions';
import ExploreTravelHeader from '../ExploreTravelStory/ExploreTravelHeader';

export default function CommercialStory(): React.ReactElement {
  const story = useAppSelector((state) => state.story);
  const seoTitle = story.seoTitle || story.title;
  const { elements, seoDescription } = story;

  return (
    <TemplateWrapper
      seoDescription={seoDescription}
      seoTitle={seoTitle}
      showNavigationAd={false}
    >
      <Container className="flex flex-col pt-5" noGutter>
        <ExploreTravelHeader showCommentCount={false} />
        <StoryElements
          adFrequency={0}
          authors={story.authors}
          byline={story.byline}
          classNameForStoryComponentWrapper="!mx-0 md:!mx-auto flex font-inter"
          customStoryComponentMap={exploreTravelStoryComponentMap}
          elements={elements}
          hideRecommendation
          isTextToSpeechEnabled={story.isTextToSpeechEnabled}
          noMaxWidthForStoryElements
          publishingTimes={
            <ExploreTravelPublishingTimes
              publishFrom={story.publishFrom}
              updatedOn={story.updatedOn}
            />
          }
          showByAuthor
          showConversationStarter={false}
          textToSpeechProjectId={story.textToSpeechProjectId}
        />
        <div className="mx-5 border-gray-300 px-0 py-4 md:mx-auto md:w-180 md:border-t-1 md:py-8">
          <ExploreTravelBottomActions showCommentCount={false} />
        </div>
      </Container>
    </TemplateWrapper>
  );
}
