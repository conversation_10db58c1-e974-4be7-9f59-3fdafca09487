'use client';

import {
  type <PERSON>EventHand<PERSON>,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { getPianoReady, onPianoReady } from 'components/Piano/ready';
import { useAppSelector } from 'store/hooks';
import { CompleteProfileEnrichment } from 'store/slices/features';
import Button from 'themes/autumn/components/generic/Button';
import Link from 'themes/autumn/components/generic/Link';
import Skeleton from 'themes/autumn/components/generic/Skeleton';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import {
  type PhoenixApiError,
  type PhoenixSharedSubscriptionAccount,
  type PhoenixSharedSubscriptionListResponse,
} from 'types/phoenix-types/responses';
import { authSuccessToast, handleErrors, redirectToLogin } from 'util/auth';
import {
  listSharedSubscriptions,
  removeSharedInvite,
  resendSharedInvite,
  sendSharedInvite,
} from 'util/auth/shared-subs';
import { setGtmDataLayer } from 'util/gtm';
import { useFocusOnMobileKeyboard, useOnce } from 'util/hooks';

import TextField from './TextField';

interface SharedSeatProps {
  acc: PhoenixSharedSubscriptionAccount | undefined;
  remove: (email: string) => void;
  resend: (email: string) => void;
}

function SharedSeat({ acc, remove, resend }: SharedSeatProps) {
  return (
    <div className="flex flex-col gap-y-3">
      <div className="flex flex-row justify-between gap-x-2">
        <Skeleton
          className="w-full grow truncate"
          showContent={acc !== undefined}
        >
          <span className="text-sm font-medium leading-4">{acc?.email}</span>
        </Skeleton>
        <Skeleton showContent={acc !== undefined}>
          <button
            className="shrink-0 text-sm leading-3 text-blue-600 underline decoration-blue-400 visited:text-gray-500 visited:decoration-gray-500 hover:text-blue-600 hover:decoration-blue-600"
            onClick={() => acc && remove(acc.email)}
            type="button"
          >
            Remove
          </button>
        </Skeleton>
      </div>
      <Skeleton showContent={acc !== undefined}>
        {acc === undefined || !!acc.userId ? (
          <span className="text-xs font-medium leading-3 text-green-600">
            Active
          </span>
        ) : (
          <div className="flex flex-row gap-x-2 text-xs font-medium leading-3 text-gray-500">
            <span>Invite not claimed.</span>
            <button
              className="underline"
              onClick={() => resend(acc.email)}
              type="button"
            >
              Resend
            </button>
          </div>
        )}
      </Skeleton>
    </div>
  );
}

export default function SharedInvite() {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const [sharedState, setSharedState] = useState<
    | Extract<PhoenixSharedSubscriptionListResponse, { status: 'found' }>
    | undefined
  >(undefined);
  const [errors, setErrors] = useState<PhoenixApiError[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [email, setEmail] = useState('');
  const [isCheckout, setIsCheckout] = useState(false);

  useEffect(() => {
    if (
      new URLSearchParams(window.location.search).get('checkout') === 'true'
    ) {
      setIsCheckout(true);
    }
  }, []);

  const emailErrors = useMemo(
    () => errors.filter((e) => e.field === 'email'),
    [errors],
  );

  const fetchSharedSubscriptions = useCallback(async () => {
    const tp = await getPianoReady();
    const accessToken = tp.pianoId.getToken();
    if (!accessToken) {
      redirectToLogin();
      return;
    }

    try {
      const res = await listSharedSubscriptions({
        accessToken,
      });

      if (!res.success) {
        handleErrors({
          errors: res.errors,
          setErrors,
        });
        return;
      }

      if (res.data.status !== 'found') {
        window.location.href = '/';
        return;
      }

      setSharedState(res.data);
    } catch (e) {
      console.error(e);
    }
  }, []);

  const onSubmit = useCallback<FormEventHandler<HTMLFormElement>>(
    (ev) => {
      ev.preventDefault();
      if (!sharedState) {
        return;
      }

      setIsSubmitting(true);
      onPianoReady((tp) => {
        const accessToken = tp.pianoId.getToken();
        if (!accessToken) {
          redirectToLogin();
          return;
        }

        sendSharedInvite({
          accessToken,
          email,
          subscriptionId: sharedState.subscriptionId,
        })
          .then((res) => {
            if (res.success) {
              fetchSharedSubscriptions()
                .catch((e) => console.error(e))
                .finally(() => {
                  authSuccessToast('Invite sent!');
                  setIsSubmitting(false);
                  setEmail('');
                });
              return;
            }

            handleErrors({
              errors: res.errors,
              setErrors,
            });
            setIsSubmitting(false);
          })
          .catch((e) => {
            console.error(e);
            setIsSubmitting(false);
          });
      });
    },
    [email, sharedState, fetchSharedSubscriptions],
  );

  const onEmailFocus = useCallback(() => {
    setErrors((curr) => curr.filter((e) => e.field !== 'email'));
  }, []);

  const resendInvite = useCallback(
    (target: string) => {
      if (!sharedState) {
        return;
      }

      setIsSubmitting(true);
      onPianoReady((tp) => {
        const accessToken = tp.pianoId.getToken();
        if (!accessToken) {
          redirectToLogin();
          return;
        }

        resendSharedInvite({
          accessToken,
          email: target,
          subscriptionId: sharedState.subscriptionId,
        })
          .then((res) => {
            if (res.success) {
              authSuccessToast('Invite has been re-sent.');
              return;
            }

            handleErrors({
              errors: res.errors,
              setErrors,
            });
          })
          .catch((e) => console.error(e))
          .finally(() => {
            setIsSubmitting(false);
          });
      });
    },
    [sharedState],
  );

  const removeInvite = useCallback(
    (target: string) => {
      if (!sharedState) {
        return;
      }

      setIsSubmitting(true);
      onPianoReady((tp) => {
        const accessToken = tp.pianoId.getToken();
        if (!accessToken) {
          redirectToLogin();
          return;
        }

        removeSharedInvite({
          accessToken,
          email: target,
          subscriptionId: sharedState.subscriptionId,
        })
          .then((res) => {
            if (res.success) {
              fetchSharedSubscriptions()
                .catch((e) => console.error(e))
                .finally(() => {
                  authSuccessToast('Invite removed.');
                  setIsSubmitting(false);
                });
              return;
            }

            handleErrors({
              errors: res.errors,
              setErrors,
            });
            setIsSubmitting(false);
          })
          .catch((e) => {
            console.error(e);
            setIsSubmitting(false);
          });
      });
    },
    [sharedState, fetchSharedSubscriptions],
  );

  const onNeedMoreSeatsClick = useCallback(() => {
    setGtmDataLayer({
      data: {
        label: 'need_more_seats',
      },
      event: 'quicklink_clicks',
    });
  }, []);

  const onDoThisLaterClick = useCallback(() => {
    setGtmDataLayer({
      data: {
        label: 'do_this_later',
      },
      event: 'quicklink_clicks',
    });
  }, []);

  useFocusOnMobileKeyboard({ offset: 80 });

  useOnce(() => {
    fetchSharedSubscriptions().catch((e) => console.error(e));
    return true;
  }, [fetchSharedSubscriptions]);

  if (!pianoFeature.enabled) {
    return null;
  }

  const isLoading = sharedState === undefined;
  const supportsEnrichment =
    pianoFeature.data.completeProfileEnrichments !==
    CompleteProfileEnrichment.NONE;
  const step = supportsEnrichment ? 3 : 2;
  const totalSeats = sharedState?.sharedAccountLimit ?? 0;
  const freeSeats = totalSeats - (sharedState?.sharedAccounts.length ?? 0);

  const buttonDestination = isCheckout ? '/' : '/my-account/';
  let buttonText = isCheckout ? "I'll do this later" : 'Return to My Account';
  if (isCheckout && freeSeats <= 0) {
    buttonText = 'Done';
  }

  return (
    <TemplateWrapper
      footer={null}
      free
      hideSmartBanner
      nav={
        <SubscriptionNav
          showContact={false}
          showHelp={false}
          showLogin={false}
          showPageName={false}
        />
      }
      showGutterAd={false}
      showNavigationAd={false}
      showOutages={false}
    >
      <div className="mx-auto flex flex-col items-center pt-11 font-inter text-gray-900">
        <div className="flex w-full max-w-344 flex-col items-center gap-y-3">
          {(isLoading || isCheckout) && (
            <Skeleton className="my-1 w-full" showContent={!isLoading}>
              <div className="relative text-sm leading-4 text-gray-600">
                Step {step}/{step}
              </div>
            </Skeleton>
          )}
          <Skeleton showContent={!isLoading}>
            <div className="text-xl font-semibold leading-5 md:text-2xl md:leading-6">
              Please enter the email address for your invites
            </div>
          </Skeleton>
        </div>
        <div className="mt-3.5 w-full max-w-344 md:mt-5">
          <form onSubmit={onSubmit}>
            <TextField
              disabled={freeSeats <= 0}
              errors={emailErrors}
              name={`You have ${freeSeats}/${totalSeats} invites remaining`}
              onFocus={onEmailFocus}
              placeholder="Email Address"
              setField={(v: string) => setEmail(v)}
              show={!isLoading}
              skeleton
              value={email ?? 'Email Address'}
            />
            <Skeleton className="mt-3" showContent={!isLoading}>
              <Button
                bgColor="bg-red-600 disabled:bg-gray-300"
                desktopFullWidth
                disabled={isSubmitting || freeSeats <= 0}
                fontSize="text-sm"
                height="h-10.5 md:h-10.5 lg:h-10.5"
                hoverColor="hover:bg-red-700 disabled:hover:bg-gray-300"
                mobileFullWidth
                text="Send Invite"
                textColor="text-white"
                type="submit"
              />
            </Skeleton>
          </form>
          <div className="mt-10 w-full">
            {(isLoading || sharedState.sharedAccounts?.length > 0) && (
              <>
                <Skeleton showContent={!isLoading}>
                  <div className="text-sm font-semibold leading-none">
                    Manage seats
                  </div>
                </Skeleton>
                <div className="mt-6 flex flex-col">
                  {(
                    sharedState?.sharedAccounts ??
                    (new Array(3).fill(undefined) as undefined[])
                  ).map((acc, idx) => (
                    <>
                      {idx !== 0 && (
                        <div className="my-4 h-px w-full bg-gray-200" />
                      )}
                      <SharedSeat
                        acc={acc}
                        key={acc?.email ?? idx}
                        remove={removeInvite}
                        resend={resendInvite}
                      />
                    </>
                  ))}
                </div>
              </>
            )}
            <Skeleton className="mt-10" showContent={!isLoading}>
              <Button
                bgColor="border border-ct-blue hover:border-gray-300"
                buttonClassName="transition-all"
                className="select-none"
                desktopFullWidth
                fontSize="font-semibold"
                hoverColor=""
                href={buttonDestination}
                mobileFullWidth
                onClick={onDoThisLaterClick}
                text={buttonText}
                textColor="text-gray-900"
              />
            </Skeleton>
            <div className="mt-6 flex items-center justify-center">
              <Skeleton showContent={!isLoading}>
                <Link
                  className="text-xs leading-3"
                  href="mailto:<EMAIL>"
                  onClick={onNeedMoreSeatsClick}
                >
                  Need more seats? Upgrade
                </Link>
              </Skeleton>
            </div>
          </div>
        </div>
      </div>
    </TemplateWrapper>
  );
}
