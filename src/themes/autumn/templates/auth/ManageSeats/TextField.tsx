'use client';

import clsx from 'clsx';

import type { RefObject } from 'react';
import type { PhoenixApiError } from 'types/phoenix-types/responses';

interface TextField {
  disabled?: boolean;
  errors: PhoenixApiError[];
  name: string;
  onFocus: () => void;
  placeholder?: string;
  ref?: RefObject<HTMLInputElement | null>;
  setField: (val: string) => void;
  show?: boolean;
  skeleton?: boolean;
  value: string;
}

export default function TextField({
  disabled,
  errors,
  name,
  onFocus,
  placeholder,
  ref,
  setField,
  show = true,
  skeleton,
  value,
}: TextField) {
  return (
    <div
      className={clsx({
        hidden: !show && !skeleton,
        'pointer-events-none select-none': !show,
      })}
    >
      <div className="relative inline-block text-sm font-semibold leading-3">
        {skeleton && !show && (
          <div className="absolute size-full bg-white">
            <div className="size-full animate-pulse bg-gray-300" />
          </div>
        )}
        <span
          className={clsx({
            'opacity-0': !show,
          })}
        >
          {name}
        </span>
      </div>
      <div className="relative mt-2">
        {skeleton && !show && (
          <div className="absolute size-full bg-white">
            <div className="size-full animate-pulse bg-gray-300" />
          </div>
        )}
        {disabled ? (
          <input
            className={clsx(
              'w-full text-ellipsis rounded border border-gray-400 py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-400',
              {
                'opacity-0': !show,
              },
            )}
            disabled
            placeholder={value}
          />
        ) : (
          <input
            className={clsx(
              'w-full text-ellipsis rounded border border-gray-400 py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500',
              errors.length > 0
                ? 'border-red-500 pr-12'
                : 'border-gray-900 pr-5',
              {
                'opacity-0': !show,
              },
            )}
            disabled={!show}
            onChange={(e) => setField(e.target.value)}
            onFocus={onFocus}
            placeholder={placeholder}
            ref={ref}
            value={value}
          />
        )}

        {errors.length > 0 && (
          <div className="absolute right-4 top-1/2 -translate-y-1/2">
            <svg fill="none" height="24" width="24">
              <path
                clipRule="evenodd"
                d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2ZM13 13V7h-2v6h2Zm0 4v-2h-2v2h2Zm-9-5c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8-8 3.58-8 8Z"
                fill="#DC2626"
                fillRule="evenodd"
              />
            </svg>
          </div>
        )}
      </div>
      {errors.length > 0 && (
        <div className="mt-2 flex flex-col gap-y-2 text-sm font-medium text-red-600">
          {errors.map((e) => (
            <div key={e.message}>
              {e.message.slice(0, 1).toUpperCase()}
              {e.message.slice(1)}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
