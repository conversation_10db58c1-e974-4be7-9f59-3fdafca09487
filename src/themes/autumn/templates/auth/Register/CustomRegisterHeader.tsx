import React from 'react';

import NewsletterRegisterCard from './NewsletterRegisterCard';

import type { RegionalNewsletter } from 'types/mail';

interface GenericHeaderProps {
  subtitle: React.ReactNode;
  title: string;
}

function GenericHeader({
  subtitle,
  title,
}: GenericHeaderProps): React.ReactElement {
  return (
    <>
      <div className="pt-11">
        <div className="flex justify-center">
          <span className="flex h-5 w-14 items-center justify-center rounded-full bg-teal-600 text-xs uppercase text-white">
            free
          </span>
        </div>
        <h2 className="mb-4 text-center font-inter text-2xl font-semibold text-slate-900 md:text-3xl">
          {title}
        </h2>
        <p className="text-center font-inter text-sm text-slate-600">
          {subtitle}
        </p>
      </div>
      <div className="mt-12 w-full text-center text-2xl font-semibold">
        Log in or create an account
      </div>
    </>
  );
}

const GENERIC_REGISTER_CONTENT: Record<string, React.ReactElement> = {
  bookmark: (
    <div className="mt-12 w-full text-center text-2xl font-semibold">
      Log in or create a free account to save this to My Saved List
    </div>
  ),
  'ugc-form': (
    <GenericHeader
      subtitle={
        <>
          You can now contribute content to our community section. Just create
          a new <span className="font-semibold">free account</span> or login
          with your existing one.
        </>
      }
      title="Share Your Content"
    />
  ),
};

interface CustomRegisterScreenProps {
  newsletter?: RegionalNewsletter;
  referrer: string;
}

export default function CustomRegisterHeader({
  newsletter,
  referrer,
}: CustomRegisterScreenProps) {
  if (newsletter) {
    return <NewsletterRegisterCard newsletter={newsletter} />;
  }

  if (!GENERIC_REGISTER_CONTENT[referrer]) {
    return null;
  }

  const content = GENERIC_REGISTER_CONTENT[referrer];

  return content;
}
