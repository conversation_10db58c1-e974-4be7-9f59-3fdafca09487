import { faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React from 'react';

import Link from 'themes/autumn/components/generic/Link';

import type { RegionalNewsletter } from 'types/mail';

interface NewsletterRegisterCardProps {
  newsletter: RegionalNewsletter;
}

function NewsletterRegisterCard({
  newsletter,
}: NewsletterRegisterCardProps): React.ReactElement | null {
  const { description, frequency, icon, name, subscriberOnly } = newsletter;

  return (
    <>
      <div className="mt-4">
        <Link
          className="flex max-w-fit items-center gap-x-2 text-sm"
          href="/newsletters/"
          noStyle
        >
          <FontAwesomeIcon icon={faArrowLeft} />
          View all newsletters
        </Link>
        <div className="flex w-full border-b border-gray-300 bg-white">
          <div className="flex flex-1 flex-col justify-between py-4 pr-4">
            <div>
              <h3 className="font-merriweather text-lg font-bold text-gray-900">
                {name}
              </h3>
              <p className="mt-1 text-sm text-gray-600">{description}</p>
            </div>

            <div className="mt-4">
              <span className="text-sm text-gray-500">
                {frequency?.toUpperCase()}
              </span>
              {subscriberOnly ? (
                <span className="ml-2 inline-flex items-center rounded-full bg-orange-100 px-2 py-0.5 text-xs font-bold text-orange-700">
                  Subscriber Only
                </span>
              ) : (
                <span className="ml-2 inline-flex items-center rounded-full bg-green-600 px-2 py-0.5 text-xs font-bold text-white">
                  FREE
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center">
            <div className="flex w-28 items-center justify-center overflow-hidden rounded-md">
              <img
                alt={`${name} Newsletter`}
                className="size-full object-contain"
                src={icon}
                title={`${name} Newsletter`}
              />
            </div>
          </div>
        </div>
      </div>
      {subscriberOnly ? (
        <div className="mt-12 w-full text-center text-xl font-semibold">
          Log in or create an account to receive this newsletter
        </div>
      ) : (
        <div className="mt-12 w-full text-center text-xl font-semibold">
          Log in or create an account to receive this FREE newsletter
        </div>
      )}
    </>
  );
}

export default NewsletterRegisterCard;
