import { useState } from 'react';

import Button from 'themes/autumn/components/generic/Button';
import { useOnce } from 'util/hooks';

import FieldCheckboxInput from '../inputs/CheckboxInput';
import {
  type EnrichmentComponentProps,
  FARMING_INVOLVMENT_ANSWERS,
  FARMING_SECTORS_ANSWERS,
  mapMultipleChoice,
  parseMultipleChoice,
  updateMultipleChoice,
} from '../util/enrichments';

export default function AgsEnrichments({
  extendedProfile,
  isSubmitting,
  submit,
}: EnrichmentComponentProps) {
  const [farmingInvolvement, setFarmingInvolvement] = useState(
    Array<boolean>(FARMING_INVOLVMENT_ANSWERS.length).fill(false),
  );
  const [farmingSectors, setFarmingSectors] = useState(
    Array<boolean>(FARMING_SECTORS_ANSWERS.length).fill(false),
  );

  const farmingInvolvementNoneIdx = FARMING_INVOLVMENT_ANSWERS.length - 1;
  const hasFarmingInvolvement = farmingInvolvement.some(
    (checked, idx) => checked && idx !== farmingInvolvementNoneIdx,
  );

  function updateFarmingInvolement(idx: number, value: boolean) {
    if (idx === farmingInvolvementNoneIdx) {
      setFarmingInvolvement(
        updateMultipleChoice(
          Array<boolean>(FARMING_INVOLVMENT_ANSWERS.length).fill(false),
          idx,
          value,
        ),
      );
      setFarmingSectors(
        Array<boolean>(FARMING_SECTORS_ANSWERS.length).fill(false),
      );
      return;
    }

    const updatedArray = updateMultipleChoice(farmingInvolvement, idx, value);
    if (value) {
      updatedArray[farmingInvolvementNoneIdx] = false;
    }
    setFarmingInvolvement(updatedArray);
  }

  useOnce(() => {
    if (extendedProfile) {
      extendedProfile.custom_field_values.forEach((customField) => {
        const fieldName = customField.field_name.toLowerCase();
        const fieldValue = customField.value;

        switch (fieldName) {
          case 'farming_involvement':
            setFarmingInvolvement(
              parseMultipleChoice(fieldValue, FARMING_INVOLVMENT_ANSWERS),
            );
            break;
          case 'farming_sectors':
            setFarmingSectors(
              parseMultipleChoice(fieldValue, FARMING_SECTORS_ANSWERS),
            );
            break;
          default:
          // ignored
        }
      });
      return true;
    }
    return false;
  }, [extendedProfile]);

  return (
    <>
      <div className="mt-3 text-xl font-semibold md:text-2xl">
        Final step to improve your experience
      </div>
      <form
        className="mt-5 flex w-full flex-col gap-y-4"
        onSubmit={(e) => {
          e.preventDefault();
          submit({
            farmingInvolvement: mapMultipleChoice(
              farmingInvolvement,
              FARMING_INVOLVMENT_ANSWERS,
            ),
            farmingSectors: mapMultipleChoice(
              farmingSectors,
              FARMING_SECTORS_ANSWERS,
            ),
          });
        }}
      >
        <div className="pb-1 text-sm font-semibold leading-5 text-gray-900">
          Which of the following best describes your involvement in farming
          (tick all boxes that apply):
        </div>
        {FARMING_INVOLVMENT_ANSWERS.map((answer, idx) => (
          <FieldCheckboxInput
            checked={farmingInvolvement[idx]}
            key={answer}
            setField={(value: boolean) => {
              updateFarmingInvolement(idx, value);
            }}
            text={answer}
          />
        ))}
        {hasFarmingInvolvement && (
          <>
            <div className="pb-1 pt-3 text-sm font-semibold leading-5 text-gray-900">
              Which farming sectors are relevant to your business? (tick all
              answers that apply):
            </div>
            {FARMING_SECTORS_ANSWERS.map((answer, idx) => (
              <FieldCheckboxInput
                checked={farmingSectors[idx]}
                key={answer}
                setField={(value: boolean) => {
                  setFarmingSectors(
                    updateMultipleChoice(farmingSectors, idx, value),
                  );
                }}
                text={answer}
              />
            ))}
          </>
        )}
        <Button
          bgColor="bg-red-600 disabled:bg-gray-300"
          className="mb-8 mt-6"
          desktopFullWidth
          disabled={isSubmitting}
          fontSize="text-sm"
          height="h-10.5 md:h-10.5 lg:h-10.5"
          hoverColor="hover:bg-red-700 disabled:hover:bg-gray-300"
          mobileFullWidth
          text="Finish"
          textColor="text-white"
          type="submit"
        />
      </form>
    </>
  );
}
