import { useState } from 'react';

import Button from 'themes/autumn/components/generic/Button';
import { AuthField } from 'util/auth';
import { useOnce, useRegion } from 'util/hooks';

import FieldCheckboxInput from '../inputs/CheckboxInput';
import ValidatedTextInput from '../inputs/ValidatedTextInput';
import {
  type EnrichmentComponentProps,
  REGIONAL_CONNECTIONS_ANSWERS,
  mapMultipleChoice,
  parseMultipleChoice,
  sendEnrichmentErrorToGtm,
  updateMultipleChoice,
} from '../util/enrichments';
import { scrollToField, trimFields, validateFields } from '../util/fields';

import type { PhoenixApiError } from 'types/phoenix-types/responses';

export default function RegionalsEnrichments({
  errors,
  extendedProfile,
  fields,
  isSubmitting,
  setErrors,
  setField,
  submit,
}: EnrichmentComponentProps) {
  const [regionalConnections, setRegionalConnections] = useState(
    Array<boolean>(REGIONAL_CONNECTIONS_ANSWERS.length).fill(false),
  );

  const region = useRegion();

  function attemptSubmit() {
    const trimmedFields = trimFields(fields);
    const validationErrors: PhoenixApiError[] = validateFields({
      fieldValues: trimmedFields,
      fieldsToValidate: [AuthField.REGIONAL_BIRTH_YEAR],
    });

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      scrollToField(validationErrors[0]?.field);
      sendEnrichmentErrorToGtm(
        `validation_error:${validationErrors.map((e) => e.field).join(',')}`,
      );
      return;
    }

    submit({
      regionalBirthYear: trimmedFields.birth_year,
      regionalConnections: mapMultipleChoice(
        regionalConnections,
        REGIONAL_CONNECTIONS_ANSWERS,
      ),
    });
  }

  useOnce(() => {
    if (extendedProfile) {
      extendedProfile.custom_field_values.forEach((customField) => {
        const fieldName = customField.field_name.toLowerCase();
        const fieldValue = customField.value;

        switch (fieldName) {
          case 'regional_connections':
            setRegionalConnections(
              parseMultipleChoice(fieldValue, REGIONAL_CONNECTIONS_ANSWERS),
            );
            break;
          case 'regional_birth_year':
            setField(AuthField.REGIONAL_BIRTH_YEAR, fieldValue);
            break;
          default:
          // ignored
        }
      });
      return true;
    }
    return false;
  }, [extendedProfile, setField]);

  return (
    <>
      <div className="mt-3 text-xl font-semibold md:text-2xl">
        Please help us improve your experience by answering the following
        questions
      </div>
      <form
        className="mt-5 flex w-full flex-col gap-y-4"
        onSubmit={(e) => {
          e.preventDefault();
          attemptSubmit();
        }}
      >
        <ValidatedTextInput
          errors={errors}
          fieldName={AuthField.REGIONAL_BIRTH_YEAR}
          fields={fields}
          maxlength={4}
          name="In what year were you born"
          setErrors={setErrors}
          setField={setField}
        />
        <div className="pb-1 text-sm font-semibold leading-5 text-gray-900">
          It helps us to understand your connection to {region} and how you
          engage with our community. Please tick{' '}
          <span className="font-bold">all of the following</span> that apply:
        </div>
        {REGIONAL_CONNECTIONS_ANSWERS.map((answer, idx) => (
          <FieldCheckboxInput
            checked={regionalConnections[idx]}
            key={answer}
            setField={(value: boolean) => {
              setRegionalConnections(
                updateMultipleChoice(regionalConnections, idx, value),
              );
            }}
            text={answer.replaceAll(/\[region\]/g, region)}
          />
        ))}
        <Button
          bgColor="bg-red-600 disabled:bg-gray-300"
          className="mb-8 mt-6"
          desktopFullWidth
          disabled={isSubmitting}
          fontSize="text-sm"
          height="h-10.5 md:h-10.5 lg:h-10.5"
          hoverColor="hover:bg-red-700 disabled:hover:bg-gray-300"
          mobileFullWidth
          text="Finish"
          textColor="text-white"
          type="submit"
        />
      </form>
    </>
  );
}
