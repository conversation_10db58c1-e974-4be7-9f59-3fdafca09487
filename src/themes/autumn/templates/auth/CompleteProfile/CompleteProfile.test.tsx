import { render } from '@testing-library/react';

import { CompleteProfileEnrichment } from 'store/slices/features';
import { createStore } from 'store/store';
import { AuthField } from 'util/auth';
import { TestWrapper } from 'util/jest';

import EnrichmentForm from './EnrichmentForm';

import CompleteProfile from './index';

import type { PianoLoginUserData } from 'types/Piano';

describe('<CompleteProfile />', () => {
  it('renders with no enrichments', () => {
    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.NONE,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: true,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: true,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: true,
            supportLoginFacebook: false,
            supportLoginGoogle: true,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <CompleteProfile />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with Ags enrichments', () => {
    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.AGS,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: true,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: true,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: true,
            supportLoginFacebook: false,
            supportLoginGoogle: true,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <CompleteProfile />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with ALPA', () => {
    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.NONE,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: true,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: true,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: true,
            supportLoginFacebook: false,
            supportLoginGoogle: true,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <CompleteProfile />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});

describe('<EnrichmentForm />', () => {
  it('Renders with Ags enrichments', () => {
    const user: PianoLoginUserData = {
      email: '<EMAIL>',
      firstName: 'First',
      lastName: 'Last',
    } as PianoLoginUserData;

    const fields = {
      [AuthField.BILLING_ADDRESS]: '',
      [AuthField.BILLING_POSTCODE]: '',
      [AuthField.BILLING_STATE]: '',
      [AuthField.BILLING_SUBURB]: '',
      [AuthField.CONFIRM_PASSWORD]: '',
      [AuthField.CONTACT_NUMBER]: '',
      [AuthField.DELIVERY_ADDRESS]: '',
      [AuthField.DELIVERY_POSTCODE]: '',
      [AuthField.DELIVERY_STATE]: '',
      [AuthField.DELIVERY_SUBURB]: '',
      [AuthField.EMAIL]: '',
      [AuthField.FIRST_NAME]: '',
      [AuthField.LAST_NAME]: '',
      [AuthField.PASSWORD]: '',
      [AuthField.POSTCODE]: '',
      [AuthField.REGIONAL_BIRTH_YEAR]: '',
    };

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.AGS,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: true,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: true,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: true,
            supportLoginFacebook: false,
            supportLoginGoogle: true,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
      piano: {
        ...state.piano,
        user,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <EnrichmentForm
          enrichment={CompleteProfileEnrichment.AGS}
          extendedProfile={undefined}
          fields={fields}
          nextScreen={() => {}}
          setField={() => {}}
          showAlpaBusiness
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('Renders with Ags enrichments and ALPA', () => {
    const user: PianoLoginUserData = {
      email: '<EMAIL>',
      firstName: 'First',
      lastName: 'Last',
    } as PianoLoginUserData;

    const fields = {
      [AuthField.BILLING_ADDRESS]: '',
      [AuthField.BILLING_POSTCODE]: '',
      [AuthField.BILLING_STATE]: '',
      [AuthField.BILLING_SUBURB]: '',
      [AuthField.CONFIRM_PASSWORD]: '',
      [AuthField.CONTACT_NUMBER]: '',
      [AuthField.DELIVERY_ADDRESS]: '',
      [AuthField.DELIVERY_POSTCODE]: '',
      [AuthField.DELIVERY_STATE]: '',
      [AuthField.DELIVERY_SUBURB]: '',
      [AuthField.EMAIL]: '',
      [AuthField.FIRST_NAME]: '',
      [AuthField.LAST_NAME]: '',
      [AuthField.PASSWORD]: '',
      [AuthField.POSTCODE]: '',
      [AuthField.REGIONAL_BIRTH_YEAR]: '',
    };

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.AGS,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: true,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: true,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: true,
            supportLoginFacebook: false,
            supportLoginGoogle: true,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
      piano: {
        ...state.piano,
        user,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <EnrichmentForm
          enrichment={CompleteProfileEnrichment.AGS}
          extendedProfile={undefined}
          fields={fields}
          nextScreen={() => {}}
          setField={() => {}}
          showAlpaBusiness
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
