import { useCallback, useEffect, useState } from 'react';

import { onPianoReady } from 'components/Piano/ready';
import { CompleteProfileEnrichment } from 'store/slices/features';
import { redirectToLogin } from 'util/auth';
import { sendToGtm } from 'util/gtm';

import AgsEnrichments from './enrichments/AgsEnrichments';
import RegionalsEnrichments from './enrichments/RegionalsEnrichments';
import { type SubmitEnrichmentsFields, submitEnrichments } from './util/auth';
import {
  type EnrichmentComponentProps,
  type EnrichmentValidatedFields,
  sendEnrichmentErrorToGtm,
} from './util/enrichments';

import type { PianoExtendedUserCallbackData } from 'types/Piano';
import type { PhoenixApiError } from 'types/phoenix-types/responses';

interface EnrichmentFormProps {
  enrichment: CompleteProfileEnrichment;
  extendedProfile: PianoExtendedUserCallbackData | undefined;
  fields: Record<EnrichmentValidatedFields, string>;
  nextScreen: () => void;
  setField: (field: EnrichmentValidatedFields, value: string) => void;
  showAlpaBusiness: boolean;
}

const EnrichmentComponents: Record<
  CompleteProfileEnrichment,
  React.FC<EnrichmentComponentProps> | undefined
> = {
  [CompleteProfileEnrichment.NONE]: undefined,
  [CompleteProfileEnrichment.AGS]: AgsEnrichments,
  [CompleteProfileEnrichment.REGIONALS]: RegionalsEnrichments,
};

export default function EnrichmentForm({
  enrichment,
  extendedProfile,
  fields,
  nextScreen,
  setField,
  showAlpaBusiness,
}: EnrichmentFormProps) {
  const [errors, setErrors] = useState<PhoenixApiError[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const setAndTrackErrors = useCallback(
    (untrackedErrors: PhoenixApiError[]) => {
      untrackedErrors.forEach((error) => {
        sendEnrichmentErrorToGtm(`server_error:${error.message}`);
      });
      setErrors(untrackedErrors);
    },
    [setErrors],
  );

  const submit = useCallback(
    (submittedFields: Partial<SubmitEnrichmentsFields>) => {
      onPianoReady(async (tp) => {
        const accessToken = tp.pianoId.getToken();
        if (!accessToken) {
          sendEnrichmentErrorToGtm('validation_error:session_timed_out');
          redirectToLogin();
          return;
        }

        setIsSubmitting(true);
        const successful = await submitEnrichments({
          accessToken,
          setErrors: setAndTrackErrors,
          ...submittedFields,
        });

        if (!successful) {
          setIsSubmitting(false);
          return;
        }

        sendToGtm({
          action: 'finish',
          section: 'step_2',
          trigger: 'complete_new_profile',
        });

        nextScreen();
      });
    },
    [nextScreen, setAndTrackErrors],
  );

  useEffect(() => {
    sendToGtm({
      action: 'profile_stage_impressions',
      section: 'step_2',
      trigger: 'complete_new_profile',
    });
  }, []);

  const Component = EnrichmentComponents[enrichment];
  if (!Component) {
    return null;
  }

  return (
    <>
      <div className="mt-6 text-sm leading-6 text-gray-600">
        Step 2/{showAlpaBusiness ? '3' : '2'}
      </div>
      <Component
        errors={errors}
        extendedProfile={extendedProfile}
        fields={fields}
        isSubmitting={isSubmitting}
        setErrors={setAndTrackErrors}
        setField={setField}
        submit={submit}
      />
    </>
  );
}
