interface FieldCheckboxInputProps {
  checked: boolean;
  setField: (value: boolean) => void;
  text: string;
}

export default function FieldCheckboxInput({
  checked,
  setField,
  text,
}: FieldCheckboxInputProps) {
  return (
    <label
      className="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      htmlFor={text}
    >
      <input
        checked={checked}
        className="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id={text}
        name={text}
        onChange={(e) => {
          setField(e.currentTarget.checked);
        }}
        type="checkbox"
      />

      {text}
    </label>
  );
}
