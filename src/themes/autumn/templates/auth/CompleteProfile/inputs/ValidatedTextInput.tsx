'use client';

import clsx from 'clsx';
import { useState } from 'react';

import type { RefObject } from 'react';
import type { PhoenixApiError } from 'types/phoenix-types/responses';
import type { AuthField } from 'util/auth';

interface ValidatedTextInputProps<T extends AuthField> {
  disabled?: boolean;
  errors: PhoenixApiError[];
  fieldName: T;
  fields: Record<T, string>;
  maxlength?: number;
  name: string;
  ref?: RefObject<HTMLInputElement | null>;
  setErrors: (errors: PhoenixApiError[]) => void;
  setField: (field: T, val: string) => void;
  show?: boolean;
  skeleton?: boolean;
  type?: 'number' | 'tel' | 'password';
}

export default function ValidatedTextInput<T extends AuthField>({
  disabled,
  errors,
  fieldName,
  fields,
  maxlength,
  name,
  ref,
  setErrors,
  setField,
  show = true,
  skeleton,
  type,
}: ValidatedTextInputProps<T>) {
  const fieldValue = fields[fieldName];
  const fieldErrors = errors.filter((e) => e.field === fieldName);
  const [showPassword, setShowPassword] = useState(false);

  let renderedType = type ?? 'text';
  if (type === 'password' && showPassword) {
    renderedType = 'text';
  }

  return (
    <div
      className={clsx({
        hidden: !show && !skeleton,
        'pointer-events-none select-none': !show,
      })}
    >
      <div className="relative inline-block text-sm font-semibold leading-3">
        {skeleton && !show && (
          <div className="absolute size-full bg-white">
            <div className="size-full animate-pulse bg-gray-300" />
          </div>
        )}
        <span
          className={clsx({
            'opacity-0': !show,
          })}
        >
          {name}
        </span>
      </div>
      <div className="relative mt-2">
        {skeleton && !show && (
          <div className="absolute size-full bg-white">
            <div className="size-full animate-pulse bg-gray-300" />
          </div>
        )}
        {disabled ? (
          <input
            className={clsx(
              'w-full text-ellipsis rounded border border-gray-400 py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-400',
              {
                'opacity-0': !show,
              },
            )}
            disabled
            name={fieldName}
            placeholder={fieldValue}
          />
        ) : (
          <input
            className={clsx(
              'w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500',
              fieldErrors.length > 0
                ? 'border-red-500 pr-12'
                : 'border-gray-900 pr-5',
              {
                'opacity-0': !show,
              },
            )}
            disabled={!show}
            maxLength={maxlength}
            name={fieldName}
            onChange={(e) => setField(fieldName, e.target.value)}
            onFocus={() =>
              setErrors(errors.filter((e) => e.field !== fieldName))
            }
            ref={ref}
            type={renderedType}
            value={fieldValue}
          />
        )}

        {type === 'password' && fieldErrors.length === 0 && (
          <button
            className="absolute right-4 top-1/2 -translate-y-1/2 text-sm font-medium"
            onClick={() => setShowPassword((curr) => !curr)}
            type="button"
          >
            {showPassword ? 'Hide' : 'Show'}
          </button>
        )}
        {fieldErrors.length > 0 && (
          <div className="absolute right-4 top-1/2 -translate-y-1/2">
            <svg fill="none" height="24" width="24">
              <path
                clipRule="evenodd"
                d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2ZM13 13V7h-2v6h2Zm0 4v-2h-2v2h2Zm-9-5c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8-8 3.58-8 8Z"
                fill="#DC2626"
                fillRule="evenodd"
              />
            </svg>
          </div>
        )}
      </div>
      {fieldErrors.length > 0 && (
        <div className="mt-2 flex flex-col gap-y-2 text-sm font-medium text-red-600">
          {fieldErrors.map((e) => (
            <div key={e.message}>
              {e.message.slice(0, 1).toUpperCase()}
              {e.message.slice(1)}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
