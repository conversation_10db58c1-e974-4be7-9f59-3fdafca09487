/* eslint-disable import/prefer-default-export */
import { AuthField } from 'util/auth';

import { type CompleteProfileValidatedFields } from './CompleteProfileForm';

export const CUSTOM_FIELD_MAP: Record<string, CompleteProfileValidatedFields> =
  {
    ba_zip: AuthField.DELIVERY_POSTCODE,
    billing_address: AuthField.BILLING_ADDRESS,
    billing_postcode: AuthField.BILLING_POSTCODE,
    billing_state: AuthField.BILLING_STATE,
    billing_suburb: AuthField.BILLING_SUBURB,
    'contact-number': AuthField.CONTACT_NUMBER,
    postcode: AuthField.DELIVERY_POSTCODE,
    send_address: AuthField.DELIVERY_ADDRESS,
    send_postcode: AuthField.DELIVERY_POSTCODE,
    send_state: AuthField.DELIVERY_STATE,
    send_suburb: AuthField.DELIVERY_SUBURB,
  };
