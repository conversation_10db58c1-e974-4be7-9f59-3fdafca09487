import { AuthField } from 'util/auth';

import { trimFields, validateFields } from './fields';

describe('trimFields', () => {
  // eslint-disable-next-line @stylistic/max-len
  it('trims all leading & trailing whitespace, and all spaces for contact number', () => {
    expect(
      trimFields({
        [AuthField.BILLING_ADDRESS]: '\t test \n',
        [AuthField.BILLING_POSTCODE]: '\t test \n',
        [AuthField.BILLING_STATE]: '\t test \n',
        [AuthField.BILLING_SUBURB]: '\t test \n',
        [AuthField.CONTACT_NUMBER]: ' t\te\ns\u00A0t \n',
        [AuthField.DELIVERY_ADDRESS]: '\t test \n',
        [AuthField.DELIVERY_POSTCODE]: '\t test \n',
        [AuthField.DELIVERY_STATE]: '\t test \n',
        [AuthField.DELIVERY_SUBURB]: '\t test \n',
        [AuthField.FIRST_NAME]: '\t test \n',
        [AuthField.LAST_NAME]: '\t t e s t \n',
        [AuthField.PASSWORD]: '\t te st \n',
      }),
    ).toStrictEqual({
      [AuthField.BILLING_ADDRESS]: 'test',
      [AuthField.BILLING_POSTCODE]: 'test',
      [AuthField.BILLING_STATE]: 'test',
      [AuthField.BILLING_SUBURB]: 'test',
      [AuthField.CONTACT_NUMBER]: 'test',
      [AuthField.DELIVERY_ADDRESS]: 'test',
      [AuthField.DELIVERY_POSTCODE]: 'test',
      [AuthField.DELIVERY_STATE]: 'test',
      [AuthField.DELIVERY_SUBURB]: 'test',
      [AuthField.FIRST_NAME]: 'test',
      [AuthField.LAST_NAME]: 't e s t',
      [AuthField.PASSWORD]: 'te st',
    });
  });
});

describe('validateFields', () => {
  it('validates with no address or password', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_ADDRESS]: '',
        [AuthField.DELIVERY_POSTCODE]: '1234',
        [AuthField.DELIVERY_STATE]: '',
        [AuthField.DELIVERY_SUBURB]: '',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: false,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(0);
  });

  it('validates with no password with canSetPassword', () => {
    const errors = validateFields({
      canSetPassword: true,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_ADDRESS]: '',
        [AuthField.DELIVERY_POSTCODE]: '1234',
        [AuthField.DELIVERY_STATE]: '',
        [AuthField.DELIVERY_SUBURB]: '',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: false,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(0);
  });

  it('validates with password and canSetPassword', () => {
    const errors = validateFields({
      canSetPassword: true,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_ADDRESS]: '',
        [AuthField.DELIVERY_POSTCODE]: '1234',
        [AuthField.DELIVERY_STATE]: '',
        [AuthField.DELIVERY_SUBURB]: '',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: 'Password',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: false,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(0);
  });

  it('validates with isAddressRequired', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_ADDRESS]: 'Address',
        [AuthField.DELIVERY_POSTCODE]: '1234',
        [AuthField.DELIVERY_STATE]: 'State',
        [AuthField.DELIVERY_SUBURB]: 'Suburb',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: true,
      isBillingSameAsDelivery: true,
    });
    expect(errors).toHaveLength(0);
  });

  it('validates with isAddressRequired and not isBillingSameAsDelivery', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: 'Billing Address',
        [AuthField.BILLING_POSTCODE]: '5678',
        [AuthField.BILLING_STATE]: 'Billing State',
        [AuthField.BILLING_SUBURB]: 'Billing Suburb',
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_ADDRESS]: 'Address',
        [AuthField.DELIVERY_POSTCODE]: '1234',
        [AuthField.DELIVERY_STATE]: 'State',
        [AuthField.DELIVERY_SUBURB]: 'Suburb',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: true,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(0);
  });

  it('validates with all fields present', () => {
    const errors = validateFields({
      canSetPassword: true,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: 'Billing Address',
        [AuthField.BILLING_POSTCODE]: '5678',
        [AuthField.BILLING_STATE]: 'Billing State',
        [AuthField.BILLING_SUBURB]: 'Billing Suburb',
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_ADDRESS]: 'Address',
        [AuthField.DELIVERY_POSTCODE]: '1234',
        [AuthField.DELIVERY_STATE]: 'State',
        [AuthField.DELIVERY_SUBURB]: 'Suburb',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: 'Password',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: true,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(0);
  });

  it('fails for blank values on required fields', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: '',
        [AuthField.DELIVERY_ADDRESS]: '',
        [AuthField.DELIVERY_POSTCODE]: '',
        [AuthField.DELIVERY_STATE]: '',
        [AuthField.DELIVERY_SUBURB]: '',
        [AuthField.FIRST_NAME]: '',
        [AuthField.LAST_NAME]: '',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: false,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(4);
    expect(errors.map((err) => err.field).sort()).toEqual(
      [
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.DELIVERY_POSTCODE,
        AuthField.CONTACT_NUMBER,
      ].sort(),
    );
  });

  it('fails for invalid phone numbers', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: 'Phone Number',
        [AuthField.DELIVERY_ADDRESS]: '',
        [AuthField.DELIVERY_POSTCODE]: '1234',
        [AuthField.DELIVERY_STATE]: '',
        [AuthField.DELIVERY_SUBURB]: '',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: false,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(1);
    expect(errors.map((err) => err.field).sort()).toEqual(
      [AuthField.CONTACT_NUMBER].sort(),
    );
  });

  it('Validates for valid phone numbers with a leading +', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: '+0412312312',
        [AuthField.DELIVERY_ADDRESS]: '',
        [AuthField.DELIVERY_POSTCODE]: '1234',
        [AuthField.DELIVERY_STATE]: '',
        [AuthField.DELIVERY_SUBURB]: '',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: false,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(0);
  });

  it('fails for blank address with isAddressRequired', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_ADDRESS]: '',
        [AuthField.DELIVERY_POSTCODE]: '',
        [AuthField.DELIVERY_STATE]: '',
        [AuthField.DELIVERY_SUBURB]: '',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: true,
      isBillingSameAsDelivery: true,
    });
    expect(errors).toHaveLength(4);
    expect(errors.map((err) => err.field).sort()).toEqual(
      [
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
      ].sort(),
    );
  });

  // eslint-disable-next-line @stylistic/max-len
  it('fails for blank billing address with isAddressRequired and not isBillingSameAsDelivery', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_ADDRESS]: 'Address',
        [AuthField.DELIVERY_POSTCODE]: '1234',
        [AuthField.DELIVERY_STATE]: 'State',
        [AuthField.DELIVERY_SUBURB]: 'Suburb',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: true,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(4);
    expect(errors.map((err) => err.field).sort()).toEqual(
      [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
      ].sort(),
    );
  });

  // eslint-disable-next-line @stylistic/max-len
  it('fails for blank address and billing address with isAddressRequired and not isBillingSameAsDelivery', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.BILLING_ADDRESS]: '',
        [AuthField.BILLING_POSTCODE]: '',
        [AuthField.BILLING_STATE]: '',
        [AuthField.BILLING_SUBURB]: '',
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_ADDRESS]: '',
        [AuthField.DELIVERY_POSTCODE]: '',
        [AuthField.DELIVERY_STATE]: '',
        [AuthField.DELIVERY_SUBURB]: '',
        [AuthField.FIRST_NAME]: 'First',
        [AuthField.LAST_NAME]: 'Last',
        [AuthField.PASSWORD]: '',
      },
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired: true,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(8);
    expect(errors.map((err) => err.field).sort()).toEqual(
      [
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
      ].sort(),
    );
  });

  // eslint-disable-next-line @stylistic/max-len
  it('validates while only validating provided fields', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.CONTACT_NUMBER]: '0412312312',
        [AuthField.DELIVERY_POSTCODE]: 'Invalid',
      },
      fieldsToValidate: [AuthField.CONTACT_NUMBER],
      isAddressRequired: false,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(0);
  });

  // eslint-disable-next-line @stylistic/max-len
  it('fails while only validating provided fields', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.CONTACT_NUMBER]: 'Invalid',
        [AuthField.DELIVERY_POSTCODE]: 'Invalid',
      },
      fieldsToValidate: [AuthField.DELIVERY_POSTCODE],
      isAddressRequired: false,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(1);
    expect(errors.map((err) => err.field).sort()).toEqual(
      [AuthField.DELIVERY_POSTCODE].sort(),
    );
  });

  // eslint-disable-next-line @stylistic/max-len
  it('validates regional birth year for 19XX', () => {
    const errors = validateFields({
      canSetPassword: false,
      fieldValues: {
        [AuthField.REGIONAL_BIRTH_YEAR]: '1900',
      },
      fieldsToValidate: [AuthField.REGIONAL_BIRTH_YEAR],
      isAddressRequired: true,
      isBillingSameAsDelivery: false,
    });
    expect(errors).toHaveLength(0);
  });
});

// eslint-disable-next-line @stylistic/max-len
it('validates regional birth year for 20XX', () => {
  const errors = validateFields({
    canSetPassword: false,
    fieldValues: {
      [AuthField.REGIONAL_BIRTH_YEAR]: '2000',
    },
    fieldsToValidate: [AuthField.REGIONAL_BIRTH_YEAR],
    isAddressRequired: true,
    isBillingSameAsDelivery: false,
  });
  expect(errors).toHaveLength(0);
});

// eslint-disable-next-line @stylistic/max-len
it('fails if regional birth year is not 4 digits', () => {
  const errors = validateFields({
    canSetPassword: false,
    fieldValues: {
      [AuthField.REGIONAL_BIRTH_YEAR]: '19000',
    },
    fieldsToValidate: [AuthField.REGIONAL_BIRTH_YEAR],
    isAddressRequired: true,
    isBillingSameAsDelivery: false,
  });
  expect(errors).toHaveLength(1);
  expect(errors.map((err) => err.field).sort()).toEqual(
    [AuthField.REGIONAL_BIRTH_YEAR].sort(),
  );
});

// eslint-disable-next-line @stylistic/max-len
it('fails if regional birth year is not only digits', () => {
  const errors = validateFields({
    canSetPassword: false,
    fieldValues: {
      [AuthField.REGIONAL_BIRTH_YEAR]: 'abcd',
    },
    fieldsToValidate: [AuthField.REGIONAL_BIRTH_YEAR],
    isAddressRequired: true,
    isBillingSameAsDelivery: false,
  });
  expect(errors).toHaveLength(1);
  expect(errors.map((err) => err.field).sort()).toEqual(
    [AuthField.REGIONAL_BIRTH_YEAR].sort(),
  );
});

// eslint-disable-next-line @stylistic/max-len
it('fails if regional birth year is not 19XX or 20XX', () => {
  const errors = validateFields({
    canSetPassword: false,
    fieldValues: {
      [AuthField.REGIONAL_BIRTH_YEAR]: '2147',
    },
    fieldsToValidate: [AuthField.REGIONAL_BIRTH_YEAR],
    isAddressRequired: true,
    isBillingSameAsDelivery: false,
  });
  expect(errors).toHaveLength(1);
  expect(errors.map((err) => err.field).sort()).toEqual(
    [AuthField.REGIONAL_BIRTH_YEAR].sort(),
  );
});
