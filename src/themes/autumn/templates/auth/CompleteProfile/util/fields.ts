import { AuthField } from 'util/auth';

import type { PhoenixApiError } from 'types/phoenix-types/responses';

interface ValidateFieldsParams<
  ProvidedFields extends AuthField,
  FieldsToValidate extends ProvidedFields,
> {
  canSetPassword?: boolean;
  fieldValues: Record<ProvidedFields, string>;
  fieldsToValidate: FieldsToValidate[];
  isAddressRequired?: boolean;
  isBillingSameAsDelivery?: boolean;
}

interface ValidationTest {
  message: (name: string) => string;
  validator: (value: string) => boolean;
}

interface ValidationConfig {
  name: string;
  onlyIfAddressRequired?: boolean;
  onlyIfCanSetPassword?: boolean;
  onlyIfNotBillingSameAsDelivery?: boolean;
  optional?: boolean;
  tests?: ValidationTest[];
}

const mustOnlyContainNumbersTest: ValidationTest = {
  message: (name) => `${name} must only contain numbers`,
  validator: (value) => /^\+?[0-9]+$/.test(value),
};

const FIELD_VALIDATIONS: Partial<Record<AuthField, ValidationConfig>> = {
  [AuthField.FIRST_NAME]: {
    name: 'First Name',
  },
  [AuthField.LAST_NAME]: {
    name: 'Last Name',
  },
  [AuthField.CONTACT_NUMBER]: {
    name: 'Contact Number',
    tests: [mustOnlyContainNumbersTest],
  },
  [AuthField.PASSWORD]: {
    name: 'Password',
    onlyIfCanSetPassword: true,
    optional: true,
  },
  [AuthField.DELIVERY_ADDRESS]: {
    name: 'Address',
    onlyIfAddressRequired: true,
  },
  [AuthField.DELIVERY_POSTCODE]: {
    name: 'Postcode',
    tests: [mustOnlyContainNumbersTest],
  },
  [AuthField.DELIVERY_STATE]: {
    name: 'State',
    onlyIfAddressRequired: true,
  },
  [AuthField.DELIVERY_SUBURB]: {
    name: 'Suburb',
    onlyIfAddressRequired: true,
  },

  [AuthField.BILLING_ADDRESS]: {
    name: 'Billing Address',
    onlyIfAddressRequired: true,
    onlyIfNotBillingSameAsDelivery: true,
  },
  [AuthField.BILLING_POSTCODE]: {
    name: 'Billing Postcode',
    onlyIfAddressRequired: true,
    onlyIfNotBillingSameAsDelivery: true,
    tests: [mustOnlyContainNumbersTest],
  },
  [AuthField.BILLING_STATE]: {
    name: 'Billing State',
    onlyIfAddressRequired: true,
    onlyIfNotBillingSameAsDelivery: true,
  },
  [AuthField.BILLING_SUBURB]: {
    name: 'Billing Suburb',
    onlyIfAddressRequired: true,
    onlyIfNotBillingSameAsDelivery: true,
  },
  [AuthField.REGIONAL_BIRTH_YEAR]: {
    name: 'Birth Year',
    tests: [
      mustOnlyContainNumbersTest,
      {
        message: (name) => `${name} must be within the range 1900-2099`,
        validator: (value) => /^([^0-9]|19|20)/.test(value),
      },
      {
        message: (name) => `${name} must be 4 digits long`,
        validator: (value) => /^.{4}$/.test(value),
      },
    ],
  },
};

export function validateFields<
  ProvidedFields extends AuthField,
  FieldsToValidate extends ProvidedFields,
>({
  canSetPassword = false,
  fieldValues,
  fieldsToValidate,
  isAddressRequired = false,
  isBillingSameAsDelivery = false,
}: ValidateFieldsParams<ProvidedFields, FieldsToValidate>): PhoenixApiError[] {
  const verificationErrors: PhoenixApiError[] = [];

  fieldsToValidate.forEach((field) => {
    const config = FIELD_VALIDATIONS[field];
    const value = fieldValues[field]?.trim();
    if (!config) {
      return;
    }
    if (config.onlyIfCanSetPassword && !canSetPassword) {
      return;
    }
    if (config.onlyIfAddressRequired && !isAddressRequired) {
      return;
    }
    if (config.onlyIfNotBillingSameAsDelivery && isBillingSameAsDelivery) {
      return;
    }
    if (!value || value.trim() === '') {
      if (config.optional) {
        return;
      }

      verificationErrors.push({
        field,
        message: `${config.name} can not be empty`,
      });
      return;
    }

    config.tests?.forEach((test) => {
      if (!test.validator(value)) {
        verificationErrors.push({
          field,
          message: test.message(config.name),
        });
      }
    });
  });
  return verificationErrors;
}

export function trimFields<T extends AuthField>(
  fields: Record<T, string>,
): Record<T, string> {
  /* eslint-disable no-param-reassign */
  return Object.keys(fields).reduce(
    (trimmedFields, key) => {
      const fieldName = key as T;
      if (fieldName === AuthField.CONTACT_NUMBER) {
        trimmedFields[fieldName] = fields[fieldName].replaceAll(/\s+/g, '');
      } else {
        trimmedFields[fieldName] = fields[fieldName].trim();
      }
      return trimmedFields;
    },
    {} as Record<T, string>,
  );
  /* eslint-enable no-param-reassign */
}

export function scrollToField(fieldName: string | undefined | null) {
  if (!fieldName) {
    return;
  }

  const field = document.getElementsByName(fieldName)[0];
  if (!field) {
    return;
  }

  const offsetParent = field.offsetParent as HTMLElement;
  window.scrollTo({
    behavior: 'smooth',
    top: offsetParent ? offsetParent.offsetTop - 72 : 0,
  });
}
