import { sendToGtm } from 'util/gtm';

import type { SubmitEnrichmentsFields } from './auth';
import type { PianoExtendedUserCallbackData } from 'types/Piano';
import type { PhoenixApiError } from 'types/phoenix-types/responses';
import type { AuthField } from 'util/auth';

export type EnrichmentValidatedFields = AuthField.REGIONAL_BIRTH_YEAR;

export interface EnrichmentComponentProps {
  errors: PhoenixApiError[];
  extendedProfile: PianoExtendedUserCallbackData | undefined;
  fields: Record<EnrichmentValidatedFields, string>;
  isSubmitting: boolean;
  setErrors: (errors: PhoenixApiError[]) => void;
  setField: (field: EnrichmentValidatedFields, value: string) => void;
  submit: (fields: Partial<SubmitEnrichmentsFields>) => void;
}

export function sendEnrichmentErrorToGtm(label: string) {
  sendToGtm({
    action: 'finish_button_click',
    label,
    section: 'step_2',
    trigger: 'complete_new_profile',
  });
}

export const FARMING_INVOLVMENT_ANSWERS = [
  'I own a farm',
  'I manage a farm',
  'I work on a farm',
  'I have a hobby farm or lifestyle property',
  'My family are farmers',
  'I supply farm equipment or products',
  'I provide consulting or support services to farms',
  "None of the above, I'm not involved with farming",
];

export const FARMING_SECTORS_ANSWERS = [
  'Beef',
  'Sheep',
  'Other livestock',
  'Broadacre Cropping',
  'Horticultural Cropping',
  'Other',
];

export const REGIONAL_CONNECTIONS_ANSWERS = [
  'I volunteer in [region]',
  "I'm a member of a local sporting club",
  "I'm a supporter of a local sports team",
  'I own my home in [region]',
  'I rent my home in [region]',
  'I own a business in [region]',
  'I work in [region]',
  'My children go to primary school here',
  'My children go to secondary school here',
  'I have an interest in [region] but do not currently live there',
];

export function parseMultipleChoice(
  value: string,
  answers: string[],
): boolean[] {
  const splitAnswers = value.split(/;/g);
  return answers.map((answer) => splitAnswers.includes(answer));
}

export function updateMultipleChoice(
  answers: boolean[],
  idx: number,
  value: boolean,
): boolean[] {
  const newAnswers = [...answers];
  newAnswers[idx] = value;
  return newAnswers;
}

export function mapMultipleChoice(
  answers: boolean[],
  answersText: string[],
): string[] {
  return answers
    .map((checked, idx) => (checked ? answersText[idx] : ''))
    .filter((answer) => answer !== '');
}
