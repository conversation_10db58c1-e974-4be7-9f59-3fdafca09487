import { parseMultipleChoice } from './enrichments';

describe('parseEnrichment', () => {
  it('Splits on semicolon', () => {
    const splitAnswers = parseMultipleChoice('Test 1;Test 2;Test 3', [
      'Test 1',
      'Test 2',
      'Test 3',
    ]);
    expect(splitAnswers).toEqual([true, true, true]);
  });

  it('Only returns true for found answers', () => {
    const splitAnswers = parseMultipleChoice('Test 1;Test 3', [
      'Test 1',
      'Test 2',
      'Test 3',
    ]);
    expect(splitAnswers).toEqual([true, false, true]);
  });

  it('Ignores missing answers', () => {
    const splitAnswers = parseMultipleChoice('Test 1;Test 2;Test 6;Test 3', [
      'Test 1',
      'Test 2',
      'Test 3',
    ]);
    expect(splitAnswers).toEqual([true, true, true]);
  });
});
