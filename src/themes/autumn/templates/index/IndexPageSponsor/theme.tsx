import type { NavThemeRecord } from 'types/Nav';

const DEFAULT_NAV_THEME: NavThemeRecord = {
  currentUrl: {
    icon: '',
    textBackground: 'bg-blue-850',
    textColor: 'text-white',
  },
  default: {
    icon: '',
    textBackground: 'bg-gray-350 hover:bg-gray-100',
    textColor: 'text-slate-750',
  },
  externalUrl: {},
};

const RED_NAV_THEME: NavThemeRecord = {
  currentUrl: {
    icon: '',
    textBackground: 'bg-red-500',
    textColor: 'text-white',
  },
  default: {
    icon: '',
    textBackground: 'bg-gray-350 hover:bg-gray-100',
    textColor: 'text-slate-750',
  },
  externalUrl: {
    icon: '<svg width="12" height="12" aria-hidden="true" focusable="false" viewBox="0 0 512 512" role="img"><path fill="currentColor" d="M352 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9L370.7 96 201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L416 141.3l41.4 41.4c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6l0-128c0-17.7-14.3-32-32-32L352 0zM80 32C35.8 32 0 67.8 0 112L0 432c0 44.2 35.8 80 80 80l320 0c44.2 0 80-35.8 80-80l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 112c0 8.8-7.2 16-16 16L80 448c-8.8 0-16-7.2-16-16l0-320c0-8.8 7.2-16 16-16l112 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 32z"></path></svg>',
  },
};

const THEMES: Record<string, NavThemeRecord> = {
  default: DEFAULT_NAV_THEME,
  toyota: RED_NAV_THEME,
};

export default THEMES;
