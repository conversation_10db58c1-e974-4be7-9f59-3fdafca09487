import { useAppSelector } from 'store/hooks';
import { IndexPageFeatures } from 'themes/autumn/components/features';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageHeading from 'themes/autumn/components/page/PageHeading';
import Zone from 'themes/autumn/components/zone/Zone';
import { ZoneName } from 'types/ZoneItems';

function IndexPageSponsor(): React.ReactElement {
  const page = useAppSelector((state) => state.page);
  const showHeading = useAppSelector((state) => state.page.showHeading);
  const name = page.altMenuName || page.name;

  return (
    <TemplateWrapper showStickyFooterAd>
      <Container className="mt-6">
        <PageHeading show={showHeading} text={name} />
        <div className="mb-4 empty:hidden">
          <Zone name={ZoneName.TOP} />
        </div>
        <div className="lg:flex">
          <div className="grow">
            <div className="border-gray-300" data-testid="index-newswell">
              <Zone name={ZoneName.NEWSWELL} />
            </div>
          </div>
          <div className="flex shrink-0 flex-col justify-items-stretch gap-6 lg:w-side lg:gap-10 lg:pl-7">
            <Zone name={ZoneName.NEWSWELL_SIDE} />
          </div>
        </div>
      </Container>
      <Container className="mt-6 lg:flex">
        <div className="w-full lg:w-7/10">
          <div className="border-gray-300 lg:pr-7" data-testid="index-main">
            <Zone name={ZoneName.MAIN} />
          </div>
        </div>
        <div className="mt-6 flex flex-col gap-6 md:mt-0 lg:w-3/10 lg:min-w-side lg:gap-10 lg:pl-7">
          <Zone name={ZoneName.MAIN_SIDE} />
        </div>
      </Container>
      <IndexPageFeatures />
    </TemplateWrapper>
  );
}

export default IndexPageSponsor;
