import clsx from 'clsx';

import { useAppSelector } from 'store/hooks';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import Zone from 'themes/autumn/components/zone/Zone';
import { ZoneName } from 'types/ZoneItems';

import AuthorBio from './AuthorBio';
import AuthorMugshot from './AuthorMugshot';
import AuthorSocials from './AuthorSocials';

function IndexPageAuthorV2(): React.ReactElement {
  const author = useAppSelector((state) => state.author);

  return (
    <TemplateWrapper showStickyFooterAd>
      <div className="flex size-full flex-col gap-10 border-t border-gray-300 py-10">
        <div className="flex flex-col items-center justify-center gap-2 px-4 md:flex-row md:items-start md:justify-start md:gap-16 md:px-0 xl:mx-auto xl:w-[1220px]">
          <AuthorMugshot author={author} />
          <div
            className={clsx(
              'flex w-full flex-1 flex-col justify-center gap-3 md:justify-start',
              {
                'pl-4': !author.mugshot,
              },
            )}
          >
            <h1 className="text-center font-inter text-2xl font-bold text-gray-900 md:text-left md:text-[40px]">
              {author.name}
            </h1>
            {author.position && (
              <span className="text-center font-inter text-base font-semibold text-gray-900 md:text-left">
                {author.position}
              </span>
            )}
            <AuthorBio author={author} />
          </div>
        </div>
        <AuthorSocials author={author} />
        <Zone name={ZoneName.MAIN} />
      </div>
    </TemplateWrapper>
  );
}

export default IndexPageAuthorV2;
