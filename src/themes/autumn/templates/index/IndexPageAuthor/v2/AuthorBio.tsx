import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import { useCallback, useMemo, useState } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { getAuthorBioHtml } from 'util/author/author';

import styles from './AuthorBio.module.css';

import type { StoryAuthor } from 'types/Story';

interface Props {
  author: StoryAuthor;
}

const AuthorBio: React.FC<Props> = ({ author }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleReadMore = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const authorBioHtml = useMemo(() => getAuthorBioHtml(author), [author]);

  return (
    <div>
      <div
        className={clsx(styles.authorBio, {
          [styles.expanded]: isExpanded,
        })}
        dangerouslySetInnerHTML={{ __html: authorBioHtml }}
      />
      <button
        className={styles.readMore}
        onClick={handleReadMore}
        type="button"
      >
        Read More
        <FontAwesomeIcon
          className="ml-2"
          icon={isExpanded ? faChevronUp : faChevronDown}
        />
      </button>
    </div>
  );
};

export default AuthorBio;
