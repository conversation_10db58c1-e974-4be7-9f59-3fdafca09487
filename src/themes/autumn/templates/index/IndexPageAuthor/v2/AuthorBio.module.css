.authorBio {
  @apply prose max-w-[800px] font-inter text-sm font-normal text-gray-900 prose-a:text-gray-800 prose-a:underline prose-a:decoration-gray-500 hover:prose-a:decoration-gray-800 md:text-base;
}

.expanded {
}

.authorBio:not(.expanded) > p:first-of-type ~ * {
  @apply hidden;
}

.readMore {
  @apply mt-2 hidden h-6 font-inter text-base font-semibold text-gray-900;
}

.authorBio:has(p:nth-child(2)) + .readMore {
  @apply block;
}
