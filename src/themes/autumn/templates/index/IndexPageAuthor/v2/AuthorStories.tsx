/* eslint-disable arrow-body-style */
import { useCallback, useEffect, useRef, useState } from 'react';

import Ad from 'themes/autumn/components/ads/Ad';
import Link from 'themes/autumn/components/generic/Link';
import NativeImage from 'themes/autumn/components/storyElements/common/NativeImage';
import Button from 'themes/autumn/templates/zoneItems/storylist/ExploreTravelInfiniteScroll/Button';
import { AdSize } from 'util/ads';
import { fetchAuthorStoryList } from 'util/organization/suzuka';

import type React from 'react';
import type { Story } from 'types/Story';

/**
 * For each chunk, an ad is displayed at this index.
 *
 * Ex. If the API returns 10 stories for each page,
 * then the Ad is shown at index $SHOW_AD_INDEX (1-based index).
 */
const SHOW_AD_INDEX = 8;

const STORY_LIMIT = 10;

interface StoryItemProps {
  story: Story;
}

interface StoriesProps {
  index: number;
  stories: Story[];
}

interface AdSectionProps {
  position: number;
}

const AdSection: React.FC<AdSectionProps> = ({ position }) => {
  return (
    <div className="flex w-full items-center justify-center bg-gray-100 py-4">
      <Ad
        mdSizes={AdSize.billboard}
        position={position}
        publiftName="incontent-hrec-lg-1"
        sizes={AdSize.mrec}
        slotId="author-stories-billboard"
      />
    </div>
  );
};

const StoryItemSkeleton: React.FC = () => {
  return (
    <div className="flex w-full flex-1 animate-pulse flex-col gap-3 px-4 md:mx-auto md:max-w-[840px] md:flex-row">
      <div className="order-2 flex w-full flex-1 flex-col gap-1 md:order-1 md:gap-2">
        <div className="h-4 w-full bg-gray-200" />
        <div className="h-4 w-full bg-gray-200" />
        <div className="h-4 w-full bg-gray-200" />
      </div>
      <div className="order-1 !mx-0 h-[228px] w-full rounded-md bg-gray-200 md:order-2 md:h-[144px] md:w-[216px]" />
    </div>
  );
};

const StoryItem: React.FC<StoryItemProps> = ({ story }) => {
  return (
    <Link
      className="flex w-full flex-1 flex-row gap-3 px-4 md:px-24 xl:px-64"
      href={story.storyUrl}
      noStyle
    >
      <div className="flex w-full flex-1 flex-col gap-3 border-b border-gray-300 pb-7 md:mx-auto md:max-w-[840px] md:flex-row">
        <div className="order-2 flex flex-1 flex-col gap-1 md:order-1 md:gap-2">
          <h2 className="font-merriweather text-xl font-bold text-gray-900 hover:underline">
            {story.title}
          </h2>
          <p className="line-clamp-2 font-merriweather text-sm font-normal text-gray-600">
            {story.summaryFull ?? story.summary}
          </p>
          <span className="line-clamp-2 font-inter text-xs font-medium text-gray-600 md:text-sm">
            {story.byline}
          </span>
        </div>
        {story.leadImage && (
          <div className="order-1 md:order-2">
            <NativeImage
              alt={story.leadImage?.title || story.title}
              height={228}
              image={story.leadImage}
              imageClassName="size-full object-cover 
                !mx-0 md:!h-[144px] md:!w-[216px] rounded-md"
              width={343}
            />
          </div>
        )}
      </div>
    </Link>
  );
};

const Stories: React.FC<StoriesProps> = ({ index: adPosition, stories }) => {
  return (
    <>
      {stories.map((story, index) => {
        const showAd = index === SHOW_AD_INDEX - 1;
        return (
          <>
            <StoryItem key={story.id} story={story} />
            {showAd && <AdSection position={adPosition} />}
          </>
        );
      })}
    </>
  );
};

const generateUniqueKeyForChunk = (chunk: Story[]) => {
  return chunk.map((story) => story.id).join('-');
};

interface AuthorStoriesProps {
  initialStories: Story[];
}

const AuthorStories: React.FC<AuthorStoriesProps> = ({ initialStories }) => {
  const bottomLoaderRef = useRef(null);
  const topLoaderRef = useRef(null);
  const [authorChunks, setAuthorChunks] = useState<Story[][]>(() => {
    if (initialStories.length > 0) {
      return [initialStories];
    }
    return [];
  });
  const [isFetching, setIsFetching] = useState(false);
  const [isAtEnd, setIsAtEnd] = useState(false);
  const [page, setPage] = useState(2);

  const fetchStories = useCallback(
    (storyPage: number) => {
      setIsFetching(true);
      fetchAuthorStoryList({
        page: storyPage,
      })
        .then((stories) => {
          setAuthorChunks((items) => [...items, stories]);
          setPage((p) => p + 1);
          if (stories.length < STORY_LIMIT) {
            setIsAtEnd(true);
          }
        })
        .catch((error) => {
          console.error('An error occurred while fetching stories', error);
        })
        .finally(() => {
          setIsFetching(false);
        });
    },
    [setAuthorChunks, setIsAtEnd],
  );

  const backToTop = useCallback(() => {
    window.scrollTo({
      behavior: 'smooth',
      top: 0,
    });
  }, []);

  useEffect(() => {
    const bottomLoader = bottomLoaderRef.current;
    // eslint-disable-next-line compat/compat
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && !isFetching && !isAtEnd) {
          fetchStories(page);
        }
      },
      { rootMargin: '500px 0px 0px 0px' },
    );
    if (bottomLoader) {
      observer.observe(bottomLoader);
    }
    return () => {
      if (bottomLoader) {
        observer.unobserve(bottomLoader);
      }
    };
  }, [fetchStories, isAtEnd, isFetching, page]);

  return (
    <div className="flex flex-col gap-7">
      <div className="h-1" ref={topLoaderRef} />
      {authorChunks.map((chunk, index) => (
        <Stories
          index={index}
          key={generateUniqueKeyForChunk(chunk)}
          stories={chunk}
        />
      ))}
      {isFetching && !isAtEnd && <StoryItemSkeleton />}
      {isAtEnd && <Button label="Back to top" onClick={backToTop} />}
      <div className="h-1" ref={bottomLoaderRef} />
    </div>
  );
};

export default AuthorStories;
