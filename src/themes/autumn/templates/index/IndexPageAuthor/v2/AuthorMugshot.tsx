import { useAppSelector } from 'store/hooks';
import AuthorImage from 'themes/autumn/components/storyElements/common/AuthorImage';

import type { StoryAuthor } from 'types/Story';

interface Props {
  author: StoryAuthor;
}

const AuthorMugshot: React.FC<Props> = ({ author }) => {
  const { transformUrl } = useAppSelector((state) => state.settings);

  if (!author.mugshot) {
    return null;
  }

  return (
    <AuthorImage
      author={author}
      className="size-[140px] rounded-full md:size-[160px]"
      height={160}
      transformUrl={transformUrl}
      width={160}
    />
  );
};

export default AuthorMugshot;
