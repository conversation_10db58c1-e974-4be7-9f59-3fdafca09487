import Script from 'next/script';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import { IndexPageFeatures } from 'themes/autumn/components/features';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageHeading from 'themes/autumn/components/page/PageHeading';
import PageNavigation from 'themes/autumn/components/page/PageNavigation';
import Zone from 'themes/autumn/components/zone/Zone';
import { ZoneName } from 'types/ZoneItems';

function SearchPage(): React.ReactElement {
  const page = useAppSelector((state) => state.page);
  const configId = useAppSelector((state) => state.conf.googleVertexConfigId);
  const src = 'https://cloud.google.com/ai/gen-app-builder/client?hl=en_US';
  const triggerId = 'searchWidgetTrigger';

  return (
    <TemplateWrapper showNavigationAd={false}>
      <Container className="mt-4 md:mt-10">
        <Zone name={ZoneName.TOP} />
        <PageHeading show={page.showHeading} text={page.name} />
        <PageNavigation pages={page.children} />

        <Script async src={src} />
        <gen-search-widget
          configId={configId}
          placeholder="Ask a question or Search"
          triggerId={triggerId}
        />
        <input
          // eslint-disable-next-line @stylistic/max-len
          className="h-10 w-64 text-ellipsis rounded border border-slate-300 px-5 py-3 text-center text-sm leading-tight opacity-100 placeholder:leading-tight placeholder:text-slate-400"
          id={triggerId}
          placeholder="Ask a question or Search"
        />

        <div className="mt-8">
          <p>You are using a beta version of our new search tool.</p>
          <p>We are currently testing it internally before a wider release.</p>
          <p>
            If you have feedback or notice any issues, use the feedback widget
            below
          </p>
        </div>

        <Zone name={ZoneName.MAIN} />
      </Container>
      <IndexPageFeatures />
    </TemplateWrapper>
  );
}

export default SearchPage;
