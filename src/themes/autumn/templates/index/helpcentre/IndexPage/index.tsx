import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import { IndexPageFeatures } from 'themes/autumn/components/features';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageHeading from 'themes/autumn/components/page/PageHeading';
import Zone from 'themes/autumn/components/zone/Zone';
import { ZoneName } from 'types/ZoneItems';

import styles from './styles.module.css';

const MARGIN_BETWEEN_ITEMS = 'mb-18';
const MARGIN_HEADING = 'mb-6 md:mb-7';

export default function IndexPage(): React.ReactElement {
  const page = useAppSelector((state) => state.page);
  return (
    <TemplateWrapper bgColor="bg-stone-100" showNavigationAd={false}>
      <Container maxWidthMd="max-w-full" maxWidthSm="max-w-full" noGutter>
        <div className={clsx(styles.bonzaiMargin, 'border-b border-gray-300')}>
          <Zone itemMargin="m-0" name={ZoneName.NAVIGATION} snapHeading />
        </div>
        <Container
          className="flex h-64 flex-col items-center justify-center gap-6 bg-white shadow"
          maxWidthMd="max-w-full"
          maxWidthSm="max-w-full"
          noGutter
        >
          <PageHeading
            className="font-inter text-3xl font-medium leading-normal text-gray-900 md:!text-3xl"
            show={page.showHeading}
            text={page.name}
          />
          <div className="mx-4 text-center text-base font-normal text-gray-600">
            How can we help you? Find helpful information below about our
            products & subscriptions.
          </div>
        </Container>
        <Zone
          itemMargin={MARGIN_BETWEEN_ITEMS}
          name={ZoneName.TOP}
          snapHeading
        />
      </Container>
      <Container
        className="flex flex-col"
        maxWidthMd="max-w-container"
        maxWidthSm="max-w-container"
      >
        <div className="mt-8">
          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.MAIN}
            snapHeading
            snapMargin={MARGIN_HEADING}
          />
        </div>
        <div className="mt-8">
          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.BOTTOM}
            snapHeading
          />
        </div>
      </Container>
      <IndexPageFeatures />
    </TemplateWrapper>
  );
}
