import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import { IndexPageFeatures } from 'themes/autumn/components/features';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageBreadcrumb from 'themes/autumn/components/page/PageBreadcrumb';
import PageHeading from 'themes/autumn/components/page/PageHeading';
import Zone from 'themes/autumn/components/zone/Zone';
import { ZoneName } from 'types/ZoneItems';
import { usePageParents } from 'util/hooks';

import { MARGIN_BETWEEN_ITEMS, MARGIN_HEADING } from './constants';
import styles from './styles.module.css';

export default function IndexPage2ndLevel(): React.ReactElement {
  const page = useAppSelector((state) => state.page);
  const { parent: parentPage } = usePageParents();

  return (
    <TemplateWrapper bgColor="bg-stone-100" showNavigationAd={false}>
      <Container maxWidthMd="max-w-full" maxWidthSm="max-w-full" noGutter>
        <div className={clsx(styles.bonzaiMargin, 'border-b border-gray-300')}>
          <Zone itemMargin="m-0" name={ZoneName.NAVIGATION} snapHeading />
        </div>
        <Zone
          itemMargin={MARGIN_BETWEEN_ITEMS}
          name={ZoneName.TOP}
          snapHeading
        />
        <div className="mx-auto mt-8 flex w-full max-w-container flex-col px-4 font-inter md:px-6 xl:px-0">
          <PageBreadcrumb />
          {parentPage?.name && (
            <PageHeading
              className="mt-8 font-inter text-3xl font-medium leading-normal text-gray-900 md:!text-3xl"
              show={page.showHeading}
              text={page.name}
            />
          )}
        </div>
      </Container>
      <Container
        className="flex flex-col"
        maxWidthMd="max-w-container"
        maxWidthSm="max-w-container"
      >
        <div className="mt-8">
          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.MAIN}
            snapHeading
            snapMargin={MARGIN_HEADING}
          />
        </div>
        <div className="mt-8">
          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.BOTTOM}
            snapHeading
          />
        </div>
      </Container>
      <IndexPageFeatures />
    </TemplateWrapper>
  );
}
