'use client';

import { faBookmark } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useCallback, useEffect, useRef, useState } from 'react';

import { BOOKMARK_GTM_EVENT } from 'components/Bookmark/constants';
import { deleteBookmark, fetchBookmarks } from 'components/Bookmark/sepang';
import { useAppSelector } from 'store/hooks';
import Container from 'themes/autumn/components/generic/Container';
import Link from 'themes/autumn/components/generic/Link';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageHeading from 'themes/autumn/components/page/PageHeading';
import Zone from 'themes/autumn/components/zone/Zone';
import { ZoneName } from 'types/ZoneItems';
import { UserBookmarkResourceType } from 'types/sepang-types/bookmark';
import {
  type GetUserBookmarksRequest,
  Order,
} from 'types/sepang-types/request';
import { redirectToRegister } from 'util/auth';
import { setGtmDataLayer } from 'util/gtm';
import { getTimeBasedGreeting, useDate } from 'util/time';

import {
  BookmarkButtons,
  BookmarkContent,
  BookmarkRoot,
  BookmarkThumbnail,
} from './components/Bookmark';

import type { Bookmark } from 'components/Bookmark/types';
import type { ImageElement } from 'types/Story';

function BookmarkedStory({
  bookmark,
  onDelete,
}: {
  bookmark: Bookmark;
  onDelete: (bookmark: Bookmark) => void;
}) {
  const thumbnail = bookmark.metadata?.thumbnail as string | ImageElement;
  const mobileImageRef = useRef<HTMLDivElement>(null);
  const desktopImageRef = useRef<HTMLDivElement>(null);
  return (
    <BookmarkRoot>
      <BookmarkButtons bookmark={bookmark} onDelete={onDelete} />
      <BookmarkContent bookmark={bookmark}>
        <div
          className="block aspect-[3/2] w-40 shrink-0 overflow-hidden rounded-md bg-gray-100 md:hidden"
          ref={mobileImageRef}
        >
          <BookmarkThumbnail
            height={mobileImageRef.current?.clientHeight}
            thumbnail={thumbnail}
            title={bookmark.metadata?.title}
            url={bookmark.metadata?.url}
            width={mobileImageRef.current?.clientWidth}
          />
        </div>
      </BookmarkContent>
      <div
        className="hidden aspect-[3/2] h-auto w-56 shrink-0 overflow-hidden rounded-md bg-gray-100 md:block"
        ref={desktopImageRef}
      >
        <BookmarkThumbnail
          height={desktopImageRef.current?.clientHeight}
          thumbnail={thumbnail}
          title={bookmark.metadata?.title}
          url={bookmark.metadata?.url}
          width={desktopImageRef.current?.clientWidth}
        />
      </div>
    </BookmarkRoot>
  );
}

function BookmarkedPage({
  bookmark,
  onDelete,
}: {
  bookmark: Bookmark;
  onDelete: (bookmark: Bookmark) => void;
}) {
  const thumbnail = bookmark.metadata?.thumbnail as string | ImageElement;
  const mobileImageRef = useRef<HTMLDivElement>(null);
  const desktopImageRef = useRef<HTMLDivElement>(null);
  return (
    <BookmarkRoot>
      <BookmarkButtons bookmark={bookmark} onDelete={onDelete} />
      <BookmarkContent bookmark={bookmark}>
        <div
          className="block aspect-[3/2] w-40 shrink-0 overflow-hidden rounded-md bg-gray-100 md:hidden"
          ref={mobileImageRef}
        >
          <BookmarkThumbnail
            height={mobileImageRef.current?.clientHeight}
            thumbnail={thumbnail}
            title={bookmark.metadata?.title}
            url={bookmark.metadata?.url}
            width={mobileImageRef.current?.clientWidth}
          />
        </div>
      </BookmarkContent>
      <div
        className="hidden aspect-[3/2] h-auto w-56 shrink-0 overflow-hidden rounded-md bg-gray-100 md:block"
        ref={desktopImageRef}
      >
        <BookmarkThumbnail
          height={desktopImageRef.current?.clientHeight}
          thumbnail={thumbnail}
          title={bookmark.metadata?.title}
          url={bookmark.metadata?.url}
          width={desktopImageRef.current?.clientWidth}
        />
      </div>
    </BookmarkRoot>
  );
}

function BookmarkedAuthor({
  bookmark,
  onDelete,
}: {
  bookmark: Bookmark;
  onDelete: (bookmark: Bookmark) => void;
}) {
  const thumbnail = bookmark.metadata?.thumbnail as string | ImageElement;
  const mobileImageRef = useRef<HTMLDivElement>(null);
  const desktopImageRef = useRef<HTMLDivElement>(null);

  return (
    <BookmarkRoot>
      <BookmarkButtons bookmark={bookmark} onDelete={onDelete} />
      <BookmarkContent bookmark={bookmark}>
        <div
          className="block aspect-square size-30 shrink-0 overflow-hidden rounded-full bg-gray-100 md:hidden"
          ref={mobileImageRef}
        >
          <BookmarkThumbnail
            height={mobileImageRef.current?.clientHeight}
            thumbnail={thumbnail}
            title={bookmark.metadata?.title}
            url={bookmark.metadata?.url}
            width={mobileImageRef.current?.clientWidth}
          />
        </div>
      </BookmarkContent>
      <div className="hidden h-full w-56 items-center justify-center md:flex">
        <div
          className="aspect-square size-36 shrink-0 overflow-hidden rounded-full bg-gray-100"
          ref={desktopImageRef}
        >
          <BookmarkThumbnail
            height={desktopImageRef.current?.clientHeight}
            thumbnail={thumbnail}
            title={bookmark.metadata?.title}
            url={bookmark.metadata?.url}
            width={desktopImageRef.current?.clientWidth}
          />
        </div>
      </div>
    </BookmarkRoot>
  );
}

function BookmarkSkeleton() {
  return (
    <div className="mt-6 flex w-full animate-pulse flex-col-reverse gap-y-6 overflow-hidden rounded-lg bg-white md:mx-auto md:w-72 md:flex-row md:gap-x-12 md:py-0 lg:w-full lg:max-w-[968px]">
      <div className="flex items-center gap-x-4">
        <div className="size-10 rounded-full bg-gray-100" />
        <div className="size-10 rounded-full bg-gray-100" />
      </div>
      <div className="flex grow gap-x-4">
        <div className="flex grow flex-col pt-2">
          <div className="h-5 w-3/4 bg-gray-100" />
          <div className="mt-1.5 h-5 w-full bg-gray-100" />
          <div className="mt-3 h-4 w-3/4 bg-gray-100" />
          <div className="mt-1 h-4 w-full bg-gray-100" />
          <div className="mt-2 h-3 w-2/3 bg-gray-100" />
        </div>
        <div className="relative aspect-[3/2] w-32 rounded-lg bg-gray-100 md:hidden" />
      </div>
      <div className="relative hidden aspect-[3/2] w-56 rounded-lg bg-gray-100 md:block" />
    </div>
  );
}

function NoBookmarks() {
  return (
    <div className="mt-8 flex w-full max-w-80 flex-col items-center justify-center text-center text-gray-900">
      <div className="flex items-center justify-center gap-x-2">
        <FontAwesomeIcon className="size-5" icon={faBookmark} />
        Save
      </div>
      <h3 className="mt-6 font-inter text-base font-semibold text-gray-900">
        You have no saved items.
      </h3>
      <p className="mt-4 font-inter text-sm font-normal text-gray-600">
        Add articles & pages to my saved list using the save button & come back
        to them here any time.
      </p>
      <Link
        className="mt-4 font-inter text-sm font-normal text-blue-500 hover:underline"
        href="/"
        noStyle
      >
        Go to the homepage
      </Link>
    </div>
  );
}

function ErrorLoadingBookmarks() {
  return (
    <div className="mt-8 flex w-full max-w-80 flex-col items-center justify-center text-center text-gray-900">
      <h3 className="font-inter text-base font-semibold text-gray-900">
        There&apos;s been an issue.
      </h3>
      <p className="mt-6 font-inter text-sm font-normal text-gray-600">
        Please try again using the button below. If the issue persists, please
        contact support.
      </p>
      <button
        className="mt-4 block w-full rounded border border-gray-900 p-2 text-center font-inter text-sm font-medium hover:border-gray-300 hover:shadow-md"
        onClick={() => {
          window.location.reload();
        }}
        type="button"
      >
        Refresh Page
      </button>
    </div>
  );
}

function BookmarkedResource({
  bookmark,
  onDelete,
}: {
  bookmark: Bookmark;
  onDelete: (bookmark: Bookmark) => void;
}) {
  if (bookmark.resourceType === UserBookmarkResourceType.STORY) {
    return <BookmarkedStory bookmark={bookmark} onDelete={onDelete} />;
  }
  if (bookmark.resourceType === UserBookmarkResourceType.PAGE) {
    return <BookmarkedPage bookmark={bookmark} onDelete={onDelete} />;
  }
  if (bookmark.resourceType === UserBookmarkResourceType.AUTHOR) {
    return <BookmarkedAuthor bookmark={bookmark} onDelete={onDelete} />;
  }
  return null;
}

function IndexPageUserBookmarks(): React.ReactElement | null {
  const page = useAppSelector((state) => state.page);
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
  const [hasFetched, setHasFetched] = useState(false);
  const [queryParams, setQueryParams] = useState<GetUserBookmarksRequest>({
    limit: '10',
    order: Order.DESC,
  });
  const [hasMore, setHasMore] = useState(true);
  const [requestError, setRequestError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(false);
  const userBookmarksEnabled = useAppSelector(
    (state) => state.features.userBookmarks.enabled,
  );
  const { initialized: pianoInitialized, user } = useAppSelector(
    (state) => state.piano,
  );
  const bottomLoaderRef = useRef<HTMLDivElement>(null);
  const date = useDate();

  const loadBookmarks = useCallback(async () => {
    setHasFetched(true);
    setLoading(true);
    try {
      const response = await fetchBookmarks(queryParams);
      if (response.success) {
        setBookmarks([...bookmarks, ...response.data]);
        setHasMore(!!response?.meta?.nextToken);
        setQueryParams({
          ...queryParams,
          ...(response?.meta?.nextToken && {
            next_token: response?.meta?.nextToken,
          }),
        });
      }
    } catch (error) {
      setRequestError(error as Error);
    } finally {
      setLoading(false);
    }
  }, [queryParams, bookmarks]);

  useEffect(() => {
    if (!userBookmarksEnabled) {
      window.location.href = '/';
    }
  }, [userBookmarksEnabled]);

  useEffect(() => {
    if (!pianoInitialized) {
      return;
    }

    if (!user) {
      redirectToRegister(window.location.href, { referrer: 'bookmark' });
    }
  }, [pianoInitialized, user]);

  useEffect(() => {
    const bottomLoader = bottomLoaderRef.current;
    // eslint-disable-next-line compat/compat
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (
          target.isIntersecting &&
          hasMore &&
          !loading &&
          !requestError &&
          user
        ) {
          loadBookmarks().catch(console.error);
        }
      },
      { rootMargin: '500px 0px 0px 0px' },
    );
    if (bottomLoader) {
      observer.observe(bottomLoader);
    }
    return () => {
      if (bottomLoader) {
        observer.unobserve(bottomLoader);
      }
    };
  }, [hasMore, loadBookmarks, loading, requestError, user]);

  const handleDeleteBookmark = useCallback(
    async (bookmark: Bookmark) => {
      try {
        await deleteBookmark(bookmark.resourceType, bookmark.resourceId);
        setBookmarks(
          bookmarks.filter(
            (b) => b.resourceTypeAndId !== bookmark.resourceTypeAndId,
          ),
        );
        setGtmDataLayer({
          data: {
            action: 'click',
            label: 'remove',
          },
          event: BOOKMARK_GTM_EVENT,
        });
      } catch (error) {
        console.error(error);
      }
    },
    [bookmarks, setBookmarks],
  );

  if (!userBookmarksEnabled) {
    return null;
  }

  return (
    <TemplateWrapper showNavigationAd showRevBanner={false} showStickyFooterAd>
      <Container className="mx-auto mt-4 md:mt-10" noGutter>
        <Zone name={ZoneName.TOP} />
        <div className="mx-4 gap-3 border-b-2 border-gray-800 md:mx-6 xl:mx-0">
          <PageHeading show={page.showHeading} text={page.name} />

          <h2 className="w-auto pb-4 font-inter text-sm font-normal leading-[21px] text-gray-600 md:text-base">
            {date.toLocaleDateString('en-US', {
              day: 'numeric',
              month: 'long',
            })}
            ・{getTimeBasedGreeting(date.getHours())},{' '}
            {user?.given_name || user?.email}
          </h2>
        </div>

        <div className="mx-4 md:mx-6 xl:mx-0">
          <div className="flex flex-col items-center justify-center gap-6">
            {!user && !loading && !hasFetched && <BookmarkSkeleton />}
            {!bookmarks.length && hasFetched && !loading && !requestError && (
              <NoBookmarks />
            )}
            {requestError && <ErrorLoadingBookmarks />}
            {bookmarks.map((bookmark) => (
              <div
                className="w-full border-t border-gray-200 pt-6 first:border-none"
                key={bookmark.resourceTypeAndId}
              >
                <BookmarkedResource
                  bookmark={bookmark}
                  onDelete={handleDeleteBookmark}
                />
              </div>
            ))}
            {user && loading && hasFetched && !requestError && (
              <BookmarkSkeleton />
            )}
          </div>
        </div>
        <div className="h-2" ref={bottomLoaderRef} />
      </Container>
    </TemplateWrapper>
  );
}

export default IndexPageUserBookmarks;
