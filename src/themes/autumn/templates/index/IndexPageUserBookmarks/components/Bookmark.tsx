import { faExternalLinkAlt } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import CloseIcon from 'themes/autumn/components/icons/CloseIcon';
import NativeImage from 'themes/autumn/components/storyElements/common/NativeImage';
import { IconButton } from 'themes/autumn/templates/stories/common/IconButton';
import ShareButton from 'themes/autumn/templates/stories/common/ShareButton';
import { cardImageUrl } from 'util/image';
import { isExternalLink } from 'util/page';

import type { Bookmark } from 'components/Bookmark/types';
import type { ImageElement } from 'types/Story';

export function BookmarkButtons({
  bookmark,
  onDelete,
}: {
  bookmark: Bookmark;
  onDelete: (bookmark: Bookmark) => void;
}) {
  return (
    <div className="flex w-full gap-x-4 md:w-auto md:items-center">
      <IconButton
        Icon={CloseIcon}
        aria-label="Remove bookmark"
        className="size-10 gap-0 border border-gray-300"
        onClick={() => onDelete(bookmark)}
        title="Remove bookmark"
      />
      {bookmark.metadata?.url && (
        <ShareButton
          className="size-10 gap-0 border border-gray-300"
          showTriggerTitle={false}
          url={bookmark.metadata.url}
        />
      )}
    </div>
  );
}

export function BookmarkRoot({ children }: { children: React.ReactNode }) {
  return (
    <div className="group mx-auto flex w-full max-w-[968px] flex-col-reverse items-center gap-y-1 md:flex-row md:gap-x-12 md:gap-y-0">
      {children}
    </div>
  );
}

export function BookmarkContent({
  bookmark,
  children,
}: {
  bookmark: Bookmark;
  children: React.ReactNode;
}) {
  const currentSiteId = useAppSelector((state) => state.settings.siteId);
  const isExternal = bookmark.metadata?.siteId !== currentSiteId;
  return (
    <Link
      className="flex w-full grow flex-col gap-y-3 md:w-auto"
      href={bookmark.metadata?.url}
      noStyle
      target={isExternal ? '_blank' : undefined}
    >
      <h3 className="font-merriweather text-xl font-bold text-gray-900 group-hover:underline">
        {bookmark.metadata?.title}
        {isExternal && (
          <FontAwesomeIcon
            className="ml-3"
            icon={faExternalLinkAlt}
            size="xs"
          />
        )}
      </h3>
      <div className="flex gap-x-4">
        <div className="line-clamp-3 h-14 grow font-merriweather text-sm font-normal text-gray-600 md:h-auto">
          {bookmark.metadata?.description}
        </div>
        {children}
      </div>
    </Link>
  );
}

export function BookmarkThumbnail({
  height,
  onClick,
  thumbnail,
  title,
  url,
  width,
}: {
  height?: number;
  onClick?: () => void;
  thumbnail?: string | ImageElement;
  title?: string;
  url?: string;
  width?: number;
}) {
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);

  if (!height || !width) {
    return null;
  }

  if (!thumbnail) return null;

  const isObject = typeof thumbnail === 'object' && 'cropConfig' in thumbnail;
  const thumbnailContent = isObject ? (
    <NativeImage
      height={height}
      image={thumbnail}
      imageClassName="rounded-md object-cover object-center"
      width={width}
    />
  ) : (
    <img
      alt={title}
      className="size-full object-cover object-center"
      src={
        isExternalLink(thumbnail as string)
          ? (thumbnail as string)
          : cardImageUrl({
              height: height * 2,
              image: { uri: thumbnail as string },
              transformUrl,
              width: width * 2,
            })
      }
    />
  );

  return (
    <Link href={url} noStyle onClick={onClick}>
      {thumbnailContent}
    </Link>
  );
}
