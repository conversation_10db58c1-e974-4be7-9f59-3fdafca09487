// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SubcategoryPage Explore Travel V2 renders bottom zone with content 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-full max-w-full"
    >
      <div
        class="border-b border-gray-300"
      />
    </div>
    <div
      class="mx-auto w-full max-w-container max-w-container px-4 md:px-6 xl:px-0 flex flex-col"
    >
      <div
        class="mt-6 flex"
      >
        <div
          class="hidden w-[250px] md:block md:self-end"
        />
      </div>
      <div
        class="mx-auto w-full empty:hidden md:max-w-md"
      />
      <div
        class="block w-[200px] empty:hidden md:hidden"
      />
      <div
        class="mt-7"
      >
        <hr
          class="mb-9 hidden h-px border-gray-900 md:block"
        />
      </div>
      <div
        class="mt-8"
      >
        <div>
          Bottom zone content
        </div>
      </div>
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;

exports[`SubcategoryPage Explore Travel V2 renders correctly 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-full max-w-full"
    >
      <div
        class="border-b border-gray-300"
      />
    </div>
    <div
      class="mx-auto w-full max-w-container max-w-container px-4 md:px-6 xl:px-0 flex flex-col"
    >
      <div
        class="mt-6 flex"
      >
        <div
          class="hidden w-[250px] md:block md:self-end"
        />
      </div>
      <div
        class="mx-auto w-full empty:hidden md:max-w-md"
      />
      <div
        class="block w-[200px] empty:hidden md:hidden"
      />
      <div
        class="mt-7"
      >
        <hr
          class="mb-9 hidden h-px border-gray-900 md:block"
        />
      </div>
      <div
        class="mt-8"
      />
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;

exports[`SubcategoryPage Explore Travel V2 renders divider in MD viewport 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-full max-w-full"
    >
      <div
        class="border-b border-gray-300"
      />
    </div>
    <div
      class="mx-auto w-full max-w-container max-w-container px-4 md:px-6 xl:px-0 flex flex-col"
    >
      <div
        class="mt-6 flex"
      >
        <div
          class="hidden w-[250px] md:block md:self-end"
        />
      </div>
      <div
        class="mx-auto w-full empty:hidden md:max-w-md"
      />
      <div
        class="block w-[200px] empty:hidden md:hidden"
      />
      <div
        class="mt-7"
      >
        <hr
          class="mb-9 hidden h-px border-gray-900 md:block"
        />
      </div>
      <div
        class="mt-8"
      />
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;

exports[`SubcategoryPage Explore Travel V2 renders main top small width zone correctly 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-full max-w-full"
    >
      <div
        class="border-b border-gray-300"
      />
    </div>
    <div
      class="mx-auto w-full max-w-container max-w-container px-4 md:px-6 xl:px-0 flex flex-col"
    >
      <div
        class="mt-6 flex"
      >
        <div
          class="hidden w-[250px] md:block md:self-end"
        />
      </div>
      <div
        class="mx-auto w-full empty:hidden md:max-w-md"
      >
        <div>
          Zone item 1
        </div>
      </div>
      <div
        class="block w-[200px] empty:hidden md:hidden"
      />
      <div
        class="mt-7"
      >
        <hr
          class="mb-9 hidden h-px border-gray-900 md:block"
        />
      </div>
      <div
        class="mt-8"
      />
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;

exports[`SubcategoryPage Explore Travel V2 renders main zone with content 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-full max-w-full"
    >
      <div
        class="border-b border-gray-300"
      />
    </div>
    <div
      class="mx-auto w-full max-w-container max-w-container px-4 md:px-6 xl:px-0 flex flex-col"
    >
      <div
        class="mt-6 flex"
      >
        <div
          class="hidden w-[250px] md:block md:self-end"
        />
      </div>
      <div
        class="mx-auto w-full empty:hidden md:max-w-md"
      />
      <div
        class="block w-[200px] empty:hidden md:hidden"
      />
      <div
        class="mt-7"
      >
        <hr
          class="mb-9 hidden h-px border-gray-900 md:block"
        />
        <div>
          Main zone content
        </div>
      </div>
      <div
        class="mt-8"
      />
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;

exports[`SubcategoryPage Explore Travel V2 renders with empty main top zone 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-full max-w-full"
    >
      <div
        class="border-b border-gray-300"
      />
    </div>
    <div
      class="mx-auto w-full max-w-container max-w-container px-4 md:px-6 xl:px-0 flex flex-col"
    >
      <div
        class="mt-6 flex"
      >
        <div
          class="hidden w-[250px] md:block md:self-end"
        />
      </div>
      <div
        class="mx-auto w-full empty:hidden md:max-w-md"
      />
      <div
        class="block w-[200px] empty:hidden md:hidden"
      />
      <div
        class="mt-7"
      >
        <hr
          class="mb-9 hidden h-px border-gray-900 md:block"
        />
      </div>
      <div
        class="mt-8"
      />
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;

exports[`SubcategoryPage Explore Travel V2 renders with empty main top zone 2`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-full max-w-full"
    >
      <div
        class="border-b border-gray-300"
      />
    </div>
    <div
      class="mx-auto w-full max-w-container max-w-container px-4 md:px-6 xl:px-0 flex flex-col"
    >
      <div
        class="mt-6 flex"
      >
        <div
          class="hidden w-[250px] md:block md:self-end"
        />
      </div>
      <div
        class="mx-auto w-full empty:hidden md:max-w-md"
      />
      <div
        class="block w-[200px] empty:hidden md:hidden"
      />
      <div
        class="mt-7"
      >
        <hr
          class="mb-9 hidden h-px border-gray-900 md:block"
        />
      </div>
      <div
        class="mt-8"
      />
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;

exports[`SubcategoryPage Explore Travel V2 renders with empty main top zone 3`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-full max-w-full"
    >
      <div
        class="border-b border-gray-300"
      />
    </div>
    <div
      class="mx-auto w-full max-w-container max-w-container px-4 md:px-6 xl:px-0 flex flex-col"
    >
      <div
        class="mt-6 flex"
      >
        <div
          class="hidden w-[250px] md:block md:self-end"
        />
      </div>
      <div
        class="mx-auto w-full empty:hidden md:max-w-md"
      />
      <div
        class="block w-[200px] empty:hidden md:hidden"
      />
      <div
        class="mt-7"
      >
        <hr
          class="mb-9 hidden h-px border-gray-900 md:block"
        />
      </div>
      <div
        class="mt-8"
      />
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;

exports[`SubcategoryPage Explore Travel V2 renders with empty main top zone 4`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-full max-w-full"
    >
      <div
        class="border-b border-gray-300"
      />
    </div>
    <div
      class="mx-auto w-full max-w-container max-w-container px-4 md:px-6 xl:px-0 flex flex-col"
    >
      <div
        class="mt-6 flex"
      >
        <div
          class="hidden w-[250px] md:block md:self-end"
        />
      </div>
      <div
        class="mx-auto w-full empty:hidden md:max-w-md"
      />
      <div
        class="block w-[200px] empty:hidden md:hidden"
      />
      <div
        class="mt-7"
      >
        <hr
          class="mb-9 hidden h-px border-gray-900 md:block"
        />
      </div>
      <div
        class="mt-8"
      />
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;
