import React from 'react';

import { IndexPageFeatures } from 'themes/autumn/components/features';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageBreadcrumb from 'themes/autumn/components/page/PageBreadcrumb';
import Zone from 'themes/autumn/components/zone/Zone';
import { ExploreTravelLogo } from 'themes/autumn/templates/zoneItems/navigation/ExploreTravelSubNav';
import { ZoneName } from 'types/ZoneItems';

const MARGIN_BETWEEN_ITEMS = 'mb-8';
const MARGIN_HEADING = 'mb-10';

export default function SubcategoryPage(): React.ReactElement {
  return (
    <TemplateWrapper
      navigationStickyMobileContent={<ExploreTravelLogo />}
      navigationStickyWideContent={
        <Zone itemMargin="m-0" name={ZoneName.NAVIGATION} snapHeading />
      }
      showNavigationAd={false}
      showStickyFooterAd
    >
      <Container maxWidthMd="max-w-full" maxWidthSm="max-w-full" noGutter>
        <div className="border-b border-gray-300">
          <Zone itemMargin="m-0" name={ZoneName.NAVIGATION} snapHeading />
        </div>
        <Zone
          itemMargin={MARGIN_BETWEEN_ITEMS}
          name={ZoneName.MAIN_TOP}
          snapHeading
        />
      </Container>
      <Container
        className="flex flex-col"
        maxWidthMd="max-w-container"
        maxWidthSm="max-w-container"
      >
        <div className="mt-6 flex">
          <PageBreadcrumb className="grow" />
          <div className="hidden w-[250px] md:block md:self-end">
            <Zone name={ZoneName.RIGHT_OF_PAGE_BREADCRUMB} />
          </div>
        </div>
        <div className="mx-auto w-full empty:hidden md:max-w-md">
          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.MAIN_TOP_SMALL_WIDTH}
            snapHeading
            snapMargin="mb-4"
          />
        </div>
        <div className="block w-[200px] empty:hidden md:hidden">
          <Zone name={ZoneName.RIGHT_OF_PAGE_BREADCRUMB} />
        </div>
        <div className="mt-7">
          <hr className="mb-9 hidden h-px border-gray-900 md:block" />

          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.MAIN}
            snapHeading
            snapMargin={MARGIN_HEADING}
          />
        </div>
        <div className="mt-8">
          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.BOTTOM}
            snapHeading
          />
        </div>
      </Container>
      <IndexPageFeatures />
    </TemplateWrapper>
  );
}
