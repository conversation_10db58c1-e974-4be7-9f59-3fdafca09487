import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper } from 'util/jest';

import SubcategoryPage from '.';

describe('SubcategoryPage Explore Travel V2', () => {
  it('renders correctly', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      zoneItems: {
        ...state.zoneItems,
        page: [],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <SubcategoryPage />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with empty main top zone', () => {
    expect.assertions(8);

    for (let count = 0; count < 4; count += 1) {
      const store = createStore((state) => ({
        ...state,

        zoneItems: {
          ...state.zoneItems,
          page: new Array(count).fill(undefined).map((x, i) => ({
            elementId: i,
            index: 1,
            order: 0,
            zoneItemData: {
              code: `Zone item ${i + 1}`,
              id: i,
              template: 'default.html',
            },
            zoneItemId: i,
            zoneItemType: ZoneItemType.CodeSnippet,
            zoneName: ZoneName.MAIN_SIDE,
          })),
        },
      }));

      const { container, unmount } = render(
        <TestWrapper store={store}>
          <SubcategoryPage />
        </TestWrapper>,
      );

      expect(container.firstChild).toMatchSnapshot();
      expect(container.querySelector('.justify-center')).toBeTruthy();

      unmount();
    }
  });

  it('renders main top small width zone correctly', () => {
    expect.assertions(2);

    const store = createStore((state) => ({
      ...state,
      layoutTheme: 'Autumn',
      themeDir: 'autumn',
      zoneItems: {
        ...state.zoneItems,
        page: [
          {
            elementId: 0,
            index: 1,
            order: 0,
            zoneItemData: {
              code: 'Zone item 1',
              id: 0,
              template: 'default.html',
            },
            zoneItemId: 0,
            zoneItemType: ZoneItemType.CodeSnippet,
            zoneName: ZoneName.MAIN_TOP_SMALL_WIDTH,
          },
        ],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <SubcategoryPage />
      </TestWrapper>,
    );

    const smallWidthZone = container.querySelector('.md\\:max-w-md');
    expect(smallWidthZone).toBeTruthy();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders main zone with content', () => {
    expect.assertions(2);

    const store = createStore((state) => ({
      ...state,
      layoutTheme: 'Autumn',
      themeDir: 'autumn',
      zoneItems: {
        ...state.zoneItems,
        page: [
          {
            elementId: 1,
            index: 1,
            order: 0,
            zoneItemData: {
              code: 'Main zone content',
              id: 1,
              template: 'default.html',
            },
            zoneItemId: 1,
            zoneItemType: ZoneItemType.CodeSnippet,
            zoneName: ZoneName.MAIN,
          },
        ],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <SubcategoryPage />
      </TestWrapper>,
    );

    const mainZone = container.querySelector('.mt-7');
    expect(mainZone).toBeTruthy();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders bottom zone with content', () => {
    expect.assertions(2);

    const store = createStore((state) => ({
      ...state,
      layoutTheme: 'Autumn',
      themeDir: 'autumn',
      zoneItems: {
        ...state.zoneItems,
        page: [
          {
            elementId: 1,
            index: 1,
            order: 0,
            zoneItemData: {
              code: 'Bottom zone content',
              id: 1,
              template: 'default.html',
            },
            zoneItemId: 1,
            zoneItemType: ZoneItemType.CodeSnippet,
            zoneName: ZoneName.BOTTOM,
          },
        ],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <SubcategoryPage />
      </TestWrapper>,
    );

    const bottomZone = container.querySelector('.mt-8');
    expect(bottomZone).toBeTruthy();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders divider in MD viewport', () => {
    expect.assertions(3);

    const store = createStore((state) => ({
      ...state,
      zoneItems: {
        ...state.zoneItems,
        page: [],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <SubcategoryPage />
      </TestWrapper>,
    );

    const divider = container.querySelector('hr');
    expect(divider).toBeTruthy();
    expect(divider).toHaveClass('hidden', 'md:block');
    expect(container.firstChild).toMatchSnapshot();
  });
});
