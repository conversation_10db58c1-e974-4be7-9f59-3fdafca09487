import type { NavThemeRecord } from 'types/Nav';

export const COMMUNITY_NAV_THEME: NavThemeRecord = {
  currentUrl: {
    icon: '',
    textBackground: 'bg-gray-900',
    textColor: 'text-white',
  },
  default: {
    icon: '',
    textBackground: 'bg-gray-350 hover:bg-gray-100',
    textColor: 'text-slate-750',
  },
};

export const classifiedsPage = {
  menuName: 'Classifieds',
  name: 'Classifieds',
  newWindow: false,
  url: 'classifieds',
};

export const tributesFuneralsPage = {
  menuName: 'Tributes & Funerals',
  name: 'Tributes & Funerals',
  newWindow: false,
  url: 'tributes-funerals',
};

export const localBusinessPage = {
  menuName: 'Local Business',
  name: 'Local Business',
  newWindow: false,
  url: 'local-business',
};
