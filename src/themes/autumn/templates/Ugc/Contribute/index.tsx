import ContactUs from 'components/Ugc/ContactUs';
import TradeBox from 'components/Ugc/TradeBox';
import { useAppSelector } from 'store/hooks';
import Ad from 'themes/autumn/components/ads/Ad';
import Container from 'themes/autumn/components/generic/Container';
import Link from 'themes/autumn/components/generic/Link';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import PageHeading from 'themes/autumn/components/page/PageHeading';
import PageNavigation from 'themes/autumn/components/page/PageNavigation';
import PageParentHeading from 'themes/autumn/components/page/PageParentHeading';
import { NavigationType } from 'types/Nav';
import { AdSize } from 'util/ads';
import { usePageParents, usePages } from 'util/hooks';
import { pageUrlPath } from 'util/page';

import {
  COMMUNITY_NAV_THEME,
  classifiedsPage,
  localBusinessPage,
  tributesFuneralsPage,
} from '../../index/IndexPageCommunity/constants';

interface ContributeCardProps {
  buttonText: string;
  description: React.ReactNode;
  href: string;
  icon: React.ReactNode;
  title: React.ReactNode;
}

const ContributeCard: React.FC<ContributeCardProps> = ({
  buttonText,
  description,
  href,
  icon,
  title,
}) => (
  <div className="flex flex-col items-start rounded-lg bg-teal-50 px-4 pb-4 pt-0 md:px-6 md:pt-6">
    <div className="md:mb-4">{icon}</div>
    <h2 className="min-h-[48px] font-semibold text-green-900 md:min-h-[96px] md:text-2xl lg:min-h-[64px]">
      {title}
    </h2>
    <p className="mt-2 min-h-[80px] text-sm font-normal text-green-900 md:min-h-[72px] md:text-base lg:min-h-[56px]">
      {description}
    </p>
    <Link
      className="mt-1 w-full rounded-md bg-green-700 py-2 text-center text-sm font-medium text-white transition-colors hover:bg-green-800 sm:mt-2 md:px-4 md:text-base"
      href={href}
      noStyle
    >
      {buttonText}
    </Link>
  </div>
);

const EventIcon = () => (
  <div className="flex items-start justify-start md:mt-6 md:justify-center">
    <svg
      className="w-2/3 md:w-full"
      fill="none"
      height="123"
      viewBox="0 0 124 123"
      width="124"
    >
      <path
        clipRule="evenodd"
        d="M47.5614 56.2341L44.8929 63.1841L37.4593 63.5758C35.8863 63.6585 34.5275 64.702 34.0379 66.1986C33.5513 67.6981 34.0379 69.3384 35.262 70.333L41.0491 75.0184L39.1241 82.2101C38.7141 83.7311 39.2863 85.3469 40.5594 86.2711C41.8356 87.1984 43.5493 87.2413 44.8683 86.3844L51.1145 82.3294L57.3606 86.3844C58.6796 87.2413 60.3934 87.1984 61.6695 86.2711C62.9426 85.3469 63.5149 83.7311 63.1048 82.2101L61.1799 75.0184L66.9669 70.333C68.1911 69.3384 68.6777 67.6981 68.1911 66.1986C67.7014 64.702 66.3426 63.6585 64.7696 63.5758L57.3361 63.1841L54.6675 56.2341C54.1014 54.7652 52.6906 53.792 51.1145 53.792C49.5383 53.792 48.1276 54.7652 47.5614 56.2341Z"
        fill="#FCD34D"
        fillRule="evenodd"
      />
      <path
        clipRule="evenodd"
        d="M46.7644 52.7881L43.4948 61.3035L34.3868 61.7835C32.4595 61.8847 30.7947 63.1634 30.1947 64.997C29.5985 66.8344 30.1947 68.8441 31.6946 70.0627L38.7852 75.8035L36.4267 84.6152C35.9242 86.4787 36.6254 88.4586 38.1852 89.591C39.7488 90.7271 41.8486 90.7796 43.4647 89.7297L51.1178 84.7614L58.7709 89.7297C60.387 90.7796 62.4868 90.7271 64.0504 89.591C65.6103 88.4586 66.3115 86.4787 65.809 84.6152L63.4505 75.8035L70.5411 70.0627C72.0409 68.8441 72.6371 66.8343 72.0409 64.997C71.441 63.1634 69.7761 61.8848 67.8488 61.7835L58.7409 61.3035L55.4712 52.7881C54.7775 50.9882 53.049 49.7958 51.1178 49.7958C49.1867 49.7958 47.4582 50.9882 46.7644 52.7881ZM50.4991 54.2204C50.5966 53.9655 50.844 53.7967 51.1178 53.7967C51.3915 53.7967 51.639 53.9655 51.7365 54.2204L55.4711 63.9511C55.7561 64.6897 56.446 65.1885 57.2334 65.2297L67.6388 65.7771C67.9126 65.7921 68.1488 65.9721 68.235 66.2346C68.3213 66.4933 68.235 66.7783 68.0213 66.9545L59.9258 73.5126C59.3108 74.0076 59.0484 74.8213 59.2508 75.5824L61.9468 85.6499C62.0181 85.9124 61.9206 86.1974 61.6956 86.3549C61.4744 86.5161 61.1782 86.5273 60.9457 86.3773L52.209 80.7003C51.5453 80.2691 50.6904 80.2691 50.0267 80.7003L41.29 86.3773C41.0575 86.5273 40.7613 86.5161 40.54 86.3549C40.315 86.1974 40.2176 85.9124 40.2888 85.6499L42.9848 75.5824C43.1873 74.8212 42.9248 74.0075 42.3099 73.5126L34.2144 66.9545C34.0006 66.7783 33.9144 66.4933 34.0006 66.2346C34.0869 65.9721 34.3231 65.7921 34.5968 65.7771L45.0023 65.2297C45.7897 65.1884 46.4797 64.6897 46.7646 63.9511L50.4991 54.2204Z"
        fill="#1F2937"
        fillRule="evenodd"
      />
      <path
        clipRule="evenodd"
        d="M45.1169 18.2015V21.8011H33.1178V18.2015C33.1178 17.0353 32.6528 15.9142 31.8317 15.0892C31.003 14.2643 29.8855 13.7993 28.7194 13.7993H25.5173C24.3511 13.7993 23.2337 14.2643 22.405 15.0892C21.5839 15.9142 21.1189 17.0354 21.1189 18.2015V21.8011H15.1195C12.4685 21.8011 9.92244 22.8548 8.04764 24.7259C6.17284 26.6044 5.11914 29.1468 5.11914 31.7977V95.7931C5.11914 98.4441 6.17284 100.99 8.04764 102.865C9.92244 104.74 12.4685 105.793 15.1195 105.793H61.0438C60.4408 104.423 59.9078 103.085 59.4599 101.792H15.1197C13.526 101.792 12.0037 101.159 10.8788 100.034C9.75388 98.909 9.12024 97.3866 9.12024 95.793V41.798H93.1125V65.7404C94.4849 66.0396 95.821 66.436 97.1133 66.9219V31.7977C97.1133 29.1468 96.0595 26.6044 94.1847 24.7259C92.3099 22.8548 89.7639 21.8011 87.1129 21.8011H81.1134V18.2015C81.1134 17.0353 80.6485 15.9142 79.8274 15.0892C78.9987 14.2643 77.8812 13.7993 76.7151 13.7993H73.513C72.3467 13.7993 71.2294 14.2643 70.4007 15.0892C69.5796 15.9142 69.1146 17.0354 69.1146 18.2015V21.8011H57.1157V18.2015C57.1157 17.0353 56.6508 15.9142 55.8296 15.0892C55.001 14.2643 53.8835 13.7993 52.7174 13.7993H49.5153C48.3491 13.7993 47.2317 14.2643 46.403 15.0892C45.5819 15.9142 45.1169 17.0354 45.1169 18.2015ZM15.1197 25.7982H21.1191L21.1194 29.3979C21.1194 30.5641 21.5843 31.6852 22.4055 32.5101C23.2342 33.3351 24.3516 33.8 25.5177 33.8H28.7199C29.8861 33.8 31.0035 33.335 31.8321 32.5101C32.6533 31.6851 33.1182 30.564 33.1182 29.3979V25.7982H45.1171V29.3979C45.1171 30.5641 45.582 31.6852 46.4032 32.5101C47.2319 33.3351 48.3493 33.8 49.5154 33.8H52.7176C53.8838 33.8 55.0012 33.335 55.8298 32.5101C56.651 31.6851 57.1159 30.564 57.1159 29.3979V25.7982H69.1148V29.3979C69.1148 30.5641 69.5797 31.6852 70.4009 32.5101C71.2296 33.3351 72.347 33.8 73.5131 33.8H76.7153C77.8815 33.8 78.9989 33.335 79.8275 32.5101C80.6487 31.6851 81.1136 30.564 81.1136 29.3979V25.7982H87.1131C88.7067 25.7982 90.229 26.4318 91.354 27.5568C92.4788 28.6817 93.1125 30.2078 93.1125 31.7976V37.7971H9.12024V31.7976C9.12024 30.2077 9.75388 28.6817 10.8788 27.5568C12.0037 26.4319 13.526 25.7982 15.1197 25.7982ZM77.1127 29.3978V18.2014C77.1127 18.0926 77.0715 17.9914 76.9965 17.9164C76.9215 17.8414 76.8203 17.8002 76.7153 17.8002H73.5131C73.4081 17.8002 73.3069 17.8414 73.2319 17.9164C73.1569 17.9914 73.1156 18.0926 73.1156 18.2014V29.3978C73.1156 29.5065 73.1569 29.6078 73.2319 29.6827C73.3069 29.7577 73.4081 29.799 73.5131 29.799H76.7153C76.8203 29.799 76.9215 29.7577 76.9965 29.6827C77.0715 29.6078 77.1127 29.5065 77.1127 29.3978ZM53.1149 29.3978V18.2014C53.1149 18.0926 53.0737 17.9914 52.9987 17.9164C52.9237 17.8414 52.8225 17.8002 52.7175 17.8002H49.5153C49.4103 17.8002 49.3091 17.8414 49.2341 17.9164C49.1591 17.9914 49.1178 18.0926 49.1178 18.2014V29.3978C49.1178 29.5065 49.1591 29.6078 49.2341 29.6827C49.3091 29.7577 49.4103 29.799 49.5153 29.799H52.7175C52.8225 29.799 52.9237 29.7577 52.9987 29.6827C53.0737 29.6078 53.1149 29.5065 53.1149 29.3978ZM29.1171 29.3978V18.2014C29.1171 18.0926 29.0759 17.9914 29.0009 17.9164C28.9259 17.8414 28.8247 17.8002 28.7197 17.8002H25.5175C25.4125 17.8002 25.3113 17.8414 25.2363 17.9164C25.1613 17.9914 25.12 18.0926 25.12 18.2014V29.3978C25.12 29.5065 25.1613 29.6078 25.2363 29.6827C25.3113 29.7577 25.4125 29.799 25.5175 29.799H28.7197C28.8247 29.799 28.9259 29.7577 29.0009 29.6827C29.0759 29.6078 29.1171 29.5065 29.1171 29.3978Z"
        fill="#1F2937"
        fillRule="evenodd"
      />
      <path
        d="M93.9017 73.8976H94.03C97.4916 73.9296 100.927 75.2593 103.568 77.9002C106.658 80.9911 107.97 85.1992 107.486 89.2285L107.332 90.5155H108.628H109.333C114.287 90.5155 118.272 94.5006 118.272 99.4551C118.272 104.41 114.287 108.395 109.333 108.395H80.251C74.3297 108.395 69.5308 103.596 69.5308 97.6746C69.5308 92.0848 73.8009 87.5016 79.2606 87.0055L80.1914 86.9209L80.2989 85.9924C80.641 83.0351 81.9466 80.1711 84.2169 77.9008C86.8895 75.2283 90.395 73.8976 93.9017 73.8976ZM93.8933 84.0614L93.8849 84.0614L93.8765 84.0616C93.2995 84.0743 92.7177 84.3034 92.3295 84.6583C92.3285 84.6591 92.3276 84.66 92.3267 84.6608L85.8095 90.5855C84.8288 91.451 84.7991 92.9841 85.6304 93.9026C86.4867 94.8488 88.0234 94.8936 88.9445 94.0558L88.9448 94.0555L91.5648 91.6707V101.829C91.5648 103.121 92.6106 104.166 93.9018 104.166C95.1929 104.166 96.2387 103.121 96.2387 101.829V91.6707L98.8587 94.0555L98.8591 94.0558C99.8176 94.9276 101.301 94.7764 102.134 93.944L102.134 93.944C103.124 92.9531 102.875 91.3812 102 90.5908C101.999 90.59 101.998 90.5892 101.997 90.5884L95.4739 84.6581C95.4737 84.658 95.4735 84.6578 95.4733 84.6576C95.2557 84.4594 95.0052 84.2908 94.7011 84.1841C94.4031 84.0796 94.1241 84.0597 93.8933 84.0614Z"
        fill="#0D9488"
        stroke="#1F2937"
        stroke-width="2.29985"
      />
    </svg>
  </div>
);

const PictureIcon = () => (
  <div className="flex items-start justify-start md:mt-6 md:justify-center">
    <svg
      className="w-2/3 md:w-full"
      fill="none"
      height="123"
      viewBox="0 0 124 123"
      width="124"
    >
      <path
        clipRule="evenodd"
        d="M13.3426 81.9357L7.23211 32.4295C6.94293 30.0843 8.62436 27.9434 10.9818 27.6557L70.7956 20.3493C71.9389 20.2121 73.0655 20.5232 73.9702 21.2258C74.8782 21.9283 75.4532 22.942 75.5945 24.0794L75.6853 24.8288C75.7828 25.6116 76.4924 26.1669 77.2827 26.0733C78.073 25.9762 78.6312 25.267 78.5337 24.4841L78.4429 23.7314C78.2075 21.8413 77.2491 20.1484 75.7391 18.9775C74.2292 17.8033 72.3526 17.2848 70.4459 17.5156L10.6321 24.822C6.70753 25.3004 3.90285 28.87 4.38375 32.7775L10.4943 82.2837C10.585 83.0062 11.2038 83.5382 11.9168 83.5382C11.9773 83.5382 12.0345 83.5348 12.095 83.5281C12.8785 83.4311 13.4401 82.7185 13.3426 81.9357ZM40.2034 57.4285C40.2039 57.4291 40.2047 57.4297 40.206 57.4297C42.5769 57.4263 44.5038 55.5094 44.5072 53.1509C44.5072 53.1475 44.5038 53.1475 44.5038 53.1475C44.5038 53.1459 44.5047 53.145 44.5055 53.1442C44.5064 53.1433 44.5072 53.1425 44.5072 53.1408C44.5038 50.7823 42.5735 48.8653 40.2026 48.8653C37.8284 48.8653 35.8981 50.7856 35.8981 53.1475C35.8981 55.506 37.825 57.4263 40.1959 57.4297C40.1976 57.4297 40.1984 57.4288 40.1993 57.428C40.2001 57.4272 40.201 57.4263 40.2026 57.4263C40.2026 57.4263 40.2026 57.4275 40.2034 57.4285ZM53.1163 53.1475C53.1163 53.9337 52.4739 54.5727 51.6803 54.5727L47.2311 54.5727C47.0427 55.4827 46.6795 56.3291 46.1818 57.0751L49.3329 60.2098C49.8945 60.7686 49.8945 61.6718 49.3329 62.2305C49.0538 62.5082 48.6872 62.6487 48.3173 62.6487C47.9507 62.6487 47.5841 62.5082 47.305 62.2305L44.1505 59.0958C43.3972 59.591 42.5498 59.9523 41.635 60.1396V64.5657C41.635 65.3519 40.9927 65.9942 40.2024 65.9942C39.4087 65.9942 38.7664 65.3518 38.7664 64.5657V60.1396C37.8517 59.9522 37.0042 59.5909 36.2509 59.0958L33.0998 62.2305C32.8173 62.5082 32.4507 62.6487 32.0841 62.6487C31.7176 62.6487 31.351 62.5082 31.0685 62.2305C30.5069 61.6718 30.5069 60.7685 31.0685 60.2098L34.223 57.0751C33.7219 56.3291 33.3587 55.4827 33.1703 54.5727H28.7211C27.9308 54.5727 27.2885 53.9337 27.2885 53.1475C27.2885 52.358 27.9308 51.719 28.7211 51.719H33.1703C33.3587 50.809 33.7253 49.9659 34.223 49.2166L31.0685 46.0819C30.5069 45.5231 30.5069 44.6199 31.0651 44.0612C31.6268 43.5025 32.5348 43.5025 33.0964 44.0612L36.2542 47.1959C37.0042 46.7007 37.855 46.3394 38.7664 46.1521V41.726C38.7664 40.9365 39.4054 40.2975 40.199 40.2975C40.9927 40.2975 41.635 40.9332 41.635 41.7227L41.6384 46.1521C42.5531 46.3395 43.4006 46.7008 44.1505 47.1992L47.305 44.0612C47.8633 43.5025 48.7746 43.5025 49.3329 44.0578C49.8945 44.6166 49.8945 45.5198 49.3329 46.0785L46.1818 49.2166C46.6795 49.9659 47.0427 50.809 47.2311 51.719H51.6803C52.474 51.719 53.1163 52.358 53.1163 53.1475ZM84.681 80.6168V36.0192C84.681 35.2297 84.0386 34.5904 83.2483 34.5904H22.9843C22.1906 34.5904 21.5483 35.2294 21.5483 36.0189V94.5408C21.5483 95.163 21.9518 95.7117 22.5436 95.899C23.1389 96.093 23.7879 95.8756 24.1512 95.3704C24.2184 95.2767 31.0014 85.9762 40.2029 85.9762C49.481 85.9762 54.9194 91.2185 54.9693 91.2687C55.3157 91.6099 55.81 91.7538 56.2875 91.6534C56.7651 91.5497 57.1585 91.2152 57.33 90.7601C57.3603 90.6765 60.0237 83.9856 69.3424 81.336C69.3611 81.3297 69.3803 81.3249 69.3994 81.3201C69.4318 81.3119 69.4641 81.3038 69.4937 81.2891C71.7603 80.6602 74.4069 80.2654 77.5076 80.2654C78.2979 80.2654 78.9436 79.6264 78.9436 78.8402C78.9436 78.0507 78.2979 77.4117 77.5076 77.4117C74.8273 77.4117 72.4497 77.676 70.3345 78.1243V74.4142C73.6033 73.7485 76.0717 70.868 76.0717 67.4188C76.0717 67.2349 75.991 48.8651 68.8985 48.8651C61.8061 48.8651 61.7254 67.2347 61.7254 67.4188C61.7254 70.868 64.1938 73.7484 67.4626 74.4142V78.9038C60.4543 81.1921 56.9732 85.5679 55.5174 88.0137C53.1062 86.2205 47.8364 83.1227 40.2026 83.1227C33.1472 83.1227 27.511 87.5721 24.4169 90.7068V37.4443H81.8123V82.1569C82.683 81.8827 83.5937 81.6977 84.5336 81.6127C84.5723 81.2796 84.6214 80.9474 84.681 80.6168ZM73.1334 97.3948C72.9169 96.4775 72.7988 95.5224 72.7908 94.5411H28.7215C27.9312 94.5411 27.2888 95.1801 27.2888 95.9696C27.2888 96.7558 27.9311 97.3948 28.7215 97.3948H73.1334ZM87.5529 73.9565V36.0189C87.5529 33.657 85.6192 31.7367 83.2483 31.7367H22.9843C20.61 31.7367 18.6797 33.657 18.6797 36.0189V95.9693C18.6797 98.3278 20.61 100.251 22.9843 100.251H74.1807C74.7101 101.292 75.3772 102.251 76.158 103.105H22.9843C19.0261 103.105 15.8078 99.9035 15.8078 95.9692V36.0188C15.8078 32.0813 19.026 28.8797 22.9843 28.8797H83.2483C87.2031 28.8797 90.4214 32.0812 90.4214 36.0188V70.9617C89.9869 71.3126 89.5665 71.6893 89.1622 72.0919C88.5703 72.6813 88.0339 73.305 87.5529 73.9565ZM68.8986 51.7191C66.5143 51.7191 64.594 60.3101 64.594 67.4192C64.594 69.7811 66.5243 71.7014 68.8986 71.7014C71.2728 71.7014 73.2031 69.7811 73.2031 67.4192C73.2031 60.31 71.2829 51.7191 68.8986 51.7191Z"
        fill="#1F2937"
        fillRule="evenodd"
      />
      <circle cx="40.0935" cy="53.2229" fill="#FCD34D" r="4.99193" />
      <path
        d="M99.3849 72.7294H99.5042C102.724 72.7592 105.92 73.9961 108.376 76.4528C111.251 79.328 112.471 83.2425 112.022 86.9907L111.878 88.1879H113.084H113.739C118.348 88.1879 122.055 91.895 122.055 96.5039C122.055 101.113 118.348 104.82 113.739 104.82H86.6865C81.1783 104.82 76.7142 100.356 76.7142 94.8476C76.7142 89.6478 80.6865 85.3843 85.7652 84.9228L86.6311 84.8441L86.731 83.9805C87.0493 81.2294 88.2639 78.5652 90.3757 76.4533C92.8619 73.9673 96.1228 72.7294 99.3849 72.7294ZM99.377 82.1841L99.3692 82.1842L99.3615 82.1844C98.8246 82.1961 98.2832 82.4094 97.9221 82.7396C97.9213 82.7403 97.9205 82.741 97.9197 82.7418L91.8572 88.2531C90.9449 89.0583 90.9173 90.4843 91.6906 91.3387C92.4872 92.2189 93.9166 92.2606 94.7735 91.4812L94.7738 91.481L97.211 89.2626V98.7125C97.211 99.9137 98.1838 100.886 99.3849 100.886C100.586 100.886 101.559 99.9137 101.559 98.7125V89.2626L103.996 91.481L103.996 91.4813C104.888 92.2922 106.268 92.1515 107.043 91.3773L107.043 91.3772C107.964 90.4555 107.732 88.9933 106.918 88.258C106.917 88.2572 106.916 88.2565 106.915 88.2557L100.847 82.7392C100.847 82.7389 100.847 82.7386 100.846 82.7383C100.644 82.5541 100.411 82.3975 100.128 82.2983C99.8512 82.2011 99.5917 82.1825 99.377 82.1841Z"
        fill="#0D9488"
        stroke="#1F2937"
        stroke-width="2.1394"
      />
    </svg>
  </div>
);

const NewsIcon = () => (
  <div className="flex items-start justify-start md:mt-6 md:justify-center">
    <svg
      className="w-2/3 md:w-full"
      fill="none"
      height="123"
      viewBox="0 0 124 123"
      width="124"
    >
      <path
        d="M61.1072 29.5062L68.9299 49.3866L89.7278 50.3175C90.2881 50.3401 90.7717 50.7107 90.9389 51.244C91.1107 51.7818 90.9254 52.3602 90.478 52.7037L74.0232 66.2569C74.0232 66.2569 77.9251 76.9431 77.4114 77.5502C76.8977 78.1573 75.8703 79.8151 75.8703 79.8151C75.8703 79.8151 75.9476 79.1752 74.9597 82.1501C73.9717 85.125 73.7391 85.2576 73.7391 85.2576L59.5623 76.3216L42.1854 87.7959C41.5708 88.1981 40.7438 88.0309 40.337 87.4163C40.1201 87.0864 40.0613 86.6706 40.1743 86.291L45.516 65.6748L29.2382 52.6908C28.7999 52.3473 28.6191 51.7643 28.7954 51.2356C28.9671 50.7023 29.4552 50.3363 30.011 50.3182L51.3052 49.0167L58.6218 29.5296L58.6263 29.5341C58.8161 29.0189 59.3087 28.6755 59.86 28.6709C60.4114 28.6664 60.904 29.0008 61.1073 29.516L61.1072 29.5062Z"
        fill="#FCD34D"
      />
      <path
        clipRule="evenodd"
        d="M69.8238 49.3134L61.1831 27.3542L61.1833 27.3651C60.9587 26.796 60.4146 26.4266 59.8056 26.4316C59.1966 26.4366 58.6525 26.816 58.4428 27.385L58.4378 27.38L50.3562 48.9048L26.8354 50.3424C26.2214 50.3624 25.6823 50.7667 25.4926 51.3557C25.2979 51.9398 25.4976 52.5837 25.9818 52.9631L43.9617 67.3048L38.0614 90.0768C37.9366 90.4961 38.0015 90.9553 38.2411 91.3197C38.6904 91.9986 39.6039 92.1833 40.2828 91.739L59.4767 79.0649L71.6043 86.7952C72.3621 86.5944 73.1449 86.4548 73.9472 86.3819C74.0024 85.9041 74.0772 85.4282 74.1713 84.9554L60.243 76.0783C59.7538 75.7638 59.1249 75.7688 58.6457 76.0933V76.0833L41.844 87.175L47.0354 67.1083C47.1852 66.5492 46.9855 65.9552 46.5362 65.5908L30.7978 53.0466L51.4842 51.7837C52.0983 51.7487 52.6224 51.3344 52.8071 50.7504L59.8454 31.9859L67.4379 51.2692L67.4366 51.2643C67.6563 51.8284 68.2004 52.2027 68.8094 52.2027V52.2077L88.8518 53.1062L72.8526 66.2889C72.4033 66.6533 72.2137 67.2523 72.3684 67.8114L75.8102 80.2834C76.3982 79.1501 77.1169 78.0671 77.9663 77.0564L75.4497 67.9478L93.6251 52.9773C94.1193 52.598 94.3239 51.959 94.1342 51.365C93.9495 50.776 93.4154 50.3667 92.7964 50.3417L69.8238 49.3134ZM37.6126 24.414C38.3165 24.0097 38.5561 23.1112 38.1518 22.4073L34.9919 16.9313C34.5826 16.2275 33.6841 15.9879 32.9803 16.3922C32.2764 16.8015 32.0368 17.7 32.4462 18.4039H32.4412L35.601 23.8799C36.0103 24.5837 36.9088 24.8233 37.6126 24.414ZM21.3402 40.6475C21.7345 39.9586 21.5049 39.0751 20.8211 38.6608L15.345 35.496C14.6462 35.1016 13.7527 35.3462 13.3533 36.0451C12.954 36.744 13.1886 37.6374 13.8825 38.0418H13.8775L19.3634 41.2066C20.0673 41.591 20.9409 41.3414 21.3402 40.6475ZM15.4199 62.8657C15.4199 62.052 14.7611 61.3931 13.9474 61.3931H7.6229C6.8093 61.3931 6.15039 62.052 6.15039 62.8657C6.15039 63.6793 6.80925 64.3382 7.6229 64.3382H13.9474C14.761 64.3382 15.4199 63.6793 15.4199 62.8657ZM21.3652 85.0684C20.9609 84.3646 20.0623 84.12 19.3585 84.5293L13.8825 87.6891C13.1787 88.0984 12.9391 88.9969 13.3434 89.7008C13.7527 90.4046 14.6512 90.6442 15.3551 90.2349V90.2399L20.8311 87.0801C21.5349 86.6708 21.7745 85.7722 21.3652 85.0684ZM37.6134 101.321C36.9095 100.917 36.0111 101.161 35.6017 101.86L32.4469 107.341C32.0525 108.045 32.2971 108.933 32.996 109.333C33.6948 109.737 34.5833 109.497 34.9927 108.804L34.9977 108.809L38.1525 103.323C38.3472 102.983 38.3971 102.584 38.2973 102.209C38.1974 101.835 37.9478 101.511 37.6134 101.321ZM58.3391 108.734H58.3341L58.3392 115.053C58.3392 115.867 58.9981 116.526 59.8117 116.526C60.6253 116.526 61.2842 115.867 61.2842 115.053V108.734C61.3242 108.319 61.1844 107.905 60.9049 107.596C60.6253 107.286 60.226 107.111 59.8117 107.111C59.3923 107.111 58.993 107.286 58.7135 107.596C58.4339 107.905 58.2991 108.319 58.3391 108.734ZM104.212 62.8694C104.212 63.683 104.871 64.3419 105.685 64.3419H112.009C112.823 64.3419 113.482 63.683 113.482 62.8694C113.482 62.0558 112.823 61.3968 112.009 61.3968H105.685C104.871 61.3968 104.212 62.0557 104.212 62.8694ZM98.2671 40.6716C98.6714 41.3754 99.5699 41.615 100.274 41.2057L105.75 38.0409C106.454 37.6366 106.693 36.7381 106.289 36.0342C105.88 35.3304 104.981 35.0908 104.277 35.4951L98.8012 38.6599C98.0973 39.0692 97.8577 39.9678 98.2671 40.6716ZM82.0335 24.389C82.7225 24.7834 83.606 24.5538 84.0203 23.8699L87.1851 18.3938C87.5844 17.69 87.3398 16.7965 86.641 16.3921C86.3015 16.2025 85.9022 16.1476 85.5228 16.2524C85.1485 16.3522 84.829 16.6018 84.6393 16.9363L81.4745 22.4123C81.0901 23.1161 81.3397 23.9897 82.0335 24.389ZM60.9187 18.1344C61.1982 17.8249 61.333 17.4106 61.293 16.9963L61.2929 10.6717C61.2929 9.85812 60.6341 9.19922 59.8204 9.19922C59.0068 9.19922 58.3479 9.85807 58.3479 10.6717V16.9963C58.308 17.4106 58.4477 17.8249 58.7273 18.1344C59.0068 18.4389 59.4061 18.6186 59.8205 18.6186C60.2398 18.6186 60.6391 18.4389 60.9187 18.1344Z"
        fill="#1F2937"
        fillRule="evenodd"
      />
      <path
        d="M91.5482 75.442H91.6765C95.1381 75.4741 98.5731 76.8037 101.214 79.4446C104.305 82.5356 105.616 86.7436 105.133 90.7729L104.978 92.0599H106.274H106.979C111.934 92.0599 115.919 96.045 115.919 101C115.919 105.954 111.934 109.939 106.979 109.939H77.8975C71.9761 109.939 67.1773 105.14 67.1773 99.219C67.1773 93.6293 71.4474 89.046 76.9071 88.5499L77.8379 88.4653L77.9453 87.5369C78.2875 84.5796 79.5931 81.7155 81.8634 79.4452C84.536 76.7727 88.0415 75.442 91.5482 75.442ZM91.5398 85.6058L91.5314 85.6059L91.523 85.6061C90.946 85.6187 90.3642 85.8479 89.976 86.2027C89.975 86.2036 89.9741 86.2044 89.9732 86.2053L83.456 92.13C82.4753 92.9955 82.4456 94.5285 83.2768 95.447C84.1332 96.3932 85.6699 96.438 86.591 95.6002L86.5913 95.5999L89.2113 93.2151V103.374C89.2113 104.665 90.257 105.711 91.5482 105.711C92.8394 105.711 93.8852 104.665 93.8852 103.374V93.2151L96.5052 95.5999L96.5055 95.6002C97.4641 96.472 98.9478 96.3208 99.7802 95.4885L99.7802 95.4884C100.771 94.4976 100.522 92.9257 99.6462 92.1352C99.6454 92.1344 99.6445 92.1336 99.6436 92.1328L93.1204 86.2026C93.1202 86.2024 93.12 86.2022 93.1198 86.2021C92.9022 86.0038 92.6517 85.8353 92.3476 85.7286C92.0496 85.624 91.7706 85.6041 91.5398 85.6058Z"
        fill="#0D9488"
        stroke="#1F2937"
        stroke-width="2.29985"
      />
    </svg>
  </div>
);

export default function CommunityContribute() {
  const page = useAppSelector((state) => state.page);
  const { parent: parentPage } = usePageParents();
  const pages = usePages();
  const ugcFeature = useAppSelector((state) => state.features.ugc);
  const showShareStory = ugcFeature.enabled && ugcFeature.data?.showShareStory;
  const ctaShareStory = ugcFeature.enabled && ugcFeature.data?.ctaShareStory;
  const hasDirectory = useAppSelector(
    (state) =>
      state.features.ownLocal.enabled &&
      state.features.ownLocal.data.hasDirectory,
  );

  return (
    <TemplateWrapper showNavigationAd showRevBanner showStickyFooterAd>
      <Container className="mt-4 md:mt-10" noGutter>
        <div className="mx-4 overflow-y-visible md:mx-6 xl:mx-0">
          {parentPage?.name && parentPage?.url && (
            <PageParentHeading
              className="w-full"
              show={page.showHeading}
              text={parentPage.name}
              url={pageUrlPath(parentPage.url)}
            />
          )}
          <PageHeading
            className="mb-4 grow md:mb-5"
            show={page.showHeading}
            text={page.name}
          />
          <PageNavigation
            currentAsHeading
            desktopMoreOptionEnabled
            fontStyle="text-sm font-medium normal-case"
            mobileMoreOptionEnabled={false}
            moreOptionLabel="More"
            navClassName=""
            navWrapperClassName="border-gray-300 md:block"
            navigationType={NavigationType.Pill}
            pages={pages}
            shortcutPages={[
              tributesFuneralsPage,
              classifiedsPage,
              hasDirectory && localBusinessPage,
            ].filter((p) => !!p)}
            theme={COMMUNITY_NAV_THEME}
          />
          <div className="mt-4 text-sm text-gray-500">
            Share your news, events and photos—be a part of our community
            noticeboard. Content may appear on the website and/or in the
            newspaper. All content is moderated.
          </div>
        </div>

        <div className="m-4 overflow-y-visible md:mx-6 xl:mx-0">
          <div className="mt-4 grid grid-cols-2 gap-4 md:grid-cols-3">
            {/* eslint-disable @stylistic/max-len */}
            <ContributeCard
              buttonText="Add your event"
              description={
                <>
                  Promote your event to the region.{' '}
                  <span className="font-bold">It&apos;s free</span>
                </>
              }
              href="/notice-board/share-event/"
              icon={<EventIcon />}
              title={
                <>
                  <div className="hidden md:block">
                    Share your events with our community
                  </div>
                  <div className="block md:hidden">
                    Share your events with us
                  </div>
                </>
              }
            />
            <ContributeCard
              buttonText="Send your pics"
              description={
                <>
                  Showcase your best pics to our community.{' '}
                  <span className="font-bold">It&apos;s free</span>
                </>
              }
              href="/notice-board/share-photos/"
              icon={<PictureIcon />}
              title={
                <>
                  <div className="hidden md:block">
                    Share photos with our community
                  </div>
                  <div className="block md:hidden">Share your photos</div>
                </>
              }
            />
            {showShareStory && (
              <ContributeCard
                buttonText="Send your news"
                description={
                  <>
                    Tell all the stories that matter to the community.{' '}
                    <span className="font-bold">It&apos;s free</span>
                  </>
                }
                href={ctaShareStory || '/notice-board/send-your-news/'}
                icon={<NewsIcon />}
                title="Got a story to tell?"
              />
            )}

            {/* eslint-enable @stylistic/max-len */}
          </div>
        </div>
        <div className="mx-4 mb-4 grid gap-4 @container md:mx-6 md:grid-cols-2 xl:mx-0">
          <TradeBox />
          <ContactUs />
        </div>
        <Ad
          mdSizes={[]}
          position={1}
          publiftName="mrec-1"
          sizes={AdSize.mrec}
          slotId="mrec-1"
        />
      </Container>
    </TemplateWrapper>
  );
}
