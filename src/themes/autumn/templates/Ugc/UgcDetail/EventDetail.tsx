import {
  faEnvelope,
  faGlobeOceania,
  faMapMarkedAlt,
  faPhone,
  faShare,
  faUserCircle,
} from '@fortawesome/free-solid-svg-icons';
import React from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { useAppSelector } from 'store/hooks';
import AddToCalendarButton from 'themes/autumn/components/generic/AddToCalendarButton';
import Link from 'themes/autumn/components/generic/Link';
import ShareButton from 'themes/autumn/components/generic/ShareButton';
import RecirculationSections from 'themes/autumn/components/recirculation/RecirculationSections';
import {
  formattedDateRange,
  getDisplayEndDate,
  getDisplayStartDate,
  processRecurrenceText,
} from 'util/ugc';

import ContactDetails from './ContactDetails';
import Description from './Description';
import UgcGallery, { UgcGalleryPreview } from './UgcGallery';

import type { Details } from './ContactDetails';
import type { UGC } from 'types/ugc';

interface Props {
  isPreview?: boolean;
  ugc: UGC;
}

export default function EventDetails({
  isPreview = false,
  ugc,
}: Props): React.ReactElement {
  const recirculationSections = useAppSelector(
    (state) => state.ugc.recirculationSections,
  );

  let contactDetails = [] as Details;

  if (ugc.organiserDetails) {
    contactDetails = [
      [faUserCircle, ugc.organiserDetails.name],
      [
        faMapMarkedAlt,
        ugc.location,
        ugc.lat
          ? (loc) =>
              `https://www.google.com.au/maps/?q=${encodeURIComponent(loc)}`
          : undefined,
      ],
      [faGlobeOceania, ugc.organiserDetails.websiteUrl, (url) => url],
      [faEnvelope, ugc.organiserDetails.email, (email) => `mailto:${email}`],
      [faPhone, ugc.organiserDetails.contactNumber, (phone) => `tel:${phone}`],
    ];
  }

  const startDate = getDisplayStartDate(ugc);
  const endDate = getDisplayEndDate(ugc);

  const fullStartDate = new Date(ugc.startDatetime);
  const addToCalendarDate = new Date(
    fullStartDate.getFullYear(),
    fullStartDate.getMonth(),
    fullStartDate.getDate(),
  );

  let locationUrl = null;
  if (ugc.location?.match(/^https?:/)) {
    locationUrl = ugc.location;
  } else if (ugc.lat && ugc.lng) {
    // eslint-disable-next-line @stylistic/max-len
    locationUrl = `https://www.google.com.au/maps/?q=${encodeURIComponent(ugc.location)}`;
  }

  return (
    <div className="space-y-8">
      {isPreview ? (
        <UgcGalleryPreview previewImages={ugc.images} />
      ) : (
        <UgcGallery ugc={ugc} />
      )}
      <div className="mt-10 items-center border-b pb-4 text-gray-900">
        <div className="mb-2 inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800 md:mb-3">
          {ugc.categoryName}
        </div>
        <div className="w-3/4 text-2xl font-semibold md:text-3xl">
          {ugc.title}
        </div>
        <div className="relative mt-6 flex flex-col gap-x-10 break-words font-medium md:flex-row">
          <div className="mb-4 min-w-64">
            <div className="mb-3 flex items-center text-lg">
              <svg
                className="mr-2 shrink-0"
                fill="none"
                height="23"
                viewBox="0 0 26 25"
                width="23"
              >
                <path
                  d="m7.8889 6.1111v-5.1111m10.222 5.1111v-5.1111m-11.5 10.222h12.778m-15.333 12.778h17.889c1.4114 0 2.5556-1.1442 2.5556-2.5556v-15.333c0-1.4114-1.1442-2.5556-2.5556-2.5556h-17.889c-1.4114 0-2.5556 1.1442-2.5556 2.5556v15.333c0 1.4114 1.1442 2.5556 2.5556 2.5556z"
                  stroke="#111827"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                />
              </svg>
              When
            </div>
            <div className="flex flex-col font-medium">
              <div className="flex flex-col">
                <div>
                  {formattedDateRange(startDate, endDate)} {ugc.startTimeText}
                </div>
                <AddToCalendarButton
                  className="text-start font-medium text-gray-500 underline underline-offset-4"
                  date={addToCalendarDate}
                  location={ugc.location}
                  name={ugc.title}
                >
                  Add to calendar
                </AddToCalendarButton>
              </div>
              {ugc.recurrenceText && (
                <div className="mr-2 text-sm font-normal">
                  {isPreview
                    ? ugc.recurrenceText
                    : processRecurrenceText(
                        ugc.recurrenceText,
                        ugc.endDatetime,
                      )}
                </div>
              )}
            </div>
          </div>

          <div className="mb-4 min-w-64">
            <div className="mb-3 flex items-center text-lg">
              <svg
                className="mr-2 shrink-0"
                fill="none"
                height="23"
                viewBox="0 0 20 25"
                width="23"
              >
                <path
                  d="m9.7727 0.5c5.1212 0 9.2728 4.1515 9.2728 9.2727 0 3.257-1.5031 6.5505-4.0159 9.7643-0.8918 1.1406-1.8617 2.2041-2.8554 3.1728-0.0819 0.0798-0.1624 0.1575-0.2414 0.233l-0.4559 0.4277-0.4162 0.3751-0.3718 0.321-0.227 0.1881c-0.3984 0.3247-0.96935 0.3273-1.3707 0.0062l-0.23737-0.1964-0.37005-0.3197-0.41544-0.3745-0.45574-0.4275-0.24139-0.233c-0.99365-0.9687-1.9636-2.0322-2.8554-3.1728-2.5128-3.2138-4.0159-6.5073-4.0159-9.7643 0-5.1212 4.1515-9.2727 9.2727-9.2727zm0 2.1818c-3.9162 0-7.0909 3.1747-7.0909 7.0909 0 2.6795 1.3121 5.5546 3.5528 8.4204 0.82728 1.058 1.7322 2.0503 2.6596 2.9544l0.43933 0.4203c0.14475 0.1357 0.2897 0.2717 0.43914 0.4024l0.43907-0.4024 0.4394-0.4203c0.9274-0.9041 1.8323-1.8964 2.6596-2.9544 2.2407-2.8658 3.5528-5.7409 3.5528-8.4204 0-3.9162-3.1747-7.0909-7.0909-7.0909zm0 2.7273c2.41 0 4.3637 1.9537 4.3637 4.3636s-1.9537 4.3637-4.3637 4.3637-4.3636-1.9537-4.3636-4.3637 1.9537-4.3636 4.3636-4.3636zm0 2.1818c-1.205 0-2.1818 0.97684-2.1818 2.1818 0 1.205 0.97684 2.1818 2.1818 2.1818 1.205 0 2.1818-0.9768 2.1818-2.1818 0-1.205-0.9768-2.1818-2.1818-2.1818z"
                  fill="#111827"
                />
              </svg>
              Where
            </div>
            {ugc.location}
            <br />
            {locationUrl && (
              <Link
                className="font-medium text-gray-500 underline underline-offset-4"
                href={locationUrl}
                noStyle
                target="_blank"
              >
                Get Directions
              </Link>
            )}
          </div>

          {ugc.priceText && (
            <div className="mb-4 min-w-40">
              <div className="mb-3 flex items-center text-lg">
                <svg
                  className="mr-2 shrink-0"
                  height="23"
                  viewBox="0 0 25 24"
                  width="23"
                >
                  <path d="m12.5 0c-6.6187 0-12 5.3813-12 12 0 6.6187 5.3813 12 12 12 6.6187 0 12-5.3813 12-12 0-6.6187-5.3813-12-12-12zm0 21.626c-5.3053 0-9.6258-4.3205-9.6258-9.6258 0-5.3053 4.3205-9.6258 9.6258-9.6258 5.3053 0 9.6258 4.3205 9.6258 9.6258 0 5.3053-4.3205 9.6258-9.6258 9.6258z" />
                  <path d="m13.032 11.04h-1.0362c-0.8082 0-1.4398-0.7076-1.3638-1.5158 0.076-0.70757 0.7322-1.2375 1.4398-1.2375h1.2128c0.4549 0 0.8339 0.32862 0.9345 0.75789 0.0257 0.12632 0.1264 0.2023 0.2527 0.2023h1.3894c0.152 0 0.2783-0.12631 0.2783-0.2783-0.1519-1.3895-1.2631-2.476-2.6784-2.5766v-1.3638c0-0.15197-0.1263-0.2783-0.2783-0.2783h-1.3638c-0.1519 0-0.2783 0.12632-0.2783 0.2783v1.3895c-1.7181 0.22698-3.0058 1.7684-2.8292 3.5871 0.17664 1.7181 1.6924 2.9555 3.4105 2.9555h0.8843c0.8082 0 1.4398 0.7076 1.3638 1.5158-0.076 0.7076-0.7323 1.2375-1.4398 1.2375h-1.2129c-0.4549 0-0.8339-0.3286-0.9345-0.7579-0.0257-0.1263-0.1263-0.2023-0.2526-0.2023h-1.3895c-0.15197 0-0.30295 0.1263-0.2783 0.2783 0.15198 1.3895 1.2631 2.476 2.6784 2.5766v1.3638c0 0.152 0.1263 0.2783 0.2783 0.2783h1.3638c0.152 0 0.2783-0.1263 0.2783-0.2783v-1.4151c1.4655-0.2527 2.6784-1.3895 2.8292-2.9053 0.2023-1.9441-1.3392-3.6117-3.2585-3.6117z" />
                </svg>
                Cost
              </div>
              {ugc.priceText}
            </div>
          )}
        </div>
      </div>
      <Description text={ugc.description} />
      {ugc.organiserDetails && (
        <ContactDetails
          details={contactDetails}
          heading="Details"
          isPreview={isPreview}
          logo={ugc.organiserDetails.logo}
        />
      )}
      <div className="w-28">
        <ShareButton>
          <div className="rounded-md border px-4 py-2 shadow-sm transition-all duration-200 ease-out hover:border-gray-300">
            <FontAwesomeIcon className="mr-2" icon={faShare} />
            <span className="font-medium text-gray-800">Share</span>
          </div>
        </ShareButton>
      </div>
      <RecirculationSections sections={recirculationSections} />
    </div>
  );
}
