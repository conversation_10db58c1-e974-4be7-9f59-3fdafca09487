import { faMapMarkerAlt, faShare } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import React from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { useAppSelector } from 'store/hooks';
import ShareButton from 'themes/autumn/components/generic/ShareButton';
import RecirculationSections from 'themes/autumn/components/recirculation/RecirculationSections';
import Comments from 'themes/autumn/components/stories/Comments';
import StorySocials from 'themes/autumn/components/stories/StorySocials';
import { StoryCommentsState } from 'types/Story';
import { UgcContentType } from 'types/ugc';
import { usePageHierarchy } from 'util/hooks';
import {
  ImageResizeMode,
  TransformOutputFormat,
  storyImageUrl,
} from 'util/image';
import { formattedDateParts } from 'util/ugc';

import Description from './Description';
import UgcGallery from './UgcGallery';

import type { UGC } from 'types/ugc';

interface Props {
  ugc: UGC;
}

export default function PhotosOrStoryDetail({
  ugc,
}: Props): React.ReactElement | null {
  const pageName = usePageHierarchy().secondaryPage?.name;
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);
  const recirculationSections = useAppSelector(
    (state) => state.ugc.recirculationSections,
  );
  const { contentType } = ugc;

  if (!ugc) {
    return null;
  }

  return (
    <div className="space-y-8">
      <div className="space-y-2.5">
        <div className="flex gap-2">
          <span className="rounded-3xl bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
            {pageName}
          </span>
          {contentType === UgcContentType.STORY && (
            <span className="rounded-3xl bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
              {ugc.categoryName}
            </span>
          )}
        </div>
        <h1 className="text-2xl font-semibold leading-7 md:text-3xl">
          {ugc.title}
        </h1>
        {contentType === UgcContentType.PHOTOS && ugc.location && (
          <div className="flex items-center text-sm text-gray-600">
            <FontAwesomeIcon
              className="mr-2 text-green-600"
              icon={faMapMarkerAlt}
            />
            {ugc.location}
          </div>
        )}
      </div>

      <UgcGallery ugc={ugc} />

      <div className="flex flex-col space-y-4 text-sm text-gray-500 md:flex-row md:items-center md:space-y-0">
        <div className="flex items-center">
          {ugc.userDetails.avatar && (
            <img
              alt="user avatar"
              className="mr-5 size-12 rounded-full object-cover"
              src={storyImageUrl({
                fit: ImageResizeMode.MAX,
                height: 160,
                image: { uri: ugc.userDetails.avatar },
                outputFormat: TransformOutputFormat.WEBP,
                transformUrl,
                width: 160,
              })}
            />
          )}
          <div>
            <div className="relative -top-1 align-top">
              <span>By </span>
              <span className="max-w-96 text-gray-900">
                {ugc.userDetails.userName}
              </span>
            </div>
            {ugc.publishedOn && (
              <div>
                Published {formattedDateParts(ugc.publishedOn).fullDate}
              </div>
            )}
          </div>
        </div>
        {contentType === UgcContentType.STORY && (
          <>
            <div className="mx-6 hidden h-12 border-l border-gray-300 md:block" />
            <StorySocials className="flex space-x-1" url={ugc.canonicalUrl} />
          </>
        )}
      </div>

      <div
        className={clsx({
          'flex justify-center lg:justify-start':
            contentType === UgcContentType.STORY,
        })}
      >
        <Description text={ugc.description} />
      </div>

      <div className="w-28">
        <ShareButton>
          <div className="rounded-md border px-4 py-2 shadow-sm transition-all duration-200 ease-out hover:border-gray-300">
            <FontAwesomeIcon className="mr-2" icon={faShare} />
            <span className="font-medium text-gray-800">Share</span>
          </div>
        </ShareButton>
      </div>

      <Comments id={`ugc:${ugc.id}`} state={StoryCommentsState.OPEN} />

      <RecirculationSections sections={recirculationSections} />
    </div>
  );
}
