import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import ShareEvent from '.';

const store = createStore((state) => ({
  ...state,
  piano: {
    ...state.piano,
    initialized: true,
    user: {
      ...state.piano.user,
      aud: 'test-aud',
      confirmed: true,
      email: '<EMAIL>',
      email_confirmation_required: false,
      exp: 1234567890,
      family_name: '<PERSON><PERSON>',
      firstName: '<PERSON>',
      given_name: '<PERSON>',
      iat: 1234567890,
      iss: 'test-iss',
      jti: 'test-jti',
      lastName: 'Doe',
      login_timestamp: '2024-03-20T12:00:00Z',
      sub: 'test-sub',
      uid: '123',
      valid: true,
    },
  },
  ugc: {
    recirculationSections: undefined,
    ugcDetail: null,
  },
}));

describe('<ShareEvent />', () => {
  it('renders', () => {
    const { container } = render(
      <TestWrapper store={store}>
        <ShareEvent />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
