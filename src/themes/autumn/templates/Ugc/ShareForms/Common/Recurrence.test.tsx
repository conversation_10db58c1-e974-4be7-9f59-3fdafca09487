/* eslint-disable sort-keys */
import { render } from '@testing-library/react';

import { Recurrence, RecurrenceMonthlySwitcher } from './Recurrence';

describe('<RecurrenceMonthlySwitcher />', () => {
  it('renders with date option selected by default', () => {
    expect.assertions(1);

    const setValue = jest.fn();
    const testDate = new Date('2025-02-15');

    const { container } = render(
      <RecurrenceMonthlySwitcher date={testDate} setValue={setValue} />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});

describe('<Recurrence />', () => {
  it('renders with weekly frequency selected', () => {
    expect.assertions(1);

    const mockProps = {
      errors: {},
      handleDayChange: jest.fn(),
      recurrenceInterval: '1',
      register: jest.fn(),
      selectedDays: {
        monday: true,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false,
        sunday: false,
      },
      setRecurrenceInterval: jest.fn(),
      setValue: jest.fn(),
      todayString: '2025-02-15',
      watch: jest.fn().mockImplementation((field) => {
        if (field === 'recurrence_frequency') return '1'; // Weekly
        if (field === 'start_datetime') return '2025-02-15';
        return '';
      }),
    };

    const { container } = render(
      <Recurrence
        errors={mockProps.errors}
        handleDayChange={mockProps.handleDayChange}
        recurrenceInterval={mockProps.recurrenceInterval}
        register={mockProps.register}
        selectedDays={mockProps.selectedDays}
        setRecurrenceInterval={mockProps.setRecurrenceInterval}
        setValue={mockProps.setValue}
        todayString={mockProps.todayString}
        watch={mockProps.watch}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with monthly frequency selected', () => {
    expect.assertions(1);

    const mockProps = {
      errors: {},
      handleDayChange: jest.fn(),
      recurrenceInterval: '1',
      register: jest.fn(),
      selectedDays: {
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false,
        sunday: false,
      },
      setRecurrenceInterval: jest.fn(),
      setValue: jest.fn(),
      todayString: '2025-02-15',
      watch: jest.fn().mockImplementation((field) => {
        if (field === 'recurrence_frequency') return '2'; // Monthly
        if (field === 'start_datetime') return '2025-02-15';
        return '';
      }),
    };

    const { container } = render(
      <Recurrence
        errors={mockProps.errors}
        handleDayChange={mockProps.handleDayChange}
        recurrenceInterval={mockProps.recurrenceInterval}
        register={mockProps.register}
        selectedDays={mockProps.selectedDays}
        setRecurrenceInterval={mockProps.setRecurrenceInterval}
        setValue={mockProps.setValue}
        todayString={mockProps.todayString}
        watch={mockProps.watch}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
