'use client';

import clsx from 'clsx';
import React, { useEffect, useState } from 'react';

import { getOrdinalIndicator } from 'themes/autumn/components/SportsHub/utils';
import DateRangeInput from 'themes/autumn/templates/explore/SearchWidget/DateRangeInput';
import { UGCFormField, type UGCFormInputs } from 'types/ugc';
import {
  WEEKDAY_MAPPINGS,
  getWeekdayInstance,
  isLastDayOfMonth,
} from 'util/ugc';

import Dropdown from './Dropdown';
import FieldInput from './FieldInput';

import type {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';

interface RecurrenceMonthlySwitcherProps {
  date: Date;
  setValue: UseFormSetValue<UGCFormInputs>;
  weekday?: string;
}

export function RecurrenceMonthlySwitcher({
  date,
  setValue,
  weekday = '',
}: RecurrenceMonthlySwitcherProps) {
  const [isChecked, setIsChecked] = useState(!!weekday);

  const handleCheckboxChange = () => {
    setIsChecked(!isChecked);
  };

  useEffect(() => {
    if (isChecked) {
      setValue('recurrence_monthly_date', '');
      setValue(
        'recurrence_weekday_instance',
        Math.ceil(date.getDate() / 7).toString(),
      );
      setValue('recurrence_monthly_weekday', date.getDay().toString());
    } else {
      setValue('recurrence_monthly_date', date.getDate().toString());
      setValue('recurrence_weekday_instance', '');
      setValue('recurrence_monthly_weekday', '');
    }
  }, [isChecked, date, setValue]);

  return (
    <label
      className="relative inline-flex cursor-pointer select-none items-center justify-center rounded-md bg-gray-200 p-1"
      htmlFor="monthlyRecurrenceType"
    >
      <input
        checked={isChecked}
        className="sr-only"
        id="monthlyRecurrenceType"
        onChange={handleCheckboxChange}
        type="checkbox"
      />
      <span
        className={clsx(
          'flex items-center space-x-2 rounded px-4 py-2 text-sm font-medium text-gray-900',
          { 'bg-white': !isChecked },
        )}
      >
        {isLastDayOfMonth(date)
          ? 'the last day'
          : `the ${date.getDate()}${getOrdinalIndicator(date.getDate())}`}
      </span>
      <span
        className={clsx(
          'flex items-center space-x-2 rounded px-4 py-2 text-sm font-medium text-gray-900',
          { 'bg-white': isChecked },
        )}
      >
        the {getWeekdayInstance(date)}
      </span>
    </label>
  );
}

interface RecurrenceProps {
  errors: FieldErrors<UGCFormInputs>;
  handleDayChange: (dayValue: string) => void;
  recurrenceInterval: string;
  register: UseFormRegister<UGCFormInputs>;
  selectedDays: Record<string, boolean>;
  setRecurrenceInterval: (value: string) => void;
  setValue: UseFormSetValue<UGCFormInputs>;
  todayString: string;
  watch: UseFormWatch<UGCFormInputs>;
}

const recurrenceTypes = [
  { id: '1', name: 'week(s)', value: 'WEEKLY' },
  { id: '2', name: 'month(s)', value: 'MONTHLY' },
];

export function Recurrence({
  errors,
  handleDayChange,
  recurrenceInterval,
  register,
  selectedDays,
  setRecurrenceInterval,
  setValue,
  todayString,
  watch,
}: RecurrenceProps) {
  const startDatetime = watch('start_datetime');
  const startDate = React.useMemo(
    () => new Date(startDatetime),
    [startDatetime],
  );

  const isWeeklyRecurrence = watch('recurrence_frequency') === '1';

  return (
    <div className="gap-6 space-y-4 rounded-lg bg-white p-6 shadow-lg">
      <h3 className="text-lg font-medium">Custom Recurrence</h3>

      <div>
        <div className="mb-1.5 text-sm font-medium leading-tight text-slate-900">
          Select start and end date*
        </div>
        <DateRangeInput<UGCFormInputs>
          date1Name={UGCFormField.START_DATETIME}
          date2Name={UGCFormField.END_DATETIME}
          maxDurationDays={365}
          register={register}
          requireRange
          setValue={setValue}
          todayString={todayString}
          watch={watch}
        />
        {errors[UGCFormField.END_DATETIME] && (
          <p className="text-sm text-red-600">
            {errors[UGCFormField.END_DATETIME].message}
          </p>
        )}
      </div>

      <div className="flex items-center">
        <div className="mr-2 w-24 text-sm font-medium">Repeat every</div>
        <div className="flex items-center space-x-2">
          <div className="w-20">
            <FieldInput
              errors={errors}
              fieldName={UGCFormField.RECURRENCE_INTERVAL}
              fieldValue={recurrenceInterval}
              register={register}
              required
              setField={setRecurrenceInterval}
              type="number"
            />
          </div>

          <Dropdown
            errors={errors}
            fieldName="recurrence_frequency"
            items={recurrenceTypes}
            register={register}
            required
          />
        </div>
      </div>

      <div>
        {isWeeklyRecurrence ? (
          <>
            <div className="mb-2 text-sm font-medium">Repeat on</div>
            <div className="grid grid-cols-7 gap-2">
              {WEEKDAY_MAPPINGS.map((day) => (
                <div className="flex flex-col items-center" key={day.id}>
                  <input
                    checked={selectedDays[day.value]}
                    className="form-checkbox size-5 rounded-md text-gray-600"
                    onChange={() => handleDayChange(day.value)}
                    type="checkbox"
                  />
                  <span className="mt-1 text-sm">{day.shortName}</span>
                </div>
              ))}
            </div>
          </>
        ) : (
          <RecurrenceMonthlySwitcher
            date={startDate}
            setValue={setValue}
            weekday={watch('recurrence_monthly_weekday')}
          />
        )}
      </div>
    </div>
  );
}
