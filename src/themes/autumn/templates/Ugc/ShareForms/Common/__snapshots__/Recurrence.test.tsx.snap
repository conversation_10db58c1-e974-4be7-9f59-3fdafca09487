// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Recurrence /> renders with monthly frequency selected 1`] = `
<div
  class="gap-6 space-y-4 rounded-lg bg-white p-6 shadow-lg"
>
  <h3
    class="text-lg font-medium"
  >
    Custom Recurrence
  </h3>
  <div>
    <div
      class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
    >
      Select start and end date*
    </div>
    <div>
      <input
        class="h-[38px] border-r-0 border-gray-300 text-sm shadow-sm focus:border-sky-500 focus:ring-sky-500 md:text-xs lg:text-sm w-1/2 rounded-l-full lg:w-36"
        min="2025-02-15"
        type="date"
      />
      <input
        class="h-[38px] w-1/2 rounded-r-full border-gray-300 text-sm shadow-sm focus:border-sky-500 focus:ring-sky-500 md:text-xs lg:w-36 lg:text-sm"
        max="2026-02-15"
        min="2025-02-15"
        type="date"
      />
    </div>
  </div>
  <div
    class="flex items-center"
  >
    <div
      class="mr-2 w-24 text-sm font-medium"
    >
      Repeat every
    </div>
    <div
      class="flex items-center space-x-2"
    >
      <div
        class="w-20"
      >
        <div>
          <div
            class="relative"
          >
            <input
              class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
              maxlength="100"
              min="1"
              type="number"
              value="1"
            />
          </div>
        </div>
      </div>
      <div>
        <select
          class="h-10 rounded-md text-sm border-slate-300"
        >
          <option
            disabled=""
            value=""
          >
            Select option
          </option>
          <option
            value="1"
          >
            week(s)
          </option>
          <option
            value="2"
          >
            month(s)
          </option>
        </select>
      </div>
    </div>
  </div>
  <div>
    <label
      class="relative inline-flex cursor-pointer select-none items-center justify-center rounded-md bg-gray-200 p-1"
      for="monthlyRecurrenceType"
    >
      <input
        class="sr-only"
        id="monthlyRecurrenceType"
        type="checkbox"
      />
      <span
        class="flex items-center space-x-2 rounded px-4 py-2 text-sm font-medium text-gray-900 bg-white"
      >
        the 15th
      </span>
      <span
        class="flex items-center space-x-2 rounded px-4 py-2 text-sm font-medium text-gray-900"
      >
        the 
        3rd Saturday
      </span>
    </label>
  </div>
</div>
`;

exports[`<Recurrence /> renders with weekly frequency selected 1`] = `
<div
  class="gap-6 space-y-4 rounded-lg bg-white p-6 shadow-lg"
>
  <h3
    class="text-lg font-medium"
  >
    Custom Recurrence
  </h3>
  <div>
    <div
      class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
    >
      Select start and end date*
    </div>
    <div>
      <input
        class="h-[38px] border-r-0 border-gray-300 text-sm shadow-sm focus:border-sky-500 focus:ring-sky-500 md:text-xs lg:text-sm w-1/2 rounded-l-full lg:w-36"
        min="2025-02-15"
        type="date"
      />
      <input
        class="h-[38px] w-1/2 rounded-r-full border-gray-300 text-sm shadow-sm focus:border-sky-500 focus:ring-sky-500 md:text-xs lg:w-36 lg:text-sm"
        max="2026-02-15"
        min="2025-02-15"
        type="date"
      />
    </div>
  </div>
  <div
    class="flex items-center"
  >
    <div
      class="mr-2 w-24 text-sm font-medium"
    >
      Repeat every
    </div>
    <div
      class="flex items-center space-x-2"
    >
      <div
        class="w-20"
      >
        <div>
          <div
            class="relative"
          >
            <input
              class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
              maxlength="100"
              min="1"
              type="number"
              value="1"
            />
          </div>
        </div>
      </div>
      <div>
        <select
          class="h-10 rounded-md text-sm border-slate-300"
        >
          <option
            disabled=""
            value=""
          >
            Select option
          </option>
          <option
            value="1"
          >
            week(s)
          </option>
          <option
            value="2"
          >
            month(s)
          </option>
        </select>
      </div>
    </div>
  </div>
  <div>
    <div
      class="mb-2 text-sm font-medium"
    >
      Repeat on
    </div>
    <div
      class="grid grid-cols-7 gap-2"
    >
      <div
        class="flex flex-col items-center"
      >
        <input
          class="form-checkbox size-5 rounded-md text-gray-600"
          type="checkbox"
        />
        <span
          class="mt-1 text-sm"
        >
          Mon
        </span>
      </div>
      <div
        class="flex flex-col items-center"
      >
        <input
          class="form-checkbox size-5 rounded-md text-gray-600"
          type="checkbox"
        />
        <span
          class="mt-1 text-sm"
        >
          Tue
        </span>
      </div>
      <div
        class="flex flex-col items-center"
      >
        <input
          class="form-checkbox size-5 rounded-md text-gray-600"
          type="checkbox"
        />
        <span
          class="mt-1 text-sm"
        >
          Wed
        </span>
      </div>
      <div
        class="flex flex-col items-center"
      >
        <input
          class="form-checkbox size-5 rounded-md text-gray-600"
          type="checkbox"
        />
        <span
          class="mt-1 text-sm"
        >
          Thu
        </span>
      </div>
      <div
        class="flex flex-col items-center"
      >
        <input
          class="form-checkbox size-5 rounded-md text-gray-600"
          type="checkbox"
        />
        <span
          class="mt-1 text-sm"
        >
          Fri
        </span>
      </div>
      <div
        class="flex flex-col items-center"
      >
        <input
          class="form-checkbox size-5 rounded-md text-gray-600"
          type="checkbox"
        />
        <span
          class="mt-1 text-sm"
        >
          Sat
        </span>
      </div>
      <div
        class="flex flex-col items-center"
      >
        <input
          class="form-checkbox size-5 rounded-md text-gray-600"
          type="checkbox"
        />
        <span
          class="mt-1 text-sm"
        >
          Sun
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`<RecurrenceMonthlySwitcher /> renders with date option selected by default 1`] = `
<label
  class="relative inline-flex cursor-pointer select-none items-center justify-center rounded-md bg-gray-200 p-1"
  for="monthlyRecurrenceType"
>
  <input
    class="sr-only"
    id="monthlyRecurrenceType"
    type="checkbox"
  />
  <span
    class="flex items-center space-x-2 rounded px-4 py-2 text-sm font-medium text-gray-900 bg-white"
  >
    the 15th
  </span>
  <span
    class="flex items-center space-x-2 rounded px-4 py-2 text-sm font-medium text-gray-900"
  >
    the 
    3rd Saturday
  </span>
</label>
`;
