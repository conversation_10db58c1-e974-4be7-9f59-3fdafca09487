import {
  DndContext,
  type Drag<PERSON>ndEvent,
  KeyboardSensor,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { faXmark } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import { useState } from 'react';

export interface FileWithSrc {
  file: File;
  id: string;
  src: string;
}

interface ImageSorterProps {
  files: FileWithSrc[];
  onChange: (fileList: FileWithSrc[]) => void;
}

interface SortableImageProps {
  image: FileWithSrc;
  onRemove: (image: FileWithSrc) => void;
}

function SortableImage({ image, onRemove }: SortableImageProps) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: image.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <li
      className="relative aspect-square w-full cursor-move touch-manipulation"
      ref={setNodeRef}
      style={style}
      /* eslint-disable react/jsx-props-no-spreading */
      {...attributes}
      {...listeners}
      /* eslint-enable react/jsx-props-no-spreading */
    >
      <button
        aria-label="Remove"
        className="absolute right-1 top-1 m-auto aspect-square size-6 rounded-full transition-colors hover:bg-neutral-300/75"
        onClick={() => {
          onRemove(image);
        }}
        type="button"
      >
        <FontAwesomeIcon className="text-gray-700" icon={faXmark} size="lg" />
      </button>
      <img
        alt={image.file.name}
        className="aspect-square size-full object-cover"
        src={image.src}
      />
    </li>
  );
}

export default function ImageSorter({ files, onChange }: ImageSorterProps) {
  const [isDragging, setIsDragging] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const onDragEnd = (event: DragEndEvent) => {
    setIsDragging(false);

    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = files.findIndex((image) => image.id === active.id);
      const newIndex = files.findIndex((image) => image.id === over.id);

      onChange(arrayMove(files, oldIndex, newIndex));
    }
  };

  const onRemove = (image: FileWithSrc) => {
    onChange(files.toSpliced(files.indexOf(image), 1));
    URL.revokeObjectURL(image.src);
  };

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragEnd={onDragEnd}
      onDragStart={() => {
        setIsDragging(true);
      }}
      sensors={sensors}
    >
      <div
        className={clsx('my-4 w-full p-3', {
          'rounded-xl bg-neutral-200': isDragging,
        })}
      >
        <SortableContext items={files}>
          <ul className="grid touch-none grid-cols-3 gap-2">
            {files.map((file) => (
              <SortableImage
                image={file}
                key={file.file.name}
                onRemove={onRemove}
              />
            ))}
          </ul>
        </SortableContext>
      </div>
      <ul className="mt-2 list-inside list-disc text-sm text-slate-600">
        {files.map(({ file: { name } }) => (
          <li key={name}>{name}</li>
        ))}
      </ul>
    </DndContext>
  );
}
