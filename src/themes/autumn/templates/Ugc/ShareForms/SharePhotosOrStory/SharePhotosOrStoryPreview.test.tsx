import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import SharePhotosOrStoryPreview from './SharePhotosOrStoryPreview';

describe('<SharePhotosOrStoryPreview />', () => {
  it('renders', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={createStore()}>
        <SharePhotosOrStoryPreview
          avatarSrc="https://example.com/avatar.jpg"
          description="Test description"
          isOpen
          location="Sydney, NSW"
          onClose={() => {}}
          pillText="Photos"
          previewImages={['https://example.com/image1.jpg']}
          previewText="Photos Preview"
          title="Test Title"
          userName="Test User"
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('does not render preview content when not open', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={createStore()}>
        <SharePhotosOrStoryPreview
          avatarSrc="https://example.com/avatar.jpg"
          description="Test description"
          isOpen={false}
          location="Sydney, NSW"
          onClose={() => {}}
          pillText="Photos"
          previewImages={['https://example.com/image1.jpg']}
          previewText="Photos Preview"
          title="Test Title"
          userName="Test User"
        />
      </TestWrapper>,
    );

    expect(container.querySelector('.fixed')).toBeNull();
  });
});
