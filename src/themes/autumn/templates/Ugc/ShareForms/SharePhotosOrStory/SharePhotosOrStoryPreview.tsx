import { faMapMarkerAlt } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import Button from 'themes/autumn/components/generic/Button';

import Description from '../../UgcDetail/Description';
import { UgcGalleryPreview } from '../../UgcDetail/UgcGallery';

interface PreviewProps {
  avatarSrc: string;
  description: string;
  isOpen: boolean;
  location?: string;
  onClose: () => void;
  pillText: string;
  previewImages: string[];
  previewText: string;
  title: string;
  userName: string;
}

export default function SharePhotosOrStoryPreview({
  avatarSrc,
  description,
  isOpen,
  location = '',
  onClose,
  pillText,
  previewImages,
  previewText,
  title,
  userName,
}: PreviewProps): React.ReactElement | null {
  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/25">
      <div className="relative z-50 flex size-full flex-col overflow-y-auto rounded bg-white p-6 shadow-lg lg:scale-100">
        <div className="self-center md:w-3/4 lg:w-1/2">
          <div className="flex items-center justify-start">
            <h2 className="mr-6 text-2xl">{previewText}</h2>
            <Button
              bgColor="bg-black disabled:bg-gray-300"
              buttonClassName="shadow-md w-28"
              className="mt-0"
              fontSize="text-sm"
              height="h-10.5"
              hoverColor="hover:bg-gray-300 disabled:hover:bg-gray-300"
              onClick={() => onClose()}
              text="Back to Edit"
              textColor="text-white"
              type="button"
            />
          </div>
          <div className="my-6 border-b-1 border-gray-300" />
          <div className="mt-4 space-y-8">
            <div className="space-y-2.5">
              <div className="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
                {pillText}
              </div>
              <h1 className="text-2xl font-semibold leading-7 md:text-3xl">
                {title}
              </h1>
              {location && (
                <div className="flex items-center text-sm text-gray-600">
                  <FontAwesomeIcon
                    className="mr-2 text-green-600"
                    icon={faMapMarkerAlt}
                  />
                  {location}
                </div>
              )}
            </div>
            {previewImages?.length > 0 && (
              <UgcGalleryPreview previewImages={previewImages} />
            )}
            <div className="flex items-center text-sm text-gray-500">
              {avatarSrc && (
                <img
                  alt="user avatar"
                  className="mr-5 size-12 rounded-full object-cover"
                  src={avatarSrc}
                />
              )}
              <div>
                <div>
                  By <span className="text-gray-900">{userName}</span>
                </div>
              </div>
            </div>
            <Description text={description} />
          </div>
        </div>
      </div>
    </div>
  );
}
