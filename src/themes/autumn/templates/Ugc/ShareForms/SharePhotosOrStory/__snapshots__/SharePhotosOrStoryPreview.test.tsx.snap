// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SharePhotosOrStoryPreview /> renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black/25"
  >
    <div
      class="relative z-50 flex size-full flex-col overflow-y-auto rounded bg-white p-6 shadow-lg lg:scale-100"
    >
      <div
        class="self-center md:w-3/4 lg:w-1/2"
      >
        <div
          class="flex items-center justify-start"
        >
          <h2
            class="mr-6 text-2xl"
          >
            Photos Preview
          </h2>
          <div
            class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end mt-0"
          >
            <button
              class="flex items-center justify-center rounded-md bg-black disabled:bg-gray-300 h-10.5 hover:bg-gray-300 disabled:hover:bg-gray-300 shadow-md w-28 md:w-auto"
              type="button"
            >
              <span
                class="py-2 font-medium leading-6 text-sm px-4 text-white"
              >
                Back to Edit
              </span>
            </button>
          </div>
        </div>
        <div
          class="my-6 border-b-1 border-gray-300"
        />
        <div
          class="mt-4 space-y-8"
        >
          <div
            class="space-y-2.5"
          >
            <div
              class="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800"
            >
              Photos
            </div>
            <h1
              class="text-2xl font-semibold leading-7 md:text-3xl"
            >
              Test Title
            </h1>
            <div
              class="flex items-center text-sm text-gray-600"
            >
              <svg
                aria-hidden="true"
                class="svg-inline--fa fa-location-dot mr-2 text-green-600"
                data-icon="location-dot"
                data-prefix="fas"
                focusable="false"
                role="img"
                viewBox="0 0 384 512"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M215.7 499.2C267 435 384 279.4 384 192C384 86 298 0 192 0S0 86 0 192c0 87.4 117 243 168.3 307.2c12.3 15.3 35.1 15.3 47.4 0zM192 128a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"
                  fill="currentColor"
                />
              </svg>
              Sydney, NSW
            </div>
          </div>
          <div
            class="flex w-full flex-col gap-x-2 md:flex-row"
          >
            <button
              class="relative flex-1 focus-visible:outline-none"
              type="button"
            >
              <img
                alt="preview"
                class="mx-auto max-h-[240px] w-full rounded-md bg-gray-300 object-contain md:max-h-[460px]"
                src="https://example.com/image1.jpg"
              />
              <div
                class="absolute bottom-4 right-4 flex select-none flex-row items-center justify-center rounded bg-gray-100 px-2.5 py-2 font-inter text-xs font-medium text-gray-800"
              >
                <svg
                  class="fill-current text-gray-500"
                  fill="none"
                  height="14"
                  viewBox="0 0 16 14"
                  width="16"
                >
                  <path
                    clip-rule="evenodd"
                    d="M2 0C0.895431 0 0 0.89543 0 2V12C0 13.1046 0.895431 14 2 14H14C15.1046 14 16 13.1046 16 12V2C16 0.895431 15.1046 0 14 0H2ZM14 12H2L6 4L9 10L11 6L14 12Z"
                    fill-rule="evenodd"
                  />
                </svg>
                <span
                  class="ml-2.5"
                >
                  View Photo
                </span>
              </div>
            </button>
          </div>
          <div
            class="flex items-center text-sm text-gray-500"
          >
            <img
              alt="user avatar"
              class="mr-5 size-12 rounded-full object-cover"
              src="https://example.com/avatar.jpg"
            />
            <div>
              <div>
                By 
                <span
                  class="text-gray-900"
                >
                  Test User
                </span>
              </div>
            </div>
          </div>
          <p
            class="whitespace-pre-wrap"
          >
            Test description
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;
