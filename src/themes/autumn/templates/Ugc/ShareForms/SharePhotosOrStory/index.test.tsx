import { render } from '@testing-library/react';

import { CompleteProfileEnrichment } from 'store/slices/features';
import { createStore } from 'store/store';
import * as authUtils from 'util/auth';
import { TestWrapper } from 'util/jest';
import * as ugcUtils from 'util/ugc';

import SharePhotosOrStory from './index';

jest.mock('util/ugc');
jest.mock('util/page', () => ({
  pageUrlPath: jest.fn((path: string): string => path),
}));

jest.mock('util/auth', () => ({
  redirectToRegister: jest.fn(),
}));

const mockCategories = [
  { id: '1', name: 'Category 1' },
  { id: '2', name: 'Category 2' },
];

describe('<SharePhotosOrStory />', () => {
  beforeEach(() => {
    (ugcUtils.fetchCategories as jest.Mock).mockResolvedValue(mockCategories);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders with photos form type', () => {
    const store = createStore((state) => ({
      ...state,
      user: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <SharePhotosOrStory formType="photos" />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with story form type', () => {
    const store = createStore((state) => ({
      ...state,
      user: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <SharePhotosOrStory formType="story" />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('does not render form when user is not logged in', () => {
    const store = createStore();

    const { container } = render(
      <TestWrapper store={store}>
        <SharePhotosOrStory formType="photos" />
      </TestWrapper>,
    );

    expect(container.querySelector('form')).toBeNull();
  });

  it('renders banned screen for banned users', () => {
    const store = createStore((state) => ({
      ...state,
      user: {
        email: '<EMAIL>',
        firstName: 'Test',
        isBanned: true,
        lastName: 'User',
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <SharePhotosOrStory formType="photos" />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('redirects to register when user is not logged in', () => {
    Object.defineProperty(window, 'location', {
      value: { href: 'http://localhost/' },
      writable: true,
    });

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.NONE,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: true,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: true,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: true,
            supportLoginFacebook: false,
            supportLoginGoogle: true,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
      piano: {
        ...state.piano,
        initialized: true,
        user: null,
      },
    }));

    render(
      <TestWrapper store={store}>
        <SharePhotosOrStory formType="photos" />
      </TestWrapper>,
    );

    expect(authUtils.redirectToRegister).toHaveBeenCalledWith(
      'http://localhost/',
      { referrer: 'ugc-form' },
    );
  });
});
