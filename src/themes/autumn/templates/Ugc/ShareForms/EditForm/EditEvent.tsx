'use client';

import { faCircleNotch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import React, { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

import { useAppSelector } from 'store/hooks';
import Button from 'themes/autumn/components/generic/Button';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import ToggleSwitch from 'themes/autumn/components/generic/ToggleSwitch';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import DateRangeInput from 'themes/autumn/templates/explore/SearchWidget/DateRangeInput';
import { UGCFormField, UgcContentType } from 'types/ugc';
import { authErrorToast, redirectToRegister } from 'util/auth';
import {
  ImageResizeMode,
  TransformOutputFormat,
  storyImageUrl,
} from 'util/image';
import { userDisplayName } from 'util/piano';
import { dateToString, useDate } from 'util/time';
import {
  WEEKDAY_MAPPINGS,
  buildRecurrenceRule,
  fetchCategories,
  fetchOrganiserIfExists,
  fetchUserIfExists,
  getRecurrenceText,
  submitForm,
} from 'util/ugc';

import BannedScreen from '../Common/BannedScreen';
import Dropdown from '../Common/Dropdown';
import FieldInput, {
  AutocompleteLocationField,
  FileUploadField,
} from '../Common/FieldInput';
import { Recurrence } from '../Common/Recurrence';
import SubmitError from '../Common/SubmitError';
import SubmittedScreen from '../Common/SubmittedScreen';
import ShareEventPreview from '../ShareEvent/ShareEventPreview';

import type { SubmitHandler } from 'react-hook-form';
import type { UGC, UGCCategory, UGCFormInputs, UgcImage } from 'types/ugc';

const INITIAL_WEEKDAYS = Object.fromEntries(
  WEEKDAY_MAPPINGS.map((day) => [day.value, false]),
) as Record<string, boolean>;

export default function EditEvent() {
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);
  const ugc = useAppSelector((state) => state.ugc.ugcDetail as UGC);
  const user = useAppSelector((state) => state.piano.user);
  const csrftoken = useAppSelector((state) => state.page.csrftoken);
  const [categories, setCategories] = useState<UGCCategory[]>([]);
  const [organiserName, setOrganiserName] = useState(
    ugc?.organiserDetails.name,
  );
  const [organiserEmail, setOrganiserEmail] = useState(
    ugc?.organiserDetails.email,
  );
  const [organiserPhone, setOrganiserPhone] = useState(
    ugc?.organiserDetails.contactNumber,
  );
  const [organiserWebsite, setOrganiserWebsite] = useState(
    ugc?.organiserDetails.websiteUrl,
  );
  const [title, setTitle] = useState(ugc?.title);
  const [description, setDescription] = useState(ugc?.description);
  const [priceText, setPriceText] = useState(ugc?.priceText);
  const [startTimeText, setStartTimeText] = useState(ugc?.startTimeText);
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const [logo, setOrganiserLogo] = useState<FileList | null>(null);
  const [existingLogoUrl, setExistingLogoUrl] = useState('');
  const [images, setImages] = useState<FileList | null>(null);
  const [userName, setUserName] = useState('');
  const [existingUserName, setExistingUserName] = useState('');
  const [isBannedUser, setIsBannedUser] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [previewImagesSrc, setPreviewImagesSrc] = useState<string[]>(
    ugc?.imagesWithId?.map((img) =>
      storyImageUrl({
        fit: ImageResizeMode.MAX,
        image: { uri: img.uri },
        outputFormat: TransformOutputFormat.WEBP,
        transformUrl,
        width: 640,
      }),
    ) || [],
  );
  const [previewImagesWithID, setPreviewImagesWithID] = useState<
    (UgcImage & { isNew?: boolean })[]
  >(
    ugc?.imagesWithId?.map((img) => ({
      id: img.id,
      isNew: false,
      uri: storyImageUrl({
        fit: ImageResizeMode.MAX,
        image: { uri: img.uri },
        outputFormat: TransformOutputFormat.WEBP,
        transformUrl,
        width: 640,
      }),
    })) || [],
  );
  const [logoSrc, setLogoSrc] = useState<string>('');
  const [recurrenceInterval, setRecurrenceInterval] = useState('1');
  const [showRecurrence, setShowRecurrence] = useState(false);
  const [selectedDays, setSelectedDays] = useState(INITIAL_WEEKDAYS);
  const [deletedImageIds, setDeletedImageIds] = useState<number[]>([]);

  const handleDayChange = (dayValue: string) => {
    setSelectedDays((prev) => ({
      ...prev,
      [dayValue]: !prev[dayValue],
    }));
  };

  const today = useDate();
  const todayString = dateToString(today);
  const originalStartDate = new Date(ugc?.startDatetime);
  const formStartDateString =
    originalStartDate < today ? todayString : dateToString(originalStartDate);

  const {
    clearErrors,
    formState: { errors },
    handleSubmit,
    register,
    reset,
    setError,
    setValue,
    trigger,
    watch,
  } = useForm<UGCFormInputs>({
    defaultValues: {
      category: ugc?.category,
      description: ugc?.description,
      end_datetime: dateToString(new Date(ugc?.endDatetime)),
      lat: ugc?.lat,
      lng: ugc?.lng,
      location: ugc?.location,
      organiser_email: ugc?.organiserDetails.email,
      start_datetime: dateToString(new Date(ugc?.startDatetime)),
      user_email: '',
      user_name: '',
    },
    shouldFocusError: false,
  });

  const startDatetime = watch('start_datetime');
  const endDatetime = watch('end_datetime');
  const recurrenceType = watch('recurrence_frequency');
  const recurrenceMonthlyDate = watch('recurrence_monthly_date');

  const recurrenceText = useMemo(() => {
    if (!showRecurrence) return '';
    return getRecurrenceText(
      recurrenceType === '1' ? 'WEEKLY' : 'MONTHLY',
      Number(recurrenceInterval),
      new Date(startDatetime),
      selectedDays,
      Number(recurrenceMonthlyDate),
      endDatetime,
    );
  }, [
    showRecurrence,
    recurrenceInterval,
    recurrenceMonthlyDate,
    recurrenceType,
    startDatetime,
    selectedDays,
    endDatetime,
  ]);

  useEffect(() => {
    if (!startDatetime || !showRecurrence) return;

    const weekdayIndex = new Date(startDatetime).getDay().toString();
    const weekdayValue = WEEKDAY_MAPPINGS.find(
      (day) => day.id === (weekdayIndex === '0' ? '7' : weekdayIndex),
    )?.value;

    if (!weekdayValue) return;

    setSelectedDays(() =>
      Object.fromEntries(
        WEEKDAY_MAPPINGS.map((day) => [day.value, day.value === weekdayValue]),
      ),
    );
  }, [startDatetime, showRecurrence]);

  const handleError = (message: React.ReactNode) => {
    setLoading(false);
    authErrorToast(message);
  };

  useEffect(() => {
    if (pianoInitialized && !user) {
      redirectToRegister(window.location.href, {
        referrer: 'ugc-form',
      });
    }
  }, [pianoInitialized, user, csrftoken, ugc.id]);

  useEffect(() => {
    setLoading(true);
    fetchCategories('event')
      .then((cats) => {
        if (cats) {
          setCategories(cats);
        }
      })
      .catch((error) => {
        console.error(error);
        handleError('Failed to load the form. Please try again later.');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    if (!user) {
      return;
    }

    setLoading(true);

    fetchUserIfExists(user.email)
      .then((userProfile) => {
        if (userProfile && !userProfile.is_active) {
          setIsBannedUser(true);
          return null;
        }

        const name = userProfile
          ? userProfile.user_name
          : userDisplayName(user);

        reset({
          description: ugc?.description,
          end_datetime: dateToString(new Date(ugc?.endDatetime)),
          location: ugc?.location,
          start_datetime: formStartDateString,
          user_email: user.email,
          user_name: name,
        });

        // parse recurrence rule if it exists
        let recurrenceRuleObject: Record<string, string> = {};
        if (ugc?.recurrences) {
          try {
            recurrenceRuleObject = Object.fromEntries(
              ugc.recurrences
                .replace(/^RRULE:/, '')
                .split(';')
                .map((pair) => {
                  const [key, value] = pair.split('=');
                  return [key, value || ''];
                }),
            );

            // set recurrence state if recurrence rule exists
            if (Object.keys(recurrenceRuleObject).length > 0) {
              setShowRecurrence(true);

              // parse frequency and interval
              if (recurrenceRuleObject.FREQ) {
                const frequency =
                  recurrenceRuleObject.FREQ === 'WEEKLY' ? '1' : '2';
                setValue('recurrence_frequency', frequency);
              }

              if (recurrenceRuleObject.INTERVAL) {
                setRecurrenceInterval(recurrenceRuleObject.INTERVAL);
                setValue('recurrence_interval', recurrenceRuleObject.INTERVAL);
              }

              // parse weekly recurrence days
              if (
                recurrenceRuleObject.BYDAY &&
                recurrenceRuleObject.FREQ === 'WEEKLY'
              ) {
                const days = recurrenceRuleObject.BYDAY.split(',');
                const newSelectedDays = { ...INITIAL_WEEKDAYS };
                days.forEach((day) => {
                  if (day in newSelectedDays) {
                    newSelectedDays[day] = true;
                  }
                });
                setSelectedDays(newSelectedDays);
              }

              // parse monthly recurrence
              if (recurrenceRuleObject.FREQ === 'MONTHLY') {
                if (recurrenceRuleObject.BYMONTHDAY) {
                  setValue(
                    'recurrence_monthly_date',
                    recurrenceRuleObject.BYMONTHDAY,
                  );
                }
                // handle BYDAY for monthly (e.g. "1MO" for first Monday)
                if (
                  recurrenceRuleObject.BYDAY &&
                  recurrenceRuleObject.BYDAY.match(/^-?\d+[A-Z]{2}$/)
                ) {
                  const match =
                    recurrenceRuleObject.BYDAY.match(/^(-?\d+)([A-Z]{2})$/);
                  if (match) {
                    const [, instance, weekday] = match;
                    setValue('recurrence_weekday_instance', instance);
                    const weekdayMapping = WEEKDAY_MAPPINGS.find(
                      (w) => w.value === weekday,
                    );
                    if (weekdayMapping) {
                      setValue(
                        'recurrence_monthly_weekday',
                        weekdayMapping.id.toString(),
                      );
                    }
                  }
                }
              }
            }
          } catch (error) {
            console.error('Failed to parse recurrence rule:', error);
          }
        }

        setExistingUserName(name);

        return fetchOrganiserIfExists(user.email);
      })
      .then((org) => {
        if (org) {
          setOrganiserPhone(org.contact_number);
          setOrganiserEmail(org.email);
          setOrganiserName(org.name);
          setOrganiserWebsite(org.website_url);
          setExistingLogoUrl(org.logo);

          reset((formValues) => ({
            ...formValues,
            organiser_contact_number: org.contact_number,
            organiser_email: org.email,
            organiser_name: org.name,
            organiser_website_url: org.website_url,
          }));
        }
      })
      .catch((error) => {
        console.error(error);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [
    categories.length,
    formStartDateString,
    reset,
    setValue,
    todayString,
    ugc?.description,
    ugc?.endDatetime,
    ugc?.location,
    ugc.recurrences,
    user,
  ]);

  const onSubmit: SubmitHandler<UGCFormInputs> = async (data) => {
    if (!user || !csrftoken) {
      return;
    }

    setLoading(true);
    try {
      const formData = new FormData();
      const formFields = {
        [UGCFormField.CONTENT_TYPE]: 'event',
        [UGCFormField.PIANO_USER_ID]: user.uid,
        [UGCFormField.USER_NAME]: data.user_name,
        [UGCFormField.USER_EMAIL]: user.email,
        [UGCFormField.ORGANISER_EMAIL]: data.organiser_email ?? organiserEmail,
        [UGCFormField.ORGANISER_NAME]: data.organiser_name,
        [UGCFormField.ORGANISER_WEBSITE]: data.organiser_website_url,
        [UGCFormField.ORGANISER_PHONE]: data.organiser_contact_number,
        [UGCFormField.TITLE]: data.title,
        [UGCFormField.CATEGORY]: data.category.toString(),
        [UGCFormField.LOCATION]: data.location,
        [UGCFormField.START_DATETIME]: `${data.start_datetime}T00:00:00Z`,
        [UGCFormField.END_DATETIME]: `${data.end_datetime}T00:00:00Z`,
        [UGCFormField.DESCRIPTION]: data.description,
        [UGCFormField.PRICE_TEXT]:
          data.price_text.charAt(0).toUpperCase() + data.price_text.slice(1),
        [UGCFormField.START_TIME_TEXT]: data.start_time_text,
        [UGCFormField.LATITUDE]: data.lat || '',
        [UGCFormField.LONGITUDE]: data.lng || '',
      };
      Object.entries(formFields).forEach(([key, value]) => {
        formData.append(key, value);
      });

      if (deletedImageIds.length > 0) {
        formData.append('deleted_image_ids', deletedImageIds.join(','));
      }

      if (logo && logo[0]) {
        formData.append(UGCFormField.ORGANISER_LOGO, logo[0]);
      }

      if (images) {
        Array.from(images).forEach((img) => {
          formData.append('images', img);
        });
      }

      if (showRecurrence) {
        const rule = buildRecurrenceRule(data, selectedDays);
        formData.append(UGCFormField.RECURRENCE_RULE, rule);
      }

      const response = await submitForm(
        formData,
        csrftoken,
        ugc?.id.toString(),
      );
      if (response) {
        setSubmitted(true);
      }
    } catch (error) {
      console.error(error);
      handleError(<SubmitError />);
    } finally {
      setLoading(false);
    }
  };

  const handleLogoChange = (selectedFiles: FileList) => {
    setOrganiserLogo(selectedFiles);
    const logoPreviewFile = selectedFiles[0];
    if (logoPreviewFile) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setLogoSrc(e.target.result as string);
        }
      };
      reader.readAsDataURL(logoPreviewFile);
    } else {
      setLogoSrc('logoPlaceholder.png');
    }
  };

  const handleImagesChange = (selectedFiles: FileList) => {
    setImages(selectedFiles);
    const imageSrcArray: string[] = [];

    Array.from(selectedFiles).forEach((file) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        if (e.target?.result) {
          imageSrcArray.push(e.target.result as string);
          if (imageSrcArray.length === selectedFiles.length) {
            // filter out deleted images from existing ones
            const existingImages = ugc?.imagesWithId
              ?.filter((img) => !deletedImageIds.includes(img.id))
              .map((img) => ({
                id: img.id,
                isNew: false,
                uri: storyImageUrl({
                  fit: ImageResizeMode.MAX,
                  image: { uri: img.uri },
                  outputFormat: TransformOutputFormat.WEBP,
                  transformUrl,
                  width: 640,
                }),
              }));

            setPreviewImagesSrc([
              ...(existingImages?.map((img) => img.uri) || []),
              ...imageSrcArray,
            ]);

            // create temporary objects for new images with isNew flag
            const newImageObjects = imageSrcArray.map((uri, index) => ({
              id: -1 - index,
              isNew: true,
              uri,
            }));

            // combine new images with existing ones
            setPreviewImagesWithID([
              ...(existingImages || []),
              ...newImageObjects,
            ]);
          }
        }
      };

      reader.readAsDataURL(file);
    });

    // if no new files uploaded, just show existing images
    if (selectedFiles.length === 0 && ugc?.imagesWithId) {
      const existingImages = ugc.imagesWithId
        .filter((img) => !deletedImageIds.includes(img.id))
        .map((img) => ({
          id: img.id,
          isNew: false,
          uri: storyImageUrl({
            fit: ImageResizeMode.MAX,
            image: { uri: img.uri },
            outputFormat: TransformOutputFormat.WEBP,
            transformUrl,
            width: 640,
          }),
        }));

      setPreviewImagesSrc(existingImages.map((img) => img.uri));
      setPreviewImagesWithID(existingImages);
    }
  };

  const scrollToError = () => {
    if (Object.keys(errors).length > 0) {
      const firstErrorField = Object.keys(errors)[0];
      const element = errors[firstErrorField as keyof UGCFormInputs]?.ref;
      element?.scrollIntoView?.({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      });
    }
  };

  const onError = () => {
    scrollToError();
  };

  const handlePreview = async () => {
    const isValid = await trigger();
    if (isValid) {
      setIsPreviewModalOpen(true);
    }
    if (errors) {
      scrollToError();
    }
  };

  if (!user) {
    return null;
  }

  const previewUgc: UGC = {
    canonicalUrl: '',
    category: ugc?.category,
    categoryName:
      categories.find((category) => category.id === Number(watch('category')))
        ?.name || '',
    contentType: UgcContentType.EVENT,
    description,
    endDatetime,
    id: 0, // Placeholder ID for preview
    images: previewImagesSrc,
    lat: watch('lat'),
    lng: watch('lng'),
    location: watch('location'),
    masthead: 0,
    nextOccurrence: startDatetime,
    organiserDetails: {
      contactNumber: organiserPhone,
      email: organiserEmail,
      logo:
        logoSrc ||
        (existingLogoUrl &&
          storyImageUrl({
            fit: ImageResizeMode.MAX,
            height: 160,
            image: { uri: existingLogoUrl },
            outputFormat: TransformOutputFormat.WEBP,
            transformUrl,
            width: 160,
          })),
      name: organiserName,
      websiteUrl: organiserWebsite,
    },
    priceText,
    publishedOn: '',
    recurrenceText,
    startDatetime,
    startTimeText,
    title,
    userDetails: {
      avatar: '',
      userEmail: user?.email || '',
      userName: userName || existingUserName,
    },
  };

  return (
    <TemplateWrapper
      footer={null}
      free
      hideSmartBanner
      nav={
        <SubscriptionNav
          showContact={false}
          showHelp={false}
          showLogin={false}
          showPageName={false}
        />
      }
      showGutterAd={false}
      showNavigationAd={false}
      showOutages={false}
    >
      <div className="flex flex-col items-center border-t border-gray-300 p-4">
        <div className="flex max-w-[600px] flex-col items-center font-inter text-gray-900">
          {isLoading && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/50">
              <FontAwesomeIcon
                className="text-5xl text-slate-800"
                icon={faCircleNotch}
                spin
              />
            </div>
          )}
          {isBannedUser ? (
            <BannedScreen />
          ) : (
            <>
              {!submitted ? (
                <div
                  className={clsx(
                    'm-4 flex w-full flex-col space-y-4 rounded border border-gray-300 p-[23px] shadow-lg',
                    isLoading || !categories ? 'opacity-25' : 'opacity-100',
                  )}
                >
                  <h2 className="font-inter text-3xl font-semibold leading-9 text-slate-900">
                    Edit Contribution
                  </h2>
                  <p className="font-inter text-sm text-slate-600">
                    {/* eslint-disable-next-line @stylistic/max-len */}
                    You can now edit your content on our Noticeboard.{' '}
                    <br className="hidden md:block" />
                    Please note that any updates{' '}
                    <span className="font-semibold">
                      will be sent back for moderation
                    </span>{' '}
                    before being republished.
                  </p>

                  <form noValidate onSubmit={handleSubmit(onSubmit, onError)}>
                    <div className="space-y-6">
                      {!existingUserName && (
                        <FieldInput
                          errors={errors}
                          fieldName={UGCFormField.USER_NAME}
                          fieldValue={userName}
                          name="Your Name"
                          register={register}
                          required
                          setField={setUserName}
                        />
                      )}
                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.ORGANISER_NAME}
                        fieldValue={organiserName}
                        name="Organiser / Display Name"
                        register={register}
                        required
                        setField={setOrganiserName}
                      />
                      <div className="flex items-center space-x-8">
                        <FileUploadField
                          clearErrors={clearErrors}
                          errors={errors}
                          fieldName={UGCFormField.ORGANISER_LOGO}
                          maxFileCount={1}
                          name="Organiser Logo"
                          onFileChange={handleLogoChange}
                          register={register}
                          setError={setError}
                          setValue={setValue}
                          uploadText={existingLogoUrl ? 'Change' : 'Upload'}
                        />
                        {existingLogoUrl && (
                          <img
                            alt="Current Organiser Logo"
                            className="self-center"
                            height="80"
                            src={storyImageUrl({
                              fit: ImageResizeMode.MAX,
                              height: 160,
                              image: { uri: existingLogoUrl },
                              outputFormat: TransformOutputFormat.WEBP,
                              transformUrl,
                              width: 160,
                            })}
                            width="80"
                          />
                        )}
                      </div>

                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.ORGANISER_EMAIL}
                        fieldValue={organiserEmail}
                        name="Contact Email"
                        register={register}
                        required
                        setField={setOrganiserEmail}
                        type="email"
                      />
                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.ORGANISER_PHONE}
                        fieldValue={organiserPhone}
                        name="Contact Number"
                        register={register}
                        required
                        setField={setOrganiserPhone}
                      />
                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.ORGANISER_WEBSITE}
                        fieldValue={organiserWebsite}
                        name="Contact Website"
                        register={register}
                        setField={setOrganiserWebsite}
                        type="url"
                      />
                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.TITLE}
                        fieldValue={title}
                        name="Event Title"
                        register={register}
                        required
                        setField={setTitle}
                      />
                      {categories && (
                        <Dropdown
                          defaultValue={ugc?.category}
                          errors={errors}
                          fieldName="category"
                          items={categories}
                          label="Event Category"
                          placeholder="Select a category"
                          register={register}
                          required
                        />
                      )}
                      <AutocompleteLocationField
                        errors={errors}
                        name="Event Location"
                        register={register}
                        setValue={setValue}
                        watch={watch}
                      />
                      {!showRecurrence && (
                        <div>
                          <div className="mb-1.5 text-sm font-medium leading-tight text-slate-900">
                            Select date or date range *
                          </div>
                          <DateRangeInput<UGCFormInputs>
                            date1Name={UGCFormField.START_DATETIME}
                            date2Name={UGCFormField.END_DATETIME}
                            maxDurationDays={365}
                            register={register}
                            setValue={setValue}
                            todayString={todayString}
                            watch={watch}
                          />
                        </div>
                      )}

                      <div className="flex flex-col gap-2 text-sm font-medium">
                        Does the event repeat?
                        <ToggleSwitch
                          backgroundClassName="!w-10"
                          buttonClassName="!size-5"
                          enabled={showRecurrence}
                          onClick={() => {
                            setShowRecurrence(!showRecurrence);
                            reset((formValues) => ({
                              ...formValues,
                              recurrence_frequency: '1',
                            }));
                          }}
                        />
                        {showRecurrence && (
                          <Recurrence
                            errors={errors}
                            handleDayChange={handleDayChange}
                            recurrenceInterval={recurrenceInterval}
                            register={register}
                            selectedDays={selectedDays}
                            setRecurrenceInterval={setRecurrenceInterval}
                            setValue={setValue}
                            todayString={todayString}
                            watch={watch}
                          />
                        )}
                      </div>
                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.START_TIME_TEXT}
                        fieldValue={startTimeText}
                        maxlength={50}
                        name="Event Start Time"
                        register={register}
                        setField={setStartTimeText}
                        showCharCount
                      />
                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.PRICE_TEXT}
                        fieldValue={priceText}
                        name="Event Price"
                        register={register}
                        setField={setPriceText}
                      />
                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.DESCRIPTION}
                        fieldValue={description}
                        name="Event Description"
                        register={register}
                        required
                        setField={(value) => {
                          setValue(UGCFormField.DESCRIPTION, value, {
                            shouldDirty: true,
                            shouldValidate: true,
                          });
                          setDescription(value);
                        }}
                        type="textarea"
                      />

                      <FileUploadField
                        clearErrors={clearErrors}
                        errors={errors}
                        fieldName={UGCFormField.IMAGES}
                        maxFileCount={5 - previewImagesWithID.length}
                        name="Pictures (max 5)"
                        onFileChange={handleImagesChange}
                        register={register}
                        required={previewImagesWithID.length === 0}
                        setError={setError}
                        setValue={setValue}
                        uploadText="Upload pictures"
                      />

                      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
                        {previewImagesWithID.map((img) => (
                          <div className="group relative" key={img.id}>
                            <img
                              alt="Uploaded"
                              className="h-40 w-full rounded-md object-cover shadow"
                              src={img.uri}
                            />
                            <button
                              aria-label="Delete image"
                              className="absolute right-1 top-1 rounded-full bg-black/50 p-1 text-white transition hover:bg-black/75"
                              onClick={() => {
                                setPreviewImagesWithID((prev) =>
                                  prev.filter((i) => i.id !== img.id),
                                );
                                setPreviewImagesSrc((prev) =>
                                  prev.filter((i) => i !== img.uri),
                                );

                                if (!img.isNew) {
                                  setDeletedImageIds((prev) => [
                                    ...prev,
                                    img.id,
                                  ]);
                                }
                              }}
                              type="button"
                            >
                              ✕
                            </button>
                          </div>
                        ))}
                      </div>

                      <div className="flex gap-x-2">
                        <Button
                          bgColor="bg-white disabled:bg-gray-300"
                          buttonClassName="shadow-md"
                          className="mt-4"
                          desktopFullWidth
                          fontSize="text-sm"
                          height="h-10.5"
                          // eslint-disable-next-line @stylistic/max-len
                          hoverColor="hover:bg-gray-300 disabled:hover:bg-gray-300"
                          mobileFullWidth
                          onClick={() => handlePreview()}
                          text="Preview"
                          textColor="text-gray"
                          type="button"
                        />
                        <Button
                          bgColor="bg-red-600 disabled:bg-gray-300"
                          buttonClassName="shadow-md"
                          className="mt-4"
                          desktopFullWidth
                          fontSize="text-sm"
                          height="h-10.5"
                          // eslint-disable-next-line @stylistic/max-len
                          hoverColor="hover:bg-red-700 disabled:hover:bg-gray-300"
                          mobileFullWidth
                          text="Submit"
                          textColor="text-white"
                          type="submit"
                        />
                      </div>
                    </div>
                  </form>
                  <p className="font-inter text-sm text-slate-600">
                    All content will be moderated before being published. You
                    will be notified when your story is published.
                  </p>
                </div>
              ) : (
                <SubmittedScreen email={user.email} />
              )}
            </>
          )}
        </div>
        <ShareEventPreview
          isOpen={isPreviewModalOpen}
          onClose={() => setIsPreviewModalOpen(false)}
          ugc={previewUgc}
        />
      </div>
    </TemplateWrapper>
  );
}
