'use client';

import React from 'react';

import { useAppSelector } from 'store/hooks';
import Status404 from 'themes/autumn/templates/status/Status404';
import { UgcContentType } from 'types/ugc';

import EditEvent from './EditEvent';
import EditPhotosOrStory from './EditPhotosOrStory';

import type { UGC } from 'types/ugc';

export default function EditForm(): React.ReactElement {
  const ugc = useAppSelector((state) => state.ugc.ugcDetail as UGC);

  if (!ugc) {
    return <Status404 />;
  }

  if (ugc.contentType === UgcContentType.EVENT) {
    return <EditEvent />;
  }
  return <EditPhotosOrStory formType={ugc.contentType} />;
}
