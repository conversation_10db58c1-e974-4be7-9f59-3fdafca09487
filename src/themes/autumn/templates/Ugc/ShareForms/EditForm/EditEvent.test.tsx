import { render, screen } from '@testing-library/react';

import { createStore } from 'store/store';
import { UgcContentType } from 'types/ugc';
import { TestWrapper } from 'util/jest';

import EditEvent from './EditEvent';

const mockUgcItem = {
  canonicalUrl: '/notice-board/whats-on/1/sample-event-title',
  categoryName: 'General',
  contentType: UgcContentType.EVENT,
  createdOn: '2024-12-01T09:00:00+00:00',
  description:
    '<p>This is a sample event description with <strong>basic formatting</strong>.</p>',
  endDatetime: '2025-01-01T12:00:00+00:00',
  id: 1,
  images: ['generic/path/to-image.png'],
  lat: '-33.0',
  lng: '151.0',
  location: '123 Example Street',
  masthead: 1,
  nextOccurrence: '2025-01-01T10:00:00+00:00',
  organiserDetails: {
    contactNumber: '0000 000 000',
    email: '<EMAIL>',
    id: 100,
    logo: '',
    name: 'Sample Organizer',
    websiteUrl: '',
  },
  priceText: 'Free',
  publishedOn: '2024-12-02T10:00:00+00:00',
  recurrenceText: '',
  startDatetime: '2025-01-01T10:00:00+00:00',
  startTimeText: '10:00am',
  title: 'Sample Event Title',
  userDetails: {
    avatar: '',
    isActive: true,
    userEmail: '<EMAIL>',
    userName: 'sample.user',
  },
};

const store = createStore((state) => ({
  ...state,
  piano: {
    ...state.piano,
    initialized: true,
    user: {
      ...state.piano.user,
      aud: 'test-aud',
      confirmed: true,
      email: '<EMAIL>',
      email_confirmation_required: false,
      exp: 1234567890,
      family_name: 'Doe',
      firstName: 'John',
      given_name: 'John',
      iat: 1234567890,
      iss: 'test-iss',
      jti: 'test-jti',
      lastName: 'Doe',
      login_timestamp: '2024-03-20T12:00:00Z',
      sub: 'test-sub',
      uid: '123',
      valid: true,
    },
  },
  ugc: {
    recirculationSections: undefined,
    ugcDetail: mockUgcItem,
  },
}));

describe('<EditEvent />', () => {
  it('renders with UGC detail from store', () => {
    const { container } = render(
      <TestWrapper store={store}>
        <EditEvent />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
    expect(screen.getByDisplayValue(mockUgcItem.title)).toBeInTheDocument();
  });
});
