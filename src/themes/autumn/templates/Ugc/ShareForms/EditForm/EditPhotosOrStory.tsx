'use client';

import { faCircleNotch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { useAppSelector } from 'store/hooks';
import Button from 'themes/autumn/components/generic/Button';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import { UGCFormField } from 'types/ugc';
import { authErrorToast, redirectToRegister } from 'util/auth';
import {
  ImageResizeMode,
  TransformOutputFormat,
  storyImageUrl,
} from 'util/image';
import { userDisplayName } from 'util/piano';
import { fetchCategories, fetchUserIfExists, submitForm } from 'util/ugc';

import BannedScreen from '../Common/BannedScreen';
import Dropdown from '../Common/Dropdown';
import FieldInput, { FileUploadField } from '../Common/FieldInput';
import SubmitError from '../Common/SubmitError';
import SubmittedScreen from '../Common/SubmittedScreen';
import SharePhotosOrStoryPreview from '../SharePhotosOrStory/SharePhotosOrStoryPreview';

import type { SubmitHandler } from 'react-hook-form';
import type { UGC, UGCCategory, UGCFormInputs, UgcImage } from 'types/ugc';

interface EditPhotosOrStoryProps {
  formType: 'photos' | 'story';
}

export default function EditPhotosOrStory({
  formType,
}: EditPhotosOrStoryProps) {
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const user = useAppSelector((state) => state.piano.user);
  const csrftoken = useAppSelector((state) => state.page.csrftoken);
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);
  const ugc = useAppSelector((state) => state.ugc.ugcDetail as UGC);

  const [categories, setCategories] = useState<UGCCategory[]>([]);
  const [userName, setUserName] = useState(ugc?.userDetails.userName || '');
  const [userEmail, setUserEmail] = useState(ugc?.userDetails.userEmail || '');
  const [title, setTitle] = useState(ugc?.title || '');
  const [location, setLocation] = useState(ugc?.location || '');
  const [description, setDescription] = useState(ugc?.description || '');
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const [avatar, setAvatar] = useState<FileList | null>(null);
  const [images, setImages] = useState<FileList | null>(null);
  const [existingAvatarUrl, setExistingAvatarUrl] = useState(
    ugc?.userDetails.avatar || '',
  );
  const [isBannedUser, setIsBannedUser] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [previewImagesSrc, setPreviewImagesSrc] = useState<string[]>(
    ugc?.imagesWithId?.map((img) =>
      storyImageUrl({
        fit: ImageResizeMode.MAX,
        image: { uri: img.uri },
        outputFormat: TransformOutputFormat.WEBP,
        transformUrl,
        width: 640,
      }),
    ) || [],
  );
  const [previewImagesWithID, setPreviewImagesWithID] = useState<
    (UgcImage & { isNew?: boolean })[]
  >(
    ugc?.imagesWithId?.map((img) => ({
      id: img.id,
      isNew: false,
      uri: storyImageUrl({
        fit: ImageResizeMode.MAX,
        image: { uri: img.uri },
        outputFormat: TransformOutputFormat.WEBP,
        transformUrl,
        width: 640,
      }),
    })) || [],
  );
  const [avatarSrc, setAvatarSrc] = useState<string>('');
  const [deletedImageIds, setDeletedImageIds] = useState<number[]>([]);

  const {
    clearErrors,
    formState: { errors },
    handleSubmit,
    register,
    reset,
    setError,
    setValue,
    trigger,
  } = useForm<UGCFormInputs>({
    defaultValues: {
      category: ugc?.category,
      description: ugc?.description,
      location: ugc?.location,
      title: ugc?.title,
      user_email: ugc?.userDetails.userEmail,
      user_name: ugc?.userDetails.userName,
    },
    shouldFocusError: false,
  });

  const handleError = (message: React.ReactNode) => {
    setLoading(false);
    authErrorToast(message);
  };

  useEffect(() => {
    if (user) {
      reset({
        category: ugc?.category,
        description: ugc?.description,
        location: ugc?.location,
        title: ugc?.title,
        user_email: user?.email,
        user_name: ugc?.userDetails.userName || userDisplayName(user),
      });

      setUserName(ugc?.userDetails.userName || userDisplayName(user));
      setUserEmail(user.email ?? '');
    }
  }, [user, reset, ugc]);

  useEffect(() => {
    if (!user) {
      return;
    }

    setLoading(true);
    setUserEmail(user.email);

    fetchUserIfExists(user.email)
      .then((userProfile) => {
        if (userProfile && !userProfile.is_active) {
          setIsBannedUser(true);
          return;
        }

        const name = userProfile
          ? userProfile.user_name
          : ugc?.userDetails.userName || userDisplayName(user);

        setUserName(name);
        setExistingAvatarUrl(
          userProfile?.avatar || ugc?.userDetails.avatar || '',
        );
        setLoading(false);

        reset({
          category: ugc?.category,
          description: ugc?.description,
          location: ugc?.location,
          title: ugc?.title,
          user_email: user.email,
          user_name: name,
        });
      })
      .catch((error) => {
        console.error('Failed to fetch user profile:', error);
        setLoading(false);
      });
  }, [reset, user, ugc]);

  useEffect(() => {
    if (pianoInitialized && !user) {
      redirectToRegister(window.location.href, {
        referrer: 'ugc-form',
      });
    }
  }, [pianoInitialized, user]);

  useEffect(() => {
    setValue(UGCFormField.DESCRIPTION, description, { shouldValidate: true });
  }, [description, setValue]);

  useEffect(() => {
    setLoading(true);
    fetchCategories(formType)
      .then((cats) => {
        if (cats) {
          setCategories(cats);
        }
        setLoading(false);
      })
      .catch((error) => {
        console.error(error);
        handleError('Failed to load the form. Please try again later.');
      });
  }, [formType]);

  const isPhotosForm = formType === 'photos';

  const onSubmit: SubmitHandler<UGCFormInputs> = async (data) => {
    if (!user || !csrftoken) {
      handleError(<SubmitError />);
      return;
    }
    setLoading(true);
    try {
      const formData = new FormData();
      const formFields = {
        [UGCFormField.CONTENT_TYPE]: formType,
        [UGCFormField.PIANO_USER_ID]: user.uid,
        [UGCFormField.USER_NAME]: data.user_name,
        [UGCFormField.USER_EMAIL]: user.email,
        [UGCFormField.TITLE]: data.title,
        [UGCFormField.CATEGORY]: data.category.toString(),
        [UGCFormField.LOCATION]: data.location ?? '',
        [UGCFormField.DESCRIPTION]: data.description,
      };
      Object.entries(formFields).forEach(([key, value]) => {
        formData.append(key, value);
      });

      if (deletedImageIds.length > 0) {
        formData.append('deleted_image_ids', deletedImageIds.join(','));
      }

      if (avatar && avatar[0]) {
        formData.append(UGCFormField.USER_AVATAR, avatar[0]);
      }

      if (images) {
        Array.from(images).forEach((img) => {
          formData.append('images', img);
        });
      }

      const response = await submitForm(
        formData,
        csrftoken,
        ugc?.id.toString(),
      );
      if (response?.success) {
        setSubmitted(true);
        return;
      }
      if (response?.errors) {
        console.error('Form submission errors:', response.errors);
      }
    } catch (error) {
      console.error(error);
      handleError(<SubmitError />);
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = (selectedFiles: FileList) => {
    setAvatar(selectedFiles);
    const avatarPreviewFile = selectedFiles[0];
    if (avatarPreviewFile) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setAvatarSrc(e.target.result as string);
        }
      };
      reader.readAsDataURL(avatarPreviewFile);
    } else {
      setAvatarSrc('avatarPlaceholder.png');
    }
  };

  const handleImagesChange = (selectedFiles: FileList) => {
    setImages(selectedFiles);
    const imageSrcArray: string[] = [];
    let count = 0;

    Array.from(selectedFiles).forEach((file, i) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        if (e.target?.result) {
          count += 1;
          imageSrcArray[i] = e.target.result as string;
          if (count === selectedFiles.length) {
            // filter out deleted images from existing ones
            const existingImages = ugc?.imagesWithId
              ?.filter((img) => !deletedImageIds.includes(img.id))
              .map((img) => ({
                id: img.id,
                isNew: false,
                uri: storyImageUrl({
                  fit: ImageResizeMode.MAX,
                  image: { uri: img.uri },
                  outputFormat: TransformOutputFormat.WEBP,
                  transformUrl,
                  width: 640,
                }),
              }));

            setPreviewImagesSrc([
              ...(existingImages?.map((img) => img.uri) || []),
              ...imageSrcArray,
            ]);

            // create temporary objects for new images with isNew flag
            const newImageObjects = imageSrcArray.map((uri, index) => ({
              id: -1 - index,
              isNew: true,
              uri,
            }));

            // combine new images with existing ones
            setPreviewImagesWithID([
              ...(existingImages || []),
              ...newImageObjects,
            ]);
          }
        }
      };

      reader.readAsDataURL(file);
    });

    // if no new files uploaded, just show existing images
    if (selectedFiles.length === 0 && ugc?.imagesWithId) {
      const existingImages = ugc.imagesWithId
        .filter((img) => !deletedImageIds.includes(img.id))
        .map((img) => ({
          id: img.id,
          isNew: false,
          uri: storyImageUrl({
            fit: ImageResizeMode.MAX,
            image: { uri: img.uri },
            outputFormat: TransformOutputFormat.WEBP,
            transformUrl,
            width: 640,
          }),
        }));

      setPreviewImagesSrc(existingImages.map((img) => img.uri));
      setPreviewImagesWithID(existingImages);
    }
  };

  const scrollToError = () => {
    if (Object.keys(errors).length > 0) {
      const firstErrorField = Object.keys(errors)[0];
      const element = errors[firstErrorField as keyof UGCFormInputs]?.ref;

      if (element?.name === UGCFormField.USER_AVATAR) {
        window.scrollTo({ behavior: 'smooth', top: 0 });
      }
      element?.scrollIntoView?.({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      });
    }
  };

  const onError = () => {
    scrollToError();
  };

  const handlePreview = async () => {
    const isValid = await trigger();
    if (isValid) {
      setIsPreviewModalOpen(true);
    }
    if (errors) {
      scrollToError();
    }
  };

  if (!user) {
    return null;
  }
  return (
    <TemplateWrapper
      footer={null}
      free
      hideSmartBanner
      nav={
        <SubscriptionNav
          showContact={false}
          showHelp={false}
          showLogin={false}
          showPageName={false}
        />
      }
      showGutterAd={false}
      showNavigationAd={false}
      showOutages={false}
    >
      <div className="flex flex-col items-center border-t border-gray-300 p-4">
        <div className="flex max-w-[600px] flex-col items-center font-inter text-gray-900">
          {isBannedUser ? (
            <BannedScreen />
          ) : (
            <>
              {!submitted ? (
                <div
                  className={clsx(
                    'm-4 flex w-full flex-col space-y-4 rounded border border-gray-300 p-[23px] shadow-lg',
                    isLoading || !categories ? 'opacity-25' : 'opacity-100',
                  )}
                >
                  {isLoading && (
                    <div className="absolute inset-0 top-2/3 z-10 flex items-center justify-center">
                      <FontAwesomeIcon
                        className="text-5xl text-slate-800"
                        icon={faCircleNotch}
                        spin
                      />
                    </div>
                  )}
                  <h2 className="font-inter text-3xl font-semibold leading-9 text-slate-900">
                    {isPhotosForm ? 'Edit photos' : 'Edit story'}
                  </h2>
                  <p className="font-inter text-sm text-slate-600">
                    {/* eslint-disable-next-line @stylistic/max-len */}
                    You can now edit your content for our Noticeboard.{' '}
                    <br className="hidden md:block" />
                    All content{' '}
                    <span className="font-semibold">
                      will be moderated
                    </span>{' '}
                    before being published.
                  </p>

                  <form onSubmit={handleSubmit(onSubmit, onError)}>
                    <div className="space-y-6">
                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.USER_NAME}
                        fieldValue={userName}
                        name="Your Display Name"
                        register={register}
                        required
                        setField={setUserName}
                      />
                      <div className="flex items-center space-x-8">
                        <FileUploadField
                          clearErrors={clearErrors}
                          errors={errors}
                          fieldName={UGCFormField.USER_AVATAR}
                          maxFileCount={1}
                          name="Your Photo / Avatar"
                          onFileChange={handleAvatarChange}
                          register={register}
                          required={!existingAvatarUrl}
                          setError={setError}
                          setValue={setValue}
                          uploadText={existingAvatarUrl ? 'Change' : 'Upload'}
                        />
                        {existingAvatarUrl && (
                          <img
                            alt="Current Avatar"
                            className="self-center"
                            height="80"
                            src={storyImageUrl({
                              fit: ImageResizeMode.MAX,
                              height: 160,
                              image: { uri: existingAvatarUrl },
                              outputFormat: TransformOutputFormat.WEBP,
                              transformUrl,
                              width: 160,
                            })}
                            width="80"
                          />
                        )}
                      </div>
                      <FieldInput
                        disabled
                        errors={errors}
                        fieldName={UGCFormField.USER_EMAIL}
                        fieldValue={userEmail}
                        name="Email"
                        register={register}
                        required
                        setField={setUserEmail}
                      />
                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.TITLE}
                        fieldValue={title}
                        name={
                          isPhotosForm ? 'Photo Gallery Title' : 'Story Title'
                        }
                        register={register}
                        required
                        setField={setTitle}
                      />

                      {categories && (
                        <Dropdown
                          errors={errors}
                          fieldName="category"
                          items={categories}
                          label={
                            isPhotosForm
                              ? 'Photo Category'
                              : 'Your Expertise/Category'
                          }
                          register={register}
                          required
                        />
                      )}

                      {isPhotosForm && (
                        <FieldInput
                          errors={errors}
                          fieldName={UGCFormField.LOCATION}
                          fieldValue={location}
                          name="Photo Location"
                          register={register}
                          required={isPhotosForm}
                          setField={setLocation}
                        />
                      )}

                      <FieldInput
                        errors={errors}
                        fieldName={UGCFormField.DESCRIPTION}
                        fieldValue={description}
                        name="Description"
                        register={register}
                        required
                        setField={setDescription}
                        type="textarea"
                      />

                      <FileUploadField
                        clearErrors={clearErrors}
                        errors={errors}
                        fieldName={UGCFormField.IMAGES}
                        maxFileCount={5 - previewImagesWithID.length}
                        name="Pictures (max 5)"
                        onFileChange={handleImagesChange}
                        register={register}
                        required={previewImagesWithID.length === 0}
                        setError={setError}
                        setValue={setValue}
                        uploadText="Upload pictures"
                      />

                      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
                        {previewImagesWithID.map((img) => (
                          <div className="group relative" key={img.id}>
                            <img
                              alt="Uploaded"
                              className="h-40 w-full rounded-md object-cover shadow"
                              src={img.uri}
                            />
                            <button
                              aria-label="Delete image"
                              className="absolute right-1 top-1 rounded-full bg-black/50 p-1 text-white transition hover:bg-black/75"
                              onClick={() => {
                                setPreviewImagesWithID((prev) =>
                                  prev.filter((i) => i.id !== img.id),
                                );
                                setPreviewImagesSrc((prev) =>
                                  prev.filter((i) => i !== img.uri),
                                );

                                if (!img.isNew) {
                                  setDeletedImageIds((prev) => [
                                    ...prev,
                                    img.id,
                                  ]);
                                }
                              }}
                              type="button"
                            >
                              ✕
                            </button>
                          </div>
                        ))}
                      </div>

                      <div className="flex gap-x-2">
                        <Button
                          bgColor="bg-white disabled:bg-gray-300"
                          buttonClassName="shadow-md"
                          className="mt-4"
                          desktopFullWidth
                          fontSize="text-sm"
                          height="h-10.5"
                          // eslint-disable-next-line @stylistic/max-len
                          hoverColor="hover:bg-gray-300 disabled:hover:bg-gray-300"
                          mobileFullWidth
                          onClick={() => handlePreview()}
                          text="Preview"
                          textColor="text-gray"
                          type="button"
                        />
                        <Button
                          bgColor="bg-red-600 disabled:bg-gray-300"
                          buttonClassName="shadow-md"
                          className="mt-4"
                          desktopFullWidth
                          fontSize="text-sm"
                          height="h-10.5"
                          // eslint-disable-next-line @stylistic/max-len
                          hoverColor="hover:bg-red-700 disabled:hover:bg-gray-300"
                          mobileFullWidth
                          text="Submit"
                          textColor="text-white"
                          type="submit"
                        />
                      </div>
                    </div>
                  </form>
                  <p className="font-inter text-sm text-slate-600">
                    All content will be moderated before being published. You
                    will be notified when your story is published.
                  </p>
                </div>
              ) : (
                <SubmittedScreen email={user.email} />
              )}
            </>
          )}
        </div>
        <SharePhotosOrStoryPreview
          avatarSrc={
            avatarSrc ||
            (existingAvatarUrl &&
              storyImageUrl({
                fit: ImageResizeMode.MAX,
                height: 160,
                image: { uri: existingAvatarUrl },
                outputFormat: TransformOutputFormat.WEBP,
                transformUrl,
                width: 160,
              }))
          }
          description={description}
          isOpen={isPreviewModalOpen}
          location={isPhotosForm ? location : undefined}
          onClose={() => setIsPreviewModalOpen(false)}
          pillText={isPhotosForm ? 'Photos' : 'Local Expert'}
          previewImages={previewImagesSrc}
          previewText={isPhotosForm ? 'Photos Preview' : 'Story Preview'}
          title={title}
          userName={userName}
        />
      </div>
    </TemplateWrapper>
  );
}
