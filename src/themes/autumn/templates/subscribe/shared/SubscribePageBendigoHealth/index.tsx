import clsx from 'clsx';
import { useCallback, useEffect, useRef, useState } from 'react';

import PianoHook from 'components/Piano/PianoHook';
import { useAppSelector } from 'store/hooks';
import SubscriptionFooter from 'themes/autumn/components/footer/SubscriptionFooter';
import Container from 'themes/autumn/components/generic/Container';
import Link from 'themes/autumn/components/generic/Link';
import Skeleton from 'themes/autumn/components/generic/Skeleton';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import { SimplePeriodUnit } from 'types/Piano';
import { redirectToRegister } from 'util/auth';
import { getTrackingQueryParams } from 'util/tracking';

import FaqContainerBendigoHealth from '../../common/FaqContainerBendigoHealth';

import Check from './check.svg';

import type React from 'react';

function Checkmark({ children }: React.PropsWithChildren) {
  return (
    <div className="flex flex-row items-center justify-start gap-x-6 border-b border-gray-200 py-3 text-sm leading-none first:pt-0 last:border-b-0 last:pb-0">
      <div className="shrink-0">
        <Check />
      </div>
      <div>{children}</div>
    </div>
  );
}

function SubscribePageBendigoHealth(): React.ReactElement | null {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const terms = useAppSelector((state) => state.piano.terms);
  const offerId = useAppSelector((state) => state.piano.offerId);
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  const clusterSites = useAppSelector((state) => state.cluster.sites);
  const [showCluster, setShowCluster] = useState(false);
  const clusterInfoRef = useRef<HTMLDivElement | null>(null);
  const showClusterButtonRef = useRef<HTMLButtonElement | null>(null);

  const hasClusterSites = clusterSites.length > 0;
  const isLoading = terms.length !== 2;
  const originalTerm = terms[1];
  const term = terms[0];

  const onClickSubscribe = useCallback(() => {
    if (!term || !offerId) {
      return;
    }

    const searchQueryArgs = new URLSearchParams({
      ...getTrackingQueryParams(),
      offerId,
      source: 'bendigo-health',
      termId: term.termId,
    });
    redirectToRegister(`/payment/?${searchQueryArgs.toString()}`);
  }, [term, offerId]);

  useEffect(() => {
    window.addEventListener('click', (e): void => {
      const clusterInfo = clusterInfoRef.current;
      const showClusterButton = showClusterButtonRef.current;
      if (!clusterInfo || !showClusterButton || !(e.target instanceof Node)) {
        return;
      }

      if (
        !clusterInfo.contains(e.target) &&
        !showClusterButton.contains(e.target)
      ) {
        setShowCluster(false);
      }
    });
  }, []);

  if (!pianoFeature.enabled) {
    return null;
  }

  return (
    <TemplateWrapper
      className="font-inter text-gray-900"
      footer={<SubscriptionFooter showLinksOnDesktop />}
      free
      hideSmartBanner
      nav={<SubscriptionNav showContact={false} showHelp={false} showLogin />}
    >
      <div className="relative z-0 aspect-[700/400] w-full bg-blue-950 text-white md:aspect-[3000/650]">
        <picture className="h-auto w-full">
          <source
            media="(min-width:768px)"
            srcSet={`${staticUrl}images/bendigohealth/desktop.jpg`}
          />
          <img
            alt="Bendigo Health Subscription Offer"
            className="w-full"
            src={`${staticUrl}images/bendigohealth/mobile.jpg`}
          />
        </picture>
      </div>
      <div className="mx-auto w-full">
        <PianoHook
          className="hidden h-0"
          id="subscription-select-wrap-fullpage"
        />
        <div className="flex w-full flex-col items-center bg-gray-100 px-4 pb-18 lg:pb-14">
          <div className="z-10 -mt-10 mb-7 flex flex-col-reverse items-center gap-y-7 rounded-2xl bg-white pb-11 pt-5.5 shadow-md md:-mt-4 lg:-mt-7 lg:mb-9 lg:flex-row lg:py-16 xl:-mt-8">
            <div className="flex flex-col gap-y-5.5 px-6 lg:pl-24 lg:pr-12">
              <div className="hidden font-medium lg:block">Digital Access</div>
              <div className="flex flex-col">
                <Checkmark>All articles from our website & app</Checkmark>
                <Checkmark>
                  The digital version of Today&rsquo;s Paper
                </Checkmark>
                <Checkmark>Crosswords, Sudoku & Trivia</Checkmark>
                <Checkmark>
                  <b>Bonus</b> access to{' '}
                  <div className="relative inline-block">
                    <button
                      aria-controls="cluster-sites-menu"
                      aria-expanded={showCluster}
                      aria-haspopup="listbox"
                      className={clsx('cursor-pointer whitespace-nowrap', {
                        underline: hasClusterSites,
                      })}
                      onClick={() => {
                        if (hasClusterSites) {
                          setShowCluster(!showCluster);
                        }
                      }}
                      ref={showClusterButtonRef}
                      type="button"
                    >
                      other news sites
                      <span className="hidden lg:inline"> in the region</span>
                    </button>
                    {showCluster && hasClusterSites && (
                      <div
                        className="absolute left-1/2 top-full z-40 flex -translate-x-1/2"
                        ref={clusterInfoRef}
                      >
                        <ul
                          className="mt-2 cursor-pointer list-inside list-disc flex-col gap-y-1 whitespace-nowrap rounded border border-gray-300 bg-white py-3 pl-4 pr-5 text-xs text-gray-600 shadow-md"
                          id="cluster-sites-menu"
                        >
                          {clusterSites.map((site) => (
                            <li key={site.name}>{site.name}</li>
                          ))}
                        </ul>
                        <div className="absolute left-1/2 top-1 z-50 -ml-1 size-2 rotate-45 border-l border-t border-gray-300 bg-white" />
                      </div>
                    )}
                  </div>
                </Checkmark>
                <Checkmark>Environmentally friendly</Checkmark>
              </div>
            </div>
            <div className="hidden w-px self-stretch bg-gray-200 lg:block" />
            <div className="flex flex-col items-center justify-center lg:py-6 lg:pl-20 lg:pr-28">
              <Skeleton className="mb-3 lg:mb-5" showContent={!isLoading}>
                <div className="flex h-5 items-center justify-center rounded-full bg-orange-600 px-3.5 text-xs font-medium uppercase leading-none text-white">
                  Save{' '}
                  {term?.finalPlan.calculate.savingsPercentageString(
                    originalTerm?.finalPlan,
                  ) ?? 30}
                  %
                </div>
              </Skeleton>
              <Skeleton
                className="mb-6 min-w-mrec text-center lg:mb-3 lg:min-w-[270px]"
                showContent={!isLoading}
              >
                <div className="font-medium">Bendigo Health Offer</div>
              </Skeleton>
              <Skeleton
                className="mb-3 min-w-mrec text-center lg:min-w-[270px]"
                showContent={!isLoading}
              >
                <div className="text-xl leading-6 lg:text-lg">
                  <span className="text-gray-400 line-through">
                    $
                    {originalTerm?.finalPlan.format(({ price }) => price, {
                      convertPeriodToSingleFactor: true,
                      convertPeriodToUnit: SimplePeriodUnit.YEARS,
                      truncateUnnecessaryDecimals: true,
                    })}
                  </span>{' '}
                  <span className="font-semibold">
                    $
                    {term?.finalPlan.format(({ price }) => price, {
                      convertPeriodToSingleFactor: true,
                      convertPeriodToUnit: SimplePeriodUnit.YEARS,
                      truncateUnnecessaryDecimals: true,
                    })}
                    /year
                  </span>
                </div>
              </Skeleton>
              <Skeleton className="mb-6 lg:mb-5" showContent={!isLoading}>
                <div className="text-sm font-medium leading-none text-green-600">
                  Save $
                  {term?.finalPlan.calculate.savingsString(originalTerm) ??
                    '56.00'}{' '}
                  per year
                </div>
              </Skeleton>
              <Skeleton className="mx-5 lg:mx-0" showContent={!isLoading}>
                <button
                  className="flex h-11 w-[300px] items-center justify-center rounded bg-gray-950 text-sm leading-none text-white hover:bg-gray-900 lg:w-[270px]"
                  onClick={onClickSubscribe}
                  type="button"
                >
                  Subscribe
                </button>
              </Skeleton>
            </div>
          </div>
          <div
            className={clsx(
              'max-w-sm px-4 text-center text-xs text-gray-500 transition-opacity duration-500',
              {
                'opacity-0': isLoading,
                'opacity-100': !isLoading,
              },
            )}
          >
            Digital Subscription offer costs $
            {term?.finalPlan.format(({ price }) => price, {
              convertPeriodToSingleFactor: true,
              convertPeriodToUnit: SimplePeriodUnit.YEARS,
            })}{' '}
            (min. cost) upfront, then renewed annually. Renewals occur unless
            canceled in accordance with the full Terms and Conditions. Not in
            conjunction with any other offer. See{' '}
            <Link href="/about-us/terms-conditions/digital-subscription/">
              Terms & Conditions
            </Link>{' '}
            for full subscription terms.
          </div>
        </div>
      </div>
      <div className="mx-auto mt-12 max-w-[940px] lg:mt-16">
        <Container>
          <FaqContainerBendigoHealth />
        </Container>
      </div>
    </TemplateWrapper>
  );
}

export default SubscribePageBendigoHealth;
