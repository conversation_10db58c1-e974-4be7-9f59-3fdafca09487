import { useAppSelector } from 'store/hooks';
import SubscriptionFooter from 'themes/autumn/components/footer/SubscriptionFooter';
import Container from 'themes/autumn/components/generic/Container';
import Link from 'themes/autumn/components/generic/Link';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';

import ALPALogo from '../SubscribePageALPA/svgs/alpa-logo.svg';
import { ALPA_DISCOUNT } from '../constants';

import FarmOnlineLogo from './svgs/farm-online.svg';
import FarmWeeklyLogo from './svgs/farm-weekly.svg';
import NorthQueenslandRegisterLogo from './svgs/north-queensland-register.svg';
import QueenslandCountryLifeLogo from './svgs/queensland-country-life.svg';
import StockJournalLogo from './svgs/stock-journal.svg';
import StockLandLogo from './svgs/stock-land.svg';
import The<PERSON>and<PERSON>ogo from './svgs/the-land.svg';

import type React from 'react';

interface ALPAButtonProps {
  children: React.ReactNode;
  href: string;
}

function ALPAButton({ children, href }: ALPAButtonProps) {
  return (
    <Link
      className="flex aspect-[168/72] items-center justify-center rounded-md border border-gray-200 px-5 shadow-sm hover:shadow-md md:h-18 [&>svg]:h-auto [&>svg]:w-full"
      href={href}
      noStyle
    >
      {children}
    </Link>
  );
}

function LandingPageALPA(): React.ReactElement | null {
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);

  return (
    <TemplateWrapper
      footer={
        <SubscriptionFooter hidePaymentMethod showLinksOnDesktop withBorders />
      }
      free
      hideSmartBanner
      nav={<SubscriptionNav showContact={false} showHelp={false} showLogin />}
    >
      <div className="relative h-[294px] w-full text-white md:h-[340px]">
        <div
          className="absolute inset-0 z-0 bg-cover bg-center md:hidden"
          style={{
            backgroundImage: `url('${staticUrl}images/alpa/mobile.jpg')`,
          }}
        />
        <div
          className="absolute inset-0 z-0 hidden bg-cover bg-center md:block"
          style={{
            backgroundImage: `url('${staticUrl}images/alpa/desktop.jpg')`,
          }}
        />
        <div className="relative z-10 flex h-full flex-col justify-center md:items-center">
          <div className="absolute left-1/2 top-1/2 hidden -translate-y-1/2 translate-x-[376px] xl:block">
            <ALPALogo />
          </div>
          <div className="px-4 font-merriweather text-2xl font-bold leading-8 md:mt-0 md:text-4xl md:leading-12">
            The news farmers trust to stay informed
          </div>
          <div className="mt-5 px-4 text-base font-semibold leading-6 md:text-lg md:leading-5">
            Save up to {ALPA_DISCOUNT}% with corporate digital subscriptions
            for ALPA members.
          </div>
          <div className="mt-5 flex flex-row items-center gap-x-2.5 px-4">
            <img
              alt="ALPA"
              className="h-[56px] w-[106px] md:h-[70px] md:w-120"
              src={`${staticUrl}images/alpa/alpa.png`}
            />
            <span className="text-lg font-semibold uppercase md:text-2xl">
              Member Offer
            </span>
          </div>
        </div>
      </div>
      <Container>
        <div className="mb-44 mt-8 flex flex-col items-center gap-y-8 md:mt-16 md:gap-y-11">
          <div className="max-w-[380px] text-center text-xl font-semibold md:max-w-full md:text-left">
            For a better experience, please select your preferred publication
            below
          </div>
          <div className="grid w-full grid-cols-2 gap-x-2 gap-y-2.5 md:max-w-[536px] md:grid-cols-3 md:gap-x-4 md:gap-y-6">
            <ALPAButton href="https://www.queenslandcountrylife.com.au/alpa/">
              <QueenslandCountryLifeLogo />
            </ALPAButton>
            <ALPAButton href="https://www.theland.com.au/alpa/">
              <TheLandLogo />
            </ALPAButton>
            <ALPAButton href="https://www.farmweekly.com.au/alpa/">
              <FarmWeeklyLogo />
            </ALPAButton>
            <ALPAButton href="https://www.stockandland.com.au/alpa/">
              <StockLandLogo />
            </ALPAButton>
            <ALPAButton href="https://www.stockjournal.com.au/alpa/">
              <StockJournalLogo />
            </ALPAButton>
            <ALPAButton href="https://www.farmonline.com.au/alpa/">
              <FarmOnlineLogo />
            </ALPAButton>
            <ALPAButton href="https://www.northqueenslandregister.com.au/alpa/">
              <NorthQueenslandRegisterLogo />
            </ALPAButton>
          </div>
        </div>
      </Container>
    </TemplateWrapper>
  );
}

export default LandingPageALPA;
