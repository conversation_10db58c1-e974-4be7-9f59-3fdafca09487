import clsx from 'clsx';
import { useCallback } from 'react';

import PianoHook from 'components/Piano/PianoHook';
import { useAppSelector } from 'store/hooks';
import SubscriptionFooter from 'themes/autumn/components/footer/SubscriptionFooter';
import Link from 'themes/autumn/components/generic/Link';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import { setGtmDataLayer } from 'util/gtm';

import Container from '../../../../components/generic/Container';
import FaqContainerALPA from '../../common/FaqContainerALPA';
import { ALPA_DISCOUNT } from '../constants';

import Mastheads from './Mastheads';
import SubscribeCard from './SubscribeCard';
import styles from './index.module.css';
import ALPALogo from './svgs/alpa-logo.svg';

import type { SimpleTerm } from 'types/Piano';

function SubscribePageALPA(): React.ReactElement | null {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const terms = useAppSelector((state) => state.piano.terms);
  const offerId = useAppSelector((state) => state.piano.offerId);
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);

  const onNeedSubsClick = useCallback(() => {
    setGtmDataLayer({
      data: {
        label: 'need_15_subscriptions',
      },
      event: 'quicklink_clicks',
    });
  }, []);

  if (!pianoFeature.enabled) {
    return null;
  }

  const fullPriceTerm = terms[terms.length - 1];
  const isLoading = terms.length !== 5;

  return (
    <TemplateWrapper
      footer={<SubscriptionFooter />}
      free
      hideSmartBanner
      nav={<SubscriptionNav showContact={false} showHelp={false} showLogin />}
    >
      <div className="relative h-[412px] w-full text-white md:h-[340px]">
        <div
          className="absolute inset-0 z-0 bg-cover bg-center md:hidden"
          style={{
            backgroundImage: `url('${staticUrl}images/alpa/mobile.jpg')`,
          }}
        />
        <div
          className="absolute inset-0 z-0 hidden bg-cover bg-center md:block"
          style={{
            backgroundImage: `url('${staticUrl}images/alpa/desktop.jpg')`,
          }}
        />
        <div className="relative z-10 flex flex-col md:items-center">
          <div className="absolute left-1/2 top-1/2 hidden -translate-y-1/2 translate-x-[376px] xl:block">
            <ALPALogo />
          </div>
          <div className="mt-18 px-4 font-merriweather text-2xl font-bold leading-8 md:mt-11 md:text-4xl md:leading-12">
            The news farmers trust to stay informed
          </div>
          <div className="mt-5 px-4 text-base font-semibold leading-6 md:text-lg md:leading-5">
            Save up to {ALPA_DISCOUNT}% with corporate digital subscriptions
            for ALPA members.
          </div>
          <div className="mt-5 flex flex-row items-center gap-x-2.5 px-4">
            <img
              alt="ALPA"
              className="h-[56px] w-[106px] md:h-[70px] md:w-120"
              src={`${staticUrl}images/alpa/alpa.png`}
            />
            <span className="text-lg font-semibold uppercase md:text-2xl">
              Member Offer
            </span>
          </div>
          <div className="mt-14 max-w-full overflow-hidden md:mt-9">
            <div
              className={clsx(
                'flex h-8 w-fit flex-row items-center gap-x-6 pl-6 lg:gap-x-3 xl:gap-x-6 [&>svg]:shrink-0',
                styles.mastheads,
              )}
            >
              <Mastheads />
              <Mastheads hiddenOnDesktop />
            </div>
          </div>
        </div>
      </div>
      <div className="mx-auto max-w-[940px]">
        <PianoHook
          className="hidden h-0"
          id="subscription-select-wrap-fullpage"
        />
        <Container>
          <div className="mt-13 flex flex-col items-center gap-5 md:grid md:grid-cols-2 md:justify-center">
            {(isLoading
              ? new Array(4).fill(undefined)
              : terms.slice(0, -1)
            ).map((term: SimpleTerm | undefined, idx) => (
              <SubscribeCard
                fullPriceTerm={fullPriceTerm}
                key={term?.termId ?? idx}
                offerId={offerId}
                term={term}
              />
            ))}
          </div>
          <div className="flex flex-row items-center justify-center">
            <Link
              className="mx-auto mt-10 font-inter text-base font-semibold text-gray-900 underline hover:opacity-50"
              href="mailto:<EMAIL>?subject=ALPA Large Group Membership"
              noStyle
              onClick={onNeedSubsClick}
            >
              Need 15+ Subscriptions?
            </Link>
          </div>
        </Container>
      </div>
      <div className="mx-auto mt-6 max-w-[940px]">
        <Container>
          <FaqContainerALPA />
        </Container>
      </div>
    </TemplateWrapper>
  );
}

export default SubscribePageALPA;
