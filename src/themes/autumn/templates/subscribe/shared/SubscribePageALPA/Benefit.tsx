interface BenefitProps {
  children: React.ReactNode;
}

export default function Benefit({ children }: BenefitProps) {
  return (
    <div className="flex w-full max-w-344 flex-row gap-x-3.5">
      <svg className="shrink-0" fill="none" height="15" width="19">
        <path
          d="M6.553 10.888 2.76 7.095 1.468 8.378l5.085 5.084L17.468 2.547l-1.282-1.282-9.633 9.623Z"
          fill="#111827"
          stroke="#111827"
        />
      </svg>
      <div>{children}</div>
    </div>
  );
}
