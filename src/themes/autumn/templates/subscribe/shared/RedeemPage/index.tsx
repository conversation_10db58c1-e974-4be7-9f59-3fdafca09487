import { useEffect } from 'react';

import { useAppSelector } from 'store/hooks';
import LoadingSpinner from 'themes/autumn/components/generic/LoadingSpinner';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import { redirectToRegister } from 'util/auth';

export default function RedeemPage() {
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const user = useAppSelector((state) => state.piano.user);

  useEffect(() => {
    if (pianoInitialized && !user) {
      redirectToRegister(window.location.href);
    }
  }, [user, pianoInitialized]);

  return (
    <TemplateWrapper
      footer={null}
      free
      hideSmartBanner
      nav={
        <SubscriptionNav
          enableLogoClick={false}
          showContact={false}
          showHelp={false}
          showLogin={false}
          showPageName={false}
        />
      }
      showGutterAd={false}
      showNavigationAd={false}
      showOutages={false}
    >
      <div className="mt-12 flex flex-col items-center justify-start gap-y-48 px-4">
        <div className="w-full font-inter text-xl font-semibold leading-5 text-gray-900 md:text-center">
          Please wait while we finish <br className="md:hidden" />
          this process
        </div>
        <div className="size-16">
          <LoadingSpinner />
        </div>
      </div>
    </TemplateWrapper>
  );
}
