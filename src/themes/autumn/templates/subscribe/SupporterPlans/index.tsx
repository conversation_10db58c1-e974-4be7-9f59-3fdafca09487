import clsx from 'clsx';
import { useCallback, useState } from 'react';

import PianoHook from 'components/Piano/PianoHook';
import { useAppSelector } from 'store/hooks';
import SubscriptionFooter from 'themes/autumn/components/footer/SubscriptionFooter';
import Container from 'themes/autumn/components/generic/Container';
import Skeleton from 'themes/autumn/components/generic/Skeleton';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import { redirectToRegister } from 'util/auth';
import { getTrackingQueryParams } from 'util/tracking';

import FaqContainerACM from '../common/FaqContainerACM';

import Benefits from './Benefits';
import Option from './Option';
import Terms from './Terms';
import PaymentMethods from './icons/payment-methods.svg';

import type React from 'react';

const PLANS = [
  {
    subtitle: 'For the news you rely on',
    title: 'Supporter',
  },
  {
    subtitle: 'For the future you believe in',
    title: 'Partner',
  },
  {
    subtitle: 'For the community you love',
    title: 'Champion',
  },
];

function SubscribePageBendigoHealth(): React.ReactElement | null {
  const domain = useAppSelector((state) => state.conf.domain);
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const terms = useAppSelector((state) => state.piano.terms);
  const offerId = useAppSelector((state) => state.piano.offerId);
  const staticSiteUrl = useAppSelector(
    (state) => state.settings.staticSiteUrl,
  );
  const [selectedTerm, setSelectedTerm] = useState<number>(0);

  const isLoading = terms.length !== 3;

  const onClickSubscribe = useCallback(() => {
    const term = terms[selectedTerm];
    if (!term || !offerId) {
      return;
    }

    const searchQueryArgs = new URLSearchParams({
      ...getTrackingQueryParams(),
      offerId,
      termId: term.termId,
    });
    redirectToRegister(`/payment/?${searchQueryArgs.toString()}`);
  }, [selectedTerm, terms, offerId]);

  if (!pianoFeature.enabled) {
    return null;
  }

  return (
    <TemplateWrapper
      className="font-inter text-gray-900"
      footer={<SubscriptionFooter showLinksOnDesktop />}
      free
      hideSmartBanner
      nav={<SubscriptionNav showContact={false} showHelp={false} showLogin />}
    >
      <div className="relative z-0 h-[241px] w-full bg-white text-white md:aspect-[3000/762] md:h-auto">
        <picture className={clsx('h-full object-cover md:h-auto md:w-full')}>
          <source
            media="(min-width:768px)"
            srcSet={`${staticSiteUrl}images/supporter/desktop.jpg`}
          />
          <img
            alt="Support the news"
            className="h-full object-cover md:w-full"
            src={`${staticSiteUrl}images/supporter/mobile.jpg`}
          />
        </picture>
      </div>
      <div className="mx-auto w-full">
        <PianoHook
          className="hidden h-0"
          id="subscription-select-wrap-fullpage"
        />
        <div className="w-full bg-gray-100">
          <div className="mx-auto flex w-full max-w-344 flex-col gap-6 py-8 md:max-w-full md:gap-7 md:pb-28 md:pt-16">
            <div className="flex flex-col gap-3 md:gap-4">
              <div className="w-full text-center text-sm md:text-base">
                Enjoy convenient access to the news that matters most in{' '}
                {domain.includes('gazette')
                  ? 'the Mountains'
                  : 'St George & the Shire'}
                .
                <br className="hidden md:block" />
                Your support helps keep our community strong, informed and
                connected.
              </div>
              <div className="mt-3 w-full text-center text-sm font-semibold leading-6">
                Select your level of support
              </div>
            </div>
            <div className="flex flex-col justify-center gap-3 md:flex-row md:gap-4">
              {PLANS.map(({ subtitle, title }, idx) => (
                <Option
                  key={title}
                  onClick={() => setSelectedTerm(idx)}
                  selected={selectedTerm === idx}
                  subtitle={subtitle}
                  term={terms[idx]}
                  title={title}
                />
              ))}
            </div>
            <Skeleton
              className="-mt-1 md:mx-auto md:mt-0"
              showContent={!isLoading}
            >
              <button
                className="flex h-12 w-full items-center justify-center rounded-lg bg-gray-950 text-base font-semibold leading-none text-white hover:bg-gray-900 md:w-[312px] md:text-lg"
                onClick={onClickSubscribe}
                type="button"
              >
                Subscribe
              </button>
            </Skeleton>
            <div className="text-center text-sm font-semibold leading-6 underline">
              No lock in contract
            </div>
            <div className="mx-auto">
              <PaymentMethods />
            </div>
            <div className="mx-auto hidden w-full max-w-screen-md text-center text-xs leading-normal text-gray-500 md:block">
              <Terms />
            </div>
          </div>
        </div>
        <div className="mx-auto w-full max-w-344 py-10 md:max-w-6xl md:px-7 md:py-16">
          <Benefits />
        </div>
        <div className="mx-auto w-full max-w-344 text-center text-xs leading-normal text-gray-500 md:hidden">
          <Terms />
        </div>
      </div>
      <div className="mx-auto mt-12 max-w-[940px] lg:mt-16">
        <Container>
          <FaqContainerACM />
        </Container>
      </div>
    </TemplateWrapper>
  );
}

export default SubscribePageBendigoHealth;
