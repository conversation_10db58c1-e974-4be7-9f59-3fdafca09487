import { useAppSelector } from 'store/hooks';

import Breaking from './icons/breaking.svg';
import Crossword from './icons/crossword.svg';
import Local from './icons/local.svg';
import Newspaper from './icons/newspaper.svg';

import type React from 'react';

interface BenefitProps {
  children: React.ReactNode;
  icon: React.ReactNode;
  name: string;
}

function Benefit({ children, icon, name }: BenefitProps) {
  return (
    <div className="flex items-center gap-x-6">
      <div className="flex size-8 shrink-0 items-center justify-center">
        {icon}
      </div>
      <div className="flex grow flex-col gap-y-2">
        <div className="text-lg font-medium leading-[22px]">{name}</div>
        <div className="text-sm leading-[17px] md:text-base md:leading-[20px]">
          {children}
        </div>
      </div>
    </div>
  );
}

export default function Benefits() {
  const name = useAppSelector((state) => state.conf.name);
  const staticSiteUrl = useAppSelector(
    (state) => state.settings.staticSiteUrl,
  );

  const mastheadIcon = (
    <img alt={name} src={`${staticSiteUrl}images/supporter/masthead.svg`} />
  );

  return (
    <div className="grid grid-cols-1 gap-x-12 gap-y-8 md:grid-cols-[auto_1fr] md:gap-y-11">
      <div className="col-span-full font-merriweather text-2xl font-bold leading-10 md:text-3xl md:leading-none">
        Benefits for all subscribers
      </div>
      <div className="flex flex-col gap-8 md:order-2 md:gap-10">
        <Benefit icon={mastheadIcon} name="Unlimited Access">
          All articles on any device, anywhere, anytime
        </Benefit>
        <Benefit icon={<Breaking />} name="Breaking news alerts">
          Headlines direct to your inbox
        </Benefit>
        <Benefit icon={<Newspaper />} name={'Today\u2019s Paper'}>
          The digital version of each print edition
        </Benefit>
        <Benefit icon={<Crossword />} name="Puzzles & Games">
          Interactive Crosswords, Sudoku and Trivia
        </Benefit>
        <Benefit icon={<Local />} name="Supporting local news">
          Keeping your community strong, informed and connected
        </Benefit>
      </div>
      <img
        alt="Devices"
        className="w-[339px] shrink-0 md:order-1 lg:w-[470px]"
        src={`${staticSiteUrl}images/supporter/devices.jpg`}
      />
    </div>
  );
}
