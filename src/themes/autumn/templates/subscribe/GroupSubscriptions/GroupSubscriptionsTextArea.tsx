import type { GroupSubscriptionsFormData } from './GroupSubscriptionsFormData';
import type {
  FieldErrors,
  RegisterOptions,
  UseFormRegister,
} from 'react-hook-form';

interface GroupTextAreaProps {
  errors: FieldErrors<GroupSubscriptionsFormData>;
  id: keyof GroupSubscriptionsFormData;
  label: string;
  options: RegisterOptions<GroupSubscriptionsFormData>;
  register: UseFormRegister<GroupSubscriptionsFormData>;
}

export default function GroupSubscriptionsTextArea({
  errors,
  id,
  label,
  options,
  register,
}: GroupTextAreaProps): React.ReactElement {
  return (
    <div className="mb-2.5 md:w-full md:px-2">
      <label htmlFor={id}>{label}</label>
      <textarea
        autoComplete={id}
        className="w-full rounded"
        id={id}
        maxLength={1000}
        // eslint-disable-next-line react/jsx-props-no-spreading
        {...register(id, options)}
      />
      {errors[id] && <div className="text-red-500">{errors[id]?.message}</div>}
    </div>
  );
}
