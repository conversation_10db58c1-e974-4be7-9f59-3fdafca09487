/* eslint-disable sort-keys */
import FaqContainerACM from 'themes/autumn/templates/subscribe/common/FaqContainerACM';

import type {
  Faqs,
  questions,
} from 'themes/autumn/templates/subscribe/common/faqContainerAcmConstants';

type Faq = Faqs[number];
type Question = (typeof questions)[number]['question'];

// Replace the default answers to these questions with ALPA specific ones
const modifiedQuestions: Partial<Record<Question, string | Faq>> = {
  'What do I get if I subscribe?':
    'As a subscriber you will gain unlimited access to our content, digital version of This Week’s Paper, breaking-news alerts and more. As an ALPA member you get access for you and your employees through especially discounted group rates.',
  'Can I pay with a Debit/Credit Card?':
    'Yes, we accept payment from Visa and Mastercard. We also support PayPal and bank direct debit.',
  'Why should I pay for local news?': {
    question: 'Why should I pay for agricultural news?',
    answer: (
      <>
        Our agricultural journalism is valued by the communities we serve. Our
        newspapers have been keeping people informed, entertained and connected
        for decades.
      </>
    ),
  },
  'How often will I get billed?':
    'ALPA members get exclusive discounted plans that are billed annually from the sign up date.',
};

const removedQuestions: Question[] = [
  'How many devices can I access my subscription on?',
];

export default function FaqContainerALPA(): React.ReactElement {
  return (
    <FaqContainerACM
      modifyFaqs={(questions) =>
        questions
          .filter(({ question }) => !removedQuestions.includes(question))
          .map((faq) => {
            const { question } = faq;
            const modifiedAnswer = modifiedQuestions[question];
            if (modifiedAnswer) {
              return typeof modifiedAnswer === 'string'
                ? { answer: <>{modifiedAnswer}</>, question }
                : modifiedAnswer;
            }
            return faq;
          })
      }
    />
  );
}
