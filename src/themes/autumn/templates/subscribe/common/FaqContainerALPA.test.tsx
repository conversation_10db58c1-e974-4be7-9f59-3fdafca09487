import { render } from '@testing-library/react';
import { Provider } from 'react-redux';

import { createStore } from 'store/store';
import FaqContainerALPA from 'themes/autumn/templates/subscribe/common/FaqContainerALPA';

describe('<FaqContainerALPA />', () => {
  it('renders', () => {
    const store = createStore();

    const { container } = render(
      <Provider store={store}>
        <FaqContainerALPA />
      </Provider>,
    );

    expect(container).toMatchSnapshot();
  });

  it('displays ALPA-specific FAQ content', () => {
    const store = createStore();

    const { getByText, queryByText } = render(
      <Provider store={store}>
        <FaqContainerALPA />
      </Provider>,
    );

    const faqBox = getByText('Why should I pay for agricultural news?')
      .parentElement?.parentElement;
    const answer = getByText('Our agricultural journalism is', {
      exact: false,
    }).parentElement;

    expect(faqBox).toBeInTheDocument();
    expect(faqBox).toContainElement(answer);
    expect(
      queryByText('How many devices can I access my subscription on?'),
    ).not.toBeInTheDocument();
  });
});
