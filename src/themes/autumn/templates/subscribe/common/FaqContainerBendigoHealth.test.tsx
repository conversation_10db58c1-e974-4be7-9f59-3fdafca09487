import { render } from '@testing-library/react';
import { Provider } from 'react-redux';

import { createStore } from 'store/store';
import FaqContainerBendigoHealth from 'themes/autumn/templates/subscribe/common/FaqContainerBendigoHealth';

describe('<FaqContainerBendigoHealth />', () => {
  it('renders', () => {
    const store = createStore();

    const { container } = render(
      <Provider store={store}>
        <FaqContainerBendigoHealth />
      </Provider>,
    );

    expect(container).toMatchSnapshot();
  });

  it('displays BendigoHealth-specific FAQ content', () => {
    const store = createStore();

    const { getByText, queryByText } = render(
      <Provider store={store}>
        <FaqContainerBendigoHealth />
      </Provider>,
    );

    const faqBox = getByText('Why should I pay for news?').parentElement
      ?.parentElement;
    const answer = getByText(
      'Our journalism is valued by the communities we serve.',
      {
        exact: false,
      },
    ).parentElement;

    expect(faqBox).toBeInTheDocument();
    expect(faqBox).toContainElement(answer);
    expect(
      queryByText('How many devices can I access my subscription on?'),
    ).not.toBeInTheDocument();
  });
});
