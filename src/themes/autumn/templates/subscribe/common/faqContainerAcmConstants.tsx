import Link from 'themes/autumn/components/generic/Link';
import { sendToGtm } from 'util/gtm';

export type Faqs = {
  answer: React.JSX.Element | ((hasPuzzles: boolean) => React.JSX.Element);
  question: string;
}[];

/* eslint-disable sort-keys */
export const questions = [
  {
    question: 'What do I get if I subscribe?',
    answer: (hasPuzzles) => (
      <>
        As a subscriber you will gain unlimited access to our content,
        {hasPuzzles && ' puzzles (updated daily),'} digital version of
        Today&apos;s Paper, breaking-news alerts and more.
      </>
    ),
  },
  {
    question: 'Can I pay with a Debit/Credit Card?',
    answer: <>Yes, we accept payment from Visa and Mastercard.</>,
  },
  {
    question: 'Why should I pay for local news?',
    answer: (
      <>
        Our local journalism is valued by the communities we serve. Our
        newspapers have been keeping people informed, entertained and connected
        for decades.
      </>
    ),
  },
  {
    question: 'How often will I get billed?',
    answer: (
      <>
        Depending on the subscription you chose, we offer plans for monthly and
        yearly options.
      </>
    ),
  },
  {
    question: 'How do I get help if I need it?',
    answer: (
      <>
        You can submit a support ticket by clicking on the Green (?) button on
        the bottom right hand corner. You can also call{' '}
        <Link href="tel:1300131095">1300 131 095</Link> or email{' '}
        <Link
          className="break-words"
          href="mailto:<EMAIL>"
        >
          <EMAIL>
        </Link>
        . Our team operate between 9am-5pm Mon to Fri AEST.
      </>
    ),
  },
  {
    question: 'What devices can I use with my subscription?',
    answer: (
      <>
        We support devices of all sizes including most phones, tablets, laptops
        and computers.
      </>
    ),
  },
  {
    question: 'How do I change or cancel my subscription?',
    answer: (
      <>
        Call Customer Support at{' '}
        <Link href="tel:1300131095">1300 131 095</Link>. Our team operate
        between 9am-5pm Mon to Fri AEST.
        <br />
        <br />
        For further details on refunds, please refer to Section 6 of the
        <span> </span>
        <Link
          href="/about-us/terms-conditions/digital-subscription/"
          onClick={() => {
            sendToGtm({
              action: 'subscribe_page_stage',
              label: 't&cs_faq_link_click',
            });
          }}
        >
          Terms & Conditions - Digital
        </Link>
        .
      </>
    ),
  },
  {
    question: 'How many devices can I access my subscription on?',
    answer: (
      <>
        You can access your subscription across four devices at the same time.
      </>
    ),
  },
] as const satisfies Faqs;
/* eslint-enable sort-keys */
