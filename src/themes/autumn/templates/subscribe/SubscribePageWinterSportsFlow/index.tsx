import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';

import Container from '../../../components/generic/Container';
import FaqContainerACM from '../common/FaqContainerACM';
import FlowCheckout from '../common/FlowCheckout';
import Compare from '../common/FlowCheckout/Compare';
import OptionCard, { Checkmark } from '../common/FlowCheckout/OptionCard';
import OtherOffers from '../common/FlowCheckout/OtherOffers';
import {
  Period,
  Plan,
  calculateTermIndexACM,
  useTermHelpers,
} from '../common/FlowCheckout/util';

import type { PlanMeta } from '../common/FlowCheckout/util';

function SubscribePageWinterSportFlow(): React.ReactElement | null {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  const terms = useAppSelector((state) => state.piano.terms);
  const termHelpers = useTermHelpers(
    calculateTermIndexACM,
    terms,
    [Plan.BASIC, Plan.PREMIUM],
    [Period.FOUR_WEEKLY, Period.YEARLY],
  );
  const {
    getHasTrial,
    getOriginalPricing,
    getPrice,
    getTrialPeriod,
    getWeeklyPrice,
  } = termHelpers;

  const planMeta: PlanMeta = {
    [Plan.BASIC]: {
      description: (
        <>
          <Checkmark bold>All articles from the website</Checkmark>
          <Checkmark bold>Subscriber exclusive app</Checkmark>
          <Checkmark bold>
            All articles from the other regional websites in your area
          </Checkmark>
        </>
      ),
      name: 'Website & App',
      tag: 'Basic Digital',
    },
    [Plan.PREMIUM]: {
      description: (
        <>
          <Checkmark bold>Everything in basic digital</Checkmark>
          <Checkmark bold>Digital version of Today&apos;s Paper</Checkmark>
          <Checkmark bold>Premium puzzles access</Checkmark>
        </>
      ),
      name: 'Unlimited Digital',
      primary: {
        border: false,
        button: true,
      },
      tag: 'Premium Digital',
    },
  };

  const termsConditions = (
    // eslint-disable-next-line @stylistic/max-len
    <div className="mx-auto my-0 w-4/5 border-b border-solid border-gray-200 px-0 pb-11 pt-2 text-center font-inter text-xs text-gray-500 md:border-0 md:border-none md:border-transparent md:pb-0">
      <sup>*</sup>Monthly Basic Digital Subscription offer costs{' '}
      {getPrice(Plan.BASIC, Period.FOUR_WEEKLY)} (min. cost) for the first 6
      weeks, then {getOriginalPricing(Plan.BASIC, Period.FOUR_WEEKLY)} billed
      monthly. Monthly Premium Digital Subscription offer costs{' '}
      {getPrice(Plan.PREMIUM, Period.FOUR_WEEKLY)} (min. cost) for the first 6
      weeks, then {getOriginalPricing(Plan.PREMIUM, Period.FOUR_WEEKLY)} billed
      monthly. Basic Annual Digital Subscription offer costs{' '}
      {getPrice(Plan.BASIC, Period.YEARLY)} upfront (min. cost) for the first
      12 months, then {getOriginalPricing(Plan.PREMIUM, Period.YEARLY)} billed
      annually. Premium Annual Digital Subscription offer costs{' '}
      {getPrice(Plan.PREMIUM, Period.YEARLY)} upfront (min. cost) for the first
      12 months, then {getOriginalPricing(Plan.PREMIUM, Period.YEARLY)} billed
      annually. The 20% off for annual subscriptions is calculated off the
      standard rate per week over 12 months minus 20%. Annual Digital
      Subscription will receive two additional months on top of the standard 12
      months for the first term only - Bonus weeks will not be applied to
      renewals. Renewals occur unless cancelled in accordance with the full
      Terms and Conditions. Not in conjunction with any other offer. New
      customers only. Offer ends May 11, 2025. See{' '}
      <Link
        className="text-blue-600"
        href="/about-us/terms-conditions/digital-subscription/"
        noStyle
        target="_blank"
      >
        Terms & Conditions
      </Link>{' '}
      for full subscription terms.
    </div>
  );

  const mobileImageUrl = `${staticUrl}images/wintersport/2025/mobile.jpg`;
  const desktopImageUrl = `${staticUrl}images/wintersport/2025/desktop.jpg`;

  if (!pianoFeature.enabled) {
    return null;
  }

  return (
    <FlowCheckout
      defaultPeriod={Period.FOUR_WEEKLY}
      planMeta={planMeta}
      termHelpers={termHelpers}
    >
      <div>
        <div className="relative flex w-full flex-col items-center">
          <picture>
            <source media="(max-width: 767px)" srcSet={mobileImageUrl} />
            <img
              alt="Winter Sport Promo"
              className="h-auto w-full"
              src={desktopImageUrl}
            />
          </picture>
        </div>

        <div className="mx-auto">
          <div
            className={clsx(
              'transition-opacity duration-200',
              terms.length === 4 ? 'opacity-100' : 'opacity-0',
            )}
          >
            <div className="flex flex-col items-center justify-start gap-y-10 pt-7 lg:pb-20 lg:pt-10">
              <div className="flex flex-col justify-center gap-y-6 lg:flex-row lg:gap-x-6 lg:gap-y-0">
                {[Plan.BASIC, Plan.PREMIUM].map((plan) => {
                  const hasTrial = getHasTrial(plan, Period.FOUR_WEEKLY);
                  const trialPeriod = getTrialPeriod(plan, Period.FOUR_WEEKLY);
                  const weeklyPrice = getWeeklyPrice(
                    plan,
                    true,
                    Period.FOUR_WEEKLY,
                  );
                  const originalPrice = getOriginalPricing(
                    plan,
                    Period.FOUR_WEEKLY,
                  );
                  const suffix = '.00';
                  const trimmedOriginalPrice = originalPrice.endsWith(suffix)
                    ? originalPrice.slice(0, -suffix.length)
                    : originalPrice;
                  const trialPeriodSubText = '/week';

                  return (
                    <OptionCard
                      borderColour="border-gray-800"
                      key={plan}
                      name={planMeta[plan]?.name ?? 'NULL'}
                      plan={plan}
                      price={weeklyPrice}
                      primary={planMeta[plan]?.primary}
                      tag={planMeta[plan]?.tag}
                      termsSubtext={
                        hasTrial ? (
                          <div className="text-center">
                            Then billed {trimmedOriginalPrice}/month.
                            Annual&nbsp;discount&nbsp;available
                          </div>
                        ) : undefined
                      }
                      trialPeriod={trialPeriod}
                      trialPeriodSubText={trialPeriodSubText}
                    >
                      <div className="flex flex-col gap-y-5 pt-[27px]">
                        {planMeta[plan]?.description ?? 'NULL'}
                      </div>
                    </OptionCard>
                  );
                })}
              </div>
              {termsConditions}
            </div>
            <Compare />
          </div>
          <OtherOffers />

          <div className="mx-auto max-w-[960px] lg:pb-8">
            <Container>
              <FaqContainerACM />
            </Container>
          </div>
        </div>
      </div>
    </FlowCheckout>
  );
}

export default SubscribePageWinterSportFlow;
