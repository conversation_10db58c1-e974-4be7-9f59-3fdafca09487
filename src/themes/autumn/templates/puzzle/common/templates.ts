export interface PuzzlePageTemplateProps {
  image: string;
  resizerContainerId: string;
  resizerScriptUrl: string;
  subTitle: string | ((siteName: string) => string);
  title: string;
}

export const puzzlePagesTemplates: Record<string, PuzzlePageTemplateProps> = {
  codeCracker: {
    image:
      'https://cdn.newsnow.io/M4qG8tFwm4mdvYjNdABvPv/' +
      'cfdddc12-2b05-4b29-a5d2-f3191b089e0a.png',
    resizerContainerId: 'PuzzleExpertsCodeCracker',
    resizerScriptUrl:
      'https://data.puzzlexperts.com/puzzleapp-v3/codecracker/scripts/ContainerResizer.js',
    subTitle: 'For crossword lovers looking for a change of pace.',
    title: 'Code Cracker',
  },
  crossword: {
    image:
      'https://cdn.newsnow.io/M4qG8tFwm4mdvYjNdABvPv/' +
      'd86076c0-5986-4f4c-a40b-7840fd7a935b.png',
    resizerContainerId: 'puzzle-container-crossword',
    resizerScriptUrl:
      'https://data.puzzlexperts.com/puzzleapp/scripts/crossword-iframe-resize.js',
    subTitle:
      'A crossword is a word puzzle that usually takes the form of a square or a rectangular grid.',
    title: 'Crossword',
  },
  crypticCrossword: {
    image:
      'https://cdn.newsnow.io/M4qG8tFwm4mdvYjNdABvPv/' +
      'ed3a3d8f-2007-4c3d-91ff-f6516b8c5a18.png',
    resizerContainerId: 'puzzle-container-crossword',
    resizerScriptUrl:
      'https://data.puzzlexperts.com/puzzleapp/scripts/crossword-iframe-resize.js',
    subTitle: 'Search for the hidden meaning to solve this cryptic puzzle.',
    title: 'Cryptic Crossword',
  },
  sudoku: {
    image:
      'https://cdn.newsnow.io/M4qG8tFwm4mdvYjNdABvPv/' +
      '874a828c-c506-4978-a1fe-c8f3ecf78c56.png',
    resizerContainerId: 'puzzle-container-sudoku',
    resizerScriptUrl:
      'https://data.puzzlexperts.com/puzzleapp/scripts/sudoku-iframe-resize.js',
    subTitle:
      'Sudoku is a logic-based, combinatorial number-placement puzzle.',
    title: 'Sudoku',
  },
  ultimateTrivia: {
    image:
      'https://cdn.newsnow.io/M4qG8tFwm4mdvYjNdABvPv/' +
      '949ed18b-cc87-4532-952e-31ea15545ae5.png',
    resizerContainerId: 'puzzle-container-trivia',
    resizerScriptUrl:
      'https://data.puzzlexperts.com/puzzleapp/scripts/trivia-iframe-resize.js',
    subTitle: (siteName: string) =>
      `Test your knowledge with ${siteName} ultimate trivia section.`,
    title: 'Ultimate Trivia',
  },
  wheelWords: {
    image:
      'https://cdn.newsnow.io/SSCTEbedgMaXDn4SNAUEiK/ddb30a2b-69a8-4d97-a891-14124b14c46f.png',
    resizerContainerId: 'puzzle-experts-wheelwords-frame',
    resizerScriptUrl:
      'https://data.puzzlexperts.com/puzzleapp-v3/wheelwords/container-resizer.js',
    subTitle:
      'Create as many words of four letters or more in this classic puzzle.',
    title: 'Wheel Words',
  },
  wordSearch: {
    image:
      'https://cdn.newsnow.io/M4qG8tFwm4mdvYjNdABvPv/' +
      '5c08d052-a44f-4f22-8be2-afabfc6fd6f2.png',
    resizerContainerId: 'puzzle-container-wordsearch',
    resizerScriptUrl:
      'https://data.puzzlexperts.com/puzzleapp/scripts/wordsearch-iframe-resize.js',
    subTitle: 'Find and cross off words in this classic puzzle.',
    title: 'Word Search',
  },
};
