'use client';

import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { format } from 'date-fns';
import { useState } from 'react';

import ToggleSwitch from 'themes/autumn/components/generic/ToggleSwitch';
import { useSearch } from 'themes/autumn/templates/Classifieds/utils';
import { useDeviceTypeFromWidth } from 'util/hooks';
import { useDate } from 'util/time';

import styles from './SearchWidget.module.css';

import type { DeviceType } from 'util/device';

interface Props {
  className?: string;
  inputClassName?: string;
  showRegionToggle?: boolean;
}

interface ResponsivePlaceholderInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'placeholder'> {
  placeholderTexts: Record<DeviceType, string>;
  ref?: React.RefObject<HTMLElement | null>;
}

function withResponsivePlaceholder(WrappedComponent: React.JSX.ElementType) {
  return function ComponentWithPlaceholder({
    placeholderTexts,
    ref,
    ...props
  }: ResponsivePlaceholderInputProps) {
    const deviceType = useDeviceTypeFromWidth();
    const placeholder = placeholderTexts[deviceType];

    // eslint-disable-next-line react/jsx-props-no-spreading
    return <WrappedComponent {...props} placeholder={placeholder} ref={ref} />;
  };
}

const ResponsivePlaceholderInput = withResponsivePlaceholder('input');

interface AndOrProps {
  className: string;
}

function AndOr({ className }: AndOrProps) {
  return (
    <span
      className={clsx(
        'relative z-10 mx-auto items-center justify-center rounded-full bg-white px-4 py-2 font-medium shadow-md md:top-0 md:flex',
        className,
      )}
    >
      and/or
    </span>
  );
}

function DateInputWithPlaceholder({
  className,
  ...props
}: ResponsivePlaceholderInputProps) {
  const [value, setValue] = useState(props.defaultValue ?? '');

  return (
    <ResponsivePlaceholderInput
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
      className={clsx(className, value && styles.hasValue)}
      onChange={(event) => {
        setValue(event.currentTarget.value);
      }}
      onFocus={(event) => {
        // iPad does not have showPicker
        event.currentTarget.showPicker?.();
      }}
      type="date"
    />
  );
}

const inputCommonClassName =
  'text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0';

export default function TributesAndFuneralSearchWidget({
  className = 'md:bg-gray-50 mb-6',
  inputClassName = 'bg-gray-50',
  showRegionToggle = true,
}: Props) {
  const date = useDate();
  const {
    date: dateSearch,
    isSearching,
    location,
    query,
    region,
    regionDefault,
  } = useSearch();

  return (
    <>
      <form
        action=""
        className={clsx(
          className,
          'w-full border-gray-200 text-sm text-gray-800 md:rounded-full md:border md:pr-1',
        )}
        onSubmit={(event) => {
          if (
            !Array.from(event.currentTarget.elements).some((element) =>
              Boolean((element as HTMLInputElement).value.trim()),
            )
          ) {
            event.preventDefault();
          }
        }}
      >
        <div className="items-center text-center md:flex md:h-14 md:flex-row">
          <ResponsivePlaceholderInput
            className={clsx(
              inputCommonClassName,
              inputClassName,
              'md:rounded-l-full md:border-r md:pl-4 md:pr-12',
            )}
            defaultValue={query}
            maxLength={50}
            name="q"
            placeholderTexts={{
              desktop: 'Search by name',
              mobile: 'Search listing by name',
              tablet: 'Name',
            }}
            // Ensures a low minimum width. Default is 20
            size={1}
            type="search"
          />
          <AndOr className="-top-1.5 md:-translate-x-1/2" />
          <DateInputWithPlaceholder
            className={clsx(
              styles.date,
              inputCommonClassName,
              inputClassName,
              'relative -my-2 md:-ml-6 md:-mr-8 md:p-0',
            )}
            defaultValue={dateSearch}
            // Disallow future dates
            max={format(date, 'y-MM-dd')}
            name="date"
            placeholderTexts={{
              desktop: 'Date',
              mobile: 'Search by date',
              tablet: 'Date',
            }}
            size={1}
          />
          <AndOr className="top-1 md:translate-x-1/2" />
          <ResponsivePlaceholderInput
            className={clsx(
              inputCommonClassName,
              inputClassName,
              'md:border-l md:py-4 md:pl-14',
            )}
            defaultValue={location}
            maxLength={50}
            name="location"
            placeholderTexts={{
              desktop: 'Location',
              mobile: 'Search by location',
              tablet: 'Location',
            }}
            size={1}
            type="search"
          />
          <button
            className="mt-3 flex h-12 w-full shrink-0 items-center justify-center rounded-full bg-yellow-400 px-6 shadow hover:bg-yellow-300 active:bg-yellow-500 md:m-0 md:w-auto"
            type="submit"
          >
            <span className="font-medium">Search</span>
            <MagnifyingGlassIcon className="ml-4 size-6 text-black" />
          </button>
        </div>
      </form>
      {isSearching && (
        <a className="my-2 mr-auto inline-block underline" href=".">
          Clear search filters
        </a>
      )}
      {showRegionToggle && !isSearching && (
        <div className="my-2 flex flex-row items-center font-medium">
          <div className="mr-2">Show notices from the region</div>
          <ToggleSwitch
            backgroundClassName="!w-10"
            buttonClassName="!size-5"
            enabled={region}
            onClick={() => {
              if (!region === regionDefault) {
                window.location.href = './';
                return;
              }

              window.location.href = `./?region=${region ? '0' : '1'}`;
            }}
          />
        </div>
      )}
    </>
  );
}
