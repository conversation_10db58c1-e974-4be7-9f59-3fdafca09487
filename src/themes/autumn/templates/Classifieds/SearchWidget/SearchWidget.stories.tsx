import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import BottomWidget from '../SearchWidgetBottom';

import Component from '.';

import type { Meta, StoryObj } from '@storybook/nextjs-vite';

const meta: Meta<typeof Component> = {
  component: Component,
  title: 'Notice board/Search',
};

export default meta;

type Story = StoryObj<typeof Component>;

export const Default: Story = {
  render: () => (
    <TestWrapper
      store={createStore((state) => ({
        ...state,
        runtime: {
          isClientSide: true,
        },
      }))}
    >
      <Component />
    </TestWrapper>
  ),
};

export const Bottom: Story = {
  render: () => (
    <TestWrapper
      store={createStore((state) => ({
        ...state,
        runtime: {
          isClientSide: true,
        },
      }))}
    >
      <BottomWidget />
    </TestWrapper>
  ),
};
