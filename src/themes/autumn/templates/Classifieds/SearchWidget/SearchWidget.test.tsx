import { render, screen } from '@testing-library/react';
import navigation from 'next/navigation';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import SearchWidget from '.';

jest.mock('next/navigation', () => ({
  ...jest.requireActual<typeof navigation>('next/navigation'),
  useSearchParams: jest.fn(),
}));

describe('<SearchWidget />', () => {
  it('renders', () => {
    expect.assertions(1);

    const { asFragment } = render(
      <TestWrapper store={createStore()}>
        <SearchWidget />
      </TestWrapper>,
    );

    expect(asFragment()).toMatchSnapshot();
  });

  it('loads data from query string', () => {
    (navigation.useSearchParams as jest.Mock).mockImplementation(
      () => new URLSearchParams('q=1234'),
    );
    render(
      <TestWrapper store={createStore()}>
        <SearchWidget />
      </TestWrapper>,
    );

    expect(screen.getByDisplayValue('1234')).toBeInTheDocument();
  });
});
