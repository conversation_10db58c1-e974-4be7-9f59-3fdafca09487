// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SearchWidget /> renders 1`] = `
<DocumentFragment>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <form
      action=""
      class="md:bg-gray-50 mb-6 w-full border-gray-200 text-sm text-gray-800 md:rounded-full md:border md:pr-1"
    >
      <div
        class="items-center text-center md:flex md:h-14 md:flex-row"
      >
        <input
          class="text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0 bg-gray-50 md:rounded-l-full md:border-r md:pl-4 md:pr-12"
          maxlength="50"
          name="q"
          placeholder="Search by name"
          size="1"
          type="search"
          value=""
        />
        <span
          class="relative z-10 mx-auto items-center justify-center rounded-full bg-white px-4 py-2 font-medium shadow-md md:top-0 md:flex -top-1.5 md:-translate-x-1/2"
        >
          and/or
        </span>
        <input
          class="date text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0 bg-gray-50 relative -my-2 md:-ml-6 md:-mr-8 md:p-0"
          max="1970-01-01"
          name="date"
          placeholder="Date"
          size="1"
          type="date"
          value=""
        />
        <span
          class="relative z-10 mx-auto items-center justify-center rounded-full bg-white px-4 py-2 font-medium shadow-md md:top-0 md:flex top-1 md:translate-x-1/2"
        >
          and/or
        </span>
        <input
          class="text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0 bg-gray-50 md:border-l md:py-4 md:pl-14"
          maxlength="50"
          name="location"
          placeholder="Location"
          size="1"
          type="search"
          value=""
        />
        <button
          class="mt-3 flex h-12 w-full shrink-0 items-center justify-center rounded-full bg-yellow-400 px-6 shadow hover:bg-yellow-300 active:bg-yellow-500 md:m-0 md:w-auto"
          type="submit"
        >
          <span
            class="font-medium"
          >
            Search
          </span>
          <svg
            aria-hidden="true"
            class="ml-4 size-6 text-black"
            data-slot="icon"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </form>
    <div
      class="my-2 flex flex-row items-center font-medium"
    >
      <div
        class="mr-2"
      >
        Show notices from the region
      </div>
      <button
        aria-label="Toggle"
        class="relative h-5.5 w-11 rounded-full transition-all !w-10 bg-gray-200 duration-100"
        type="button"
      >
        <div
          class="absolute top-1/2 size-7 -translate-y-1/2 rounded-full border bg-white shadow transition-all !size-5 left-0 border-gray-200"
        />
      </button>
    </div>
  </div>
</DocumentFragment>
`;
