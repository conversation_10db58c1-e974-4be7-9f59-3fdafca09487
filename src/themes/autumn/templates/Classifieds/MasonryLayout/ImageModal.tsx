import { Dialog, Transition, TransitionChild } from '@headlessui/react';
import { ArrowTopRightOnSquareIcon } from '@heroicons/react/20/solid';
import { XMarkIcon } from '@heroicons/react/24/outline';

import Link from 'themes/autumn/components/generic/Link';
import AdImage from 'themes/autumn/templates/Classifieds/AdImage';

import type { ClassifiedAd } from 'types/Classified';

interface Props {
  item?: ClassifiedAd;
  updateModal: (item: ClassifiedAd | undefined) => void;
}

export default function ImageModal({ item, updateModal }: Props) {
  return (
    <Transition show={!!item}>
      <Dialog
        as="div"
        className="fixed inset-0 z-30 overflow-y-auto"
        onClose={() => updateModal(undefined)}
      >
        <div className="mx-4 flex min-h-screen items-center justify-center pt-4 text-center sm:block sm:p-0">
          <TransitionChild
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500/75 transition-opacity" />
          </TransitionChild>
          <span
            aria-hidden="true"
            className="hidden align-middle sm:inline-block sm:h-screen"
          >
            &#8203;
          </span>
          <TransitionChild
            enter="ease-out duration-300"
            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enterTo="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <div className="relative my-8 inline-block h-auto overflow-auto rounded-lg bg-white px-4 pb-6 pt-12 text-left align-middle shadow-xl transition-all sm:max-w-lg sm:px-6">
              <div className="absolute right-0 top-0 pr-4 pt-4">
                <button
                  className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none"
                  onClick={() => updateModal(undefined)}
                  type="button"
                >
                  <span className="sr-only">Close</span>
                  <XMarkIcon aria-hidden="true" className="size-6" />
                </button>
              </div>

              <div className="flex h-auto flex-col items-center justify-center bg-white">
                {item && (
                  <AdImage
                    alt={item.title}
                    className="z-10"
                    url={item.images[0]}
                    width={640}
                  />
                )}
                {!!item?.url && (
                  <Link
                    className="mt-4 flex items-center gap-x-2.5 text-sm font-medium leading-5 text-blue-600"
                    href={item.url}
                    isSponsored
                    noStyle
                    target="_blank"
                  >
                    <span>View Website</span>
                    <ArrowTopRightOnSquareIcon
                      aria-hidden="true"
                      className="size-5"
                    />
                  </Link>
                )}
              </div>
            </div>
          </TransitionChild>
        </div>
      </Dialog>
    </Transition>
  );
}
