'use client';

import { faMagnifyingGlassPlus } from '@fortawesome/free-solid-svg-icons';
import { ArrowTopRightOnSquareIcon } from '@heroicons/react/20/solid';
import React, { useEffect, useState } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { useAppSelector } from 'store/hooks';
import { ClassifiedsLayout } from 'store/slices/features';
import Link from 'themes/autumn/components/generic/Link';
import AdImage from 'themes/autumn/templates/Classifieds/AdImage';
import { sendGtmEvent } from 'themes/autumn/templates/Classifieds/utils';
import { DeviceType } from 'util/device';
import { useDeviceTypeFromWidth } from 'util/hooks';
import InViewEvent from 'util/inViewEvent';
import { mathMaxOrZero } from 'util/number';

import Pagination from '../CardLayout/Pagination';
import ClusterAds from '../ClusterAds';
import FilterController from '../Controller/FilterController';
import NoAds from '../NoAds';
import PostAnAd from '../PostAnAd';

import type { LayoutProps } from '../theme';
import type { ClassifiedAd } from 'types/Classified';

interface AdProps {
  isMasonryLayout?: boolean;
  item: ClassifiedAd;
  ref?: React.Ref<HTMLDivElement> | undefined;
  updateModalData: (item: ClassifiedAd | undefined) => void;
}

const Ad = ({ isMasonryLayout, item, ref, updateModalData }: AdProps) => {
  const handleItemClick = () => {
    sendGtmEvent(item, 'ad_click');
    if (isMasonryLayout) {
      updateModalData(item);
    }
  };

  const handleLinkClick = () => {
    sendGtmEvent(item, 'ad_outbound_click');
  };

  const image = (
    <>
      {isMasonryLayout ? (
        <button className="relative" onClick={handleItemClick} type="button">
          <AdImage
            alt={item.title}
            className="gtm-hook-classified-ad"
            url={item.images[0]}
            width={320}
          />
          <span className="absolute bottom-2 right-2 flex items-center justify-center rounded border bg-white text-gray-400">
            <FontAwesomeIcon icon={faMagnifyingGlassPlus} />
          </span>
        </button>
      ) : (
        <Link
          aria-label={item.title}
          className="self-center"
          href={item.canonicalUrl}
          isSponsored
          noStyle
          onClick={handleItemClick}
        >
          <AdImage
            alt={item.title}
            className="gtm-hook-classified-ad"
            url={item.images[0]}
            width={320}
          />
        </Link>
      )}
    </>
  );
  const locationAndDate = (
    <p className="text-xs">
      {item.location && <>{item.location} &bull; </>}
      <span>{new Date(item.publicationDate).toLocaleDateString('en-AU')}</span>
    </p>
  );
  const category = item.category && (
    <span className="inline-block rounded-3xl bg-green-100 px-2 py-1 text-center text-xs text-green-700">
      {item.categoryText || item.category.name}
    </span>
  );
  const url = item.url && (
    <Link
      className="flex items-center space-x-2.5 text-sm font-medium leading-5 text-blue-600"
      href={item.url}
      onClick={handleLinkClick}
      target="_blank"
    >
      <span>View Website</span>
      <ArrowTopRightOnSquareIcon aria-hidden="true" className="size-5" />
    </Link>
  );

  const parts = [image, category, url, locationAndDate];

  return (
    <div
      className="mb-4 flex h-auto flex-col items-start gap-2 rounded-md border border-gray-300 bg-white p-2 break-inside"
      ref={ref}
    >
      {parts}
    </div>
  );
};

const COLS_FOR_DEVICE: Record<DeviceType, number> = {
  [DeviceType.DESKTOP]: 3,
  [DeviceType.TABLET]: 2,
  [DeviceType.MOBILE]: 1,
};

export default function MasonryLayout({
  ads,
  updateModalData,
}: LayoutProps): React.ReactElement {
  const deviceType = useDeviceTypeFromWidth();
  const [queryString, setQueryString] = useState('');

  const isMasonryLayout = useAppSelector(
    (state) =>
      state.features.classifieds.enabled &&
      state.features.classifieds.data.layout === ClassifiedsLayout.Masonry,
  );
  const classifiedsAds = useAppSelector((state) => state.classifieds.ads);
  const clusterAds = useAppSelector((state) => state.classifieds.clusterAds);
  const currentAdsPage = classifiedsAds?.currentPage ?? 1;
  const paginationNumPage = mathMaxOrZero(ads.numPages, clusterAds?.numPages);
  const paginationCurrentPage = mathMaxOrZero(
    currentAdsPage,
    clusterAds?.currentPage,
  );
  const paginationNextPage = mathMaxOrZero(ads.nextPage, clusterAds?.nextPage);
  const paginationPreviousPage = mathMaxOrZero(
    ads.previousPage,
    clusterAds?.previousPage,
  );

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('page', '');
    setQueryString(`?${urlParams.toString()}`);
  }, []);

  const cols = COLS_FOR_DEVICE[deviceType];
  let columnData: ClassifiedAd[];

  if (cols === 1) {
    columnData = ads.data;
  } else {
    // A masonry layout is top to bottom, left to right, but we want it to be
    // left to right, top to bottom. Reorder the array to achieve this.
    // Depending on the height of the elements, this may not be 100% accurate,
    // but it is the best we can do without reordering after rendering
    columnData = [];
    let col = 0;

    while (col < cols) {
      for (let i = 0; i < ads.data.length; i += cols) {
        const val = ads.data[i + col];

        if (val !== undefined) {
          columnData.push(val);
        }
      }

      col += 1;
    }
  }

  const onEnterEvent = (classified: ClassifiedAd) => {
    sendGtmEvent(classified, 'ad_impression');
  };

  return (
    <div className="h-auto">
      <FilterController isCardLayout={false} />
      {!ads?.data?.length ? (
        <NoAds />
      ) : (
        columnData?.length > 0 && (
          <div className="pb-4 md:columns-2 lg:columns-3">
            {columnData.map((item) => (
              <InViewEvent
                key={item.id}
                onEnterEvent={() => onEnterEvent(item)}
              >
                <Ad
                  isMasonryLayout={isMasonryLayout}
                  item={item}
                  updateModalData={updateModalData}
                />
              </InViewEvent>
            ))}
          </div>
        )
      )}
      <ClusterAds
        ad={Ad}
        className="md:columns-2 lg:columns-3"
        isMasonryLayout={isMasonryLayout}
        updateModalData={updateModalData}
      />
      <Pagination
        currentPage={paginationCurrentPage}
        nextPage={paginationNextPage}
        numPages={paginationNumPage}
        previousPage={paginationPreviousPage}
        queryString={queryString}
      />
      <PostAnAd />
    </div>
  );
}
