import { faExternalLinkAlt } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { useAppSelector } from 'store/hooks';

export default function ViewJobs() {
  const slug = useAppSelector((state) => state.classifieds.category?.slug);

  if (slug !== 'jobs-training') {
    return null;
  }

  return (
    <a
      className="mb-8 flex flex-col items-center gap-4 rounded-lg bg-yellow-50 p-4"
      href="/jobs"
    >
      <p className="font-medium">
        ViewJobs <FontAwesomeIcon className="ml-4" icon={faExternalLinkAlt} />
      </p>
      <p className="text-gray-500">
        ViewJobs brings you the best job opportunities across Australia
      </p>
      <span className="rounded-full bg-yellow-300 px-8 py-2">ViewJobs</span>
    </a>
  );
}
