import clsx from 'clsx';
import React from 'react';

import Button from 'themes/autumn/components/generic/Button';

interface Props {
  currentPage: number;
  nextPage: number | null;
  numPages: number;
  previousPage: number | null;
  queryString: string;
}

export default function Pagination({
  currentPage,
  nextPage,
  numPages,
  previousPage,
  queryString,
}: Props) {
  if (numPages === 1) {
    return null;
  }

  return (
    <nav className="grid grid-cols-5 grid-rows-2 border-t border-gray-200 sm:grid-rows-1">
      <div
        className={clsx(
          'col-span-1 col-start-1 row-span-1 row-start-2 flex w-0 items-center justify-start sm:row-start-1',
          { invisible: !previousPage },
        )}
      >
        <div className="border-t-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700">
          <Button
            bgColor="bg-white"
            className="rounded-md border border-gray-300 shadow-sm md:w-auto lg:w-auto"
            fontSize="text-base"
            hoverColor="hover:bg-gray-50"
            href={`${queryString}${previousPage || ''}`}
            text="Prev"
            textColor="text-black"
          />
        </div>
      </div>
      <div className="col-span-5 col-start-1 row-span-1 row-start-1 -mt-px flex h-16 justify-center sm:col-span-3">
        {currentPage - 3 >= 0 && (
          <a
            className="inline-flex items-center px-4 pt-4 text-sm font-medium text-gray-500 hover:text-gray-700"
            href={`${queryString}${currentPage - 3}`}
          >
            ...
          </a>
        )}
        {/* eslint-disable-next-line @typescript-eslint/no-unsafe-assignment */}
        {[...Array(numPages)].map((_, i) => {
          const page = i + 1;
          if (page > currentPage - 3 && page < currentPage + 3) {
            return (
              <a
                className={clsx(
                  'inline-flex items-center border-t-2 px-4 pt-4 text-sm font-medium',
                  page === currentPage
                    ? 'border-indigo-500'
                    : 'border-transparent hover:border-gray-300',
                  page === currentPage
                    ? 'text-indigo-600'
                    : 'text-gray-500 hover:text-gray-700',
                )}
                href={`${queryString}${page}`}
                key={page}
              >
                {page}
              </a>
            );
          }

          return undefined;
        })}
        {numPages > currentPage + 2 && (
          <a
            className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700"
            href={`${queryString}${currentPage + 3}`}
          >
            ...
          </a>
        )}
      </div>
      <div
        className={clsx(
          'col-span-1 col-start-6 row-span-1 row-start-2 flex w-0 items-center justify-end sm:row-start-1',
          { invisible: !nextPage },
        )}
      >
        <div className="text-sm font-medium text-gray-500 hover:text-gray-700">
          <Button
            bgColor="bg-white"
            className="rounded-md border border-gray-300 shadow-sm md:w-auto lg:w-auto"
            fontSize="text-base"
            hoverColor="hover:bg-gray-50"
            href={`${queryString}${nextPage || ''}`}
            text="Next"
            textColor="text-black"
          />
        </div>
      </div>
    </nav>
  );
}
