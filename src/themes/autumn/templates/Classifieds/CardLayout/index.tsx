'use client';

import { format } from 'date-fns';
import { Fragment, forwardRef, useEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import AdImage from 'themes/autumn/templates/Classifieds/AdImage';
import SearchWidgetBottom from 'themes/autumn/templates/Classifieds/SearchWidgetBottom';
import {
  getAdImage,
  getTributeAgeDetails,
  sendGtmEvent,
  useSearch,
} from 'themes/autumn/templates/Classifieds/utils';
import useMobileDetect, { stripTags } from 'util/device';
import { usePageHierarchy } from 'util/hooks';
import InViewEvent from 'util/inViewEvent';
import { mathMaxOrZero } from 'util/number';
import { formatLocation, formatLocationShort } from 'util/string';
import { plural } from 'util/text';

import ClusterAds from '../ClusterAds';
import FilterController from '../Controller/FilterController';
import MaybeDisclosure from '../MaybeDisclosure';
import NoAds from '../NoAds';
import PostAnAd from '../PostAnAd';
import StorySearchResults from '../StorySearchResults';

import Pagination from './Pagination';
import ViewJobs from './ViewJobs';

import type { LayoutProps } from '../theme';
import type { ClassifiedAd } from 'types/Classified';

interface DescriptionProps {
  maxLength: number;
  text: string;
}

function Description({ maxLength, text }: DescriptionProps) {
  // Clean whitespace
  const cleanText = stripTags(text)
    .replace(/\s*•\s*/g, ' ')
    .replace(/\n+/g, '\n')
    .trim();

  if (!cleanText) {
    return null;
  }

  // Truncate
  const truncatedInput =
    cleanText.length > maxLength
      ? `${cleanText.substring(0, maxLength)}...`
      : cleanText;

  const parts = truncatedInput.split(/\n/g);

  return (
    <p className="text-sm leading-snug text-gray-600">
      {truncatedInput.split(/\n/g).map((part, i) => (
        // eslint-disable-next-line react/no-array-index-key
        <Fragment key={i}>
          {part.trim()}
          {i !== parts.length - 1 && (
            <span className="mx-2 text-sm font-medium text-gray-500">•</span>
          )}
        </Fragment>
      ))}
    </p>
  );
}

const Ad = forwardRef<HTMLAnchorElement, { item: ClassifiedAd }>(
  ({ item }, ref) => {
    const currentDevice = useMobileDetect();
    const descriptionMaxLength = currentDevice.isMobile() ? 82 : 150;
    const primaryPageUrl = usePageHierarchy().primaryPage?.url;
    const slug = primaryPageUrl ? primaryPageUrl.split('/')[0] : '';
    const url = item.canonicalUrl;
    const isTributes = slug === TRIBUTES_FUNERALS_SLUG;

    function onClick() {
      sendGtmEvent(item, 'ad_click');
    }

    const imageUrl = getAdImage(item, slug);
    const ageDetails = getTributeAgeDetails(item);

    return (
      <a
        className="mb-4 flex cursor-pointer items-center rounded-md border border-gray-300 bg-white p-4 md:px-14 md:py-6"
        href={url}
        onClick={onClick}
        ref={ref}
      >
        <div className="flex w-full flex-col">
          <div className="flex gap-x-6">
            <div className="flex-1">
              <span className="mb-4 inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800">
                {item.categoryText || item.category.name}
              </span>
              <div className="font-semibold">{item.title}</div>
              {isTributes ? (
                ageDetails.length !== 0 && (
                  <p className="mt-2">{ageDetails[0]}</p>
                )
              ) : (
                <Description
                  maxLength={descriptionMaxLength}
                  text={item.text}
                />
              )}
            </div>
            {imageUrl && (
              <div className="flex size-32 items-center justify-center">
                <AdImage
                  alt={item.title}
                  className="rounded border"
                  height={128}
                  url={imageUrl}
                  width={128}
                />
              </div>
            )}
          </div>
          <div className="mt-4 flex gap-2 text-sm text-gray-600">
            {item.location && (
              <span>
                {isTributes
                  ? formatLocationShort(item.location)
                  : formatLocation(item.location)}
              </span>
            )}
            {item.publicationDate && (
              <>
                {item.location && (
                  <span className="text-sm font-medium text-gray-500">•</span>
                )}
                <span>
                  {format(
                    item.publicationDate,
                    isTributes ? 'd MMM y' : 'd MMM',
                  )}
                </span>
              </>
            )}
          </div>
        </div>
      </a>
    );
  },
);
Ad.displayName = 'Ad';

export { Ad as CardLayoutAd };

export default function CardLayout({ ads }: LayoutProps) {
  const siteName = useAppSelector(
    (state) => state.classifieds.cluster?.name ?? state.conf.name,
  );
  const { isSearching } = useSearch();
  const classifiedsAds = useAppSelector((state) => state.classifieds.ads);
  const clusterAds = useAppSelector((state) => state.classifieds.clusterAds);
  const numPerPage = classifiedsAds?.numPerPage ?? 0;
  const currentAdsPage = classifiedsAds?.currentPage ?? 1;
  const adsTotalNum = classifiedsAds?.totalNum ?? 0;
  const [queryString, setQueryString] = useState('');
  const adsNum = Math.min(currentAdsPage * numPerPage, adsTotalNum);
  const paginationNumPage = mathMaxOrZero(ads.numPages, clusterAds?.numPages);
  const paginationCurrentPage = mathMaxOrZero(
    currentAdsPage,
    clusterAds?.currentPage,
  );
  const paginationNextPage = mathMaxOrZero(ads.nextPage, clusterAds?.nextPage);
  const paginationPreviousPage = mathMaxOrZero(
    ads.previousPage,
    clusterAds?.previousPage,
  );
  const hasOtherContent = useAppSelector(
    (state) =>
      state.classifieds.clusterAds?.data.length !== 0 ||
      state.classifieds.stories?.length !== 0,
  );

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    // Ensure `page` is always the last item in the query string for easy
    // manipulation for pagination links
    urlParams.delete('page');
    urlParams.set('page', '');
    setQueryString(`?${urlParams.toString()}`);
  }, []);

  const onEnterEvent = (classified: ClassifiedAd) => {
    sendGtmEvent(classified, 'ad_impression');
  };

  return (
    <>
      <div>
        <FilterController isCardLayout />
        <ViewJobs />
        <MaybeDisclosure
          button={
            isSearching &&
            adsNum > 0 &&
            `${adsNum} result${plural(adsNum)} from ${siteName} (${
              adsTotalNum
            } total)`
          }
          buttonClassName="mb-4 text-lg font-medium"
          defaultOpen
          enableDisclosure={isSearching && adsNum > 0 && hasOtherContent}
          isLast={!hasOtherContent}
          panel={ads.data.map((item, i) => (
            <Fragment key={item.id}>
              <InViewEvent onEnterEvent={() => onEnterEvent(item)}>
                <Ad item={item} />
              </InViewEvent>
              {(i === 1 || ads.data.length < 1) && <PostAnAd />}
            </Fragment>
          ))}
        />
        {!ads?.data?.length && (
          <>
            <PostAnAd />
            <NoAds />
          </>
        )}
      </div>
      <StorySearchResults />
      <ClusterAds ad={Ad} isMasonryLayout={false} updateModalData={() => {}} />
      {!isSearching && !!ads?.data?.length && <SearchWidgetBottom />}
      <Pagination
        currentPage={paginationCurrentPage}
        nextPage={paginationNextPage}
        numPages={paginationNumPage}
        previousPage={paginationPreviousPage}
        queryString={queryString}
      />
    </>
  );
}
