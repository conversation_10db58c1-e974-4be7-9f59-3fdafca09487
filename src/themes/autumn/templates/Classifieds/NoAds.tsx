import { useAppSelector } from 'store/hooks';
import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import { usePageHierarchy } from 'util/hooks';
import { plural } from 'util/text';

export default function NoAds() {
  const numOfClusterAds = useAppSelector(
    (state) => state.classifieds.clusterAds?.totalNum ?? 0,
  );
  const numOfStories = useAppSelector(
    (state) => state.classifieds.stories?.length ?? 0,
  );
  const primaryPageUrl = usePageHierarchy().primaryPage?.url;
  const slug = primaryPageUrl ? primaryPageUrl.split('/')[0] : '';
  const isTributes = slug === TRIBUTES_FUNERALS_SLUG;
  const currentAdsPage = useAppSelector(
    (state) => state.classifieds.ads?.currentPage ?? 1,
  );

  if (currentAdsPage > 1) {
    return null;
  }

  const extraResults: string[] = [];

  if (numOfClusterAds) {
    extraResults.push(
      `${numOfClusterAds} ad${plural(numOfClusterAds)} from further away`,
    );
  }

  if (numOfStories) {
    extraResults.push(`${numOfStories} article${plural(numOfStories)}`);
  }

  return (
    <div className="mb-8">
      <span className="font-semibold">
        Sorry, we didn&rsquo;t find any results in your area
        {extraResults.length !== 0 &&
          ` but did find ${extraResults.join(' and ')}`}
        .
      </span>{' '}
      {!isTributes && (
        <a className="text-blue-600 underline" href="/classifieds/">
          View all classifieds
        </a>
      )}
    </div>
  );
}
