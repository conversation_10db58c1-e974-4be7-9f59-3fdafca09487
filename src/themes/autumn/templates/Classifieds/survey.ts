'use client';

import { useEffect } from 'react';

import { useAppSelector } from 'store/hooks';

// Local storage keys
const KEY_LAST_SURVEY = 'communityHubSurvey';
const KEY_VIEW_COUNT = 'communityHubViews';

// Number of days between survey prompts
const SURVEY_INTERVAL_DAYS = 30;
// Minimum page views to trigger a survey
const PAGE_VIEW_THRESHOLD = 3;

export default function useSurvey() {
  const email = useAppSelector(
    (state) => state.features.piano.enabled && state.piano.user?.email,
  );
  const retentlyEnabled = useAppSelector(
    (state) => state.features.retently.enabled,
  );

  useEffect(() => {
    if (!email || !window.localStorage || !retentlyEnabled) {
      return;
    }

    const date = window.localStorage.getItem(KEY_LAST_SURVEY);

    if (date) {
      // eslint-disable-next-line rulesdir/prefer-use-date
      const timestamp = Date.parse(date);

      if (!Number.isNaN(timestamp)) {
        const date1 = new Date(timestamp);
        // eslint-disable-next-line rulesdir/prefer-use-date
        const date2 = new Date();
        const diffTime = Math.abs(date2.getTime() - date1.getTime());
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < SURVEY_INTERVAL_DAYS) {
          return;
        }
      }
    }

    const pageViews =
      parseInt(window.localStorage.getItem(KEY_VIEW_COUNT) ?? '0', 10) + 1;

    if (pageViews >= PAGE_VIEW_THRESHOLD) {
      // eslint-disable-next-line rulesdir/prefer-use-date
      window.localStorage.setItem(KEY_LAST_SURVEY, new Date().toISOString());
      window.localStorage.removeItem(KEY_VIEW_COUNT);
      fetch('/classifieds/survey/', {
        body: JSON.stringify({
          email,
        }),
        method: 'POST',
      }).catch(console.error);
    } else {
      window.localStorage.setItem(KEY_VIEW_COUNT, pageViews.toString());
    }
  }, [email, retentlyEnabled]);
}
