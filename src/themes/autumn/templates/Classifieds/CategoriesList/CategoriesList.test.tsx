import { render } from '@testing-library/react';
import React from 'react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import { categories } from '../mock';

import CategoriesList from '.';

describe('categories list', () => {
  it('renders correctly', () => {
    expect.assertions(1);
    const testStore = createStore();

    const { container } = render(
      <TestWrapper store={testStore}>
        <CategoriesList categories={categories} />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
