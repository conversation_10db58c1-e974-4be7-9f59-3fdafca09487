import clsx from 'clsx';

import { useAppSelector } from 'store/hooks';

import type { Category } from 'store/slices/classifieds';

interface Props {
  categories: Category[];
}

export default function CategoriesList({ categories }: Props) {
  const currentUrl = useAppSelector((state) => `/${state.page.url}/`);

  if (!categories.length) {
    return null;
  }

  return (
    <>
      <h2 className="my-4 font-semibold">Categories</h2>
      <div className="mb-8 flex flex-wrap gap-2">
        {categories.map((category) => {
          const href = `/classifieds/${category.slug}/`;

          return (
            <a
              className={clsx(
                'inline-block rounded-3xl px-2 py-1 text-sm text-slate-800',
                {
                  'bg-yellow-100': currentUrl !== href,
                  'bg-yellow-200 font-semibold': currentUrl === href,
                },
              )}
              href={href}
              key={category.slug}
            >
              {category.name}
            </a>
          );
        })}
      </div>
    </>
  );
}
