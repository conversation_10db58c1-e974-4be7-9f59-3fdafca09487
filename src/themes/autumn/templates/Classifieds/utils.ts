import { useSearchParams } from 'next/navigation';

import { useAppSelector } from 'store/hooks';
import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import { setGtmDataLayer } from 'util/gtm';
import { plural } from 'util/text';

import type { ClassifiedAd } from 'types/Classified';

export function sendGtmEvent(ad: ClassifiedAd, event: string) {
  setGtmDataLayer({
    data: {
      ad_category: ad.categoryText || ad.category.name,
      ad_date: ad.publicationDate,
      ad_id: ad.id,
      ad_title: ad.title,
    },
    event,
  });
}

interface InteractionDataProps {
  action: string;
  businessCategory?: string;
  businessName?: string;
  category?: string;
  label: string;
  section: string;
  signPost?: string[];
  ugc_id?: number | string;
}

export function sendGtmInteractionEvent(
  data: InteractionDataProps,
  event: string,
  storyTags?: string[],
) {
  setGtmDataLayer({
    data: {
      ...data,
      ad_category: undefined,
      ad_date: undefined,
      ad_id: undefined,
      ad_title: undefined,
      businessCategory: data.businessCategory ?? undefined,
      businessName: data.businessName ?? undefined,
      category: data.category ?? undefined,
      signPost: data.signPost ?? undefined,
      ugc_id: data.ugc_id ?? undefined,
    },
    event,
    storyTags: storyTags ?? undefined,
  });
}

export function getAdImage(ad: ClassifiedAd, slug?: string) {
  return slug === TRIBUTES_FUNERALS_SLUG ? ad.images[1] : ad.logo;
}

function isLeapYear(year: number): boolean {
  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
}

export function getTributeAgeDetails(ad: ClassifiedAd): string[] {
  let age: number | undefined;
  let yearBorn: number | undefined;
  let yearDeceased: number | undefined;

  if (ad.dateBorn) {
    const dateBorn = new Date(ad.dateBorn);

    if (ad.dateDeceased) {
      const dateDeceased = new Date(ad.dateDeceased);
      yearDeceased = dateDeceased.getFullYear();

      // Calculate the age, taking into account leap years
      let days = Math.floor(
        (dateDeceased.getTime() - dateBorn.getTime()) / 1000 / 60 / 60 / 24,
      );
      age = 0;
      for (
        let y = dateBorn.getFullYear();
        y <= dateDeceased.getFullYear();
        y += 1
      ) {
        const daysInYear = isLeapYear(y) ? 366 : 365;
        if (days >= daysInYear) {
          days -= daysInYear;
          age += 1;
        }
      }
    }

    yearBorn = dateBorn.getFullYear();
  } else if (ad.yearDeceased) {
    yearDeceased = ad.yearDeceased;
    if (ad.yearBorn) {
      yearBorn = ad.yearBorn;
    }
  }

  const details = [];
  if (yearDeceased) {
    if (yearBorn) {
      details.push(`${yearBorn} - ${yearDeceased}`);
    } else {
      details.push(yearDeceased.toString());
    }
  }

  if (age !== undefined) {
    details.push(`Aged ${age} year${plural(age)}`);
  }

  return details;
}

export function useSearch() {
  const params = useSearchParams();
  const query = params?.get('q')?.trim() ?? '';
  const date = params?.get('date')?.trim() ?? '';
  const location = params?.get('location') ?? '';
  const regionDefault = useAppSelector(
    (state) => state.features.ugc.enabled && state.features.ugc.data.useRegion,
  );
  const regionParam = params?.get('region');
  const region = regionParam === null ? regionDefault : regionParam === '1';
  const isSearching = Boolean(query || date || location);

  return { date, isSearching, location, query, region, regionDefault };
}
