import { ArrowTopRightOnSquareIcon } from '@heroicons/react/20/solid';

import Link from 'themes/autumn/components/generic/Link';
import { urlToHref } from 'util/page';

import type { ChildPage } from 'store/slices/page';

interface Props {
  itemClassName?: string;
  onlineClassifiedsItems: ChildPage[];
}

// Map of hostnames or page URLs to image URLs
const imageMap: Record<string, string> = {
  'agtrader.com.au':
    'https://cdn.newsnow.io/XNYkXiBdsC832Jm3ku2C9g/8e98b871-207a-46f9-9b41-7ecb8a6e0798.svg',
  'countrycars.com.au':
    'https://cdn.newsnow.io/b5N3njmU7ggEmuQMNaxVEM/52cd61a4-19c8-4282-b445-77a260956336.png',
  'horsedeals.com.au':
    'https://cdn.newsnow.io/b5N3njmU7ggEmuQMNaxVEM/ae776f30-bca3-41c7-bd11-fec59344d4f5.svg',
  jobs:
    'https://cdn.newsnow.io/b5N3njmU7ggEmuQMNaxVEM/' +
    'fe6c0fae-3a24-4ec7-a6f4-faee4d56d6d8.svg',
  'view.com.au':
    'https://cdn.newsnow.io/209997574/f41f403d-e6b0-49d3-a96c-a803823df659.svg',
};

export default function OnlineClassifieds({
  itemClassName,
  onlineClassifiedsItems,
}: Props): React.ReactElement {
  return (
    <ul className="-mx-4 flex flex-wrap">
      {onlineClassifiedsItems.map((onlineClassifiedsItem) => {
        let src;
        try {
          let { host } = new URL(onlineClassifiedsItem.url);
          if (host.startsWith('www.')) {
            host = host.slice(4);
          }
          src = imageMap[host];
        } catch {
          src = imageMap[onlineClassifiedsItem.url];
        }

        return (
          <li className={itemClassName} key={onlineClassifiedsItem.name}>
            <Link
              className="flex h-20 items-center"
              href={urlToHref(onlineClassifiedsItem.url)}
              noStyle
              target={onlineClassifiedsItem.newWindow ? '_blank' : undefined}
            >
              <span className="grow">
                <span className="truncate text-base font-medium text-gray-900">
                  {onlineClassifiedsItem.name}
                  <ArrowTopRightOnSquareIcon
                    aria-hidden="true"
                    className="ml-4 inline-block size-5 shrink-0 text-gray-500"
                  />
                </span>
                {onlineClassifiedsItem.metaDescription && (
                  <p className="mt-1 text-sm text-gray-500">
                    {onlineClassifiedsItem.metaDescription}
                  </p>
                )}
              </span>
              {src && (
                <img
                  alt=""
                  className="size-16 shrink-0 rounded-md"
                  src={src}
                />
              )}
            </Link>
          </li>
        );
      })}
    </ul>
  );
}
