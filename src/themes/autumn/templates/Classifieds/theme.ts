import type { ClassifiedAd } from 'types/Classified';
import type { NavThemeRecord } from 'types/Nav';

export interface LayoutProps {
  ads: {
    currentPage: number;
    data: ClassifiedAd[];
    nextPage: number | null;
    numPages: number;
    previousPage: number | null;
  };
  updateModalData: (item: ClassifiedAd | undefined) => void;
}

export const CLASSIFIEDS_NAV_THEME: NavThemeRecord = {
  currentUrl: {
    icon: '',
    textBackground: 'bg-gray-900',
    textColor: 'text-white',
  },
  default: {
    icon: '',
    textBackground: 'bg-gray-350 hover:bg-gray-100',
    textColor: 'text-slate-750',
  },
};

export const TRIBUTES_NAV_THEME: NavThemeRecord = {
  currentUrl: {
    icon: '',
    textBackground: 'bg-gray-900',
    textColor: 'text-white',
  },
  default: {
    icon: '',
    textBackground: 'bg-gray-350 hover:bg-gray-100',
    textColor: 'text-slate-750',
  },
};
