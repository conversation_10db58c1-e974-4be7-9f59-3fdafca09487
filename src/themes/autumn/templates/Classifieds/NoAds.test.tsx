import { render, screen } from '@testing-library/react';

import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import NoAds from './NoAds';

const mockClassifiedsData = {
  ad: null,
  ads: {
    currentPage: 1,
    data: [],
    nextPage: null,
    numPages: 1,
    numPerPage: 14,
    previousPage: null,
    totalNum: 0,
  },
  categories: [],
  category: null,
  cluster: null,
  clusterAds: {
    currentPage: 1,
    data: [],
    nextPage: null,
    numPages: 1,
    numPerPage: 14,
    previousPage: null,
    totalNum: 0,
  },
  clusters: null,
  isMasonryLayout: true,
  primarySimilarAdsLen: null,
  query: '',
  sideBarAds: null,
  similarAds: null,
  subcategory: null,
  tooltipClicked: false,
};

describe('<NoAds />', () => {
  it('does not show beyond the first page', () => {
    expect.assertions(1);

    render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          classifieds: {
            ...mockClassifiedsData,
            ads: {
              ...mockClassifiedsData.ads,
              currentPage: 2,
            },
          },
        }))}
      >
        <NoAds />
      </TestWrapper>,
    );

    expect(
      screen.queryByText('Sorry, we didn’t find any results in your area', {
        exact: false,
      }),
    ).not.toBeInTheDocument();
  });

  it('links back to the index', () => {
    expect.assertions(1);

    render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          classifieds: mockClassifiedsData,
        }))}
      >
        <NoAds />
      </TestWrapper>,
    );

    expect(screen.queryByText('View all classifieds')).toBeInTheDocument();
  });

  it('does not link back to the index for tributes', () => {
    expect.assertions(1);

    render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          classifieds: mockClassifiedsData,
          pages: [
            {
              altMenuName: '',
              doubleClickCat: '',
              id: 2,
              menuName: '',
              name: 'News',
              showHeading: true,
              showSiblingsOnChildPages: false,
              url: TRIBUTES_FUNERALS_SLUG,
            },
          ],
        }))}
      >
        <NoAds />
      </TestWrapper>,
    );

    expect(screen.queryByText('View all classifieds')).not.toBeInTheDocument();
  });

  it('mentions cluster ads if present', () => {
    expect.assertions(1);

    render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          classifieds: {
            ...mockClassifiedsData,
            clusterAds: {
              ...mockClassifiedsData.clusterAds,
              totalNum: 1,
            },
          },
        }))}
      >
        <NoAds />
      </TestWrapper>,
    );

    expect(
      screen.queryByText('but did find 1 ad from further away', {
        exact: false,
      }),
    ).toBeInTheDocument();
  });
});
