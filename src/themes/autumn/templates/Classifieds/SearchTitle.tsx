import { useAppSelector } from 'store/hooks';
import { useSearch } from 'themes/autumn/templates/Classifieds/utils';
import { plural } from 'util/text';

function searchToText(
  query: string,
  date: string | undefined,
  location: string,
) {
  const parts: string[] = [];

  if (query) {
    parts.push(`for "${query}"`);
  }

  if (date) {
    parts.push(`on ${date}`);
  }

  if (location) {
    parts.push(`in ${location}`);
  }

  return parts.join(' ');
}

export default function SearchTitle() {
  const { date: dateSearch, isSearching, location, query } = useSearch();
  let date;
  if (dateSearch) {
    const dateObject = new Date(dateSearch);
    if (!Number.isNaN(dateObject.getTime())) {
      date = dateObject.toLocaleDateString();
    }
  }

  const classifiedsAds = useAppSelector((state) => state.classifieds.ads);
  const clusterAds = useAppSelector((state) => state.classifieds.clusterAds);
  const currentAdsPage = classifiedsAds?.currentPage ?? 1;
  const adsTotalNum = classifiedsAds?.totalNum ?? 0;
  const clusterAdsTotalNum = clusterAds?.totalNum ?? 0;

  if (!isSearching || currentAdsPage !== 1) {
    return null;
  }

  return (
    <div className="mt-2 break-words text-base font-normal md:mb-4 md:mt-0">
      {adsTotalNum + clusterAdsTotalNum} result
      {plural(adsTotalNum + clusterAdsTotalNum)}{' '}
      {searchToText(query, date, location)}
    </div>
  );
}
