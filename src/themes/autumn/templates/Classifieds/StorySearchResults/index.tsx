import { useAppSelector } from 'store/hooks';
import { StrapListView } from 'util/ZoneItems/storylist';
import { plural } from 'util/text';

import MaybeDisclosure from '../MaybeDisclosure';

export default function StorySearchResults() {
  const stories = useAppSelector((state) => state.classifieds.stories);
  const hasClusterAds = useAppSelector(
    (state) => state.classifieds.clusterAds?.data.length !== 0,
  );
  const hasOtherContent = useAppSelector(
    (state) =>
      state.classifieds.ads?.data.length !== 0 ||
      state.classifieds.clusterAds?.data.length !== 0,
  );

  if (!stories || stories.length === 0) {
    return null;
  }

  return (
    <MaybeDisclosure
      button={`${stories.length} result${plural(
        stories.length,
      )} from News & Tributes`}
      buttonClassName="mb-4 text-lg font-medium"
      defaultOpen
      enableDisclosure={hasOtherContent}
      isLast={!hasClusterAds}
      panel={
        <StrapListView
          isHeroImage={false}
          largeLeadStory={false}
          limit={20}
          // @ts-expect-error Using only a minimal subset of story attributes
          stories={stories.map((story, index) => ({ index, story }))}
        />
      }
      panelClassName="mb-4"
    />
  );
}
