import { useAppSelector } from 'store/hooks';
import {
  ImageResizeMode,
  TransformOutputFormat,
  storyImageUrl,
} from 'util/image';

type Props = {
  alt: string;
  className?: string;
  height?: number;
  url: string;
  width?: number;
  // Require at least height or width
} & ({ height: number } | { width: number });

export default function AdImage({
  alt,
  className,
  height,
  url,
  width,
}: Props) {
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);

  if (!url) {
    return null;
  }

  const image = { uri: url };

  return (
    <picture>
      <source
        srcSet={`${storyImageUrl({
          fit: ImageResizeMode.MAX,
          height,
          image,
          outputFormat: TransformOutputFormat.WEBP,
          transformUrl,
          width,
        })} 1x, ${storyImageUrl({
          fit: ImageResizeMode.MAX,
          height: height ? height * 2 : undefined,
          image,
          outputFormat: TransformOutputFormat.WEBP,
          transformUrl,
          width: width ? width * 2 : undefined,
        })} 2x`}
        type="image/webp"
      />
      <img
        alt={alt}
        aria-hidden="true"
        className={className}
        src={storyImageUrl({
          fit: ImageResizeMode.MAX,
          height,
          image,
          transformUrl,
          width,
        })}
        srcSet={`${storyImageUrl({
          fit: ImageResizeMode.MAX,
          height,
          image,
          transformUrl,
          width,
        })} 1x, ${storyImageUrl({
          fit: ImageResizeMode.MAX,
          height: height ? height * 2 : undefined,
          image,
          transformUrl,
          width: width ? width * 2 : undefined,
        })} 2x`}
      />
    </picture>
  );
}
