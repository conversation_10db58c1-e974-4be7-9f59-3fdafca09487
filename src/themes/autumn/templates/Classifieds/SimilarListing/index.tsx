'use client';

import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';

import { CardLayoutAd } from '../CardLayout';

import type { ClassifiedAd } from 'types/Classified';

interface SimilarListingPros {
  classifieds: ClassifiedAd[];
  primarySimilarAdsLen: number | null;
}

export default function SimilarListing({
  classifieds,
  primarySimilarAdsLen = 0,
}: SimilarListingPros) {
  const showSimilarAds = useAppSelector(
    (state) =>
      state.features.classifieds.enabled &&
      state.features.classifieds.data.showSimilarAds,
  );

  if (!showSimilarAds || !classifieds.length) {
    return null;
  }

  return (
    <div className="w-full">
      {primarySimilarAdsLen !== 0 && (
        <p className="mb-6 text-xl font-semibold">Similar Listings</p>
      )}
      <div className={clsx('mb-4')}>
        {classifieds.map((ad, index) => (
          <React.Fragment key={ad.id}>
            {index === primarySimilarAdsLen && (
              <p className="mb-6 text-xl font-semibold">
                Listings from further away
              </p>
            )}
            <CardLayoutAd item={ad} />
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
