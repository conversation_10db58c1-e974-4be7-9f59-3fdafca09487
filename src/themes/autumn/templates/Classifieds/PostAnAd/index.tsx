import Link from 'themes/autumn/components/generic/Link';
import { useSearch } from 'themes/autumn/templates/Classifieds/utils';
import { useRegion } from 'util/hooks';

export default function PostAnAd() {
  const region = useRegion();
  const { isSearching } = useSearch();

  if (isSearching) {
    return null;
  }

  return (
    <div className="mb-6 flex flex-col items-center gap-4 rounded-lg border border-gray-300 px-6 py-8 text-center font-medium md:px-10 md:text-left lg:flex-row">
      <p className="grow content-center">
        Post a classified ad and get in front of the {region} community today,
        it’s simple.
      </p>
      <Link
        className="w-full rounded-3xl bg-yellow-300 px-14 py-4 text-sm hover:bg-yellow-400 active:bg-yellow-500 md:w-auto"
        href="/contact/#classifieds"
        noStyle
      >
        Post&nbsp;an&nbsp;ad
      </Link>
    </div>
  );
}
