import { render } from '@testing-library/react';
import React from 'react';

import { ClassifiedsLayout } from 'store/slices/features';
import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import { ads, categories, children } from './mock';

import Classifieds from '.';

const createMockStore = (withAds: boolean) =>
  createStore((state) => ({
    ...state,
    classifieds: {
      ...state.classifieds,
      ads,
      categories: withAds ? [] : categories,
      isMasonryLayout: true,
    },
    features: {
      ...state.features,
      classifieds: {
        data: {
          layout: ClassifiedsLayout.Masonry,
          showSimilarAds: false,
        },
        enabled: true,
      },
    },
    page: {
      ...state.page,
      children,
      name: '',
      showHeading: true,
    },
    settings: {
      ...state.settings,
      host: 'test.com.au',
      transformUrl: 'https://transform.dev.newsnow.io/',
    },
  }));

describe('classifieds template', () => {
  it('render results page correctly', () => {
    expect.assertions(1);
    const testStore = createMockStore(true);

    const { container } = render(
      <TestWrapper store={testStore}>
        <Classifieds />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('render categories page correctly', () => {
    expect.assertions(1);
    const testStore = createMockStore(false);

    const { container } = render(
      <TestWrapper store={testStore}>
        <Classifieds />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
