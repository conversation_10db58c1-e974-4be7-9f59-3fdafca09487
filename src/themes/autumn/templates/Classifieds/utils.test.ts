import { getTributeAgeDetails } from './utils';

import type { ClassifiedAd } from 'types/Classified';

const mockAd: ClassifiedAd = {
  canonicalUrl: '',
  category: {
    id: 1,
    name: '',
    slug: '',
  },
  categoryText: '',
  customerEmail: '',
  customerName: '',
  customerPhone: '',
  customerPostcode: '',
  customerState: '',
  customerTown: '',
  dateBorn: null,
  dateDeceased: null,
  enableComments: false,
  expirationDate: '',
  funeralDate: '',
  funeralHomeAddress: '',
  funeralHomeCity: '',
  funeralHomeName: '',
  funeralHomePostcode: '',
  funeralHomeState: '',
  funeralStartTime: '',
  funeralVenueAddress: '',
  funeralVenueCity: '',
  funeralVenueName: '',
  funeralVenuePostcode: '',
  funeralVenueState: '',
  id: 0,
  images: [],
  location: '',
  logo: null,
  publicationDate: '',
  quoteText: '',
  text: '',
  title: '',
  url: '',
  yearBorn: null,
  yearDeceased: null,
};

describe('getTributeAgeDetails()', () => {
  it('requires dateDeceased', () => {
    expect.assertions(1);
    const actual = getTributeAgeDetails({
      ...mockAd,
      dateBorn: '2000-01-01',
    });

    expect(actual).toStrictEqual([]);
  });

  it('uses dateDeceased', () => {
    expect.assertions(1);
    const actual = getTributeAgeDetails({
      ...mockAd,
      dateBorn: '2000-01-01',
      dateDeceased: '2025-01-01',
    });

    expect(actual).toStrictEqual(['2000 - 2025', 'Aged 25 years']);
  });

  it('uses yearDeceased', () => {
    expect.assertions(1);
    const actual = getTributeAgeDetails({
      ...mockAd,
      yearDeceased: 2025,
    });

    expect(actual).toStrictEqual(['2025']);
  });

  it('uses yearBorn', () => {
    expect.assertions(1);
    const actual = getTributeAgeDetails({
      ...mockAd,
      yearBorn: 2000,
      yearDeceased: 2025,
    });

    expect(actual).toStrictEqual(['2000 - 2025']);
  });
});
