import {
  faChevronDown,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  type DisclosureProps,
} from '@headlessui/react';
import { twMerge } from 'tailwind-merge';

interface Props extends Omit<DisclosureProps, 'children' | 'refName'> {
  button: React.ReactNode;
  buttonClassName?: string;
  enableDisclosure?: boolean;
  isLast?: boolean;
  panel: React.ReactNode;
  panelClassName?: string;
}

export default function MaybeDisclosure({
  button,
  buttonClassName,
  enableDisclosure = true,
  isLast = false,
  panel,
  panelClassName,
  ...disclosureProps
}: Props) {
  if (!enableDisclosure) {
    const buttonElement = button && (
      <p className={buttonClassName}>{button}</p>
    );

    if (panelClassName) {
      return (
        <>
          {buttonElement}
          <div className={panelClassName}>{panel}</div>
        </>
      );
    }

    return (
      <>
        {buttonElement}
        {panel}
      </>
    );
  }

  return (
    // eslint-disable-next-line react/jsx-props-no-spreading
    <Disclosure {...disclosureProps}>
      {({ open }) => (
        <>
          <DisclosureButton
            as="p"
            className={twMerge(
              'flex cursor-pointer items-center justify-between',
              !open && !isLast && 'border-b pb-4',
              buttonClassName,
            )}
          >
            <span>{button}</span>
            <FontAwesomeIcon
              className="ml-4"
              icon={open ? faChevronDown : faChevronRight}
            />
          </DisclosureButton>
          <DisclosurePanel className={panelClassName}>{panel}</DisclosurePanel>
        </>
      )}
    </Disclosure>
  );
}
