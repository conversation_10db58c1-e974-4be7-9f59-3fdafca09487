// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<RegionSelector /> renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mb-8 flex flex-col gap-12"
  >
    <p
      class="text-center text-3xl"
    >
      Find the death notices published every day
      <br />
      in our network
    </p>
    <form
      action=""
      class="md:bg-gray-50 w-full border-gray-200 text-sm text-gray-800 md:rounded-full md:border md:pr-1"
    >
      <div
        class="items-center text-center md:flex md:h-14 md:flex-row"
      >
        <input
          class="text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0 bg-gray-50 md:rounded-l-full md:border-r md:pl-4 md:pr-12"
          maxlength="50"
          name="q"
          placeholder="Search by name"
          size="1"
          type="search"
          value=""
        />
        <span
          class="relative z-10 mx-auto items-center justify-center rounded-full bg-white px-4 py-2 font-medium shadow-md md:top-0 md:flex -top-1.5 md:-translate-x-1/2"
        >
          and/or
        </span>
        <input
          class="date text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0 bg-gray-50 relative -my-2 md:-ml-6 md:-mr-8 md:p-0"
          max="1970-01-01"
          name="date"
          placeholder="Date"
          size="1"
          type="date"
          value=""
        />
        <span
          class="relative z-10 mx-auto items-center justify-center rounded-full bg-white px-4 py-2 font-medium shadow-md md:top-0 md:flex top-1 md:translate-x-1/2"
        >
          and/or
        </span>
        <input
          class="text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0 bg-gray-50 md:border-l md:py-4 md:pl-14"
          maxlength="50"
          name="location"
          placeholder="Location"
          size="1"
          type="search"
          value=""
        />
        <button
          class="mt-3 flex h-12 w-full shrink-0 items-center justify-center rounded-full bg-yellow-400 px-6 shadow hover:bg-yellow-300 active:bg-yellow-500 md:m-0 md:w-auto"
          type="submit"
        >
          <span
            class="font-medium"
          >
            Search
          </span>
          <svg
            aria-hidden="true"
            class="ml-4 size-6 text-black"
            data-slot="icon"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </form>
    <p
      class="text-center text-3xl"
    >
      Or view death notices by region
    </p>
    <ul
      class="flex flex-wrap justify-center gap-6"
    >
      <li
        class="relative"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-haspopup="menu"
          class="block rounded-full border px-6 py-2 hover:border-gray-600"
          data-headlessui-state=""
          id="headlessui-menu-button-:r2:"
          type="button"
        >
          NSW
          <svg
            aria-hidden="true"
            class="svg-inline--fa fa-chevron-down ml-2"
            data-icon="chevron-down"
            data-prefix="fas"
            focusable="false"
            role="img"
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
              fill="currentColor"
            />
          </svg>
        </button>
      </li>
      <li>
        <a
          class="block rounded-full border px-6 py-2 hover:border-gray-600"
          href="./vic-region/"
        >
          VIC
        </a>
      </li>
      <li>
        <a
          class="block rounded-full border px-6 py-2 hover:border-gray-600"
          href="./qld-region/"
        >
          QLD
        </a>
      </li>
      <li>
        <a
          class="block rounded-full border px-6 py-2 hover:border-gray-600"
          href="./tas-region/"
        >
          TAS
        </a>
      </li>
      <li>
        <a
          class="block rounded-full border px-6 py-2 hover:border-gray-600"
          href="./sa-region/"
        >
          SA
        </a>
      </li>
      <li>
        <a
          class="block rounded-full border px-6 py-2 hover:border-gray-600"
          href="./wa-region/"
        >
          WA
        </a>
      </li>
      <li>
        <a
          class="block rounded-full border px-6 py-2 hover:border-gray-600"
          href="./nt-region/"
        >
          NT
        </a>
      </li>
    </ul>
  </div>
</div>
`;
