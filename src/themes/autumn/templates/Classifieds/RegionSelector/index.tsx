import { faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import clsx from 'clsx';
import { Fragment } from 'react';
import slugify from 'slugify';

import { useAppSelector } from 'store/hooks';

import SearchWidget from '../SearchWidget';

const pillClassName =
  'block rounded-full border px-6 py-2 hover:border-gray-600';

function getClusterUrl(name: string) {
  return `./${slugify(name, { lower: true })}-region/`;
}

export default function RegionSelector() {
  const clusters = useAppSelector((state) => state.classifieds.clusters);

  if (!clusters) {
    return null;
  }

  return (
    <div className="mb-8 flex flex-col gap-12">
      <p className="text-center text-3xl">
        Find the death notices published every day
        <br />
        in our network
      </p>
      <SearchWidget className="md:bg-gray-50" showRegionToggle={false} />
      <p className="text-center text-3xl">Or view death notices by region</p>
      <ul className="flex flex-wrap justify-center gap-6">
        {Object.entries(clusters).map(([group, children]) =>
          children.length === 1 ? (
            <li key={group}>
              <a className={pillClassName} href={getClusterUrl(group)}>
                {group.toUpperCase()}
              </a>
            </li>
          ) : (
            <Menu as="li" className="relative" key={group}>
              <MenuButton as={Fragment}>
                {({ active }) => (
                  <button
                    className={clsx(
                      pillClassName,
                      active && 'border-black bg-black text-white',
                    )}
                    type="button"
                  >
                    {group.toUpperCase()}
                    <FontAwesomeIcon className="ml-2" icon={faChevronDown} />
                  </button>
                )}
              </MenuButton>
              <MenuItems
                anchor={{ padding: '1rem', to: 'bottom' }}
                as="ul"
                className="focus:outline-hidden absolute z-10 mt-2 w-56 overflow-hidden rounded-md bg-white shadow-lg ring-1 ring-black/5"
                modal={false}
              >
                {children.map((cluster) => (
                  <li key={cluster}>
                    <MenuItem>
                      {({ focus }) => (
                        <a
                          className={clsx(
                            'block px-4 py-2 text-sm text-gray-700',
                            focus && 'bg-gray-100',
                          )}
                          href={getClusterUrl(cluster)}
                        >
                          {cluster}
                        </a>
                      )}
                    </MenuItem>
                  </li>
                ))}
              </MenuItems>
            </Menu>
          ),
        )}
      </ul>
    </div>
  );
}
