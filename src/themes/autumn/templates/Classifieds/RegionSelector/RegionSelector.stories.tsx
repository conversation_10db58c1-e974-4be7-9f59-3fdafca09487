import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import { clusters } from './mock';

import Component from '.';

import type { Meta, StoryObj } from '@storybook/nextjs-vite';

const meta: Meta<typeof Component> = {
  component: Component,
  title: 'Notice board/Region selector',
};

export default meta;

type Story = StoryObj<typeof Component>;

const store = createStore((state) => ({
  ...state,
  classifieds: {
    ...state.classifieds,
    clusters,
  },
}));

export const Default: Story = {
  render: () => (
    <TestWrapper store={store}>
      <Component />
    </TestWrapper>
  ),
};
