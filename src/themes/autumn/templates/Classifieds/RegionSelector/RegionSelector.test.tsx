import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import { clusters } from './mock';

import Component from '.';

const store = createStore((state) => ({
  ...state,
  classifieds: {
    ...state.classifieds,
    clusters,
  },
}));

describe('<RegionSelector />', () => {
  it('renders', () => {
    const { container } = render(
      <TestWrapper store={store}>
        <Component />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
