import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import SearchWidget from 'themes/autumn/templates/Classifieds/SearchWidget';
import { usePageHierarchy } from 'util/hooks';

export default function SearchWidgetBottom() {
  const primaryPageUrl = usePageHierarchy().primaryPage?.url;
  const slug = primaryPageUrl ? primaryPageUrl.split('/')[0] : '';

  const word = slug === TRIBUTES_FUNERALS_SLUG ? 'who' : 'what';

  return (
    <div className="mb-4 rounded-lg bg-yellow-50 p-8 text-center md:px-2 md:py-12">
      <p>Can&lsquo;t find {word} you&lsquo;re looking for?</p>
      <p className="mb-8 text-sm">
        Search below for more results within the region
      </p>
      <SearchWidget
        className="md:bg-white"
        inputClassName="bg-white"
        showRegionToggle={false}
      />
    </div>
  );
}
