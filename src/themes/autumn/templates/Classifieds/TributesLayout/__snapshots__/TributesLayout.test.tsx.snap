// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<TributesLayout /> renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mb-2 mt-5 flex flex-row items-center gap-4 md:mb-4 md:mt-0 justify-end"
  >
    <div
      class="ml-4 flex items-center gap-4"
    >
      <div>
        <span
          class="md:hidden"
        >
          Listing
        </span>
        View
      </div>
      <div
        class="flex cursor-pointer flex-row"
        data-headlessui-state=""
      >
        <div>
          <button
            aria-expanded="false"
            aria-haspopup="menu"
            class="rounded-l-md p-2 text-sm font-semibold shadow-sm ring-1 ring-inset bg-gray-900 ring-gray-900"
            data-headlessui-state=""
            id="headlessui-menu-button-:r2:"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="size-5 text-white"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <div>
          <button
            aria-expanded="false"
            aria-haspopup="menu"
            class="rounded-r-md p-2 pr-3 text-sm font-semibold shadow-sm ring-1 ring-inset bg-white ring-gray-300"
            data-headlessui-state=""
            id="headlessui-menu-button-:r3:"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="-mr-1 size-5 text-gray-900"
              data-slot="icon"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M6 4.75A.75.75 0 0 1 6.75 4h10.5a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 4.75ZM6 10a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 10Zm0 5.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75a.75.75 0 0 1-.75-.75ZM1.99 4.75a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1v-.01ZM1.99 15.25a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1v-.01ZM1.99 10a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1V10Z"
                fill-rule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
  <div
    class="my-4 flex flex-wrap gap-4"
  >
    <a
      class="flex w-[calc(50%-0.5rem)] flex-col items-center gap-4 rounded-md border border-gray-300 p-4 text-center text-sm sm:w-[calc(33.33%-0.666rem)]"
      href="/"
    >
      <div
        class="mx-auto flex aspect-square w-full items-center justify-center overflow-hidden rounded-md"
      >
        <picture>
          <source
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-9.jpg/w374_h374_fmax.webp 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-9.jpg/w748_h748_fmax.webp 2x"
            type="image/webp"
          />
          <img
            alt="Smith, John"
            aria-hidden="true"
            src="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-9.jpg/w374_h374_fmax.jpg"
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-9.jpg/w374_h374_fmax.jpg 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-9.jpg/w748_h748_fmax.jpg 2x"
          />
        </picture>
      </div>
      <span
        class="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800"
      >
        Death Notice
      </span>
      <p
        class="w-full overflow-hidden text-ellipsis text-base font-semibold sm:text-sm"
      >
        Smith, John
      </p>
      <p
        class="text-gray-600"
      >
        1970 - 2025
        <span
          class="mx-1 sm:hidden"
        >
          |
        </span>
        <br
          class="hidden sm:block"
        />
        Aged 55 years
      </p>
      <div
        class="flex grow flex-col items-center justify-end gap-4"
      >
        <p
          class="font-serif text-lg italic sm:text-sm sm:text-gray-600"
        >
          Rest in Peace
        </p>
        <p
          class="flex gap-2 text-xs text-gray-600"
        >
          Sydney
          <span
            class="font-medium text-gray-500"
          >
            •
          </span>
          1 Jan 25
        </p>
      </div>
    </a>
    <a
      class="flex w-[calc(50%-0.5rem)] flex-col items-center gap-4 rounded-md border border-gray-300 p-4 text-center text-sm sm:w-[calc(33.33%-0.666rem)]"
      href="/"
    >
      <div
        class="mx-auto flex aspect-square w-full items-center justify-center overflow-hidden rounded-md"
      >
        <picture>
          <source
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-10.jpg/w374_h374_fmax.webp 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-10.jpg/w748_h748_fmax.webp 2x"
            type="image/webp"
          />
          <img
            alt="Smith, John"
            aria-hidden="true"
            src="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-10.jpg/w374_h374_fmax.jpg"
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-10.jpg/w374_h374_fmax.jpg 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-10.jpg/w748_h748_fmax.jpg 2x"
          />
        </picture>
      </div>
      <span
        class="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800"
      >
        Death Notice
      </span>
      <p
        class="w-full overflow-hidden text-ellipsis text-base font-semibold sm:text-sm"
      >
        Smith, John
      </p>
      <div
        class="flex grow flex-col items-center justify-end gap-4"
      >
        <p
          class="flex gap-2 text-xs text-gray-600"
        >
          Sydney
          <span
            class="font-medium text-gray-500"
          >
            •
          </span>
          1 Jan 25
        </p>
      </div>
    </a>
    <a
      class="flex w-[calc(50%-0.5rem)] flex-col items-center gap-4 rounded-md border border-gray-300 p-4 text-center text-sm sm:w-[calc(33.33%-0.666rem)]"
      href="/"
    >
      <div
        class="mx-auto flex aspect-square w-full items-center justify-center overflow-hidden rounded-md"
      >
        <picture>
          <source
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-7.jpg/w374_h374_fmax.webp 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-7.jpg/w748_h748_fmax.webp 2x"
            type="image/webp"
          />
          <img
            alt="Smith, John"
            aria-hidden="true"
            src="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-7.jpg/w374_h374_fmax.jpg"
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-7.jpg/w374_h374_fmax.jpg 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-7.jpg/w748_h748_fmax.jpg 2x"
          />
        </picture>
      </div>
      <span
        class="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800"
      >
        Death Notice
      </span>
      <p
        class="w-full overflow-hidden text-ellipsis text-base font-semibold sm:text-sm"
      >
        Smith, John
      </p>
      <div
        class="flex grow flex-col items-center justify-end gap-4"
      >
        <p
          class="font-serif text-lg italic sm:text-sm sm:text-gray-600"
        >
          Rest in Peace
        </p>
        <p
          class="flex gap-2 text-xs text-gray-600"
        >
          1 Jan 25
        </p>
      </div>
    </a>
    <a
      class="flex w-[calc(50%-0.5rem)] flex-col items-center gap-4 rounded-md border border-gray-300 p-4 text-center text-sm sm:w-[calc(33.33%-0.666rem)]"
      href="/"
    >
      <div
        class="mx-auto flex aspect-square w-full items-center justify-center overflow-hidden rounded-md"
      >
        <picture>
          <source
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-19.jpg/w374_h374_fmax.webp 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-19.jpg/w748_h748_fmax.webp 2x"
            type="image/webp"
          />
          <img
            alt="Smith, John"
            aria-hidden="true"
            src="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-19.jpg/w374_h374_fmax.jpg"
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-19.jpg/w374_h374_fmax.jpg 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-19.jpg/w748_h748_fmax.jpg 2x"
          />
        </picture>
      </div>
      <span
        class="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800"
      >
        Death Notice
      </span>
      <p
        class="w-full overflow-hidden text-ellipsis text-base font-semibold sm:text-sm"
      >
        Smith, John
      </p>
      <p
        class="text-gray-600"
      >
        1970 - 2025
        <span
          class="mx-1 sm:hidden"
        >
          |
        </span>
        <br
          class="hidden sm:block"
        />
        Aged 55 years
      </p>
      <div
        class="flex grow flex-col items-center justify-end gap-4"
      >
        <p
          class="flex gap-2 text-xs text-gray-600"
        >
          1 Jan 25
        </p>
      </div>
    </a>
    <a
      class="flex w-[calc(50%-0.5rem)] flex-col items-center gap-4 rounded-md border border-gray-300 p-4 text-center text-sm sm:w-[calc(33.33%-0.666rem)]"
      href="/"
    >
      <div
        class="mx-auto flex aspect-square w-full items-center justify-center overflow-hidden rounded-md"
      >
        <picture>
          <source
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-2.jpg/w374_h374_fmax.webp 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-2.jpg/w748_h748_fmax.webp 2x"
            type="image/webp"
          />
          <img
            alt="Smith, John"
            aria-hidden="true"
            src="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-2.jpg/w374_h374_fmax.jpg"
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-2.jpg/w374_h374_fmax.jpg 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-2.jpg/w748_h748_fmax.jpg 2x"
          />
        </picture>
      </div>
      <span
        class="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800"
      >
        Death Notice
      </span>
      <p
        class="w-full overflow-hidden text-ellipsis text-base font-semibold sm:text-sm"
      >
        Smith, John
      </p>
      <p
        class="text-gray-600"
      >
        1950 - 2025
      </p>
      <div
        class="flex grow flex-col items-center justify-end gap-4"
      >
        <p
          class="flex gap-2 text-xs text-gray-600"
        >
          1 Jan 25
        </p>
      </div>
    </a>
    <a
      class="flex w-[calc(50%-0.5rem)] flex-col items-center gap-4 rounded-md border border-gray-300 p-4 text-center text-sm sm:w-[calc(33.33%-0.666rem)]"
      href="/"
    >
      <div
        class="mx-auto flex aspect-square w-full items-center justify-center overflow-hidden rounded-md"
      >
        <picture>
          <source
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-5.jpg/w374_h374_fmax.webp 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-5.jpg/w748_h748_fmax.webp 2x"
            type="image/webp"
          />
          <img
            alt="Smith, John"
            aria-hidden="true"
            src="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-5.jpg/w374_h374_fmax.jpg"
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-5.jpg/w374_h374_fmax.jpg 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-5.jpg/w748_h748_fmax.jpg 2x"
          />
        </picture>
      </div>
      <span
        class="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800"
      >
        Death Notice
      </span>
      <p
        class="w-full overflow-hidden text-ellipsis text-base font-semibold sm:text-sm"
      >
        Smith, John
      </p>
      <p
        class="text-gray-600"
      >
        2025
      </p>
      <div
        class="flex grow flex-col items-center justify-end gap-4"
      >
        <p
          class="flex gap-2 text-xs text-gray-600"
        >
          1 Jan 25
        </p>
      </div>
    </a>
    <a
      class="flex w-[calc(50%-0.5rem)] flex-col items-center gap-4 rounded-md border border-gray-300 p-4 text-center text-sm sm:w-[calc(33.33%-0.666rem)]"
      href="/"
    >
      <div
        class="mx-auto flex aspect-square w-full items-center justify-center overflow-hidden rounded-md"
      >
        <picture>
          <source
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-18.jpg/w374_h374_fmax.webp 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-18.jpg/w748_h748_fmax.webp 2x"
            type="image/webp"
          />
          <img
            alt="Smith, John"
            aria-hidden="true"
            src="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-18.jpg/w374_h374_fmax.jpg"
            srcset="transform/v1/resize/frm/tributes-funerals/tributes-placeholder-18.jpg/w374_h374_fmax.jpg 1x, transform/v1/resize/frm/tributes-funerals/tributes-placeholder-18.jpg/w748_h748_fmax.jpg 2x"
          />
        </picture>
      </div>
      <span
        class="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800"
      >
        Death Notice
      </span>
      <p
        class="w-full overflow-hidden text-ellipsis text-base font-semibold sm:text-sm"
      >
        Smith, John
      </p>
      <div
        class="flex grow flex-col items-center justify-end gap-4"
      >
        <p
          class="flex gap-2 text-xs text-gray-600"
        >
          1 Jan 25
        </p>
      </div>
    </a>
  </div>
  <div
    class="mb-4 rounded-lg bg-yellow-50 p-8 text-center md:px-2 md:py-12"
  >
    <p>
      Can‘t find 
      what
       you‘re looking for?
    </p>
    <p
      class="mb-8 text-sm"
    >
      Search below for more results within the region
    </p>
    <form
      action=""
      class="md:bg-white w-full border-gray-200 text-sm text-gray-800 md:rounded-full md:border md:pr-1"
    >
      <div
        class="items-center text-center md:flex md:h-14 md:flex-row"
      >
        <input
          class="text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0 bg-white md:rounded-l-full md:border-r md:pl-4 md:pr-12"
          maxlength="50"
          name="q"
          placeholder="Search by name"
          size="1"
          type="search"
          value=""
        />
        <span
          class="relative z-10 mx-auto items-center justify-center rounded-full bg-white px-4 py-2 font-medium shadow-md md:top-0 md:flex -top-1.5 md:-translate-x-1/2"
        >
          and/or
        </span>
        <input
          class="date text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0 bg-white relative -my-2 md:-ml-6 md:-mr-8 md:p-0"
          max="1970-01-01"
          name="date"
          placeholder="Date"
          size="1"
          type="date"
          value=""
        />
        <span
          class="relative z-10 mx-auto items-center justify-center rounded-full bg-white px-4 py-2 font-medium shadow-md md:top-0 md:flex top-1 md:translate-x-1/2"
        >
          and/or
        </span>
        <input
          class="text-sm h-12 md:h-10 w-full flex-1 md:border-0 border-b border-gray-200 rounded-full md:rounded-none md:h-full md:w-auto md:focus:ring-0 bg-white md:border-l md:py-4 md:pl-14"
          maxlength="50"
          name="location"
          placeholder="Location"
          size="1"
          type="search"
          value=""
        />
        <button
          class="mt-3 flex h-12 w-full shrink-0 items-center justify-center rounded-full bg-yellow-400 px-6 shadow hover:bg-yellow-300 active:bg-yellow-500 md:m-0 md:w-auto"
          type="submit"
        >
          <span
            class="font-medium"
          >
            Search
          </span>
          <svg
            aria-hidden="true"
            class="ml-4 size-6 text-black"
            data-slot="icon"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </form>
  </div>
  <nav
    class="grid grid-cols-5 grid-rows-2 border-t border-gray-200 sm:grid-rows-1"
  >
    <div
      class="col-span-1 col-start-1 row-span-1 row-start-2 flex w-0 items-center justify-start sm:row-start-1 invisible"
    >
      <div
        class="border-t-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700"
      >
        <div
          class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end rounded-md border border-gray-300 shadow-sm md:w-auto lg:w-auto"
        >
          <a
            class="flex items-center justify-center rounded-md bg-white md:h-10 lg:h-10 hover:bg-gray-50 md:w-auto"
            href="?page="
          >
            <span
              class="font-medium leading-6 md:py-2 lg:py-2 text-base px-4 text-black py-1"
            >
              Prev
            </span>
          </a>
        </div>
      </div>
    </div>
    <div
      class="col-span-5 col-start-1 row-span-1 row-start-1 -mt-px flex h-16 justify-center sm:col-span-3"
    >
      <a
        class="inline-flex items-center border-t-2 px-4 pt-4 text-sm font-medium border-indigo-500 text-indigo-600"
        href="?page=1"
      >
        1
      </a>
      <a
        class="inline-flex items-center border-t-2 px-4 pt-4 text-sm font-medium border-transparent hover:border-gray-300 text-gray-500 hover:text-gray-700"
        href="?page=2"
      >
        2
      </a>
    </div>
    <div
      class="col-span-1 col-start-6 row-span-1 row-start-2 flex w-0 items-center justify-end sm:row-start-1"
    >
      <div
        class="text-sm font-medium text-gray-500 hover:text-gray-700"
      >
        <div
          class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end rounded-md border border-gray-300 shadow-sm md:w-auto lg:w-auto"
        >
          <a
            class="flex items-center justify-center rounded-md bg-white md:h-10 lg:h-10 hover:bg-gray-50 md:w-auto"
            href="?page=2"
          >
            <span
              class="font-medium leading-6 md:py-2 lg:py-2 text-base px-4 text-black py-1"
            >
              Next
            </span>
          </a>
        </div>
      </div>
    </div>
  </nav>
</div>
`;
