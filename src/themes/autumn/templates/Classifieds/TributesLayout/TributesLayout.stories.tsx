import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import { CardLayoutAd } from '../CardLayout';

import { ads } from './mocks';

import { Ad } from '.';

import type { Meta } from '@storybook/nextjs-vite';

export default {
  component: Ad,
  title: 'Notice board/Tributes & Funerals',
} as Meta<typeof Ad>;

export const MasonryCard = () => (
  <TestWrapper store={createStore()}>
    <div className="container flex gap-2">
      {ads.map((ad) => (
        <Ad item={ad} key={ad.id} />
      ))}
    </div>
  </TestWrapper>
);

export const Card = () => (
  <TestWrapper
    store={createStore((state) => ({
      ...state,
      pages: {
        ...state.pages,
        primary: {
          altMenuName: '',
          id: 1,
          menuName: '',
          name: 'Tributes & Funerals',
          showHeading: true,
          showSiblingsOnChildPages: false,
          url: TRIBUTES_FUNERALS_SLUG,
        },
      },
    }))}
  >
    <div className="container">
      {ads.map((ad) => (
        <CardLayoutAd item={ad} key={ad.id} />
      ))}
    </div>
  </TestWrapper>
);
