import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import { ads } from './mocks';

import TributesLayout from '.';

describe('<TributesLayout />', () => {
  it('renders', () => {
    const { container } = render(
      <TestWrapper store={createStore()}>
        <TributesLayout
          ads={{
            currentPage: 1,
            data: ads,
            nextPage: 2,
            numPages: 2,
            previousPage: null,
          }}
          updateModalData={() => {}}
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
