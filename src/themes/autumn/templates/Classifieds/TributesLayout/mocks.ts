import type { ClassifiedAd } from 'types/Classified';

export function mockAd(options?: Partial<ClassifiedAd>): ClassifiedAd {
  return {
    canonicalUrl: '/',
    category: {
      id: 0,
      name: 'Death Notice',
      slug: '',
    },
    categoryText: '',
    customerEmail: '',
    customerName: '',
    customerPhone: '',
    customerPostcode: '',
    customerState: '',
    customerTown: '',
    dateBorn: '',
    dateDeceased: '',
    enableComments: false,
    expirationDate: '',
    funeralDate: '',
    funeralHomeAddress: '',
    funeralHomeCity: '',
    funeralHomeName: '',
    funeralHomePostcode: '',
    funeralHomeState: '',
    funeralStartTime: '',
    funeralVenueAddress: '',
    funeralVenueCity: '',
    funeralVenueName: '',
    funeralVenuePostcode: '',
    funeralVenueState: '',
    id: 1,
    images: [],
    location: '',
    logo: null,
    publicationDate: '2025-01-01',
    quoteText: '',
    text: '',
    title: '<PERSON>, <PERSON>',
    url: '',
    yearBorn: null,
    yearDeceased: null,
    ...options,
  };
}

let id = 0;
export const ads = [
  mockAd({
    dateBorn: '1/3/1970',
    dateDeceased: '2/3/2025',
    id: (id += 1),
    location: 'Sydney, NSW',
    quoteText: 'Rest in Peace',
  }),
  mockAd({
    id: (id += 1),
    location: 'Sydney, NSW',
  }),
  mockAd({
    id: (id += 1),
    quoteText: 'Rest in Peace',
  }),
  mockAd({
    dateBorn: '1/3/1970',
    dateDeceased: '2/3/2025',
    id: (id += 1),
  }),
  mockAd({
    id: (id += 1),
    yearBorn: 1950,
    yearDeceased: 2025,
  }),
  mockAd({
    id: (id += 1),
    yearDeceased: 2025,
  }),
  mockAd({
    id: (id += 1),
  }),
];
