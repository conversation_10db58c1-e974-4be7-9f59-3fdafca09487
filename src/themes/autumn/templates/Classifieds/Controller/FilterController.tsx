'use client';

import { Menu, MenuButton } from '@headlessui/react';
import { ListBulletIcon } from '@heroicons/react/20/solid';
import { Squares2X2Icon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { useState } from 'react';

import { useAppDispatch, useAppSelector } from 'store/hooks';
import classifiedsSlice from 'store/slices/classifieds';
import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import Tooltip from 'themes/autumn/components/generic/Tooltip';
import { TooltipText } from 'themes/autumn/components/generic/Tooltip/conf';
import { TooltipPosition } from 'themes/autumn/components/generic/Tooltip/enums';
import { setGtmDataLayer } from 'util/gtm';
import { usePageHierarchy } from 'util/hooks';

import TributesAndFuneralSearchWidget from '../SearchWidget';

import FilterControllerDropDown from './FilterControllerDropDown';
import { ItemsPerPageFilterDropDown, SortFilterDropDown } from './constants';

interface Props {
  className?: string;
  isCardLayout: boolean;
}

export interface FilterDropDownProps {
  defaultSelected: string;
  items: string[];
  label: string;
}

export default function FilterController({ className, isCardLayout }: Props) {
  const dispatch = useAppDispatch();
  const primaryPageUrl = usePageHierarchy().primaryPage?.url;
  const slug = primaryPageUrl ? primaryPageUrl.split('/')[0] : '';
  const cluster = useAppSelector((state) => state.classifieds.cluster);
  // Check if the view filter is clicked globally.
  const isClicked = useAppSelector(
    (state) => state.classifieds.tooltipClicked,
  );
  // Check if the view filter is clicked locally.
  const [isTooltipClicked, setTooltipClicked] = useState<boolean>(false);

  const [sortSelected, setSortSelected] = useState<string>(
    SortFilterDropDown.defaultSelected,
  );
  const [itemsPerPageSelected, setItemsPerPageSelected] = useState<string>(
    ItemsPerPageFilterDropDown.defaultSelected,
  );

  const handleMasonryLayoutClick = (masonryLayout: boolean) => {
    setGtmDataLayer({
      data: {
        label: masonryLayout ? 'masonry_layout' : 'card_layout',
        method: 'view',
      },
      event: 'classifieds_filter',
    });

    if (window.localStorage) {
      localStorage.setItem(
        'isMasonryLayout',
        masonryLayout ? 'true' : 'false',
      );
    }
    dispatch(classifiedsSlice.actions.setMasonryLayout(masonryLayout));
    dispatch(classifiedsSlice.actions.setTooltipClicked());
    setTooltipClicked(true);
  };

  return (
    <>
      {slug === TRIBUTES_FUNERALS_SLUG && (
        <TributesAndFuneralSearchWidget showRegionToggle={!cluster} />
      )}
      <div
        className={clsx(
          'mb-2 mt-5 flex flex-row items-center gap-4 md:mb-4 md:mt-0',
          className,
          cluster ? 'justify-between' : 'justify-end',
        )}
      >
        {cluster && (
          <span className="flex items-center">
            Results for{' '}
            {cluster.name.toUpperCase() === cluster.state
              ? cluster.name
              : `${cluster.state} > ${cluster.name}`}{' '}
            <a className="ml-4 text-sm underline" href="/tributes-funerals/">
              Change region
            </a>
          </span>
        )}
        {/* TODO - will remove false in upcoming change */}
        {false && (
          <FilterControllerDropDown
            items={SortFilterDropDown.items}
            label={SortFilterDropDown.label}
            onClick={setSortSelected}
            selectedItem={sortSelected}
          />
        )}
        {false && (
          <FilterControllerDropDown
            items={ItemsPerPageFilterDropDown.items}
            label={ItemsPerPageFilterDropDown.label}
            onClick={setItemsPerPageSelected}
            selectedItem={itemsPerPageSelected}
          />
        )}
        <div className="ml-4 flex items-center gap-4">
          <div>
            <span className="md:hidden">Listing</span>
            View
          </div>
          <Tooltip
            arrowClassName="left-[112px]"
            className="!ml-[-130px] w-[165px]"
            id="classified-filter"
            isTooltipEnabled={!isClicked && !isTooltipClicked}
            mobilePosition={TooltipPosition.BOTTOM}
            onlyShowOnce
            position={TooltipPosition.BOTTOM}
            text={TooltipText.COMMUNITY_VIEW_FILTER}
          >
            <Menu as="div" className="flex cursor-pointer flex-row">
              <div>
                <MenuButton
                  className={clsx(
                    'rounded-l-md p-2 text-sm font-semibold shadow-sm ring-1 ring-inset',
                    {
                      'bg-gray-900 ring-gray-900': !isCardLayout,
                      'bg-white ring-gray-300': isCardLayout,
                    },
                  )}
                  onClick={() => handleMasonryLayoutClick(true)}
                >
                  <Squares2X2Icon
                    aria-hidden="true"
                    className={clsx('size-5', {
                      'text-gray-900': isCardLayout,
                      'text-white': !isCardLayout,
                    })}
                  />
                </MenuButton>
              </div>
              <div>
                <MenuButton
                  className={clsx(
                    'rounded-r-md p-2 pr-3 text-sm font-semibold shadow-sm ring-1 ring-inset',
                    {
                      'bg-gray-900 ring-gray-900': isCardLayout,
                      'bg-white ring-gray-300': !isCardLayout,
                    },
                  )}
                  onClick={() => handleMasonryLayoutClick(false)}
                >
                  <ListBulletIcon
                    aria-hidden="true"
                    className={clsx('-mr-1 size-5', {
                      'text-gray-900': !isCardLayout,
                      'text-white': isCardLayout,
                    })}
                  />
                </MenuButton>
              </div>
            </Menu>
          </Tooltip>
        </div>
      </div>
    </>
  );
}
