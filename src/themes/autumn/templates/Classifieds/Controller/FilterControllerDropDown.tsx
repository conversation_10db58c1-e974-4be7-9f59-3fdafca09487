import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';

export interface FilterProps {
  items: string[];
  label: string;
  onClick?: (item: string) => void;
  selectedItem: string;
}

export default function FilterControllerDropDown({
  items,
  label,
  onClick,
  selectedItem,
}: FilterProps) {
  return (
    <>
      <div>{label}</div>
      <Menu as="div" className="relative inline-block text-left">
        <MenuButton className="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
          {selectedItem}
          <ChevronDownIcon
            aria-hidden="true"
            className="-mr-1 size-5 text-gray-400"
          />
        </MenuButton>

        <MenuItems className="absolute right-0 z-10 mt-2 w-36 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-gray-500/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in">
          {items
            .filter((item) => item !== selectedItem)
            .map((item) => (
              <MenuItem
                as="button"
                className="block size-full cursor-pointer px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 data-[focus]:bg-gray-100 data-[focus]:text-gray-900"
                key={item}
                onClick={() => onClick?.(item)}
                type="button"
              >
                {item}
              </MenuItem>
            ))}
        </MenuItems>
      </Menu>
    </>
  );
}
