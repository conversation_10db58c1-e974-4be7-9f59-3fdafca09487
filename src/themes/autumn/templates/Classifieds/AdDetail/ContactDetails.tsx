import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';

import Link from 'themes/autumn/components/generic/Link';
import { sendGtmEvent } from 'themes/autumn/templates/Classifieds/utils';
import { isExternalLink } from 'util/page';

import AdImage from '../AdImage';

import type { IconDefinition } from '@fortawesome/free-solid-svg-icons';
import type { ClassifiedAd } from 'types/Classified';

export type Details = (
  | [icon: IconDefinition, value: string, href?: (value: string) => string]
  | [icon: IconDefinition, value: string | React.ReactElement]
)[];

interface Props {
  ad: ClassifiedAd;
  className?: string;
  details: Details;
  heading: string;
  withLogo?: boolean;
}

export default function ContactDetails({
  ad,
  className = 'w-full',
  details,
  heading,
  withLogo = true,
}: Props) {
  const filteredDetails = details.filter(([, text]) => !!text);

  if (!filteredDetails.length) {
    return null;
  }

  return (
    <div className={clsx('max-w-full rounded-xl border px-8 py-4', className)}>
      <p className="mb-3 font-medium">{heading}</p>
      <div className="flex flex-wrap gap-8">
        <ul className="flex min-w-48 flex-1 flex-col gap-y-3 break-words text-sm text-gray-500 md:min-w-96">
          {filteredDetails.map(([icon, text, link]) => {
            let inner = (
              <>
                <FontAwesomeIcon
                  className="mr-2 mt-1 text-gray-400"
                  fixedWidth
                  icon={icon}
                />
                {text}
              </>
            );

            if (link && typeof text === 'string') {
              const href = link(text);
              const isExternal = isExternalLink(href);
              inner = (
                <Link
                  className="hover:underline"
                  href={href}
                  isSponsored={href === ad.url}
                  noStyle
                  onClick={() => {
                    sendGtmEvent(ad, 'ad_outbound_click');
                  }}
                  target={isExternal ? '_blank' : undefined}
                >
                  {inner}
                </Link>
              );
            }

            return (
              <li className="line-clamp-1 block truncate" key={icon.iconName}>
                {inner}
              </li>
            );
          })}
        </ul>
        {withLogo && !!ad.logo && (
          <AdImage alt="Logo" url={ad.logo} width={150} />
        )}
      </div>
    </div>
  );
}
