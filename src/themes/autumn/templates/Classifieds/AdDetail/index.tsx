'use client';

import { useEffect } from 'react';

import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import { sendGtmEvent } from 'themes/autumn/templates/Classifieds/utils';
import { usePageHierarchy } from 'util/hooks';

import ClassifiedAdDetail from './ClassifiedAdDetail';
import TributeDetail from './TributeDetail';

import type { ClassifiedAd } from 'types/Classified';

interface Props {
  ad: ClassifiedAd;
  updateModalData: (item: ClassifiedAd | undefined) => void;
}

export default function AdDetail({ ad, updateModalData }: Props) {
  const primaryPageUrl = usePageHierarchy().primaryPage?.url;
  const slug = primaryPageUrl ? primaryPageUrl.split('/')[0] : '';

  const Component =
    slug === TRIBUTES_FUNERALS_SLUG ? TributeDetail : ClassifiedAdDetail;

  useEffect(() => {
    sendGtmEvent(ad, 'ad_impression');
  }, [ad]);

  return <Component ad={ad} updateModalData={updateModalData} />;
}
