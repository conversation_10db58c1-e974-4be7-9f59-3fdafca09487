'use client';

import React from 'react';

import { useAppSelector } from 'store/hooks';
import NoThumbsGallery from 'themes/autumn/components/generic/NoThumbsGallery';
import PrimaryGallery from 'themes/autumn/components/generic/PrimaryGallery';
import { GalleryItemType, GalleryLayout } from 'types/Gallery';
import { storyImageUrl } from 'util/image';

import type { ClassifiedAd } from 'types/Classified';
import type { GalleryItem } from 'types/Gallery';

type FirstImageBehaviour = 'MOVE_TO_END' | 'SKIP';

interface Props {
  ad: ClassifiedAd;
  className?: string;
  firstImageBehaviour?: FirstImageBehaviour;
}

export default function ClassifiedAdGallery({
  ad,
  className,
  firstImageBehaviour = 'MOVE_TO_END',
}: Props) {
  const photoGalleryFeature = useAppSelector(
    (state) => state.features.photoGallery,
  );
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);

  let images;
  if (firstImageBehaviour === 'MOVE_TO_END') {
    // Move the first image (print version) to the end
    images = [...ad.images];
    const firstImage = images.shift();

    if (!firstImage) {
      return null;
    }

    images.push(firstImage);
  } else {
    // SKIP
    images = ad.images.slice(1);
  }

  if (!images.length) {
    return null;
  }

  const elements: GalleryItem[] = images.map((uri) => ({
    description: '',
    type: GalleryItemType.PHOTO,
    url: storyImageUrl({
      image: {
        uri,
      },
      transformUrl,
      width: 640,
    }),
  }));

  // Gallery share link must be a path only
  let url: string | undefined;
  try {
    url = new URL(ad.canonicalUrl).pathname;
  } catch {
    url = undefined;
  }

  if (
    photoGalleryFeature.enabled &&
    photoGalleryFeature.data.layout === GalleryLayout.V2
  ) {
    return (
      <PrimaryGallery
        className={className}
        elements={elements}
        images={images}
        title={ad.title}
        url={url}
      />
    );
  }

  return (
    <NoThumbsGallery
      className={className}
      elements={elements}
      images={images}
      title={ad.title}
      url={url}
    />
  );
}
