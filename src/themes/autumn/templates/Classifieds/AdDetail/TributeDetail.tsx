import { faClock, faUserCircle } from '@fortawesome/free-regular-svg-icons';
import {
  faArrowUpFromBracket,
  faGlobeOceania,
  faMapMarked,
  faMapMarkedAlt,
} from '@fortawesome/free-solid-svg-icons';
import { format } from 'date-fns';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { useAppSelector } from 'store/hooks';
import AddToCalendarButton from 'themes/autumn/components/generic/AddToCalendarButton';
import Link from 'themes/autumn/components/generic/Link';
import ShareButton from 'themes/autumn/components/generic/ShareButton';
import AdImage from 'themes/autumn/templates/Classifieds/AdImage';
import NoticeboardQuickLinks from 'themes/autumn/templates/zoneItems/codesnippet/NoticeboardQuickLinks';
import Breadcrumb from 'themes/autumn/templates/zoneItems/navigation/common/elements/Breadcrumb';
import { capitalize, formatLocation } from 'util/string';

import { getTributeAgeDetails } from '../utils';

import ContactDetails, { type Details } from './ContactDetails';
import Gallery from './Gallery';
import Guestbook from './Guestbook';

import type { ClassifiedAd } from 'types/Classified';

interface Props {
  ad: ClassifiedAd;
  updateModalData: (item: ClassifiedAd | undefined) => void;
}

function getMapUrl(address: string) {
  return `https://www.google.com.au/maps/?q=${encodeURIComponent(address)}`;
}

export default function TributeDetail({ ad, updateModalData }: Props) {
  const ugcEnabled = useAppSelector((state) => state.features.ugc.enabled);

  const contactDetails = [
    [faUserCircle, ad.funeralHomeName],
    [
      faMapMarkedAlt,
      [
        capitalize(ad.funeralHomeAddress),
        capitalize(ad.funeralHomeCity),
        ad.funeralHomePostcode,
        ad.funeralHomeState,
      ]
        .filter(Boolean)
        .join(' '),
      getMapUrl,
    ],
    [faGlobeOceania, ad.url, (url) => url],
  ] as Details;

  const details = [...getTributeAgeDetails(ad)];

  if (ad.location) {
    details.push(formatLocation(ad.location));
  }

  const date = new Date(`${ad.funeralDate}T${ad.funeralStartTime}`);
  let funeralAddress: string | undefined = (
    `${ad.funeralVenueAddress}, ${ad.funeralVenueCity} ` +
    `${ad.funeralVenueState} ${ad.funeralVenuePostcode}`
  ).trim();
  if (funeralAddress === ',') {
    funeralAddress = undefined;
  }

  return (
    <div className="mx-auto max-w-md">
      <Breadcrumb
        breadcrumbEntryClassName=""
        className="mb-4 md:px-0 lg:hidden"
      />
      <div className="text-center">
        <Gallery ad={ad} className="mb-4" firstImageBehaviour="SKIP" />
        <span className="inline-block self-start rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800 sm:self-center">
          {ad.categoryText || ad.category.name}
        </span>
        {!!ad.title && (
          <h1 className="mb-2 mt-4 text-2xl font-medium">{ad.title}</h1>
        )}
        <p className="mb-4 text-gray-900">{details.join(' • ')}</p>
        {ad.quoteText && (
          <p className="my-4 font-serif text-2xl italic text-gray-600">
            {ad.quoteText}
          </p>
        )}
        <div className="leading-loose">
          {ad.text.split('\n').map((line, i) => (
            <p
              dangerouslySetInnerHTML={{ __html: line }}
              // eslint-disable-next-line react/no-array-index-key
              key={i}
            />
          ))}
          <p className="mb-4 text-sm text-gray-500">
            Published on {format(ad.publicationDate, 'd MMM y')}
          </p>
        </div>
        <div className="mx-auto w-28">
          <ShareButton>
            <div className="rounded-md border px-4 py-2 shadow-sm transition-all duration-200 ease-out hover:border-gray-300">
              <FontAwesomeIcon className="mr-2" icon={faArrowUpFromBracket} />
              <span className="font-medium text-gray-800">Share</span>
            </div>
          </ShareButton>
        </div>
      </div>
      <ContactDetails
        ad={ad}
        className="mt-8"
        details={[
          [
            faMapMarked,
            funeralAddress ? (
              <>
                {funeralAddress}
                <Link
                  className="ml-6 text-gray-900 underline"
                  href={getMapUrl(funeralAddress)}
                  noStyle
                  target="_blank"
                >
                  View on map
                </Link>
              </>
            ) : (
              ''
            ),
          ],
          [
            faClock,
            Number.isNaN(+date) ? (
              ''
            ) : (
              <>
                {date.toLocaleString('en-au', {
                  dateStyle: 'full',
                  timeStyle: 'short',
                })}
                <AddToCalendarButton
                  className="ml-6 text-gray-900 underline"
                  date={date}
                  location={funeralAddress}
                  name={ad.title}
                >
                  Add to calendar
                </AddToCalendarButton>
              </>
            ),
          ],
        ]}
        heading="Funeral details"
        withLogo={false}
      />
      {ad.enableComments && <Guestbook id={ad.id} />}
      <div className="gap-8 sm:flex">
        <ContactDetails
          ad={ad}
          className="grow"
          details={contactDetails}
          heading="Funeral Director"
        />
        <div className="mt-8 flex justify-center sm:m-0">
          <button
            onClick={() => {
              updateModalData(ad);
            }}
            type="button"
          >
            <AdImage
              alt={ad.title}
              height={500}
              url={ad.images[0]}
              width={375}
            />
          </button>
        </div>
      </div>
      {ugcEnabled && (
        <div className="mt-8">
          <NoticeboardQuickLinks />
        </div>
      )}
    </div>
  );
}
