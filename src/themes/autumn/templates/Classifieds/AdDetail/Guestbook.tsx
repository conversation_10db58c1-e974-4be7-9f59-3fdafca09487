'use client';

import clsx from 'clsx';
import { useEffect, useRef } from 'react';

import Comments from 'themes/autumn/components/stories/Comments';
import { StoryCommentsState } from 'types/Story';

import styles from './Guestbook.module.css';

import type { RefObject } from 'react';

interface Props {
  id: number;
}

function setGuestbookPlaceholder(ref: RefObject<HTMLDivElement | null>) {
  if (!ref.current) {
    return;
  }

  const textarea = ref.current.querySelector<HTMLTextAreaElement>(
    '.vf-content-editor-form__textarea',
  );

  if (!textarea) {
    return;
  }

  textarea.placeholder = textarea.placeholder.replace(
    /(?:Start|Join) the conversation/,
    'Leave a message',
  );
  textarea.ariaLabel = textarea.placeholder;
}

export default function Guestbook({ id }: Props) {
  const ref = useRef<HTMLDivElement>(null);

  // Viafoura does not provide a way to adjust the comment box placeholder text
  // and it can't be changed via CSS, so we need to hook the loaded event and
  // change it via script
  useEffect(() => {
    window.vfQ = window.vfQ || [];
    window.vfQ.push(() => {
      window.vf.$subscribe('commenting', 'loaded', () => {
        setGuestbookPlaceholder(ref);
        setTimeout(() => setGuestbookPlaceholder(ref), 800);
      });
    });

    return () => {
      window.vf?.$unsubscribe('commenting', 'loaded');
    };
  }, [ref]);

  return (
    <Comments
      allowAds={false}
      className={clsx(
        'my-8 w-full rounded-lg sm:border sm:p-4',
        styles.comments,
      )}
      id={`classifieds:${id}`}
      ref={ref}
      showTrendingArticles={false}
      state={StoryCommentsState.OPEN}
    />
  );
}
