import {
  faEnvelope,
  faGlobeOceania,
  faMapMarkedAlt,
  faPhone,
  faShare,
  faUserCircle,
} from '@fortawesome/free-solid-svg-icons';
import React from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { useAppSelector } from 'store/hooks';
import ShareButton from 'themes/autumn/components/generic/ShareButton';
import StoryBreadcrumb from 'themes/autumn/components/stories/StoryBreadcrumb';
import { capitalize, formatLocation } from 'util/string';

import SimilarListing from '../SimilarListing';

import ContactDetails, { type Details } from './ContactDetails';
import Gallery from './Gallery';

import type { ClassifiedAd } from 'types/Classified';

interface Props {
  ad: ClassifiedAd;
}

export default function ClassifiedAdDetail({ ad }: Props) {
  const similarAds = useAppSelector((state) => state.classifieds.similarAds);
  const primarySimilarAdsLen = useAppSelector(
    (state) => state.classifieds.primarySimilarAdsLen,
  );

  const contactDetails = [
    [faUserCircle, ad.customerName],
    [
      faMapMarkedAlt,
      capitalize(ad.location),
      (address) =>
        `https://www.google.com.au/maps/?q=${encodeURIComponent(address)}`,
    ],
    [faGlobeOceania, ad.url, (url) => url],
    [faEnvelope, ad.customerEmail, (email) => `mailto:${email}`],
    [faPhone, ad.customerPhone, (phone) => `tel:${phone}`],
  ] as Details;

  return (
    <div className="flex max-w-screen-md flex-col items-start gap-8">
      <StoryBreadcrumb className="px-4 md:px-0 lg:hidden" />
      <span className="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800">
        {ad.categoryText || ad.category.name}
      </span>
      {!!ad.title && <h1 className="text-3xl font-medium">{ad.title}</h1>}
      <div className="leading-relaxed">
        {ad.text.split('\n').map((line, i) => (
          // eslint-disable-next-line react/no-array-index-key
          <p dangerouslySetInnerHTML={{ __html: line }} key={i} />
        ))}
      </div>
      <p className="flex gap-8">
        <span>{new Date(ad.publicationDate).toLocaleDateString('en-au')}</span>
        <span>{formatLocation(ad.location)}</span>
      </p>
      <ContactDetails
        ad={ad}
        details={contactDetails}
        heading="Advertiser Details"
      />
      <Gallery ad={ad} />
      <div className="w-28">
        <ShareButton>
          <div className="rounded-md border px-4 py-2 shadow-sm transition-all duration-200 ease-out hover:border-gray-300">
            <FontAwesomeIcon className="mr-2" icon={faShare} />
            <span className="font-medium text-gray-800">Share</span>
          </div>
        </ShareButton>
      </div>

      {similarAds && (
        <SimilarListing
          classifieds={similarAds}
          primarySimilarAdsLen={primarySimilarAdsLen}
        />
      )}
    </div>
  );
}
