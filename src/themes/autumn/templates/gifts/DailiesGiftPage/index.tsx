import { useCallback } from 'react';

import PianoHook from 'components/Piano/PianoHook';
import { onPianoReady } from 'components/Piano/ready';
import { useAppSelector } from 'store/hooks';
import SubscriptionFooter from 'themes/autumn/components/footer/SubscriptionFooter';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import { sendToGtm } from 'util/gtm';

import Container from '../../../components/generic/Container';
import GiftFaqContainer from '../common/GiftFaqContainer';

export default function DailiesGiftPage(): React.ReactElement | null {
  const staticSiteUrl = useAppSelector(
    (state) => state.settings.staticSiteUrl,
  );
  const { piano: pianoFeature } = useAppSelector((state) => state.features);

  const onRedeem = useCallback(() => {
    sendToGtm({ label: 'redeem_button' });
    onPianoReady((tp) => {
      tp.offer.startRedeemVoucher();
    });
  }, []);

  if (!pianoFeature.enabled) {
    return null;
  }

  return (
    <TemplateWrapper
      footer={<SubscriptionFooter />}
      free
      hideSmartBanner
      nav={<SubscriptionNav />}
    >
      <div className="-mb-10 md:-mb-5 lg:-mb-7">
        <picture>
          <source
            media="(max-width: 767px)"
            srcSet={`${staticSiteUrl}images/subscribe/gift/mobile.jpg`}
          />
          <img
            alt="Gift"
            className="h-auto w-full"
            src={`${staticSiteUrl}images/subscribe/gift/desktop.jpg`}
          />
        </picture>
      </div>

      <div className="mx-auto max-w-[1000px]">
        <Container noGutter>
          <PianoHook
            className="mt-8 min-h-[1135px] md:mt-14 md:min-h-[468px] lg:min-h-[526px]"
            id="subscription-select-wrap-fullpage"
          />
        </Container>
      </div>

      <div className="font-inter">
        <div className="flex w-full flex-col items-center bg-gray-100 py-16">
          <div className="text-base text-black">
            Have you received a gift code?
          </div>

          <button
            className="mx-auto mt-5 h-[43px] w-[196px] select-none rounded border border-black align-middle font-bold uppercase transition-opacity hover:opacity-60 md:w-[149px] lg:w-[212px]"
            onClick={onRedeem}
            type="button"
          >
            Redeem Here
          </button>
        </div>

        <GiftFaqContainer />
      </div>
    </TemplateWrapper>
  );
}
