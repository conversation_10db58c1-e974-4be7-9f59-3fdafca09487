'use client';

import clsx from 'clsx';
import React, { useCallback } from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { sendGtmInteractionEvent } from 'themes/autumn/templates/Classifieds/utils';
import { useLoadingMoreUgcItems } from 'util/hooks';
import {
  ImageResizeMode,
  TransformOutputFormat,
  storyImageUrl,
} from 'util/image';

import type { UgcListZoneItem } from 'types/ZoneItems';
import type { UGC } from 'types/ugc';

const MAX_UGC = 20;
const LOAD_UGC_COUNT = 10;

interface GridItemProps {
  category?: string;
  id: number | string;
  image?: string;
  name: string;
  onClick?: () => void;
  title: string;
  url: string;
}

export interface StrapUgcProps {
  index: number;
  ugcItem: UGC;
}

interface GridListViewProps {
  limit?: number;
  offset: number;
  ugc: StrapUgcProps[];
}

export const ListItem: React.FC<GridItemProps> = ({
  category,
  id,
  image,
  name,
  onClick,
  title,
  url,
}) => {
  const handleLinkClick = useCallback(() => {
    onClick?.();
    sendGtmInteractionEvent(
      {
        action: 'click',
        label: title,
        section: 'story',
        signPost: category ? [category] : undefined,
        ugc_id: id,
      },
      'ugc_interaction',
    );
  }, [category, id, onClick, title]);

  return (
    <div className="flex w-full border-b border-gray-300 py-5.5 text-gray-900 last:border-b-0">
      <div className="mr-4 flex w-full grow flex-col items-start justify-start gap-y-2 md:mr-2">
        <Link
          className="flex flex-col gap-y-2"
          href={url}
          noStyle
          onClick={handleLinkClick}
        >
          <h3 className="line-clamp-2 text-base font-semibold leading-6 visited:text-gray-900 hover:text-gray-900 hover:opacity-70 md:line-clamp-1 md:text-lg">
            {title}
          </h3>
        </Link>
        <div className="flex items-center text-xs font-medium text-gray-900 md:text-sm">
          {name}
        </div>
        {category && (
          <div className="flex items-center text-xs capitalize text-gray-500 md:text-sm">
            {category}
          </div>
        )}
      </div>
      {image && (
        <Link
          className="flex w-3/12 shrink-0 flex-col items-center justify-center md:w-1/5"
          href={url}
          noStyle
          onClick={handleLinkClick}
        >
          <img
            alt={title}
            className="size-20 rounded-xl object-cover md:size-40"
            src={image}
          />
        </Link>
      )}
    </div>
  );
};

function ListView({
  limit,
  offset,
  ugc,
}: GridListViewProps): React.ReactElement | null {
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);

  const renderListView = (data: StrapUgcProps[]): React.ReactElement[] =>
    data.map((item) => {
      const { id, title } = item.ugcItem;
      return (
        <React.Fragment key={id}>
          <ListItem
            category={item.ugcItem.categoryName}
            id={id}
            image={storyImageUrl({
              fit: ImageResizeMode.MAX,
              height: 400,
              image: { uri: item.ugcItem.images[0] },
              outputFormat: TransformOutputFormat.WEBP,
              transformUrl,
              width: 400,
            })}
            name={item.ugcItem.userDetails?.userName}
            title={title}
            url={item.ugcItem.canonicalUrl}
          />
        </React.Fragment>
      );
    });

  if (ugc.length < offset || offset < 1) {
    return null;
  }

  return <>{renderListView(ugc.slice(offset - 1, limit))}</>;
}

export default function LocalExpert({
  zoneItemData: { limit, offset, ugc, ugcListId },
}: UgcListZoneItem): React.ReactElement | null {
  const index0Offset = offset - 1;
  const cappedLimit = Math.min(limit ?? ugc.length, MAX_UGC);
  const limitWithOffset = cappedLimit + index0Offset;

  const { loadMore, loadedUgcItems, loadedUgcLimit, loading, noMoreUgcItems } =
    useLoadingMoreUgcItems({
      limitWithOffset,
      loadUgcCount: LOAD_UGC_COUNT,
      ugcItems: ugc,
      ugcListId,
    });

  const hideLoadMore = loadedUgcItems.length < cappedLimit - index0Offset;

  const strapUgc: StrapUgcProps[] = loadedUgcItems
    .slice(0, loadedUgcLimit)
    .map((ugcItem, ugcIndex) => ({
      index: ugcIndex + 1,
      ugcItem,
    }));

  if (strapUgc.length > 0) {
    return (
      <div className="mb-7 mt-5.5 border-gray-300">
        <div data-testid="ugc-container">
          <div className="flex w-full flex-col items-start">
            <ListView limit={loadedUgcLimit} offset={offset} ugc={strapUgc} />
          </div>
        </div>
        <div className="mb-5 mt-6 w-full">
          {noMoreUgcItems ? (
            <div
              className="m-auto text-center font-inter text-base text-gray-500"
              data-testid="no-more"
            >
              <div className="font-medium">
                You have reached the end.
                <br />
                Fancy some more?{' '}
                <a className="text-indigo-700 underline" href="/">
                  Visit our home page
                </a>
              </div>
            </div>
          ) : (
            !hideLoadMore && (
              <button
                className={clsx(
                  'm-auto mt-10 flex select-none items-center justify-center',
                  'rounded-md border border-gray-900 px-4 py-2 text-sm',
                  'font-inter text-gray-900 hover:bg-gray-50 focus:outline-none',
                  {
                    'pointer-events-none opacity-50': loading,
                  },
                )}
                data-testid="load-button"
                onClick={() => loadMore()}
                type="button"
              >
                Load More
              </button>
            )
          )}
        </div>
      </div>
    );
  }

  return null;
}
