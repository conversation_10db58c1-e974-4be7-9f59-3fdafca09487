import { useEffect, useRef } from 'react';

import { useAppSelector } from 'store/hooks';
import { IFRAME_REFRESH_INTERVAL } from 'util/constants';
import { cssStylesToObject } from 'util/string';

import siteNameToPublicationMap from './constants';

import type { IframeZoneItem } from 'types/ZoneItems';

function JotForm({
  zoneItemData: { allow, autoRefresh, style, url },
}: IframeZoneItem): React.ReactElement {
  const ref = useRef<HTMLIFrameElement>(null);

  const siteName = useAppSelector((state) => state.conf.name);

  const mappedPublication = siteNameToPublicationMap[siteName] || siteName;

  useEffect(() => {
    if (!autoRefresh || !ref.current) {
      return () => {};
    }

    const intervalId = setInterval(() => {
      if (!ref.current) {
        return;
      }

      const iframe = ref.current;
      if (iframe) {
        iframe.setAttribute('src', iframe.src);
      }
    }, IFRAME_REFRESH_INTERVAL);
    return () => clearInterval(intervalId);
  }, [autoRefresh]);

  const styleAttrib = (style && cssStylesToObject(style)) || undefined;
  const allowAttrib = (allow?.length && allow.join(';')) || undefined;

  let finalUrl = url;

  if (mappedPublication) {
    try {
      const urlObj = new URL(url);
      const host = urlObj.hostname.toLowerCase();
      if (host === 'acm.jotform.com' || host.endsWith('.acm.jotform.com')) {
        urlObj.searchParams.set('publication', mappedPublication);
        finalUrl = urlObj.toString();
      }
    } catch {
      console.error('JotForm URL processing failed: Invalid URL');
    }
  }

  return (
    // eslint-disable-next-line jsx-a11y/iframe-has-title
    <iframe
      allow={allowAttrib}
      className="mb-4 w-full"
      loading="lazy"
      onLoad={() => window.scrollTo(0, 0)}
      ref={ref}
      src={finalUrl}
      style={styleAttrib}
    />
  );
}

export default JotForm;
