import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper } from 'util/jest';

import JotForm from '.';

import type { IframeZoneItem } from 'types/ZoneItems';

describe('JotForm Iframe Component', () => {
  const createMockProps = (
    data: Partial<IframeZoneItem['zoneItemData']> = {},
  ): IframeZoneItem => ({
    editOnly: false,
    elementId: 123,
    index: 0,
    order: 1,
    zoneItemData: {
      allow: [],
      autoRefresh: false,
      style: '',
      template: 'jotform.html',
      url: 'https://acm.jotform.com/form123',
      ...data,
    },
    zoneItemId: 456,
    zoneItemType: ZoneItemType.Iframe,
    zoneName: ZoneName.MAIN,
  });

  it('should render JotForm iframe with publication parameter', () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        name: 'Bay Post-Moruya Examiner',
      },
    }));

    const props = createMockProps({
      url: 'https://acm.jotform.com/form123',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <JotForm {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();
    expect(iframe).toHaveAttribute(
      'src',
      'https://acm.jotform.com/form123?publication=Batemans+Bay+Post',
    );
    expect(iframe).toHaveClass('mb-4', 'w-full');
    expect(iframe).toHaveAttribute('loading', 'lazy');
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should handle JotForm subdomains correctly', () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        name: 'Magnet',
      },
    }));

    const props = createMockProps({
      url: 'https://forms.acm.jotform.com/form456',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <JotForm {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();
    expect(iframe).toHaveAttribute(
      'src',
      'https://forms.acm.jotform.com/form456?publication=Eden+Magnet',
    );
  });

  it('should use siteName when not in mapping', () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        name: 'The Canberra Times',
      },
    }));

    const props = createMockProps({
      url: 'https://acm.jotform.com/form123',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <JotForm {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();
    expect(iframe).toHaveAttribute(
      'src',
      'https://acm.jotform.com/form123?publication=The+Canberra+Times',
    );
  });

  it('should handle invalid URLs gracefully', () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        name: 'Test Site',
      },
    }));

    const props = createMockProps({
      url: 'invalid-url',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <JotForm {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();
    expect(iframe).toHaveAttribute('src', 'invalid-url');
  });

  it('should render with allow attribute when provided', () => {
    const store = createStore();
    const props = createMockProps({
      allow: ['camera', 'microphone'],
      url: 'https://acm.jotform.com/form123',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <JotForm {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();
    expect(iframe).toHaveAttribute('allow', 'camera;microphone');
  });

  it('should render with style attribute when provided', () => {
    const store = createStore();
    const props = createMockProps({
      style: 'width: 100%; height: 400px;',
      url: 'https://acm.jotform.com/form123',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <JotForm {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();
    expect(iframe).toHaveStyle('width: 100%');
    expect(iframe).toHaveStyle('height: 400px');
  });

  it('should always scroll to top on load for JotForm', () => {
    const store = createStore();
    const mockScrollTo = jest.fn();
    Object.defineProperty(window, 'scrollTo', {
      value: mockScrollTo,
    });

    const props = createMockProps({
      url: 'https://acm.jotform.com/form123',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <JotForm {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();

    iframe?.dispatchEvent(new Event('load'));

    expect(mockScrollTo).toHaveBeenCalledWith(0, 0);
  });
});
