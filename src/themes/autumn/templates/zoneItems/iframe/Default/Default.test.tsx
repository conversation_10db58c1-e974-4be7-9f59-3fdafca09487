import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper } from 'util/jest';

import Default from '.';

import type { IframeZoneItem } from 'types/ZoneItems';

describe('Default Iframe Component', () => {
  const createMockProps = (
    data: Partial<IframeZoneItem['zoneItemData']> = {},
  ): IframeZoneItem => ({
    editOnly: false,
    elementId: 123,
    index: 0,
    order: 1,
    zoneItemData: {
      allow: [],
      autoRefresh: false,
      style: '',
      template: 'default.html',
      url: 'https://example.com',
      ...data,
    },
    zoneItemId: 456,
    zoneItemType: ZoneItemType.Iframe,
    zoneName: ZoneName.MAIN,
  });

  it('should render iframe with basic URL', () => {
    const store = createStore();
    const props = createMockProps({
      url: 'https://example.com/test',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <Default {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();
    expect(iframe).toHaveAttribute('src', 'https://example.com/test');
    expect(iframe).toHaveClass('mb-4', 'w-full');
    expect(iframe).toHaveAttribute('loading', 'lazy');
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should render with allow attribute when provided', () => {
    const store = createStore();
    const props = createMockProps({
      allow: ['camera', 'microphone'],
      url: 'https://example.com/test',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <Default {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();
    expect(iframe).toHaveAttribute('allow', 'camera;microphone');
  });

  it('should render with style attribute when provided', () => {
    const store = createStore();
    const props = createMockProps({
      style: 'width: 100%; height: 400px;',
      url: 'https://example.com/test',
    });

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <Default {...props} />
      </TestWrapper>,
    );

    const iframe = container.querySelector('iframe');
    expect(iframe).not.toBeNull();
    expect(iframe).toHaveStyle('width: 100%');
    expect(iframe).toHaveStyle('height: 400px');
  });
});
