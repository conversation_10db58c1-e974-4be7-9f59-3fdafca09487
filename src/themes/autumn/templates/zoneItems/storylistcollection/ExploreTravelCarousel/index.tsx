import ExploreTravelCarousel from 'themes/autumn/templates/zoneItems/carousel/common/ExploreTravelCarousel';

import type { StoryListCollectionZoneItem } from 'types/ZoneItems';

export default function ExploreTravelCollectionCarousel({
  zoneItemData: { limit, storylists, tags, title, useCanonicalUrl },
  zoneItemId,
}: StoryListCollectionZoneItem): React.ReactElement | null {
  return (
    <ExploreTravelCarousel
      limit={limit}
      storylists={storylists}
      tags={tags}
      title={title}
      useCanonicalUrl={useCanonicalUrl}
      zoneItemId={zoneItemId}
    />
  );
}
