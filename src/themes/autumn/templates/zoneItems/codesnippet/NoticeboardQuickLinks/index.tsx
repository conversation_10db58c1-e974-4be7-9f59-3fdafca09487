import React from 'react';

import QuickLinks from 'themes/autumn/components/generic/QuickLinks';

export default function NoticeboardQuickLinks(): React.ReactElement | null {
  const items = [
    {
      href: '/notice-board/our-people/',
      iconUrl: '/noticeboard-quicklinks/our-people.jpg',
      label: 'Our People',
    },
    {
      href: '/tributes-funerals/',
      iconUrl: '/noticeboard-quicklinks/tributes.jpg',
      label: 'Tributes',
    },
    {
      href: '/classifieds/',
      iconUrl: '/noticeboard-quicklinks/classifieds.jpg',
      label: 'Classifieds',
    },
    {
      href: '/notice-board/whats-on/',
      iconUrl: '/noticeboard-quicklinks/whats-on.jpg',
      label: "What's On",
    },
    {
      href: '/notice-board/photos/',
      iconUrl: '/noticeboard-quicklinks/photos.jpg',
      label: 'Photos',
    },
    {
      href: '/notice-board/local-partners/',
      iconUrl: '/noticeboard-quicklinks/local-partners.jpg',
      label: 'Local Partners',
    },
    {
      href: '/news/local-news/',
      iconUrl: '/noticeboard-quicklinks/local-news.jpg',
      label: 'Local News',
    },
    {
      href: '/sport/local-sport/',
      iconUrl: '/noticeboard-quicklinks/local-sport.jpg',
      label: 'Local Sports',
    },
  ];
  return <QuickLinks items={items} title="More Local" />;
}
