import React from 'react';

import DailymotionGallery from 'themes/autumn/components/dailymotion/Gallery';

import type { DailyMotionZoneItem } from 'types/ZoneItems';

function DailymotionGalleryTemplate({
  zoneItemData: { numberOfVideo, playerId, playlistId },
}: DailyMotionZoneItem): React.ReactElement | null {
  if (!playlistId || !playerId) {
    return null;
  }

  return (
    <DailymotionGallery
      numberOfVideo={numberOfVideo}
      playerId={playerId}
      playlistId={playlistId}
    />
  );
}

export default DailymotionGalleryTemplate;
