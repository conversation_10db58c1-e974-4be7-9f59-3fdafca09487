import React from 'react';

import DailymotionGallery from 'themes/autumn/components/dailymotion/Gallery';

import type { DailyMotionZoneItem } from 'types/ZoneItems';

function DailymotionGalleryTemplate({
  zoneItemData: { numberOfVideo, playerId, playlistId },
}: DailyMotionZoneItem): React.ReactElement | null {
  if (!playlistId || !playerId) {
    return null;
  }

  return (
    <DailymotionGallery
      aspectRatio="16/9"
      galleryTitle="Weather Forecasts"
      includeAdConfig
      numberOfVideo={numberOfVideo}
      playerId={playerId}
      playlistId={playlistId}
      showDuration={false}
      showTitle
    />
  );
}

export default DailymotionGalleryTemplate;
