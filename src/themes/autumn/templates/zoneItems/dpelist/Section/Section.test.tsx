import { render } from '@testing-library/react';

import { CompleteProfileEnrichment } from 'store/slices/features';
import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper, genMockIssues } from 'util/jest';

import Default from '.';

import type { EnhancedStore } from '@reduxjs/toolkit';
import type { RootState } from 'store/store';

describe('dpelist section template', () => {
  let store: EnhancedStore<RootState>;

  beforeEach(() => {
    store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
      },
      features: {
        ...state.features,
        dpe: {
          data: {
            dpeHowtoVideoId: 'DPE-V1234567',
            dpeId: 'CT',
            dpeIndexPageUrl: 'http://masthead/dpe/',
            host: '',
            version: 'v2',
          },
          enabled: true,
        },
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.NONE,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: false,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: false,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: false,
            supportLoginFacebook: false,
            supportLoginGoogle: false,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
      page: {
        ...state.page,
        url: 'dpe',
      },
      piano: {
        ...state.piano,
        hasDPEAccess: true,
      },
    }));
  });

  it('list page', () => {
    expect.assertions(4);
    const issuesLimit = 9;
    const mockIssues = genMockIssues(issuesLimit);

    const { container, queryByTestId } = render(
      <TestWrapper store={store}>
        <Default
          elementId={10001}
          index={1}
          order={1}
          zoneItemData={{
            dpeId: 'GAN',
            issues: mockIssues,
            limit: issuesLimit,
            template: 'default.html',
            useCanonicalUrl: false,
          }}
          zoneItemId={10001}
          zoneItemType={ZoneItemType.DPEList}
          zoneName={ZoneName.MAIN}
        />
      </TestWrapper>,
    );

    expect(queryByTestId('latest-edition-link')).not.toBeInTheDocument();
    expect(queryByTestId('latest-edition-image')).not.toBeInTheDocument();
    expect(queryByTestId('next-page')).not.toBeInTheDocument();
    expect(container.firstChild).toMatchSnapshot();
  });
});
