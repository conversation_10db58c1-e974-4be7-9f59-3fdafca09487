// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<WeatherCard /> renders forecasts 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="flex flex-col gap-4 text-gray-900"
  >
    <h3
      class="text-lg font-bold"
    >
      <a
        href="/weather/"
      >
        Weather Forecast
        <i>
          {"icon":{"prefix":"fas","iconName":"chevron-right"},"className":"ml-3"}
        </i>
      </a>
    </h3>
    <p>
      in 
      Griffith
    </p>
    <svg
      style="display: none;"
    >
      <symbol
        id="weather-shower"
        viewBox="14 14 36 36"
      >
        <g
          fill="#E1E2E3"
        >
          <path
            d="M23.5 33.5h18V39h-18z"
          />
          <circle
            cx="32.5"
            cy="30"
            r="9"
          />
          <circle
            cx="23.5"
            cy="32.5"
            r="6.5"
          />
          <circle
            cx="41.5"
            cy="33.5"
            r="5.5"
          />
        </g>
        <path
          d="M30 40l-2.2 3.5c-.3.5-.9.6-1.3.3-.5-.3-.6-.9-.3-1.3l.3-.3L30 40zm-8 0l-2.2 3.5c-.3.5-.9.6-1.3.3-.5-.3-.6-.9-.3-1.3l.3-.3L22 40zm16 0l-2.2 3.5c-.3.5-.9.6-1.3.3s-.6-.9-.3-1.3l.3-.3L38 40zm8 0l-2.2 3.5c-.3.5-.9.6-1.3.3s-.6-.9-.3-1.3l.3-.3L46 40z"
          fill="#2196F3"
        />
      </symbol>
      <symbol
        id="weather-sunny"
        viewBox="14 14 36 36"
      >
        <circle
          cx="32"
          cy="32"
          fill="#FFC107"
          r="12"
        />
      </symbol>
      <symbol
        id="weather-snow"
        viewBox="14 14 36 36"
      >
        <circle
          cx="38.5"
          cy="39"
          fill="#C4C5C6"
          r="1"
        />
        <circle
          cx="38.5"
          cy="43"
          fill="#C4C5C6"
          r="1"
        />
        <circle
          cx="42"
          cy="41"
          fill="#C4C5C6"
          r="1"
        />
        <circle
          cx="38.5"
          cy="35"
          fill="#C4C5C6"
          r="1"
        />
        <circle
          cx="42"
          cy="37"
          fill="#C4C5C6"
          r="1"
        />
        <circle
          cx="35"
          cy="41"
          fill="#C4C5C6"
          r="1"
        />
        <circle
          cx="35"
          cy="37"
          fill="#C4C5C6"
          r="1"
        />
        <g
          fill="#C4C5C6"
        >
          <circle
            cx="27.8"
            cy="28.5"
            r="1.5"
          />
          <circle
            cx="27.8"
            cy="34.5"
            r="1.5"
          />
          <circle
            cx="33"
            cy="31.5"
            r="1.5"
          />
          <circle
            cx="27.8"
            cy="22.5"
            r="1.5"
          />
          <circle
            cx="33"
            cy="25.5"
            r="1.5"
          />
          <circle
            cx="22.5"
            cy="31.5"
            r="1.5"
          />
          <circle
            cx="22.5"
            cy="25.5"
            r="1.5"
          />
          <path
            d="M27.2 22.5h1v12h-1z"
          />
          <path
            d="M22.745 25.062l10.506 6.003-.497.868L22.25 25.93z"
          />
          <path
            d="M32.736 25.13l.496.869L22.727 32l-.496-.868z"
          />
        </g>
      </symbol>
      <symbol
        id="weather-storm"
        viewBox="14 14 36 36"
      >
        <g
          fill="#E1E2E3"
        >
          <path
            d="M23.5 33.5h18V39h-18z"
          />
          <circle
            cx="32.5"
            cy="30"
            r="9"
          />
          <circle
            cx="23.5"
            cy="32.5"
            r="6.5"
          />
          <circle
            cx="41.5"
            cy="33.5"
            r="5.5"
          />
        </g>
        <path
          d="M28.5 43.5l3.8-7.9 1.2 2.4h-6.6l2.4-2.4 6.2-6.1-3.8 7.9-1.2-2.4h6.6l-2.4 2.4z"
          fill="#FFC107"
        />
      </symbol>
      <symbol
        id="weather-fog"
        viewBox="14 14 36 36"
      >
        <g
          fill="#E1E2E3"
        >
          <path
            d="M23.5 35.5h18V41h-18z"
          />
          <circle
            cx="32.5"
            cy="32"
            r="9"
          />
          <circle
            cx="23.5"
            cy="34.5"
            r="6.5"
          />
          <circle
            cx="41.5"
            cy="35.5"
            r="5.5"
          />
        </g>
      </symbol>
      <symbol
        id="weather-cloudy"
        viewBox="14 14 36 36"
      >
        <g
          fill="#C4C5C6"
        >
          <path
            d="M23.5 34.5h18V40h-18z"
          />
          <circle
            cx="32.5"
            cy="31"
            r="9"
          />
          <circle
            cx="23.5"
            cy="33.5"
            r="6.5"
          />
          <circle
            cx="41.5"
            cy="34.5"
            r="5.5"
          />
        </g>
        <g
          fill="#E1E2E3"
        >
          <path
            d="M27.8 39h11.5v4H27.8z"
          />
          <circle
            cx="33.5"
            cy="37.5"
            r="5.5"
          />
          <circle
            cx="39"
            cy="39.5"
            r="3.5"
          />
          <circle
            cx="28"
            cy="40"
            r="3"
          />
        </g>
      </symbol>
      <symbol
        id="weather-windy"
        viewBox="14 14 36 36"
      >
        <path
          d="M35.5 34H18c-.6 0-1-.4-1-1s.4-1 1-1h17.5c2.2 0 4-1.8 4-4s-1.8-4-4-4-4 1.8-4 4c0 .6-.4 1-1 1s-1-.4-1-1c0-3.3 2.7-6 6-6s6 2.7 6 6-2.7 6-6 6z"
          fill="#C4C5C6"
        />
        <path
          d="M27.5 31H21c-.6 0-1-.4-1-1s.4-1 1-1h6.5c.6 0 1 .4 1 1s-.4 1-1 1zM43 43c-2.2 0-4-1.8-4-4 0-.6.4-1 1-1s1 .4 1 1c0 1.1.9 2 2 2s2-.9 2-2-.9-2-2-2H24.5c-.6 0-1-.4-1-1s.4-1 1-1H43c2.2 0 4 1.8 4 4s-1.8 4-4 4z"
          fill="#C4C5C6"
        />
      </symbol>
      <symbol
        id="weather-rain"
        viewBox="0 0 22 21"
      >
        <path
          clip-rule="evenodd"
          d="m18.9628.71172c-.1585-.316956-.4754-.50713-.8558-.50713-.3486 0-.6656.190174-.8558.50713-.5071.95087-2.0918 4.18382-2.0918 5.42001 0 1.61648 1.3312 2.94764 2.9477 2.94764 1.6164 0 2.9476-1.33122 2.9476-2.94764 0-1.12299-1.4053-4.00944-1.9807-5.19113-.0417-.085701-.079-.162435-.1112-.22888zm-.8558 7.41668c-1.0776 0-1.9968-.88748-1.9968-1.99684 0-1.07765 1.9968-4.97616 1.9968-4.97616s1.9969 3.89861 1.9969 4.97616c0 1.10936-.8875 1.99684-1.9969 1.99684zm-7.7971-.76045c.1584-.31696.5071-.50713.8558-.50713.3486 0 .6657.22188.8242.50713.5704 1.10919 3.3596 6.71945 3.3596 8.65285 0 2.3138-1.9017 4.1838-4.2155 4.1838-2.31373 0-4.18378-1.8701-4.18378-4.1838 0-1.9334 2.78912-7.5435 3.35968-8.65285zm-2.37731 8.68445c0 1.7751 1.42642 3.2331 3.23311 3.2331v-.0002c1.775 0 3.233-1.4581 3.233-3.2647 0-1.775-3.233-8.17737-3.233-8.17737s-3.23311 6.40257-3.23311 8.20917zm3.23331 2.3455c-.1902 0-.317-.1268-.317-.317 0-.1584.1268-.317.317-.317.9509 0 1.7433-.7924 1.7433-1.7432 0-.1902.1268-.317.3169-.317.1902 0 .317.1268.317.317 0 1.2995-1.0777 2.3772-2.3772 2.3772zm6.9411-10.6814c-.1902 0-.317-.12678-.317-.31696 0-.19017.1585-.31695.317-.31695.5071 0 .9192-.41204.9192-.91917 0-.19017.1267-.31695.3169-.31695s.317.12678.317.31695c0 .85578-.6973 1.55308-1.5531 1.55308zm-14.80182-4.02547c-.34866 0-.6973.22188-.85579.53882-.50713 1.01426-1.679859 3.51827-1.679859 4.53253 0 1.42632 1.109359 2.53562 2.535649 2.53562 1.4263 0 2.53565-1.1093 2.53565-2.53562 0-1.01426-1.17272-3.51827-1.67986-4.53253-.15847-.31696-.50713-.53882-.85579-.53882zm0 6.65597c-.88748 0-1.58478-.69729-1.58478-1.58477 0-.88747 1.58478-4.12033 1.58478-4.12033s1.58478 3.23306 1.58478 4.12033c0 .88728-.6973 1.58477-1.58478 1.58477zm.00011-.47521c-.19017 0-.31696-.12678-.31696-.31695 0-.15848.12679-.31696.31696-.31696.28527 0 .47545-.19019.47545-.47544 0-.19018.12678-.31696.31695-.31696s.31696.12678.31696.31696c0 .63391-.47545 1.10935-1.10936 1.10935z"
          fill="#2563eb"
          fill-rule="evenodd"
        />
      </symbol>
    </svg>
    <div
      class="flex px-6 py-3"
    >
      <span
        class="flex-1"
      >
        Wed 12
      </span>
      <span
        class="flex-1"
      >
        33
         º / 
        20
         º
      </span>
      <span>
        <svg
          class="inline"
          height="24"
          width="24"
        >
          <use
            xlink:href="#weather-sunny"
          />
        </svg>
      </span>
      <span
        class="flex flex-1 justify-end"
      >
        <svg
          class="inline mr-2"
          height="24"
          width="24"
        >
          <use
            xlink:href="#weather-rain"
          />
        </svg>
        <span
          class="inline-block w-10"
        >
          5
          %
        </span>
      </span>
    </div>
    <div
      class="flex px-6 py-3 rounded bg-gray-100 font-bold"
    >
      <span
        class="flex-1"
      >
        Today
      </span>
      <span
        class="flex-1"
      >
        35
         º / 
        20
         º
      </span>
      <span>
        <svg
          class="inline"
          height="24"
          width="24"
        >
          <use
            xlink:href="#weather-sunny"
          />
        </svg>
      </span>
      <span
        class="flex flex-1 justify-end"
      >
        <svg
          class="inline mr-2"
          height="24"
          width="24"
        >
          <use
            xlink:href="#weather-rain"
          />
        </svg>
        <span
          class="inline-block w-10"
        >
          20
          %
        </span>
      </span>
    </div>
    <div
      class="flex px-6 py-3"
    >
      <span
        class="flex-1"
      >
        Fri 14
      </span>
      <span
        class="flex-1"
      >
        37
         º / 
        21
         º
      </span>
      <span>
        <svg
          class="inline"
          height="24"
          width="24"
        >
          <use
            xlink:href="#weather-sunny"
          />
        </svg>
      </span>
      <span
        class="flex flex-1 justify-end"
      >
        <svg
          class="inline mr-2"
          height="24"
          width="24"
        >
          <use
            xlink:href="#weather-rain"
          />
        </svg>
        <span
          class="inline-block w-10"
        >
          5
          %
        </span>
      </span>
    </div>
  </div>
</div>
`;
