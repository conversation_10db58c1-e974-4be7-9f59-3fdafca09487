import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import WithBorderComponent from './WithBorder';

import Component from '.';

import type { Meta, StoryObj } from '@storybook/nextjs-vite';

const meta: Meta<typeof Component> = {
  component: Component,
  title: 'Weather/Card',
};

export default meta;

type Story = StoryObj<typeof Component>;

const store = createStore((state) => ({
  ...state,

  conf: {
    ...state.conf,
    location: 'Griffith, NSW',
  },
  racetracks: {
    ...state.racetracks,
    sochiUrl: 'https://sochi-stag.stag.newsnow.io/',
  },
  runtime: {
    ...state.runtime,
    isClientSide: true,
  },
}));

export const Default: Story = {
  render: () => (
    <TestWrapper store={store}>
      <Component />
    </TestWrapper>
  ),
};

export const WithBorder: Story = {
  render: () => (
    <TestWrapper store={store}>
      <WithBorderComponent />
    </TestWrapper>
  ),
};
