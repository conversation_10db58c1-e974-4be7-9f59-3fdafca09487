import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import { format } from 'date-fns';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import {
  WeatherIcon,
  WeatherSprite,
} from 'themes/autumn/components/icons/WeatherIcons';
import { phraseToIconName } from 'themes/autumn/components/icons/WeatherIcons/utils';
import { useRegion, useWeather } from 'util/hooks';
import { useDate } from 'util/time';

interface Props {
  className?: string;
}

export default function WeatherCard({
  className,
}: Props): React.ReactElement | null {
  const region = useRegion();

  const { data, error, isLoading } = useWeather({
    days: 3,
    fc: 2,
  });
  const today = useDate().getDay();

  if (!region) {
    return <div>Error: Need to set site location to fetch weather.</div>;
  }

  if (isLoading) {
    return <p>Loading...</p>;
  }

  if (error || !data) {
    return <div>Sorry, weather data temporarily unavailable.</div>;
  }

  const { forecasts } = data;

  if (!forecasts) {
    return null;
  }

  return (
    <div className={clsx('flex flex-col gap-4 text-gray-900', className)}>
      <h3 className="text-lg font-bold">
        <a href="/weather/">
          Weather Forecast
          <FontAwesomeIcon className="ml-3" icon={faChevronRight} />
        </a>
      </h3>
      <p>in {region}</p>
      <WeatherSprite />
      {forecasts.map((forecast) => {
        const date = new Date(forecast.date);
        const iconName = phraseToIconName(forecast.iconPhrase);
        const isToday = date.getDay() === today;
        return (
          <div
            className={clsx(
              'flex px-6 py-3',
              isToday && 'rounded bg-gray-100 font-bold',
            )}
            key={forecast.date}
          >
            <span className="flex-1">
              {isToday ? 'Today' : format(date, 'E d')}
            </span>
            <span className="flex-1">
              {forecast.max} º / {forecast.min} º
            </span>
            <span>
              {iconName && <WeatherIcon name={iconName} size={24} />}
            </span>
            <span className="flex flex-1 justify-end">
              <WeatherIcon className="mr-2" name="rain" size={24} />
              <span className="inline-block w-10">{forecast.rainProb}%</span>
            </span>
          </div>
        );
      })}
    </div>
  );
}
