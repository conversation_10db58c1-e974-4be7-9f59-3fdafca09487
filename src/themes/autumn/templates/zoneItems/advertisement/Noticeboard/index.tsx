import Ad from 'themes/autumn/components/ads/Ad';
import { AdSize } from 'util/ads';
import { useLazyLoadComponentState } from 'util/hooks';

import type { AdvertisementZoneItem } from 'types/ZoneItems';

export default function Noticeboard({
  zoneItemData: { position },
  zoneItemId,
}: AdvertisementZoneItem): React.ReactElement | null {
  const { showComponentPlaceholder } = useLazyLoadComponentState();

  if (!showComponentPlaceholder) {
    return null;
  }

  return (
    <div className="my-6">
      <Ad
        autoRefresh={false}
        mdSizes={AdSize.noticeboardDesktop}
        position={position}
        publiftName="incontent-spons-hrec"
        sizes={AdSize.noticeboardMobile}
        slotId={`noticeboard-${zoneItemId}`}
      />
    </div>
  );
}
