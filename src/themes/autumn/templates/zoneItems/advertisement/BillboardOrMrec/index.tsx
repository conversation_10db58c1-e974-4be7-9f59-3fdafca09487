import Ad from 'themes/autumn/components/ads/Ad';
import { AdSize } from 'util/ads';
import { useLazyLoadComponentState } from 'util/hooks';

import type { AdvertisementZoneItem } from 'types/ZoneItems';

export default function BillboardOrMrec({
  zoneItemData: { position },
  zoneItemId,
}: AdvertisementZoneItem): React.ReactElement | null {
  const { showComponentPlaceholder } = useLazyLoadComponentState();
  if (!showComponentPlaceholder) {
    return null;
  }

  return (
    <div className="my-6">
      <Ad
        mdSizes={AdSize.billboard}
        position={position}
        publiftName={`incontent-hrec-lg-${Math.min(4, position)}`}
        sizes={AdSize.mrec}
        slotId={`billboard-or-mrec-${zoneItemId}`}
      />
    </div>
  );
}
