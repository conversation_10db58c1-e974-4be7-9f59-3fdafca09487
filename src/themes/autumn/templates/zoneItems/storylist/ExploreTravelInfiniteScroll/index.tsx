import clsx from 'clsx';
import {
  Fragment,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { useAppSelector } from 'store/hooks';
import Ad from 'themes/autumn/components/ads/Ad';
import { sendGtmInteractionEvent } from 'themes/autumn/templates/Classifieds/utils';
import ExploreStrapStoryCard from 'themes/autumn/templates/zoneItems/carousel/common/ExploreTravelCarousel/ExploreStrapStoryCard';
import {
  CardType,
  type StoryOrNull,
  // eslint-disable-next-line @stylistic/max-len
} from 'themes/autumn/templates/zoneItems/carousel/common/ExploreTravelCarousel/types';
import { ImagePosition, type StoryListZoneItem } from 'types/ZoneItems';
import StoryListContext from 'util/ZoneItems/context/storylist-context';
import { AdSize } from 'util/ads';
import { DeviceType } from 'util/device';
import { useDeviceTypeFromWidth } from 'util/hooks';
import { fetchStoriesForStoryList } from 'util/organization/suzuka';

import Button from './Button';
import { getValidBoolean, getValidNumber } from './utils';

export default function ExploreTravelInfiniteScroll({
  zoneItemData: {
    enableOrder,
    label,
    largeLeadStory,
    limit,
    offset,
    storyListId,
    storyListTitle,
    summaryOptions,
    useCanonicalUrl,
  },
  zoneItemId,
}: StoryListZoneItem): React.ReactElement | null {
  const batchSize = limit ?? 8;
  const deviceType = useDeviceTypeFromWidth();
  const { siteId } = useAppSelector((state) => state.settings);

  const [storyList, setStoryList] = useState<StoryOrNull[]>([]);
  const [currentMaxOffset, setCurrentMaxOffset] = useState(offset);
  const [hasReachedEnd, setHasReachedEnd] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [saveScrollPosition, setSaveScrollPosition] = useState(false);
  const [currentMinOffset, setCurrentMinOffset] = useState(-1);
  const [isFetchingPrev, setIsFetchingPrev] = useState(false);
  const [enableTopInfiniteScroll, setEnableTopInfiniteScroll] =
    useState(false);

  const bottomLoaderRef = useRef(null);
  const topLoaderRef = useRef(null);
  const isFetchedRef = useRef(false);

  const fetchStories = useCallback(
    async (newOffset: number, isLoadingPrev: boolean = false) => {
      if ((isLoading || hasReachedEnd) && !isLoadingPrev) return;

      setIsLoading(true);
      if (isLoadingPrev) {
        setStoryList((prevStories) => [null, ...prevStories]);
      } else {
        setStoryList((prevStories) => [...prevStories, null]);
      }

      try {
        const stories = await fetchStoriesForStoryList({
          limit: batchSize,
          offset: newOffset,
          siteId,
          storyListId,
          useCanonicalUrl,
        });
        sendGtmInteractionEvent(
          {
            action: 'scroll',
            label: (newOffset / batchSize).toString(),
            section: storyListTitle,
          },
          'infinite_scroll',
        );
        const storiesWithOffset = stories.map((story) => ({
          ...story,
          offset: newOffset,
        }));
        if (isLoadingPrev) {
          setStoryList((prevStories) => [
            ...storiesWithOffset,
            ...prevStories.filter((story) => story !== null),
          ]);
          setCurrentMinOffset(newOffset);
        } else {
          setStoryList((prevStories) => [
            ...prevStories.filter((story) => story !== null),
            ...storiesWithOffset,
          ]);
          setCurrentMaxOffset(newOffset);
          sessionStorage.setItem('lastOffset', newOffset.toString());
          setHasReachedEnd(stories.length < batchSize);
        }

        // eslint-disable-next-line no-empty
      } catch {}
      setIsLoading(false);
    },
    [
      batchSize,
      hasReachedEnd,
      isLoading,
      siteId,
      storyListId,
      storyListTitle,
      useCanonicalUrl,
    ],
  );

  // Top Scroll
  useEffect(() => {
    if (!enableTopInfiniteScroll || isFetchingPrev) {
      return () => {};
    }
    const topLoader = topLoaderRef.current;

    // eslint-disable-next-line compat/compat
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];

        if (target.isIntersecting && !isFetchingPrev) {
          const prevOffset = currentMinOffset - batchSize;
          if (prevOffset >= 0) {
            setIsFetchingPrev(true);
            fetchStories(prevOffset, true)
              .then(() => {})
              .catch(() => {})
              .finally(() => {
                setIsFetchingPrev(false);
              });
          } else {
            setEnableTopInfiniteScroll(false);
          }
        }
      },
      { rootMargin: '500px 0px 0px 0px' },
    );
    if (topLoader) {
      observer.observe(topLoader);
    }
    return () => {
      if (topLoader) {
        observer.unobserve(topLoader);
      }
    };
  }, [
    batchSize,
    currentMinOffset,
    enableTopInfiniteScroll,
    fetchStories,
    isFetchingPrev,
  ]);

  // Bottom scroll
  useEffect(() => {
    const bottomLoader = bottomLoaderRef.current;
    // eslint-disable-next-line compat/compat
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting) {
          fetchStories(currentMaxOffset + batchSize).catch(
            () => console.error,
          );
        }
      },
      { rootMargin: '500px 0px 0px 0px' },
    );
    if (bottomLoader) {
      observer.observe(bottomLoader);
    }
    return () => {
      if (bottomLoader) {
        observer.unobserve(bottomLoader);
      }
    };
  }, [currentMaxOffset, fetchStories, batchSize]);

  useEffect(() => {
    const handleScroll = () => {
      if (saveScrollPosition) {
        sessionStorage.setItem(
          'lastScrollYPosition',
          window.scrollY.toString(),
        );
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [saveScrollPosition]);

  const resetSession = () => {
    sessionStorage.removeItem('lastOffset');
    sessionStorage.removeItem('lastScrollYPosition');
    sessionStorage.removeItem('restoreArticleListPosition');
    sessionStorage.removeItem('lastStoryId');
  };

  // Restore article list position
  useEffect(() => {
    const restoreArticleListPosition = getValidBoolean(
      'restoreArticleListPosition',
    );
    const lastStoryOffset = getValidNumber('lastStoryOffset');
    // eslint-disable-next-line compat/compat
    const navigationEntry = performance.getEntriesByType(
      'navigation',
    )[0] as PerformanceNavigationTiming;
    if (!isFetchedRef.current) {
      isFetchedRef.current = true;
      if (
        navigationEntry.type === 'back_forward' &&
        restoreArticleListPosition === true &&
        lastStoryOffset
      ) {
        setCurrentMinOffset(Number(lastStoryOffset));
        setIsRestoring(true);
        setSaveScrollPosition(false);
        fetchStories(Number(lastStoryOffset))
          .then(() => {
            const prevOffset = Number(lastStoryOffset) - batchSize;
            if (prevOffset >= 0) {
              fetchStories(prevOffset, true).catch(() => {});
            }
          })
          .catch(() => {});
      } else {
        setSaveScrollPosition(true);
        window.scrollTo(0, 0);
        fetchStories(currentMaxOffset).catch(() => console.error);
        resetSession();
      }
    }
  }, [batchSize, currentMaxOffset, fetchStories]);

  useLayoutEffect(() => {
    const lastScrollYPosition = getValidNumber('lastScrollYPosition');
    const lastStoryId = getValidNumber('lastStoryId');
    if (isRestoring && !isLoading && lastScrollYPosition && lastStoryId > 0) {
      // scroll to the story
      const storyElement = document.getElementById(
        `infinite-scroll-story-${lastStoryId}`,
      );
      if (storyElement) {
        // scroll instantly to the story
        storyElement.scrollIntoView({ behavior: 'instant', block: 'center' });
      }
      resetSession();
      setIsRestoring(false);
      setSaveScrollPosition(true);
      setEnableTopInfiniteScroll(true);
    }
  }, [isLoading, isRestoring]);

  const getCardType = (story: StoryOrNull): CardType => {
    if (story) {
      return CardType.CONTENT;
    }
    if (isLoading) {
      return CardType.LOADING;
    }
    return CardType.PADDING;
  };

  const onBackToTop = () => {
    window.scrollTo(0, 0);
    resetSession();
  };

  const value = useMemo(
    () => ({
      enableOrder,
      label,
      storyListId,
      storyListTitle,
      summaryOptions,
      useCanonicalUrl,
      zoneItemId,
    }),
    [
      enableOrder,
      label,
      storyListId,
      storyListTitle,
      summaryOptions,
      useCanonicalUrl,
      zoneItemId,
    ],
  );

  return (
    <StoryListContext.Provider value={value}>
      <div className="container mx-auto flex flex-col">
        <div className="h-1" ref={topLoaderRef} />

        <div className="divide-y divide-gray-300 md:flex md:flex-col md:gap-7 md:divide-y-0">
          {storyList.map((story, index) => {
            const adCount = Math.floor(index / 8) + 1;
            const showAd = index > 0 && adCount <= 10 && (index + 1) % 8 === 0;
            const hideDivide = index > 1 && (index + 1) % 8 === 1;
            return (
              <Fragment key={story?.id ?? `no-story-${index}`}>
                <ExploreStrapStoryCard
                  containerClassName={clsx(
                    'w-full md:py-0 md:mx-auto lg:max-w-[840px]',
                    {
                      'border-none': hideDivide,
                      'pb-7 pt-0': index === 0,
                      'py-7': index !== 0,
                    },
                  )}
                  enableDivider={
                    deviceType !== DeviceType.MOBILE &&
                    !largeLeadStory &&
                    index !== 0 &&
                    !hideDivide
                  }
                  headlineWrapperClassName={
                    deviceType === DeviceType.MOBILE
                      ? undefined
                      : 'items-start'
                  }
                  idPrefix="infinite-scroll"
                  imageHeight={
                    // eslint-disable-next-line no-nested-ternary
                    deviceType === DeviceType.MOBILE
                      ? 262
                      : largeLeadStory && index === 0
                        ? 280
                        : 144
                  }
                  imagePosition={
                    deviceType === DeviceType.MOBILE
                      ? undefined
                      : ImagePosition.RIGHT
                  }
                  imageWidth={
                    // eslint-disable-next-line no-nested-ternary
                    deviceType === DeviceType.MOBILE
                      ? 393
                      : largeLeadStory && index === 0
                        ? 420
                        : 216
                  }
                  imageWrapperClassName={
                    deviceType === DeviceType.MOBILE
                      ? undefined
                      : clsx('h-full items-end !ml-0', {
                          'my-auto': !largeLeadStory || index > 0,
                        })
                  }
                  leadingImageClassName={clsx(
                    'mb-2 md:mb-0 md:h-full md:my-auto',
                    {
                      'md:max-w-[180px] lg:max-w-48 xl:max-w-[216px]':
                        !largeLeadStory || index > 0,
                      'md:max-w-[300px] lg:max-w-[360px] xl:max-w-[420px]':
                        largeLeadStory && index === 0,
                    },
                  )}
                  restoreArticleListPosition
                  showSummaryInStoryList
                  sourceClassName="text-xs md:text-sm"
                  story={story || undefined}
                  storyListId={storyListId}
                  summaryFontClassName={
                    deviceType === DeviceType.MOBILE
                      ? undefined
                      : clsx('!font-inter !line-clamp-2', {
                          'md:!leading-5': !largeLeadStory || index > 0,
                          'md:!leading-6': largeLeadStory && index === 0,
                        })
                  }
                  textBlockPaddingClassName={
                    deviceType === DeviceType.MOBILE
                      ? undefined
                      : clsx({
                          'md:pr-8': largeLeadStory && index === 0,
                          // TODO: probably should be "md:pr-11" as a nested
                          // "pr-7" gets ordered priority but changes layout.
                          'pr-11 md:my-auto': !largeLeadStory || index > 0,
                        })
                  }
                  titleSize={
                    deviceType === DeviceType.MOBILE
                      ? undefined
                      : clsx('md:pt-0', {
                          'md:text-3xl md:leading-9':
                            largeLeadStory && index === 0,
                          'md:text-xl md:leading-[30px]':
                            !largeLeadStory || index > 0,
                        })
                  }
                  type={getCardType(story)}
                />
                {showAd && (
                  <div className="mx-auto my-6">
                    <Ad
                      className="px-4"
                      lgSizes={AdSize.billboard}
                      mdSizes={AdSize.mrec}
                      position={adCount + 1}
                      publiftName="incontent-hrec-lg-1"
                      sizes={AdSize.mrec}
                      slotId={`incontent-hrec-lg-1-${adCount + 1}`}
                    />
                  </div>
                )}
              </Fragment>
            );
          })}
        </div>

        {hasReachedEnd && <Button label="Back to top" onClick={onBackToTop} />}

        <div className="h-2" ref={bottomLoaderRef} />
      </div>
    </StoryListContext.Provider>
  );
}
