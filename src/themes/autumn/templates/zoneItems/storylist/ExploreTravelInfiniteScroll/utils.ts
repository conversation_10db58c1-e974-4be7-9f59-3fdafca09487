export function getValidBoolean(key: string, defaultValue = false): boolean {
  const value = sessionStorage.getItem(key);
  if (value === 'true') return true;
  if (value === 'false') return false;
  return defaultValue;
}

export function getValidNumber(key: string, defaultValue = 0): number {
  const value = sessionStorage.getItem(key);
  const num = Number(value);
  return Number.isNaN(num) ? defaultValue : num;
}
