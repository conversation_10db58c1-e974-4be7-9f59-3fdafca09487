import {
  act,
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper, genMockStories, renderZoneItemData } from 'util/jest';
import { fetchStoriesForStoryList } from 'util/organization/suzuka';

import ExploreTravelInfiniteScroll from '.';

jest.mock('util/organization/suzuka', () => ({
  fetchStoriesForStoryList: jest.fn(),
}));

beforeAll(() => {
  // eslint-disable-next-line compat/compat
  performance.getEntriesByType = jest.fn(() => [
    {
      duration: 0,
      entryType: 'navigation',
      name: '',
      startTime: 0,
      toJSON: () => ({}),
    },
  ]);

  class MockIntersectionObserver {
    observe = jest.fn();

    unobserve = jest.fn();

    disconnect = jest.fn();

    root: Element | null = null;

    rootMargin: string = '';

    thresholds: ReadonlyArray<number> = [];

    takeRecords = jest.fn();

    // eslint-disable-next-line @typescript-eslint/no-useless-constructor, @typescript-eslint/no-empty-function
    constructor() {}
  }

  Object.defineProperty(global, 'IntersectionObserver', {
    configurable: true,
    value: MockIntersectionObserver,
    writable: true,
  });
});

describe('ExploreTravelInfiniteScroll Component', () => {
  let store: ReturnType<typeof createStore>;

  beforeEach(() => {
    store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));
    sessionStorage.clear();
  });

  jest.mock('util/organization/suzuka', () => ({
    fetchStoriesForStoryList: jest.fn(),
  }));

  const mockedFetchStoriesForStoryList = fetchStoriesForStoryList as jest.Mock;

  it('should fetch and display stories on scroll', async () => {
    act(() => {
      render(
        <TestWrapper store={store}>
          <ExploreTravelInfiniteScroll
            elementId={0}
            index={0}
            order={0}
            zoneItemData={renderZoneItemData(
              genMockStories({ length: 4 }),
              10,
              true,
              true,
              true,
            )}
            zoneItemId={0}
            zoneItemType={ZoneItemType.StoryList}
            zoneName={ZoneName.BANNER}
          />
        </TestWrapper>,
      );
    });

    await waitFor(() => {
      expect(fetchStoriesForStoryList).toHaveBeenCalled();
    });

    act(() => {
      fireEvent.scroll(window, { target: { scrollY: 500 } });
    });

    await waitFor(() => {
      expect(fetchStoriesForStoryList).toHaveBeenCalledTimes(1);
    });
  });

  it('should show the Back to Top button when reaching the end', async () => {
    mockedFetchStoriesForStoryList.mockResolvedValue([]);

    act(() => {
      render(
        <TestWrapper store={store}>
          <ExploreTravelInfiniteScroll
            elementId={0}
            index={0}
            order={0}
            zoneItemData={renderZoneItemData(
              genMockStories({ length: 4 }),
              10,
              true,
              true,
              true,
            )}
            zoneItemId={0}
            zoneItemType={ZoneItemType.StoryList}
            zoneName={ZoneName.BANNER}
          />
        </TestWrapper>,
      );
    });

    await waitFor(() =>
      expect(screen.getByText('Back to top')).toBeInTheDocument(),
    );
  });

  it('should restore scroll position when clicking back', async () => {
    sessionStorage.setItem('lastOffset', '16');
    sessionStorage.setItem('lastScrollYPosition', '800');
    sessionStorage.setItem('restoreArticleListPosition', 'true');

    const mockStories = genMockStories({ length: 16 });
    mockedFetchStoriesForStoryList.mockResolvedValue(mockStories);

    window.scrollTo = jest.fn(((x: number | ScrollToOptions, y?: number) => {
      if (typeof x === 'object') {
        Object.defineProperty(window, 'scrollY', {
          value: x.top,
          writable: true,
        });
      } else {
        Object.defineProperty(window, 'scrollY', {
          value: y,
          writable: true,
        });
      }
    }) as (options?: ScrollToOptions | number, y?: number) => void);

    act(() => {
      render(
        <TestWrapper store={store}>
          <ExploreTravelInfiniteScroll
            elementId={0}
            index={0}
            order={0}
            zoneItemData={renderZoneItemData(
              mockStories,
              10,
              true,
              true,
              true,
            )}
            zoneItemId={0}
            zoneItemType={ZoneItemType.StoryList}
            zoneName={ZoneName.BANNER}
          />
        </TestWrapper>,
      );
    });

    act(() => {
      window.scrollTo({ top: 800 });
    });

    await waitFor(() => {
      expect(window.scrollY).toBeGreaterThanOrEqual(800);
    });
  });

  it('should not restore position on other navigation', async () => {
    sessionStorage.setItem('lastOffset', '16');
    sessionStorage.setItem('lastScrollYPosition', '800');
    sessionStorage.setItem('restoreArticleListPosition', 'false');

    const mockStories = genMockStories({ length: 8 });
    mockedFetchStoriesForStoryList.mockResolvedValue(mockStories);

    act(() => {
      render(
        <TestWrapper store={store}>
          <ExploreTravelInfiniteScroll
            elementId={0}
            index={0}
            order={0}
            zoneItemData={renderZoneItemData(
              mockStories,
              10,
              true,
              true,
              true,
            )}
            zoneItemId={0}
            zoneItemType={ZoneItemType.StoryList}
            zoneName={ZoneName.BANNER}
          />
        </TestWrapper>,
      );
    });

    await waitFor(() => expect(window.scrollY).toBe(0));
  });
  it('should match snapshot when largeLeadStory is true', () => {
    const mockStories = genMockStories({ length: 4 });

    const { asFragment } = render(
      <TestWrapper store={store}>
        <ExploreTravelInfiniteScroll
          elementId={0}
          index={0}
          order={0}
          zoneItemData={renderZoneItemData(mockStories, 10, true, true, true)}
          zoneItemId={0}
          zoneItemType={ZoneItemType.StoryList}
          zoneName={ZoneName.BANNER}
        />
      </TestWrapper>,
    );

    expect(asFragment()).toMatchSnapshot();
  });
});
