// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ExploreTravelInfiniteScroll Component should match snapshot when largeLeadStory is true 1`] = `
<DocumentFragment>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="container mx-auto flex flex-col"
    >
      <div
        class="h-1"
      />
      <div
        class="divide-y divide-gray-300 md:flex md:flex-col md:gap-7 md:divide-y-0"
      >
        <div
          class="animate-pulse overflow-hidden rounded-lg bg-white md:w-72 lg:w-full w-full md:py-0 md:mx-auto lg:max-w-[840px] pb-7 pt-0"
        >
          <div
            class="relative h-48 rounded-lg bg-gray-100"
          />
          <div
            class="flex flex-col pt-2"
          >
            <div
              class="h-5 w-3/4 bg-gray-100"
            />
            <div
              class="mt-1.5 h-5 w-full bg-gray-100"
            />
            <div
              class="mt-3 h-4 w-3/4 bg-gray-100"
            />
            <div
              class="mt-1 h-4 w-full bg-gray-100"
            />
            <div
              class="mt-2 h-3 w-2/3 bg-gray-100"
            />
          </div>
        </div>
      </div>
      <div
        class="h-2"
      />
    </div>
  </div>
</DocumentFragment>
`;
