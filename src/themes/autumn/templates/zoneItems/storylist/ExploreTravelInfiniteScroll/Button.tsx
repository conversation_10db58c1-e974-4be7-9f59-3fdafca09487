interface ButtonProps {
  label: string;
  onClick: () => void;
}

export default function Button({ label, onClick }: ButtonProps) {
  return (
    <button
      aria-label={label}
      className="mx-auto mt-5 h-12 w-64 rounded-lg border border-gray-300 px-6 py-3 font-inter text-base font-medium text-gray-700 shadow-sm"
      onClick={onClick}
      type="button"
    >
      {label}
    </button>
  );
}
