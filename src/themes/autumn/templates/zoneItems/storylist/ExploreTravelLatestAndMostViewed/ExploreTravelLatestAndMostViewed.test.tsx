import { render } from '@testing-library/react';
import React from 'react';

import { createStore } from 'store/store';
import { StoryListType, ZoneItemType, ZoneName } from 'types/ZoneItems';
import { SummaryOption } from 'util/constants';
import { TestWrapper, genMockStories } from 'util/jest';

import ExploreTravelLatestAndMostViewed from '.';

import type { EnhancedStore } from '@reduxjs/toolkit';
import type { DpeFeature } from 'store/slices/features';
import type { RootState } from 'store/store';
import type {
  DpeCardIssueData,
  StoryList,
  StoryListZoneItem,
} from 'types/ZoneItems';

describe('ExploreTravelLatestAndMostViewed', () => {
  let store: EnhancedStore<RootState>;
  const mockDpeDataProps: DpeFeature = {
    dpeHowtoVideoId: 'howtoVideoId',
    dpeId: 'dpeId',
    dpeIndexPageUrl: '/dpe-index',
    dpeLatestDate: '20240101',
    host: 'example.com/',
    version: 'v2',
  };
  const mockDpeFeatureEnabled: Partial<RootState['features']> = {
    dpe: {
      data: mockDpeDataProps,
      enabled: true,
    },
  };

  const mockDpeFeatureDisabled: Partial<RootState['features']> = {
    dpe: {
      enabled: false,
    },
  };

  const mockStories = genMockStories({ length: 5 });
  const mockPopularStories = genMockStories({ length: 6 });
  const mockDpeRenderData: DpeCardIssueData = {
    coverImg: 'test-cover-image.jpg',
    issue: 'test/issue/index.html',
  };

  const baseStoryListMock: Omit<
    StoryList,
    'stories' | 'popularStories' | 'dpeData' | 'template'
  > = {
    allowAds: false,
    disableBottomBorder: false,
    enableOrder: false,
    flipStoryDisplay: false,
    isHeroImage: false,
    label: '',
    largeLeadStory: false,
    limit: 10,
    listType: StoryListType.Default,
    offset: 0,
    pinnedStoryIds: [],
    showCanonicalSite: false,
    storyListId: 12345,
    storyListTitle: 'Mock Story List Title',
    summaryOptions: SummaryOption.ALWAYS_SHOW,
    totalStories: mockStories.length,
    urlParams: '',
    useCanonicalUrl: false,
  };

  const defaultZoneItemData: StoryListZoneItem['zoneItemData'] = {
    ...baseStoryListMock,
    dpeData: mockDpeRenderData,
    popularStories: mockPopularStories,
    stories: mockStories,
    template: 'ExploreTravelLatestAndMostViewed',
  };

  beforeEach(() => {
    store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        ...mockDpeFeatureEnabled,
      },
    }));
  });

  it('renders correctly with DPE feature enabled', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelLatestAndMostViewed
          elementId={20001}
          index={1}
          order={1}
          zoneItemData={defaultZoneItemData}
          zoneItemId={20001}
          zoneItemType={ZoneItemType.StoryList}
          zoneName={ZoneName.MAIN}
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders correctly with DPE feature disabled', () => {
    expect.assertions(1);

    store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        ...mockDpeFeatureDisabled,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelLatestAndMostViewed
          elementId={20001}
          index={1}
          order={1}
          zoneItemData={defaultZoneItemData}
          zoneItemId={20001}
          zoneItemType={ZoneItemType.StoryList}
          zoneName={ZoneName.MAIN}
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders correctly with different number of stories', () => {
    expect.assertions(1);

    const fewStories = genMockStories({ length: 2 });
    const fewPopular = genMockStories({ length: 3 });
    const zoneItemDataFewStories: StoryListZoneItem['zoneItemData'] = {
      ...defaultZoneItemData,
      popularStories: fewPopular,
      stories: fewStories,
      totalStories: fewStories.length,
    };

    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelLatestAndMostViewed
          elementId={20001}
          index={1}
          order={1}
          zoneItemData={zoneItemDataFewStories}
          zoneItemId={20001}
          zoneItemType={ZoneItemType.StoryList}
          zoneName={ZoneName.MAIN}
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders correctly when popular stories have no lead image', () => {
    expect.assertions(1);

    const popularStoriesWithoutImage = genMockStories({ length: 4 }).map(
      (s) => ({
        ...s,
        leadImage: undefined,
      }),
    );

    const zoneItemDataNoImage: StoryListZoneItem['zoneItemData'] = {
      ...defaultZoneItemData,
      popularStories: popularStoriesWithoutImage,
    };

    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelLatestAndMostViewed
          elementId={20001}
          index={1}
          order={1}
          zoneItemData={zoneItemDataNoImage}
          zoneItemId={20001}
          zoneItemType={ZoneItemType.StoryList}
          zoneName={ZoneName.MAIN}
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders correctly with a specific limit for latest stories', () => {
    expect.assertions(1);

    const limitedZoneItemData: StoryListZoneItem['zoneItemData'] = {
      ...defaultZoneItemData,
      limit: 2, // Set limit to 2, while mockStories has 5
    };

    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelLatestAndMostViewed
          elementId={20001}
          index={1}
          order={1}
          zoneItemData={limitedZoneItemData}
          zoneItemId={20001}
          zoneItemType={ZoneItemType.StoryList}
          zoneName={ZoneName.MAIN}
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
