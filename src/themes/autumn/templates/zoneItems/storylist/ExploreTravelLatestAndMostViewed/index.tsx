import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import Link from 'themes/autumn/components/generic/Link';
import NativeImage from 'themes/autumn/components/storyElements/common/NativeImage';

import type { Story } from 'types/Story';
import type { StoryListZoneItem } from 'types/ZoneItems';

const StoryItem: React.FC<{ story: Story }> = ({ story }) => (
  <div className="mb-4 flex flex-col gap-2 border-b-1 border-gray-300 pb-4 last:mb-0 last:border-b-0 last:pb-0">
    <Link href={story.storyUrl} noStyle>
      <h3 className="line-clamp-2 font-playfair text-base/[26px] font-medium text-gray-900 hover:underline">
        {story.title}
      </h3>
    </Link>
    {story.byline && (
      <span className="font-inter text-xs/[18px] font-medium text-gray-500">
        {story.byline}
      </span>
    )}
  </div>
);

const ExploreTravelLatestAndMostViewed = ({
  zoneItemData: { dpeData, limit, popularStories, stories },
}: StoryListZoneItem) => {
  const coverImg = dpeData?.coverImg;
  const latestStories = stories.slice(0, limit ?? 3);
  const mostPopularStories = popularStories?.slice(0, 4) ?? [];

  const digitalPrintEditionUrl = dpeData?.issue;

  return (
    <div className="flex w-full flex-col gap-10 lg:flex-row lg:gap-20">
      <div className="w-full rounded border-1 border-gray-200 bg-gray-50 p-5 shadow md:h-[494px] md:p-10 lg:w-2/3">
        <div className="flex size-full flex-col gap-5">
          <Link href="/travel/explore-magazine/" noStyle>
            <h2
              aria-label="Latest Issue"
              className="text-center font-inter text-2xl font-semibold text-gray-900 hover:underline md:text-left"
            >
              Latest Issue
            </h2>
          </Link>
          <div className="flex size-full flex-col justify-center gap-10 md:flex-row md:justify-between md:gap-20">
            <div
              className={clsx(
                'order-2 w-full md:order-1 md:h-88 md:overflow-y-auto',
                {
                  'md:w-2/3': !!dpeData,
                },
              )}
            >
              {/* STORIES */}
              {latestStories.map((story) => (
                <StoryItem key={story.id} story={story} />
              ))}
            </div>
            <div
              className={clsx(
                'order-1 flex w-full flex-col items-center gap-5 md:order-2 md:w-1/3',
                {
                  hidden: !dpeData,
                },
              )}
            >
              {/* MAGAZINE PIC */}
              <div className="w-[200px] shadow-2xl hover:shadow-3xl">
                <Link href={digitalPrintEditionUrl} noStyle target="_blank">
                  <img
                    alt="Explore Magazine cover"
                    className="object-cover"
                    loading="lazy"
                    src={coverImg}
                  />
                </Link>
              </div>
              <Link href={digitalPrintEditionUrl} noStyle target="_blank">
                <span className="flex flex-row items-center gap-2 hover:underline">
                  Read the magazine&nbsp;
                  <FontAwesomeIcon icon={faChevronRight} />
                </span>
              </Link>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full lg:h-[494px] lg:w-1/3">
        <h2 className="-mt-1 mb-7 font-inter text-lg font-semibold md:mb-4">
          Most Viewed
        </h2>
        <ol
          aria-label="Most Viewed Travel Articles"
          className="flex flex-col gap-4"
        >
          {mostPopularStories.map((story, index) => (
            <li
              className="border-b-1 border-gray-200 pb-4 last:border-b-0 last:pb-0"
              key={story.id}
            >
              <Link
                className="group flex items-start gap-3"
                href={story.url}
                noStyle
              >
                <div className="w-6 font-playfair text-[28px]/[26px] font-medium text-gray-900">
                  {index + 1}
                </div>
                <h3 className="line-clamp-3 flex-1 font-inter text-base/[26px] font-normal text-gray-900 hover:underline">
                  {story.title}
                </h3>
                {story.leadImage && (
                  <div className="size-[90px] overflow-hidden rounded-md">
                    <NativeImage
                      alt={story.leadImage.title || story.title}
                      height={90}
                      image={story.leadImage}
                      imageClassName="size-full object-cover"
                      width={90}
                    />
                  </div>
                )}
              </Link>
            </li>
          ))}
        </ol>
      </div>
    </div>
  );
};

export default ExploreTravelLatestAndMostViewed;
