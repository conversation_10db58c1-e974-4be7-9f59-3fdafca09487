import React, { useMemo } from 'react';

import NewsletterSignup from 'themes/autumn/templates/stories/ExploreTravelStory/NewsletterSignup';
import StoryListContext from 'util/ZoneItems/context/storylist-context';
import { ExploreNewswell } from 'util/ZoneItems/storylist';
import { useNewsletterSubscriptionState } from 'util/newsletter';

import type { StoryListZoneItem, StrapStoriesProps } from 'types/ZoneItems';

export type Props = StoryListZoneItem;

const MAX_STORIES = 5;
const EXPLORE_TRAVEL_NEWSLETTER_ID = 'Explore Travel';

function Default({
  zoneItemData: {
    enableOrder = true,
    label,
    limit,
    offset,
    stories,
    storyListId,
    storyListTitle,
    summaryOptions,
    useCanonicalUrl,
  },
  zoneItemId,
}: Props): React.ReactElement | null {
  const value = useMemo(
    () => ({
      enableOrder,
      label,
      storyListId,
      storyListTitle,
      summaryOptions,
      useCanonicalUrl,
      zoneItemId,
    }),
    [
      enableOrder,
      label,
      storyListId,
      storyListTitle,
      summaryOptions,
      useCanonicalUrl,
      zoneItemId,
    ],
  );

  const { isSubscribed } = useNewsletterSubscriptionState(
    EXPLORE_TRAVEL_NEWSLETTER_ID,
  );

  let storiesLimit = limit ?? MAX_STORIES;
  storiesLimit = storiesLimit < MAX_STORIES ? storiesLimit : MAX_STORIES;
  const strapStories: StrapStoriesProps[] = stories.map(
    (story, storyIndex) => ({
      index:
        storiesLimit < stories.length ? storyIndex + 1 : storyIndex + offset,
      story,
    }),
  );
  if (strapStories.length > 0) {
    return (
      <>
        <StoryListContext.Provider value={value}>
          <ExploreNewswell stories={strapStories} />
        </StoryListContext.Provider>
        {!isSubscribed && <NewsletterSignup onNewswell />}
      </>
    );
  }

  return null;
}

export default Default;
