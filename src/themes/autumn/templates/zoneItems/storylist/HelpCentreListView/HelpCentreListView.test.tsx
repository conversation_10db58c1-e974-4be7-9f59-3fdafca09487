import { render, screen } from '@testing-library/react';

import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { SummaryOption } from 'util/constants';
import { TestWrapper, genMockStories, renderZoneItemData } from 'util/jest';

import HelpCentreListView from '.';

import type { StoryListZoneItem } from 'types/ZoneItems';

describe('HelpCentreListView', () => {
  const mockProps: StoryListZoneItem = {
    elementId: 10001,
    index: 1,
    order: 1,
    zoneItemData: renderZoneItemData(
      genMockStories({
        length: 3,
      }).map((story, index) => ({
        ...story,
        id: `help-${index}`,
        storyUrl: `/help-centre/topic-1/article-${index + 1}`,
        title: `Help Topic ${index + 1}`,
      })),
      3,
      false,
      false,
      false,
      10001,
      false,
    ),
    zoneItemId: 10001,
    zoneItemType: ZoneItemType.StoryList,
    zoneName: ZoneName.MAIN,
  };

  mockProps.zoneItemData.label = 'List of Help Topics';
  mockProps.zoneItemData.summaryOptions = SummaryOption.DISABLED;

  it('renders the component with stories', () => {
    expect.assertions(4);

    render(
      <TestWrapper store={createStore()}>
        <HelpCentreListView
          elementId={mockProps.elementId}
          index={mockProps.index}
          order={mockProps.order}
          zoneItemData={mockProps.zoneItemData}
          zoneItemId={mockProps.zoneItemId}
          zoneItemType={mockProps.zoneItemType}
          zoneName={mockProps.zoneName}
        />
      </TestWrapper>,
    );

    expect(screen.getByText('List of Help Topics')).toBeInTheDocument();
    expect(screen.getByText('Help Topic 1')).toBeInTheDocument();
    expect(screen.getByText('Help Topic 2')).toBeInTheDocument();
    expect(screen.getByText('Help Topic 3')).toBeInTheDocument();
  });

  it('renders chevron icons for each story', () => {
    expect.assertions(1);

    render(
      <TestWrapper store={createStore()}>
        <HelpCentreListView
          elementId={mockProps.elementId}
          index={mockProps.index}
          order={mockProps.order}
          zoneItemData={mockProps.zoneItemData}
          zoneItemId={mockProps.zoneItemId}
          zoneItemType={mockProps.zoneItemType}
          zoneName={mockProps.zoneName}
        />
      </TestWrapper>,
    );

    const chevronIcons = screen.getAllByTestId('chevron-right-icon');
    expect(chevronIcons).toHaveLength(3);
  });

  it('renders correct links for each story', () => {
    expect.assertions(3);

    render(
      <TestWrapper store={createStore()}>
        <HelpCentreListView
          elementId={mockProps.elementId}
          index={mockProps.index}
          order={mockProps.order}
          zoneItemData={mockProps.zoneItemData}
          zoneItemId={mockProps.zoneItemId}
          zoneItemType={mockProps.zoneItemType}
          zoneName={mockProps.zoneName}
        />
      </TestWrapper>,
    );

    const links = screen.getAllByRole('link');
    expect(links[0]).toHaveAttribute('href', '/help-centre/topic-1/article-1');
    expect(links[1]).toHaveAttribute('href', '/help-centre/topic-1/article-2');
    expect(links[2]).toHaveAttribute('href', '/help-centre/topic-1/article-3');
  });

  it('includes structured data for SEO', () => {
    expect.assertions(1);

    render(
      <TestWrapper store={createStore()}>
        <HelpCentreListView
          elementId={mockProps.elementId}
          index={mockProps.index}
          order={mockProps.order}
          zoneItemData={mockProps.zoneItemData}
          zoneItemId={mockProps.zoneItemId}
          zoneItemType={mockProps.zoneItemType}
          zoneName={mockProps.zoneName}
        />
      </TestWrapper>,
    );

    const structuredDataScript = document.querySelector(
      'script[type="application/ld+json"]',
    );
    expect(structuredDataScript).toBeInTheDocument();
  });

  it('matches snapshot', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={createStore()}>
        <HelpCentreListView
          elementId={mockProps.elementId}
          index={mockProps.index}
          order={mockProps.order}
          zoneItemData={mockProps.zoneItemData}
          zoneItemId={mockProps.zoneItemId}
          zoneItemType={mockProps.zoneItemType}
          zoneName={mockProps.zoneName}
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
