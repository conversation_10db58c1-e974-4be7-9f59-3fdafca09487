// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HelpCentreListView matches snapshot 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="flex w-full max-w-3xl flex-col gap-6 !font-inter"
  >
    <h2
      class="font-inter text-2xl font-normal text-gray-900"
    >
      List of Help Topics
    </h2>
    <div>
      <ul
        class="flex flex-col gap-6"
      >
        <li
          class="group mx-auto w-full rounded bg-white font-inter shadow transition-shadow duration-300 ease-in-out hover:shadow-md"
        >
          <a
            class="flex w-full items-center justify-between px-6"
            href="/help-centre/topic-1/article-1"
          >
            <div
              class="flex w-full"
            >
              <div
                class="w-full py-5.5"
              >
                <div
                  class="py-1.5 pr-4"
                >
                  <h3
                    class="!font-inter text-xl text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
                  >
                    Help Topic 1
                  </h3>
                </div>
              </div>
            </div>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950 md:mr-4"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </a>
        </li>
        <li
          class="group mx-auto w-full rounded bg-white font-inter shadow transition-shadow duration-300 ease-in-out hover:shadow-md"
        >
          <a
            class="flex w-full items-center justify-between px-6"
            href="/help-centre/topic-1/article-2"
          >
            <div
              class="flex w-full"
            >
              <div
                class="w-full py-5.5"
              >
                <div
                  class="py-1.5 pr-4"
                >
                  <h3
                    class="!font-inter text-xl text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
                  >
                    Help Topic 2
                  </h3>
                </div>
              </div>
            </div>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950 md:mr-4"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </a>
        </li>
        <li
          class="group mx-auto w-full rounded bg-white font-inter shadow transition-shadow duration-300 ease-in-out hover:shadow-md"
        >
          <a
            class="flex w-full items-center justify-between px-6"
            href="/help-centre/topic-1/article-3"
          >
            <div
              class="flex w-full"
            >
              <div
                class="w-full py-5.5"
              >
                <div
                  class="py-1.5 pr-4"
                >
                  <h3
                    class="!font-inter text-xl text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
                  >
                    Help Topic 3
                  </h3>
                </div>
              </div>
            </div>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950 md:mr-4"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </a>
        </li>
      </ul>
      <script
        type="application/ld+json"
      >
        {"@context":"https://schema.org","@type":"ItemList","description":"List of topics about List of Help Topics","itemListElement":[{"@type":"ListItem","item":{"@type":"WebPage","description":"Help Topic 1","name":"Help Topic 1","url":"/help-centre/topic-1/article-1"},"position":1},{"@type":"ListItem","item":{"@type":"WebPage","description":"Help Topic 2","name":"Help Topic 2","url":"/help-centre/topic-1/article-2"},"position":2},{"@type":"ListItem","item":{"@type":"WebPage","description":"Help Topic 3","name":"Help Topic 3","url":"/help-centre/topic-1/article-3"},"position":3}],"name":"List of Help Topics","numberOfItems":3}
      </script>
    </div>
  </div>
</div>
`;
