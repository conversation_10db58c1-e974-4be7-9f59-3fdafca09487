import ChevronRightIcon from '@heroicons/react/24/solid/ChevronRightIcon';
import { useMemo } from 'react';

import Link from 'themes/autumn/components/generic/Link';
import StoryListItem from 'themes/autumn/components/stories/StoryListItem';
import StoryListContext from 'util/ZoneItems/context/storylist-context';

import type { StoryListZoneItem, StrapStoriesProps } from 'types/ZoneItems';

export type Props = StoryListZoneItem;

function HelpCentreListView({
  isLastItemInZone = false,
  zoneItemData: {
    enableOrder = true,
    label,
    limit,
    offset = 1,
    stories,
    storyListId,
    summaryOptions,
  },
  zoneItemId,
}: Props) {
  const value = useMemo(
    () => ({
      enableOrder,
      label,
      limit,
      offset,
      storyListId,
      summaryOptions,
      useCanonicalUrl: false,
      zoneItemId,
    }),
    [
      enableOrder,
      label,
      limit,
      offset,
      storyListId,
      summaryOptions,
      zoneItemId,
    ],
  );
  if (stories.length <= 0) return null;

  const itemListStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    description: `List of topics about ${label}`,
    itemListElement: stories.map((item, index) => ({
      '@type': 'ListItem',
      item: {
        '@type': 'WebPage',
        description: item.summary || item.title,
        name: item.title,
        url: item.storyUrl,
      },
      position: index + 1,
    })),
    name: label,
    numberOfItems: stories.length,
  };

  const strapStories: StrapStoriesProps[] = stories.map(
    (story, storyIndex) => ({
      index: storyIndex + offset,
      story,
    }),
  );

  return (
    <StoryListContext.Provider value={value}>
      <div className="flex w-full max-w-3xl flex-col gap-6 !font-inter">
        <h2 className="font-inter text-2xl font-normal text-gray-900">
          {label}
        </h2>
        <div>
          <ul className="flex flex-col gap-6">
            {strapStories.map((strapStory) => (
              <li
                className="group mx-auto w-full rounded bg-white font-inter shadow transition-shadow duration-300 ease-in-out hover:shadow-md"
                key={strapStory.story.id}
              >
                <Link
                  className="flex w-full items-center justify-between px-6"
                  href={strapStory.story.storyUrl}
                  noStyle
                >
                  <StoryListItem
                    className="w-full py-5.5"
                    droppable
                    headline={
                      <div className="py-1.5 pr-4">
                        <h3 className="!font-inter text-xl text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline">
                          {strapStory.story.title}
                        </h3>
                      </div>
                    }
                    imageHeight={0}
                    imageWidth={0}
                    imageWrapperClassName="w-0"
                    index={strapStory.index - 1}
                    isLastOrFirstItemInZone={isLastItemInZone}
                    key={strapStory.story.id}
                    showByline={false}
                    showSignpost={false}
                    showTimestamp={false}
                    story={strapStory.story}
                  />
                  <ChevronRightIcon
                    className="size-5 shrink-0 text-gray-950 md:mr-4"
                    data-testid="chevron-right-icon"
                  />
                </Link>
              </li>
            ))}
          </ul>

          <script
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(itemListStructuredData),
            }}
            type="application/ld+json"
          />
        </div>
      </div>
    </StoryListContext.Provider>
  );
}

export default HelpCentreListView;
