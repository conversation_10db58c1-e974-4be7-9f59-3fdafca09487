import ChevronRightIcon from '@heroicons/react/24/solid/ChevronRightIcon';
import { useMemo } from 'react';

import Link from 'themes/autumn/components/generic/Link';
import StoryListContext from 'util/ZoneItems/context/storylist-context';

import type { StoryListZoneItem } from 'types/ZoneItems';

export type Props = StoryListZoneItem;

function MostAskedQuestions({
  zoneItemData: {
    enableOrder = true,
    label,
    stories,
    storyListId,
    summaryOptions,
  },
  zoneItemId,
}: Props) {
  const value = useMemo(
    () => ({
      enableOrder,
      label,
      storyListId,
      summaryOptions,
      useCanonicalUrl: false,
      zoneItemId,
    }),
    [enableOrder, label, storyListId, summaryOptions, zoneItemId],
  );
  if (stories.length <= 0) return null;

  // ItemList schema for navigation component
  const itemListStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    description: 'A curated list of the most frequently asked questions',
    itemListElement: stories.map((item, index) => ({
      '@type': 'ListItem',
      item: {
        '@type': 'WebPage',
        description: item.summary || item.title,
        name: item.title,
        url: item.storyUrl,
      },
      numberOfItems: stories.length,
      position: index + 1,
    })),
    name: 'Most Asked Questions',
  };

  return (
    <StoryListContext.Provider value={value}>
      <div className="mx-auto w-full rounded-lg border border-gray-200 bg-white px-6 py-9 shadow md:p-14">
        <h2 className="mb-6 font-inter text-2xl font-medium text-gray-900 md:mb-10">
          Most asked questions
        </h2>

        <div className="divide-y divide-gray-200 md:divide-y-0">
          <ul className="md:grid md:grid-cols-2 md:gap-x-11">
            {stories.map((story) => (
              <li key={story.id}>
                <Link
                  aria-label={`Read FAQ: ${story.title}`}
                  className="group flex items-center justify-between border-t border-gray-200 py-6 md:py-4"
                  href={story.storyUrl}
                  noStyle
                >
                  <h3 className="pr-4 font-inter text-base font-normal text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline md:font-inter">
                    {story.title}
                  </h3>
                  <ChevronRightIcon
                    className="size-5 shrink-0 text-gray-950 md:mr-4"
                    data-testid="chevron-right-icon"
                  />
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(itemListStructuredData),
          }}
          type="application/ld+json"
        />
      </div>
    </StoryListContext.Provider>
  );
}

export default MostAskedQuestions;
