// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MostAskedQuestions matches snapshot 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mx-auto w-full rounded-lg border border-gray-200 bg-white px-6 py-9 shadow md:p-14"
  >
    <h2
      class="mb-6 font-inter text-2xl font-medium text-gray-900 md:mb-10"
    >
      Most asked questions
    </h2>
    <div
      class="divide-y divide-gray-200 md:divide-y-0"
    >
      <ul
        class="md:grid md:grid-cols-2 md:gap-x-11"
      >
        <li>
          <a
            aria-label="Read FAQ: Log in to your account"
            class="group flex items-center justify-between border-t border-gray-200 py-6 md:py-4"
            href="/story/digital-access/log-in-to-your-account-1/"
          >
            <h3
              class="pr-4 font-inter text-base font-normal text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline md:font-inter"
            >
              Log in to your account
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950 md:mr-4"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </a>
        </li>
        <li>
          <a
            aria-label="Read FAQ: Reset Your Password"
            class="group flex items-center justify-between border-t border-gray-200 py-6 md:py-4"
            href="/story/account-management/reset-your-password-2/"
          >
            <h3
              class="pr-4 font-inter text-base font-normal text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline md:font-inter"
            >
              Reset Your Password
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950 md:mr-4"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </a>
        </li>
        <li>
          <a
            aria-label="Read FAQ: Change Your Delivery Address"
            class="group flex items-center justify-between border-t border-gray-200 py-6 md:py-4"
            href="/story/delivery/change-delivery-address-3/"
          >
            <h3
              class="pr-4 font-inter text-base font-normal text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline md:font-inter"
            >
              Change Your Delivery Address
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950 md:mr-4"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </a>
        </li>
        <li>
          <a
            aria-label="Read FAQ: Cancel Your Subscription"
            class="group flex items-center justify-between border-t border-gray-200 py-6 md:py-4"
            href="/story/subscriptions/cancel-subscription-4/"
          >
            <h3
              class="pr-4 font-inter text-base font-normal text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline md:font-inter"
            >
              Cancel Your Subscription
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950 md:mr-4"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </a>
        </li>
        <li>
          <a
            aria-label="Read FAQ: Update Your Payment Information"
            class="group flex items-center justify-between border-t border-gray-200 py-6 md:py-4"
            href="/story/billing/update-payment-5/"
          >
            <h3
              class="pr-4 font-inter text-base font-normal text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline md:font-inter"
            >
              Update Your Payment Information
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950 md:mr-4"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </a>
        </li>
      </ul>
    </div>
    <script
      type="application/ld+json"
    >
      {"@context":"https://schema.org","@type":"ItemList","description":"A curated list of the most frequently asked questions","itemListElement":[{"@type":"ListItem","item":{"@type":"WebPage","description":"Learn how to log in to your account","name":"Log in to your account","url":"/story/digital-access/log-in-to-your-account-1/"},"numberOfItems":5,"position":1},{"@type":"ListItem","item":{"@type":"WebPage","description":"Instructions for resetting your password","name":"Reset Your Password","url":"/story/account-management/reset-your-password-2/"},"numberOfItems":5,"position":2},{"@type":"ListItem","item":{"@type":"WebPage","description":"How to update your delivery address","name":"Change Your Delivery Address","url":"/story/delivery/change-delivery-address-3/"},"numberOfItems":5,"position":3},{"@type":"ListItem","item":{"@type":"WebPage","description":"Steps to cancel your subscription","name":"Cancel Your Subscription","url":"/story/subscriptions/cancel-subscription-4/"},"numberOfItems":5,"position":4},{"@type":"ListItem","item":{"@type":"WebPage","description":"How to update your payment details","name":"Update Your Payment Information","url":"/story/billing/update-payment-5/"},"numberOfItems":5,"position":5}],"name":"Most Asked Questions"}
    </script>
  </div>
</div>
`;
