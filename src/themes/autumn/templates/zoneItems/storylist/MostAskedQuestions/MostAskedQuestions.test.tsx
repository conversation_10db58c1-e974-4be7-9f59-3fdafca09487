import { render, screen } from '@testing-library/react';

import { createStore } from 'store/store';
import { type Story, StoryCommentsState } from 'types/Story';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper, genMockStories, renderZoneItemData } from 'util/jest';

import MostAskedQuestions from '.';

import type { StoryListZoneItem } from 'types/ZoneItems';

const faqTitles = [
  'Log in to your account',
  'Reset Your Password',
  'Change Your Delivery Address',
  'Cancel Your Subscription',
  'Update Your Payment Information',
];

const faqSlugs = [
  'log-in-to-your-account',
  'reset-your-password',
  'change-delivery-address',
  'cancel-subscription',
  'update-payment',
];

const faqTopics = [
  'digital-access',
  'account-management',
  'delivery',
  'subscriptions',
  'billing',
];

const faqSummaries = [
  'Learn how to log in to your account',
  'Instructions for resetting your password',
  'How to update your delivery address',
  'Steps to cancel your subscription',
  'How to update your payment details',
];

const genMockFAQStories = (length: number = 5): Story[] => {
  const baseStories = genMockStories({ length });

  return baseStories.map((story, index) => {
    const slug = faqSlugs[index] || `faq-${index + 1}`;
    const topic = faqTopics[index] || 'general';
    const storyId = `${index + 1}`;

    return {
      ...story,
      comments: StoryCommentsState.OPEN,
      id: storyId,
      storyUrl: `/story/${topic}/${slug}-${storyId}/`,
      summary: faqSummaries[index] || `Summary for FAQ question ${index + 1}`,
      tags: ['faq'],
      title: faqTitles[index] || `FAQ Question ${index + 1}`,
    };
  });
};

const createMockProps = (
  stories: Story[] = genMockFAQStories(),
): StoryListZoneItem => ({
  elementId: 10001,
  index: 1,
  order: 1,
  zoneItemData: {
    ...renderZoneItemData(stories, null, false, false, false, 1),
    label: 'Most Asked Questions',
    template: 'most-asked-questions.html',
  },
  zoneItemId: 10001,
  zoneItemType: ZoneItemType.StoryList,
  zoneName: ZoneName.MAIN,
});

describe('MostAskedQuestions', () => {
  it('renders the component with title', () => {
    const props = createMockProps();

    render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    expect(screen.getByText('Most asked questions')).toBeInTheDocument();
  });

  it('renders all FAQ items with correct titles', () => {
    const mockStories = genMockFAQStories(5);
    const props = createMockProps(mockStories);

    render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    // Check that all generated FAQ titles are rendered
    mockStories.forEach((story) => {
      expect(screen.getByText(story.title)).toBeInTheDocument();
    });
  });

  it('renders with correct links', () => {
    const mockStories = genMockFAQStories();
    const props = createMockProps(mockStories);

    render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    const loginLink = screen
      .getAllByText('Log in to your account')[0]
      .closest('a');
    expect(loginLink).toHaveAttribute(
      'href',
      '/story/digital-access/log-in-to-your-account-1/',
    );
  });

  it('renders chevron icons for all FAQ items', () => {
    const props = createMockProps();

    render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    // Should have chevron icons for all stories (single responsive layout)
    const chevronIcons = screen.getAllByTestId('chevron-right-icon');
    expect(chevronIcons).toHaveLength(5);
  });

  it('applies responsive grid layout on desktop', () => {
    const props = createMockProps();

    const { container } = render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    // Check for responsive grid layout
    const responsiveGrid = container.querySelector('.md\\:grid-cols-2');
    expect(responsiveGrid).toBeInTheDocument();

    // Check for column dividers
    const columnDividers = container.querySelectorAll('.divide-y');
    expect(columnDividers.length).toBeGreaterThan(0);
  });

  it('handles odd number of stories correctly', () => {
    const oddStories = genMockFAQStories(3); // 3 stories
    const props = createMockProps(oddStories);

    render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    // Should render all 3 stories (single responsive layout)
    expect(screen.getByText('Log in to your account')).toBeInTheDocument();
    expect(screen.getByText('Reset Your Password')).toBeInTheDocument();
    expect(
      screen.getByText('Change Your Delivery Address'),
    ).toBeInTheDocument();
  });

  it('returns null when no stories are provided', () => {
    const props = createMockProps([]);

    render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    // Component should return null, so no FAQ content should be rendered
    expect(screen.queryByText('Most asked questions')).not.toBeInTheDocument();
  });

  it('includes schema.org structured data', () => {
    const props = createMockProps();

    const { container } = render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    const structuredDataScript = container.querySelector(
      'script[type="application/ld+json"]',
    );
    expect(structuredDataScript).toBeInTheDocument();
  });

  it('renders stories in the order they are provided', () => {
    // Create stories with intentionally mixed/random order and distinct titles
    const storiesInOrder = [
      {
        ...genMockFAQStories(1)[0],
        id: 'story-3',
        storyUrl: '/story/3/third-story',
        title: 'Third Story Title',
      },
      {
        ...genMockFAQStories(1)[0],
        id: 'story-1',
        storyUrl: '/story/1/first-story',
        title: 'First Story Title',
      },
      {
        ...genMockFAQStories(1)[0],
        id: 'story-2',
        storyUrl: '/story/2/second-story',
        title: 'Second Story Title',
      },
    ];

    const props = createMockProps(storiesInOrder);

    render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    // Verify the stories appear in the exact order they were provided
    const links = screen.getAllByRole('link');
    expect(links[0]).toHaveTextContent('Third Story Title');
    expect(links[1]).toHaveTextContent('First Story Title');
    expect(links[2]).toHaveTextContent('Second Story Title');
  });

  it('applies correct CSS classes for responsive design', () => {
    const props = createMockProps();

    const { container } = render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    // Check for responsive grid layout
    const responsiveLayout = container.querySelector('.md\\:grid');
    expect(responsiveLayout).toBeInTheDocument();

    // Check for responsive grid columns
    const gridCols = container.querySelector('.md\\:grid-cols-2');
    expect(gridCols).toBeInTheDocument();
  });

  it('matches snapshot', () => {
    const props = createMockProps();

    const { container } = render(
      <TestWrapper store={createStore()}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <MostAskedQuestions {...props} />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
