import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper } from 'util/jest';

import Component from '.';

import type { Meta, StoryObj } from '@storybook/nextjs-vite';

const meta: Meta<typeof Component> = {
  component: Component,
  title: 'Notice board/OwnLocal ads',
};

export default meta;

type Story = StoryObj<typeof Component>;

const store = createStore((state) => ({
  ...state,
  features: {
    ...state.features,
    adServing: {
      data: {
        apsPublisherId: '',
        autoRefreshInterval: 0,
        bottomAnchorAdPosition: 0,
        doubleClickCat: '',
        doubleClickRegion: '',
        doubleClickSite: '',
        doubleClickState: '',
        doubleClickZone: '',
        enableLazyLoadAbTest: false,
        fetchMarginPercent: 0,
        fuseLibraryVersion: '',
        mobileScaling: 0,
        renderMarginPercent: 0,
        useBottomAnchorAd: false,
        useMantis: false,
        usePublift: false,
      },
      enabled: true,
    },
  },
}));

export const Default: Story = {
  render: () => (
    <TestWrapper store={store}>
      <Component
        elementId={0}
        index={0}
        order={0}
        zoneItemData={{ id: 0, numberOfItems: 3, template: 'ownlocal.html' }}
        zoneItemId={0}
        zoneItemType={ZoneItemType.Classified}
        zoneName={ZoneName.MAIN}
      />
    </TestWrapper>
  ),
};
