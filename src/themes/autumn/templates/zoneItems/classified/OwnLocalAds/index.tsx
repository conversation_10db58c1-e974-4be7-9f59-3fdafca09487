import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';

import Ad from 'themes/autumn/components/ads/Ad';

import type { ClassifiedZoneItem } from 'types/ZoneItems';

// This component can be used as a zone item template as well
type Props = ClassifiedZoneItem | { limit: number };

function Listing({
  hiddenOnMobile = false,
  position,
}: {
  hiddenOnMobile?: boolean;
  position: number;
}) {
  return (
    <div
      className={clsx(
        'h-80 min-w-0 flex-1 overflow-hidden rounded-lg border',
        hiddenOnMobile && 'hidden sm:block',
      )}
    >
      <Ad
        mdSizes="fluid"
        position={position}
        publiftName={`ownlocal-${position}`}
        sizes={hiddenOnMobile ? [] : 'fluid'}
        slotId={`ownlocal-${position}`}
        targetingArguments={{ native: position.toString() }}
        withLabel={false}
        withPlaceholder={false}
        withPlaceholderBackground={false}
      />
    </div>
  );
}

export default function OwnLocalAds(props: Props) {
  const limit =
    // eslint-disable-next-line react/destructuring-assignment
    'limit' in props ? props.limit : props.zoneItemData.numberOfItems;

  return (
    <div>
      <h3 className="my-8 text-lg font-bold">
        {/* No trailing slash */}
        <a href="/local-business">
          Services near you
          <FontAwesomeIcon className="ml-3" icon={faChevronRight} />
        </a>
      </h3>
      <div className="flex gap-4">
        {Array.from(Array(limit).keys()).map((_, i) => (
          // eslint-disable-next-line react/no-array-index-key
          <Listing hiddenOnMobile={i !== 0} key={i} position={i + 1} />
        ))}
      </div>
    </div>
  );
}
