// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`shareAndBookmark renders share and bookmark buttons 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mt-6 flex w-full flex-row items-center justify-start gap-4 md:mt-0 md:justify-end"
  >
    <div
      class="relative"
      data-headlessui-state=""
    >
      <button
        aria-expanded="false"
        aria-label="Share"
        class="flex h-9 w-[94px] cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100 focus-visible:outline-none border-1 border-gray-300"
        data-headlessui-state=""
        id="headlessui-popover-button-:r2:"
        type="button"
      >
        <svg
          fill="none"
          height="20"
          viewBox="0 0 20 20"
          width="20"
        >
          <path
            class="fill-gray-900"
            d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
          />
        </svg>
        <span
          class="text-xs"
        >
          Share
        </span>
      </button>
      <div
        class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
      />
    </div>
  </div>
</div>
`;
