import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import ShareAndBookmark from '.';

describe('shareAndBookmark', () => {
  it('renders share and bookmark buttons', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <ShareAndBookmark />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
