import { render, screen } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import ExploreTravelSubNav from './index';

import type { Page } from 'types/Nav';

describe('ExploreTravelSubNav', () => {
  const store = createStore((state) => ({
    ...state,
    accessToken: 'test_token',
    conf: {
      ...state.conf,
      mode: RenderMode.NORMAL,
    },
    settings: {
      ...state.settings,
      staticUrl: 'https://test-static.com',
    },
  }));

  const mockPages = [
    {
      menuName: 'Destinations',
      name: 'Destinations',
      url: '/travel/destinations',
    },
    {
      menuName: 'Experiences',
      name: 'Experiences',
      url: '/travel/experiences',
    },
    {
      menuName: 'Guides',
      name: 'Guides',
      url: '/travel/guides',
    },
  ] as Page[];

  it('matches snapshot', () => {
    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelSubNav pages={mockPages} />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders logo and navigation links correctly', () => {
    render(
      <TestWrapper store={store}>
        <ExploreTravelSubNav pages={mockPages} />
      </TestWrapper>,
    );

    expect(screen.getAllByTestId('logo')).toHaveLength(1);

    mockPages.forEach((page) => {
      expect(screen.getAllByText(page.name)).toHaveLength(1);
    });
  });
});
