// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ExploreTravelSubNav matches snapshot 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <nav
    aria-label="Main Navigation"
    class="md:flex md:h-full md:items-center md:justify-center"
    role="navigation"
  >
    <div
      class="flex h-full items-stretch gap-x-4 overflow-x-auto whitespace-nowrap rounded-sm px-4 font-inter text-sm font-medium leading-3 text-gray-900 scrollbar-hide md:gap-x-3 md:font-normal"
    >
      <div
        class="flex-stretch flex w-24 shrink-0 grow-0 items-center justify-center justify-self-start md:mt-0 md:w-28"
      >
        <a
          class="text-blue-600 underline decoration-blue-400 hover:text-blue-600 hover:decoration-blue-600 visited:text-gray-500 visited:decoration-gray-500"
          data-testid="logo"
          href="/travel/"
        >
          <img
            alt="Explore Travel"
            class="size-full h-11 max-h-full w-28 max-w-full object-contain"
            src="https://test-static.com/sites/explore-travel/images/masthead/masthead-underline.svg"
          />
        </a>
      </div>
      <a
        class="flex flex-col items-center justify-center rounded-sm border-b-2 border-transparent py-5 hover:border-gray-900 md:px-3"
        href="/travel/destinations/"
      >
        Destinations
      </a>
      <a
        class="flex flex-col items-center justify-center rounded-sm border-b-2 border-transparent py-5 hover:border-gray-900 md:px-3"
        href="/travel/experiences/"
      >
        Experiences
      </a>
      <a
        class="flex flex-col items-center justify-center rounded-sm border-b-2 border-transparent py-5 hover:border-gray-900 md:px-3"
        href="/travel/guides/"
      >
        Guides
      </a>
    </div>
  </nav>
</div>
`;
