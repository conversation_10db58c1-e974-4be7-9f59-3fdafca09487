import slugify from 'slugify';

import { StoryElementType } from 'types/Story';

import type { HeadingElement, Story } from 'types/Story';

export function hasH2Headings(story: Story): boolean {
  return (
    story.elements?.some(
      (element): element is HeadingElement =>
        element.type === StoryElementType.Heading && element.level === '2',
    ) || false
  );
}

export function scrollToAnchor(text: string, onClose?: () => void): void {
  const anchorId = slugify(text, { lower: true });
  const anchorElement = document.getElementById(anchorId);
  if (anchorElement) {
    const stickyMenuOffset = -80;
    const y =
      anchorElement.getBoundingClientRect().top +
      window.scrollY +
      stickyMenuOffset;
    window.scrollTo({ behavior: 'instant', top: y });
    if (onClose) {
      onClose();
    }
  }
}
