// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StickyMobileNav closes anchor box when clicking an anchor item 1`] = `
<div>
  <div
    class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
  >
    <div
      class="mr-3 pr-3 border-r border-gray-200"
    >
      <button
        aria-label="Open menu"
        class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        data-testid="megamenu-button"
        type="button"
      >
        <svg
          class="mx-auto fill-current text-gray-900"
          height="16"
          viewBox="0 0 24 16"
          width="24"
        >
          <path
            clip-rule="evenodd"
            d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
            fill-rule="evenodd"
          />
        </svg>
      </button>
    </div>
    <div
      class="flex grow flex-row items-center"
    >
      <div
        class="flex grow"
      >
        <div
          class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
        >
          <button
            aria-label="In this article"
            class="pl-2 font-inter text-base font-bold xl:pl-4"
            type="button"
          >
            In this article
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-chevron-up ml-2 text-sm"
              data-icon="chevron-up"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"
                fill="currentColor"
              />
            </svg>
          </button>
          <div
            class="absolute left-0 z-[-1] w-full xl:left-auto xl:z-0"
          >
            <div
              class="grid grid-cols-1 divide-y divide-gray-200 bg-white px-4 shadow-md md:place-items-center xl:w-[375px] xl:place-items-start xl:rounded-lg "
            >
              <div
                class="w-full py-3 md:w-[375px] xl:w-full mt-3"
              >
                <button
                  aria-label="First Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  First Heading
                </button>
              </div>
              <div
                class="w-full py-3 md:w-[375px] xl:w-full"
              >
                <button
                  aria-label="Second Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  Second Heading
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="grow"
        />
      </div>
      <div
        class="flex flex-row items-center justify-end gap-x-4"
      >
        <div
          class="relative"
          data-headlessui-state=""
        >
          <button
            aria-expanded="false"
            aria-label="Share"
            class="flex h-9 cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100 focus-visible:outline-none w-auto"
            data-headlessui-state=""
            id="headlessui-popover-button-:rk:"
            type="button"
          >
            <svg
              fill="none"
              height="20"
              viewBox="0 0 20 20"
              width="20"
            >
              <path
                class="fill-gray-900"
                d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
              />
            </svg>
          </button>
          <div
            class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav closes anchor box when clicking outside 1`] = `
<div>
  <div
    class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
  >
    <div
      class="mr-3 pr-3 border-r border-gray-200"
    >
      <button
        aria-label="Open menu"
        class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        data-testid="megamenu-button"
        type="button"
      >
        <svg
          class="mx-auto fill-current text-gray-900"
          height="16"
          viewBox="0 0 24 16"
          width="24"
        >
          <path
            clip-rule="evenodd"
            d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
            fill-rule="evenodd"
          />
        </svg>
      </button>
    </div>
    <div
      class="flex grow flex-row items-center"
    >
      <div
        class="flex grow"
      >
        <div
          class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
        >
          <button
            aria-label="In this article"
            class="pl-2 font-inter text-base font-bold xl:pl-4"
            type="button"
          >
            In this article
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-chevron-up ml-2 text-sm"
              data-icon="chevron-up"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"
                fill="currentColor"
              />
            </svg>
          </button>
          <div
            class="absolute left-0 z-[-1] w-full xl:left-auto xl:z-0"
          >
            <div
              class="grid grid-cols-1 divide-y divide-gray-200 bg-white px-4 shadow-md md:place-items-center xl:w-[375px] xl:place-items-start xl:rounded-lg "
            >
              <div
                class="w-full py-3 md:w-[375px] xl:w-full mt-3"
              >
                <button
                  aria-label="First Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  First Heading
                </button>
              </div>
              <div
                class="w-full py-3 md:w-[375px] xl:w-full"
              >
                <button
                  aria-label="Second Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  Second Heading
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="grow"
        />
      </div>
      <div
        class="flex flex-row items-center justify-end gap-x-4"
      >
        <div
          class="relative"
          data-headlessui-state=""
        >
          <button
            aria-expanded="false"
            aria-label="Share"
            class="flex h-9 cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100 focus-visible:outline-none w-auto"
            data-headlessui-state=""
            id="headlessui-popover-button-:rq:"
            type="button"
          >
            <svg
              fill="none"
              height="20"
              viewBox="0 0 20 20"
              width="20"
            >
              <path
                class="fill-gray-900"
                d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
              />
            </svg>
          </button>
          <div
            class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav renders back link when no heading elements are available 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3 border-r border-gray-200"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow flex-row items-center"
  >
    <a
      class="flex flex-none grow flex-row items-center justify-start rounded bg-white px-2.5 py-2 text-sm font-medium transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      href="/news"
    >
      <svg
        class="fill-current text-gray-900"
        fill="none"
        height="11"
        viewBox="0 0 17 11"
        width="17"
      >
        <path
          clip-rule="evenodd"
          d="M6.50716 10.2071C6.11663 10.5976 5.48347 10.5976 5.09294 10.2071L1.09294 6.2071C0.702418 5.81658 0.702418 5.18342 1.09294 4.79289L5.09294 0.792894C5.48347 0.402369 6.11663 0.402369 6.50716 0.792893C6.89768 1.18342 6.89768 1.81658 6.50716 2.20711L4.21426 4.5L15.8 4.5C16.3523 4.5 16.8 4.94771 16.8 5.5C16.8 6.05228 16.3523 6.5 15.8 6.5L4.21426 6.5L6.50716 8.79289C6.89768 9.18341 6.89768 9.81658 6.50716 10.2071Z"
          fill-rule="evenodd"
        />
      </svg>
      <span
        class="ml-2.5"
      >
        Back to News
      </span>
    </a>
    <div
      class="mr-2 justify-self-end"
    >
      <div
        data-testid="viafoura-tray"
      >
        Viafoura Tray
      </div>
    </div>
    <div
      class="flex flex-row items-center justify-end gap-x-4"
    >
      <div
        class="relative"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-label="Share"
          class="flex h-9 cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100 focus-visible:outline-none w-auto"
          data-headlessui-state=""
          id="headlessui-popover-button-:r10:"
          type="button"
        >
          <svg
            fill="none"
            height="20"
            viewBox="0 0 20 20"
            width="20"
          >
            <path
              class="fill-gray-900"
              d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
            />
          </svg>
        </button>
        <div
          class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav renders correctly in non-story view 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3 border-r border-gray-200"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow flex-row items-center"
  >
    <div
      class="flex grow"
    >
      <div
        class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
      >
        <button
          aria-label="In this article"
          class="pl-2 font-inter text-base font-bold xl:pl-4"
          type="button"
        >
          In this article
          <svg
            aria-hidden="true"
            class="svg-inline--fa fa-chevron-down ml-2 text-sm"
            data-icon="chevron-down"
            data-prefix="fas"
            focusable="false"
            role="img"
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      <div
        class="grow"
      />
    </div>
    <div
      class="flex flex-row items-center justify-end gap-x-4"
    >
      <div
        class="relative"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-label="Share"
          class="flex h-9 cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100 focus-visible:outline-none w-auto"
          data-headlessui-state=""
          id="headlessui-popover-button-:r8:"
          type="button"
        >
          <svg
            fill="none"
            height="20"
            viewBox="0 0 20 20"
            width="20"
          >
            <path
              class="fill-gray-900"
              d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
            />
          </svg>
        </button>
        <div
          class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav renders correctly in story view with anchor links 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3 border-r border-gray-200"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow flex-row items-center"
  >
    <div
      class="flex grow"
    >
      <div
        class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
      >
        <button
          aria-label="In this article"
          class="pl-2 font-inter text-base font-bold xl:pl-4"
          type="button"
        >
          In this article
          <svg
            aria-hidden="true"
            class="svg-inline--fa fa-chevron-down ml-2 text-sm"
            data-icon="chevron-down"
            data-prefix="fas"
            focusable="false"
            role="img"
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      <div
        class="grow"
      />
    </div>
    <div
      class="flex flex-row items-center justify-end gap-x-4"
    >
      <div
        class="relative"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-label="Share"
          class="flex h-9 cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100 focus-visible:outline-none w-auto"
          data-headlessui-state=""
          id="headlessui-popover-button-:r2:"
          type="button"
        >
          <svg
            fill="none"
            height="20"
            viewBox="0 0 20 20"
            width="20"
          >
            <path
              class="fill-gray-900"
              d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
            />
          </svg>
        </button>
        <div
          class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav renders with custom logo type 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow"
  >
    <div
      class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
    >
      <button
        aria-label="In this article"
        class="pl-2 font-inter text-base font-bold xl:pl-4"
        type="button"
      >
        In this article
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-chevron-down ml-2 text-sm"
          data-icon="chevron-down"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 512 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
    <div
      class="grow"
    />
  </div>
</div>
`;

exports[`StickyMobileNav renders with viafoura tray when showViafoura is true 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3 border-r border-gray-200"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow flex-row items-center"
  >
    <div
      class="flex grow"
    >
      <div
        class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
      >
        <button
          aria-label="In this article"
          class="pl-2 font-inter text-base font-bold xl:pl-4"
          type="button"
        >
          In this article
          <svg
            aria-hidden="true"
            class="svg-inline--fa fa-chevron-down ml-2 text-sm"
            data-icon="chevron-down"
            data-prefix="fas"
            focusable="false"
            role="img"
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      <div
        class="grow"
      />
    </div>
    <div
      class="flex flex-row items-center justify-end gap-x-4"
    >
      <div
        class="relative"
        data-headlessui-state=""
      >
        <button
          aria-expanded="false"
          aria-label="Share"
          class="flex h-9 cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100 focus-visible:outline-none w-auto"
          data-headlessui-state=""
          id="headlessui-popover-button-:r16:"
          type="button"
        >
          <svg
            fill="none"
            height="20"
            viewBox="0 0 20 20"
            width="20"
          >
            <path
              class="fill-gray-900"
              d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
            />
          </svg>
        </button>
        <div
          class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav toggles anchor box when clicking the dropdown button 1`] = `
<div>
  <div
    class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
  >
    <div
      class="mr-3 pr-3 border-r border-gray-200"
    >
      <button
        aria-label="Open menu"
        class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        data-testid="megamenu-button"
        type="button"
      >
        <svg
          class="mx-auto fill-current text-gray-900"
          height="16"
          viewBox="0 0 24 16"
          width="24"
        >
          <path
            clip-rule="evenodd"
            d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
            fill-rule="evenodd"
          />
        </svg>
      </button>
    </div>
    <div
      class="flex grow flex-row items-center"
    >
      <div
        class="flex grow"
      >
        <div
          class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
        >
          <button
            aria-label="In this article"
            class="pl-2 font-inter text-base font-bold xl:pl-4"
            type="button"
          >
            In this article
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-chevron-up ml-2 text-sm"
              data-icon="chevron-up"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"
                fill="currentColor"
              />
            </svg>
          </button>
          <div
            class="absolute left-0 z-[-1] w-full xl:left-auto xl:z-0"
          >
            <div
              class="grid grid-cols-1 divide-y divide-gray-200 bg-white px-4 shadow-md md:place-items-center xl:w-[375px] xl:place-items-start xl:rounded-lg "
            >
              <div
                class="w-full py-3 md:w-[375px] xl:w-full mt-3"
              >
                <button
                  aria-label="First Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  First Heading
                </button>
              </div>
              <div
                class="w-full py-3 md:w-[375px] xl:w-full"
              >
                <button
                  aria-label="Second Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  Second Heading
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="grow"
        />
      </div>
      <div
        class="flex flex-row items-center justify-end gap-x-4"
      >
        <div
          class="relative"
          data-headlessui-state=""
        >
          <button
            aria-expanded="false"
            aria-label="Share"
            class="flex h-9 cursor-pointer flex-row items-center justify-center gap-1 rounded-3xl hover:bg-gray-100 focus-visible:outline-none w-auto"
            data-headlessui-state=""
            id="headlessui-popover-button-:re:"
            type="button"
          >
            <svg
              fill="none"
              height="20"
              viewBox="0 0 20 20"
              width="20"
            >
              <path
                class="fill-gray-900"
                d="M19 9.914 11.086 2v5.185a9.53 9.53 0 0 0-6.274 2.798A9.537 9.537 0 0 0 2 16.771v2.007l.965-1.76a8.577 8.577 0 0 1 8.12-4.428v5.238L19 9.914Zm-8.512 1.626a9.61 9.61 0 0 0-7.236 3.283C4.136 11.033 7.544 8.2 11.6 8.2h.514V4.483l5.431 5.431-5.43 5.431v-3.677l-.452-.055a9.59 9.59 0 0 0-1.175-.073Z"
              />
            </svg>
          </button>
          <div
            class="absolute left-1/2 top-[41px] z-50 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;
