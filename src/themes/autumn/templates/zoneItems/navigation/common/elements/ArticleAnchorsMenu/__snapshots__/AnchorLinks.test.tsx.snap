// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StoryAnchorBox Desktop variant renders desktop version correctly 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="grid grid-cols-1 divide-y divide-gray-200 bg-white px-4 shadow-md md:place-items-center xl:w-[375px] xl:place-items-start xl:rounded-lg "
  >
    <div
      class="w-full py-3 md:w-[375px] xl:w-full mt-3"
    >
      <button
        aria-label="Heading 1"
        class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
        type="button"
      >
        Heading 1
      </button>
    </div>
    <div
      class="w-full py-3 md:w-[375px] xl:w-full"
    >
      <button
        aria-label="Heading 2"
        class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
        type="button"
      >
        Heading 2
      </button>
    </div>
    <div
      class="w-full py-3 md:w-[375px] xl:w-full"
    >
      <button
        aria-label="Heading 3"
        class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
        type="button"
      >
        Heading 3
      </button>
    </div>
  </div>
</div>
`;
