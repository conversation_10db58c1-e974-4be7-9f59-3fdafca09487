import clsx from 'clsx';

import { htmlToText } from 'util/device';

import { scrollToAnchor } from '../../utils';

import type { HeadingElement } from 'types/Story';

export interface AnchorLinksProps {
  className?: string;
  elements: HeadingElement[];
  onClose?: () => void;
}

export default function AnchorLinks({
  className = '',
  elements,
  onClose,
}: AnchorLinksProps) {
  return (
    <div
      className={`grid grid-cols-1 divide-y divide-gray-200 bg-white px-4 shadow-md md:place-items-center xl:w-[375px] xl:place-items-start xl:rounded-lg ${className}`}
    >
      {elements.map((element, index) => {
        const text = htmlToText(element.text);
        return (
          <div
            className={clsx('w-full py-3 md:w-[375px] xl:w-full', {
              'mt-3': index === 0,
            })}
            key={text}
          >
            <button
              aria-label={text}
              className="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
              dangerouslySetInnerHTML={{ __html: text }}
              onClick={() => scrollToAnchor(text, onClose)}
              type="button"
            />
          </div>
        );
      })}
    </div>
  );
}
