import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';

import {
  type HeadingElement,
  type Story,
  StoryElementType,
} from 'types/Story';

import AnchorLinks from './AnchorLinks';

interface ArticleAnchorsMenuProps {
  story: Story;
}

export default function ArticleAnchorsMenu({
  story,
}: ArticleAnchorsMenuProps) {
  const [isAnchorBoxOpen, setIsAnchorBoxOpen] = useState(false);
  const headingElements = story.makeH2AnchorLinks
    ? story.elements?.filter(
        (element): element is HeadingElement =>
          element.type === StoryElementType.Heading && element.level === '2',
      )
    : [];
  const anchorBoxRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Close the anchor box when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        isAnchorBoxOpen &&
        anchorBoxRef.current &&
        buttonRef.current &&
        !anchorBoxRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsAnchorBoxOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isAnchorBoxOpen]);

  return (
    headingElements &&
    headingElements.length > 0 && (
      <div
        className="group-focus-within:opacity-0 lg:ml-4 xl:relative"
        onFocus={(e) => {
          e.preventDefault();
          e.target.blur();
        }}
      >
        <button
          aria-label="In this article"
          className="pl-2 font-inter text-base font-bold xl:pl-4"
          onClick={() => {
            setIsAnchorBoxOpen(!isAnchorBoxOpen);
          }}
          ref={buttonRef}
          type="button"
        >
          In this article
          <FontAwesomeIcon
            className={clsx('ml-2 text-sm')}
            icon={isAnchorBoxOpen ? faChevronUp : faChevronDown}
          />
        </button>
        {isAnchorBoxOpen && (
          <div
            className="absolute left-0 z-[-1] w-full xl:left-auto xl:z-0"
            ref={anchorBoxRef}
          >
            <AnchorLinks
              elements={headingElements}
              onClose={() => setIsAnchorBoxOpen(false)}
            />
          </div>
        )}
      </div>
    )
  );
}
