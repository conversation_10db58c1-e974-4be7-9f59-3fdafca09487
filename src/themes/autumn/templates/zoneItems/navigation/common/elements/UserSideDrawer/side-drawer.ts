import { createContext, useContext } from 'react';

import type { Bookmark } from 'components/Bookmark/types';
import type { ApiErrorResponse } from 'types/sepang-types/response';

export type BookmarksContextType = {
  bookmarks: Bookmark[];
  error: ApiErrorResponse | null;
  isOpen: boolean;
  loading: boolean;
  toggleSideDrawer: () => void;
};

export const SideDrawerContext = createContext<BookmarksContextType>({
  bookmarks: [],
  error: null,
  isOpen: false,
  loading: false,
  toggleSideDrawer: () => {},
});

export function useSideDrawer() {
  const context = useContext(SideDrawerContext);
  if (context === undefined) {
    throw new Error('useSideDrawer must be used within a SideDrawerProvider');
  }
  return context;
}
