import { fireEvent, render, screen } from '@testing-library/react';

import { createStore } from 'store/store';
import { type HeadingElement, StoryElementType } from 'types/Story';
import { TestWrapper } from 'util/jest';

import AnchorLinks from './AnchorLinks';

// htmlToText mock
jest.mock('util/device', () => ({
  htmlToText: (text: string) => text.replace(/<\/?[^>]+(>|$)/g, ''),
}));

// scrollTo mock
const scrollToMock = jest.fn();
Object.defineProperty(window, 'scrollTo', { value: scrollToMock });

// getBoundingClientRect mock
const getBoundingClientRectMock = jest.fn().mockReturnValue({ top: 100 });
Object.defineProperty(HTMLElement.prototype, 'getBoundingClientRect', {
  configurable: true,
  value: getBoundingClientRectMock,
});

describe('StoryAnchorBox', () => {
  beforeEach(() => {
    scrollToMock.mockClear();
    getBoundingClientRectMock.mockClear();

    jest.spyOn(document, 'getElementById').mockImplementation(() => {
      const element = document.createElement('div');
      Object.defineProperty(element, 'getBoundingClientRect', {
        value: getBoundingClientRectMock,
      });
      return element;
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Desktop variant', () => {
    it('renders desktop version correctly', () => {
      const store = createStore();
      const elements: HeadingElement[] = [
        { level: '2', text: 'Heading 1', type: StoryElementType.Heading },
        { level: '3', text: 'Heading 2', type: StoryElementType.Heading },
        { level: '2', text: 'Heading 3', type: StoryElementType.Heading },
      ];

      const { container } = render(
        <TestWrapper store={store}>
          <AnchorLinks elements={elements} />
        </TestWrapper>,
      );

      expect(container.firstChild).toMatchSnapshot();
    });

    it('scrolls to the correct heading when clicked', () => {
      const store = createStore();
      const elements: HeadingElement[] = [
        { level: '2', text: 'Heading 1', type: StoryElementType.Heading },
        { level: '3', text: 'Heading 2', type: StoryElementType.Heading },
      ];

      render(
        <TestWrapper store={store}>
          <AnchorLinks elements={elements} />
        </TestWrapper>,
      );

      fireEvent.click(screen.getByText('Heading 1'));
      expect(jest.spyOn(document, 'getElementById')).toHaveBeenCalledWith(
        'heading-1',
      );

      expect(
        jest.spyOn(HTMLElement.prototype, 'getBoundingClientRect'),
      ).toHaveBeenCalled();

      expect(scrollToMock).toHaveBeenCalledWith({
        behavior: 'instant',
        top: window.scrollY + 100 - 80,
      });
    });
  });

  describe('Mobile variant', () => {
    it('renders mobile version correctly', () => {
      const store = createStore();
      const elements: HeadingElement[] = [
        { level: '2', text: 'Heading 1', type: StoryElementType.Heading },
        { level: '3', text: 'Heading 2', type: StoryElementType.Heading },
      ];

      render(
        <TestWrapper store={store}>
          <AnchorLinks elements={elements} />
        </TestWrapper>,
      );

      // In mobile, "In this article" is in sticky nav
      expect(screen.queryByText('In this article')).not.toBeInTheDocument();

      expect(screen.getByText('Heading 1')).toBeInTheDocument();
      expect(screen.getByText('Heading 2')).toBeInTheDocument();
    });

    it('calls onClose when a heading is clicked in mobile variant', () => {
      const store = createStore();
      const elements: HeadingElement[] = [
        { level: '2', text: 'Heading 1', type: StoryElementType.Heading },
        { level: '3', text: 'Heading 2', type: StoryElementType.Heading },
      ];

      const onCloseMock = jest.fn();
      render(
        <TestWrapper store={store}>
          <AnchorLinks elements={elements} onClose={onCloseMock} />
        </TestWrapper>,
      );

      fireEvent.click(screen.getByText('Heading 1'));

      expect(jest.spyOn(document, 'getElementById')).toHaveBeenCalledWith(
        'heading-1',
      );
      expect(scrollToMock).toHaveBeenCalled();
      expect(onCloseMock).toHaveBeenCalled();
    });
  });

  it('handles HTML content in heading text', () => {
    expect.assertions(2);

    const store = createStore();
    const elements: HeadingElement[] = [
      {
        level: '2',
        text: '<strong>Bold Heading</strong>',
        type: StoryElementType.Heading,
      },
      {
        level: '3',
        text: 'Regular <em>Italic</em> Text',
        type: StoryElementType.Heading,
      },
    ];

    render(
      <TestWrapper store={store}>
        <AnchorLinks elements={elements} />
      </TestWrapper>,
    );

    expect(screen.getByText('Bold Heading')).toBeInTheDocument();
    expect(screen.getByText('Regular Italic Text')).toBeInTheDocument();
  });

  it('does not scroll when element is not found', () => {
    expect.assertions(2);

    jest.spyOn(document, 'getElementById').mockReturnValue(null);

    const store = createStore();
    const elements: HeadingElement[] = [
      {
        level: '2',
        text: 'Non-existent Heading',
        type: StoryElementType.Heading,
      },
    ];

    render(
      <TestWrapper store={store}>
        <AnchorLinks elements={elements} />
      </TestWrapper>,
    );

    fireEvent.click(screen.getByText('Non-existent Heading'));
    expect(jest.spyOn(document, 'getElementById')).toHaveBeenCalledWith(
      'non-existent-heading',
    );
    expect(scrollToMock).not.toHaveBeenCalled();
  });

  it('renders nothing when no elements are provided', () => {
    expect.assertions(1);

    const store = createStore();
    const elements: HeadingElement[] = [];

    const { container } = render(
      <TestWrapper store={store}>
        <AnchorLinks elements={elements} />
      </TestWrapper>,
    );

    expect(container.querySelectorAll('button')).toHaveLength(0);
  });
});
