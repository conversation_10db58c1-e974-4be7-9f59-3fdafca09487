import TopicCard from './TopicCard';

import type { CollectionPage } from 'types/ZoneItems';

interface CardGridItemProps {
  index: number;
  page: CollectionPage;
}

function CardGridItem({ index, page }: CardGridItemProps): React.ReactElement {
  const notFirstRow = index >= 3;
  const isLastInRow = (index + 1) % 3 === 0;

  return (
    <div
      className="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      key={page.id}
    >
      {/* Vertical separator */}
      {!isLastInRow && (
        <div className="absolute -right-5 top-1/2 hidden h-36 w-px -translate-y-1/2 bg-gray-200 lg:block" />
      )}
      {/* Horizontal separator for items not in first row */}
      {notFirstRow && (
        <div className="absolute left-1/2 top-0 z-10 hidden h-px w-72 -translate-x-1/2 bg-gray-200 lg:block" />
      )}
      <TopicCard
        containerClassName="h-full"
        image_url={page.cardImageUrl}
        metaDescription={page.metaDescription}
        title={page.name}
        url={page.url}
      />
    </div>
  );
}

export default CardGridItem;
