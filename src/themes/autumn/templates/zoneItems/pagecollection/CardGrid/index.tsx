import CardGridItem from './CardGridItem';

import type { PageCollectionZoneItem } from 'types/ZoneItems';

function CardGrid({
  zoneItemData: { pages = [], title },
}: PageCollectionZoneItem): React.ReactElement {
  return (
    <div className="mx-auto w-full rounded-lg border border-gray-200 bg-white px-4 pt-10 font-inter shadow lg:px-14 lg:pb-6 lg:pt-14">
      {title && (
        <h2 className="pb-5 text-xl font-medium text-gray-900 lg:pb-6 lg:text-2xl">
          {title}
        </h2>
      )}

      <div className="grid auto-rows-auto grid-cols-1 border-t border-gray-200 md:grid-cols-2 lg:grid-cols-3 lg:gap-x-10 lg:pt-4">
        {pages.map((page, index) => (
          <CardGridItem index={index} key={page.id} page={page} />
        ))}
      </div>
    </div>
  );
}

export default CardGrid;
