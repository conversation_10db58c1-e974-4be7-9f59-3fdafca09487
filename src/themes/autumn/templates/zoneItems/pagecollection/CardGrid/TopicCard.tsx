import ChevronRightIcon from '@heroicons/react/24/solid/ChevronRightIcon';
import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { TransformOutputFormat, storyImageUrl } from 'util/image';

import { type CardProps as BaseCardProps } from '../ImageCardList/Card';

interface CardProps extends BaseCardProps {
  metaDescription?: string;
}

export default function TopicCard({
  containerClassName,
  image_url,
  metaDescription,
  title,
  url,
}: CardProps): React.ReactElement {
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);

  return (
    <Link
      className={clsx(
        'group relative flex flex-col py-5 text-left md:px-6 lg:px-0',
        containerClassName,
      )}
      href={url}
      noStyle
    >
      {image_url && (
        <div className="mb-6">
          <img
            alt={title}
            aria-label={title}
            className="size-6 object-contain"
            src={storyImageUrl({
              height: 24,
              image: { uri: image_url },
              outputFormat: TransformOutputFormat.WEBP,
              transformUrl,
              width: 24,
            })}
          />
        </div>
      )}

      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline">
          {title}
        </h3>
        <ChevronRightIcon
          className="size-5 shrink-0 text-gray-950"
          data-testid="chevron-right-icon"
        />
      </div>
      {metaDescription && (
        <p className="line-clamp-3 overflow-hidden text-ellipsis text-sm text-gray-600">
          {metaDescription}
        </p>
      )}
    </Link>
  );
}
