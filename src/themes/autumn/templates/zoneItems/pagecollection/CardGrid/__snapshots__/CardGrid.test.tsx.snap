// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CardGrid Component handles many pages to test grid layout 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mx-auto w-full rounded-lg border border-gray-200 bg-white px-4 pt-10 font-inter shadow lg:px-14 lg:pb-6 lg:pt-14"
  >
    <h2
      class="pb-5 text-xl font-medium text-gray-900 lg:pb-6 lg:text-2xl"
    >
      Many Pages Collection
    </h2>
    <div
      class="grid auto-rows-auto grid-cols-1 border-t border-gray-200 md:grid-cols-2 lg:grid-cols-3 lg:gap-x-10 lg:pt-4"
    >
      <div
        class="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      >
        <div
          class="absolute -right-5 top-1/2 hidden h-36 w-px -translate-y-1/2 bg-gray-200 lg:block"
        />
        <a
          class="group relative flex flex-col py-5 text-left md:px-6 lg:px-0 h-full"
          href="/help-centre/test-page-1/"
        >
          <div
            class="mb-6"
          >
            <img
              alt="Page 1 Title"
              aria-label="Page 1 Title"
              class="size-6 object-contain"
              src="transform/v1/resize/frm/https://example.com/image1.jpg/w24_h24_fcrop.webp"
            />
          </div>
          <div
            class="mb-6 flex items-center justify-between"
          >
            <h3
              class="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
            >
              Page 1 Title
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <p
            class="line-clamp-3 overflow-hidden text-ellipsis text-sm text-gray-600"
          >
            This is the first page description
          </p>
        </a>
      </div>
      <div
        class="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      >
        <div
          class="absolute -right-5 top-1/2 hidden h-36 w-px -translate-y-1/2 bg-gray-200 lg:block"
        />
        <a
          class="group relative flex flex-col py-5 text-left md:px-6 lg:px-0 h-full"
          href="/help-centre/test-page-2/"
        >
          <div
            class="mb-6"
          >
            <img
              alt="Page 2 Title"
              aria-label="Page 2 Title"
              class="size-6 object-contain"
              src="transform/v1/resize/frm/https://example.com/image2.jpg/w24_h24_fcrop.webp"
            />
          </div>
          <div
            class="mb-6 flex items-center justify-between"
          >
            <h3
              class="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
            >
              Page 2 Title
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <p
            class="line-clamp-3 overflow-hidden text-ellipsis text-sm text-gray-600"
          >
            This is the second page description
          </p>
        </a>
      </div>
      <div
        class="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      >
        <a
          class="group relative flex flex-col py-5 text-left md:px-6 lg:px-0 h-full"
          href="/help-centre/test-page-3/"
        >
          <div
            class="mb-6"
          >
            <img
              alt="Page 3 Title"
              aria-label="Page 3 Title"
              class="size-6 object-contain"
              src="transform/v1/resize/frm/https://example.com/image3.jpg/w24_h24_fcrop.webp"
            />
          </div>
          <div
            class="mb-6 flex items-center justify-between"
          >
            <h3
              class="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
            >
              Page 3 Title
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <p
            class="line-clamp-3 overflow-hidden text-ellipsis text-sm text-gray-600"
          >
            This is the third page description
          </p>
        </a>
      </div>
      <div
        class="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      >
        <div
          class="absolute -right-5 top-1/2 hidden h-36 w-px -translate-y-1/2 bg-gray-200 lg:block"
        />
        <div
          class="absolute left-1/2 top-0 z-10 hidden h-px w-72 -translate-x-1/2 bg-gray-200 lg:block"
        />
        <a
          class="group relative flex flex-col py-5 text-left md:px-6 lg:px-0 h-full"
          href="/help-centre/test-page-4/"
        >
          <div
            class="mb-6"
          >
            <img
              alt="Page 4 Title"
              aria-label="Page 4 Title"
              class="size-6 object-contain"
              src="transform/v1/resize/frm/https://example.com/image4.jpg/w24_h24_fcrop.webp"
            />
          </div>
          <div
            class="mb-6 flex items-center justify-between"
          >
            <h3
              class="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
            >
              Page 4 Title
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <p
            class="line-clamp-3 overflow-hidden text-ellipsis text-sm text-gray-600"
          >
            This is the fourth page description
          </p>
        </a>
      </div>
      <div
        class="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      >
        <div
          class="absolute -right-5 top-1/2 hidden h-36 w-px -translate-y-1/2 bg-gray-200 lg:block"
        />
        <div
          class="absolute left-1/2 top-0 z-10 hidden h-px w-72 -translate-x-1/2 bg-gray-200 lg:block"
        />
        <a
          class="group relative flex flex-col py-5 text-left md:px-6 lg:px-0 h-full"
          href="/help-centre/test-page-5/"
        >
          <div
            class="mb-6"
          >
            <img
              alt="Page 5 Title"
              aria-label="Page 5 Title"
              class="size-6 object-contain"
              src="transform/v1/resize/frm/https://example.com/image5.jpg/w24_h24_fcrop.webp"
            />
          </div>
          <div
            class="mb-6 flex items-center justify-between"
          >
            <h3
              class="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
            >
              Page 5 Title
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <p
            class="line-clamp-3 overflow-hidden text-ellipsis text-sm text-gray-600"
          >
            This is the fifth page description
          </p>
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`CardGrid Component handles pages without metaDescription 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mx-auto w-full rounded-lg border border-gray-200 bg-white px-4 pt-10 font-inter shadow lg:px-14 lg:pb-6 lg:pt-14"
  >
    <h2
      class="pb-5 text-xl font-medium text-gray-900 lg:pb-6 lg:text-2xl"
    >
      Test Collection
    </h2>
    <div
      class="grid auto-rows-auto grid-cols-1 border-t border-gray-200 md:grid-cols-2 lg:grid-cols-3 lg:gap-x-10 lg:pt-4"
    >
      <div
        class="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      >
        <div
          class="absolute -right-5 top-1/2 hidden h-36 w-px -translate-y-1/2 bg-gray-200 lg:block"
        />
        <a
          class="group relative flex flex-col py-5 text-left md:px-6 lg:px-0 h-full"
          href="/help-centre/test-page-1/"
        >
          <div
            class="mb-6"
          >
            <img
              alt="Page 1 Title"
              aria-label="Page 1 Title"
              class="size-6 object-contain"
              src="transform/v1/resize/frm/https://example.com/image1.jpg/w24_h24_fcrop.webp"
            />
          </div>
          <div
            class="mb-6 flex items-center justify-between"
          >
            <h3
              class="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
            >
              Page 1 Title
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`CardGrid Component renders with empty pages array 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mx-auto w-full rounded-lg border border-gray-200 bg-white px-4 pt-10 font-inter shadow lg:px-14 lg:pb-6 lg:pt-14"
  >
    <h2
      class="pb-5 text-xl font-medium text-gray-900 lg:pb-6 lg:text-2xl"
    >
      Empty Collection
    </h2>
    <div
      class="grid auto-rows-auto grid-cols-1 border-t border-gray-200 md:grid-cols-2 lg:grid-cols-3 lg:gap-x-10 lg:pt-4"
    />
  </div>
</div>
`;

exports[`CardGrid Component renders with title and pages 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mx-auto w-full rounded-lg border border-gray-200 bg-white px-4 pt-10 font-inter shadow lg:px-14 lg:pb-6 lg:pt-14"
  >
    <h2
      class="pb-5 text-xl font-medium text-gray-900 lg:pb-6 lg:text-2xl"
    >
      Featured Pages
    </h2>
    <div
      class="grid auto-rows-auto grid-cols-1 border-t border-gray-200 md:grid-cols-2 lg:grid-cols-3 lg:gap-x-10 lg:pt-4"
    >
      <div
        class="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      >
        <div
          class="absolute -right-5 top-1/2 hidden h-36 w-px -translate-y-1/2 bg-gray-200 lg:block"
        />
        <a
          class="group relative flex flex-col py-5 text-left md:px-6 lg:px-0 h-full"
          href="/help-centre/test-page-1/"
        >
          <div
            class="mb-6"
          >
            <img
              alt="Page 1 Title"
              aria-label="Page 1 Title"
              class="size-6 object-contain"
              src="transform/v1/resize/frm/https://example.com/image1.jpg/w24_h24_fcrop.webp"
            />
          </div>
          <div
            class="mb-6 flex items-center justify-between"
          >
            <h3
              class="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
            >
              Page 1 Title
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <p
            class="line-clamp-3 overflow-hidden text-ellipsis text-sm text-gray-600"
          >
            This is the first page description
          </p>
        </a>
      </div>
      <div
        class="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      >
        <div
          class="absolute -right-5 top-1/2 hidden h-36 w-px -translate-y-1/2 bg-gray-200 lg:block"
        />
        <a
          class="group relative flex flex-col py-5 text-left md:px-6 lg:px-0 h-full"
          href="/help-centre/test-page-2/"
        >
          <div
            class="mb-6"
          >
            <img
              alt="Page 2 Title"
              aria-label="Page 2 Title"
              class="size-6 object-contain"
              src="transform/v1/resize/frm/https://example.com/image2.jpg/w24_h24_fcrop.webp"
            />
          </div>
          <div
            class="mb-6 flex items-center justify-between"
          >
            <h3
              class="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
            >
              Page 2 Title
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <p
            class="line-clamp-3 overflow-hidden text-ellipsis text-sm text-gray-600"
          >
            This is the second page description
          </p>
        </a>
      </div>
      <div
        class="border-t border-gray-200 lg:relative lg:min-h-48 lg:border-t-0"
      >
        <a
          class="group relative flex flex-col py-5 text-left md:px-6 lg:px-0 h-full"
          href="/help-centre/test-page-3/"
        >
          <div
            class="mb-6"
          >
            <img
              alt="Page 3 Title"
              aria-label="Page 3 Title"
              class="size-6 object-contain"
              src="transform/v1/resize/frm/https://example.com/image3.jpg/w24_h24_fcrop.webp"
            />
          </div>
          <div
            class="mb-6 flex items-center justify-between"
          >
            <h3
              class="text-base font-medium text-gray-900 decoration-gray-800 transition-all duration-300 ease-in-out group-hover:underline"
            >
              Page 3 Title
            </h3>
            <svg
              aria-hidden="true"
              class="size-5 shrink-0 text-gray-950"
              data-slot="icon"
              data-testid="chevron-right-icon"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
          <p
            class="line-clamp-3 overflow-hidden text-ellipsis text-sm text-gray-600"
          >
            This is the third page description
          </p>
        </a>
      </div>
    </div>
  </div>
</div>
`;
