import { render, screen } from '@testing-library/react';

import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper } from 'util/jest';

import CardGrid from '.';

import type { CollectionPage, PageCollectionZoneItem } from 'types/ZoneItems';

const mockPages: CollectionPage[] = [
  {
    cardImageUrl: 'https://example.com/image1.jpg',
    children: [],
    id: 1,
    menuName: 'Page 1',
    menuVisible: true,
    metaDescription: 'This is the first page description',
    name: 'Page 1 Title',
    newWindow: false,
    url: '/help-centre/test-page-1/',
  },
  {
    cardImageUrl: 'https://example.com/image2.jpg',
    children: [],
    id: 2,
    menuName: 'Page 2',
    menuVisible: true,
    metaDescription: 'This is the second page description',
    name: 'Page 2 Title',
    newWindow: false,
    url: '/help-centre/test-page-2/',
  },
  {
    cardImageUrl: 'https://example.com/image3.jpg',
    children: [],
    id: 3,
    menuName: 'Page 3',
    menuVisible: true,
    metaDescription: 'This is the third page description',
    name: 'Page 3 Title',
    newWindow: false,
    url: '/help-centre/test-page-3/',
  },
];

const createMockPageCollectionZoneItem = (
  pages: CollectionPage[] = mockPages,
  title?: string,
): PageCollectionZoneItem => ({
  elementId: 1,
  index: 0,
  order: 0,
  zoneItemData: {
    id: 1,
    pages,
    template: 'CardGrid',
    title: title || 'Test Collection Title',
  },
  zoneItemId: 1,
  zoneItemType: ZoneItemType.PageCollection,
  zoneName: ZoneName.MAIN,
});

describe('CardGrid Component', () => {
  const store = createStore();

  it('renders with title and pages', () => {
    const zoneItem = createMockPageCollectionZoneItem(
      mockPages,
      'Featured Pages',
    );

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <CardGrid {...zoneItem} />
      </TestWrapper>,
    );

    expect(screen.getByText('Featured Pages')).toBeInTheDocument();
    expect(screen.getByText('Page 1 Title')).toBeInTheDocument();
    expect(screen.getByText('Page 2 Title')).toBeInTheDocument();
    expect(screen.getByText('Page 3 Title')).toBeInTheDocument();
    const chevronIcons = screen.getAllByTestId('chevron-right-icon');
    expect(chevronIcons).toHaveLength(3);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with empty pages array', () => {
    const zoneItem = createMockPageCollectionZoneItem([], 'Empty Collection');

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <CardGrid {...zoneItem} />
      </TestWrapper>,
    );

    expect(screen.getByText('Empty Collection')).toBeInTheDocument();
    expect(screen.queryByText('Page 1 Title')).not.toBeInTheDocument();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('handles pages without metaDescription', () => {
    const pagesWithoutDescription = [
      {
        ...mockPages[0],
        metaDescription: undefined,
      },
    ];
    const zoneItem = createMockPageCollectionZoneItem(
      pagesWithoutDescription,
      'Test Collection',
    );

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <CardGrid {...zoneItem} />
      </TestWrapper>,
    );

    expect(screen.getByText('Page 1 Title')).toBeInTheDocument();
    expect(
      screen.queryByText('This is the first page description'),
    ).not.toBeInTheDocument();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('handles many pages to test grid layout', () => {
    const manyPages = [
      ...mockPages,
      {
        cardImageUrl: 'https://example.com/image4.jpg',
        children: [],
        id: 4,
        menuName: 'Page 4',
        menuVisible: true,
        metaDescription: 'This is the fourth page description',
        name: 'Page 4 Title',
        newWindow: false,
        url: '/help-centre/test-page-4/',
      },
      {
        cardImageUrl: 'https://example.com/image5.jpg',
        children: [],
        id: 5,
        menuName: 'Page 5',
        menuVisible: true,
        metaDescription: 'This is the fifth page description',
        name: 'Page 5 Title',
        newWindow: false,
        url: '/help-centre/test-page-5/',
      },
    ];
    const zoneItem = createMockPageCollectionZoneItem(
      manyPages,
      'Many Pages Collection',
    );

    const { container } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <CardGrid {...zoneItem} />
      </TestWrapper>,
    );

    expect(screen.getByText('Page 1 Title')).toBeInTheDocument();
    expect(screen.getByText('Page 2 Title')).toBeInTheDocument();
    expect(screen.getByText('Page 3 Title')).toBeInTheDocument();
    expect(screen.getByText('Page 4 Title')).toBeInTheDocument();
    expect(screen.getByText('Page 5 Title')).toBeInTheDocument();
    expect(screen.getAllByTestId('chevron-right-icon')).toHaveLength(5);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders TopicCard components with correct data', () => {
    const zoneItem = createMockPageCollectionZoneItem(
      mockPages,
      'Test Collection',
    );

    render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <CardGrid {...zoneItem} />
      </TestWrapper>,
    );

    expect(screen.getByText('Page 1 Title')).toBeInTheDocument();
    expect(screen.getByText('Page 2 Title')).toBeInTheDocument();
    expect(screen.getByText('Page 3 Title')).toBeInTheDocument();

    expect(
      screen.getByText('This is the first page description'),
    ).toBeInTheDocument();
    expect(
      screen.getByText('This is the second page description'),
    ).toBeInTheDocument();
    expect(
      screen.getByText('This is the third page description'),
    ).toBeInTheDocument();

    const links = screen.getAllByRole('link');
    expect(links).toHaveLength(3);
    expect(links[0]).toHaveAttribute('href', '/help-centre/test-page-1/');
    expect(links[1]).toHaveAttribute('href', '/help-centre/test-page-2/');
    expect(links[2]).toHaveAttribute('href', '/help-centre/test-page-3/');
  });
});
