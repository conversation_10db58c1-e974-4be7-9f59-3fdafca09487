import { useAppSelector } from 'store/hooks';
import PageNavigation from 'themes/autumn/components/page/PageNavigation';
import THEMES from 'themes/autumn/templates/index/IndexPageSponsor/theme';
import { NavigationType, type Page } from 'types/Nav';

import type { PageCollectionZoneItem } from 'types/ZoneItems';

const getTheme = (page: Page) => {
  const selectedTheme = Object.keys(THEMES).find((theme) =>
    page.url.includes(theme),
  );
  return selectedTheme ? THEMES[selectedTheme] : THEMES.default;
};

export default function PillPageNavigationPageCollection({
  zoneItemData: { pages },
}: PageCollectionZoneItem) {
  const page = useAppSelector((state) => state.page);
  return (
    <PageNavigation
      fontStyle="text-sm font-medium"
      mobileMoreOptionEnabled={false}
      navClassName=""
      navWrapperClassName="md:border-b-1 border-gray-300 pb-2 md:pb-6 md:mb-10"
      navigationType={NavigationType.Pill}
      pages={pages}
      theme={getTheme(page)}
    />
  );
}
