/* eslint-disable react/jsx-props-no-spreading */
import {
  faChevronLeft,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import clsx from 'clsx';
import { useCallback, useRef, useState } from 'react';
import Slider from 'react-slick';

import { useWindowSize } from 'util/hooks';

import AuthorCard from './AuthorCard';
import styles from './MeetTheTeam.module.css';

import type { AuthorsZoneItem } from 'types/ZoneItems';

const ITEMS_PER_PAGE = 4;
const ITEMS_PER_PAGE_COMPACT = 3;
const SLIDER_COMPACT_BREAKPOINT = 1280;

function MeetTheTeam({ zoneItemData: { authors } }: AuthorsZoneItem) {
  const { width: windowWidth } = useWindowSize();
  const [currentIndex, setCurrentIndex] = useState(0);
  const sliderRef = useRef<Slider>(null);
  const sliderSettings = {
    arrows: false,
    beforeChange: (_: number, next: number) => {
      setCurrentIndex(next);
    },
    dots: false,
    easing: 'easeInOut',
    infinite: false,
    responsive: [
      {
        breakpoint: SLIDER_COMPACT_BREAKPOINT,
        settings: {
          slidesToScroll: 3,
          slidesToShow: 3,
          swipe: true,
          touchThreshold: 100,
          variableWidth: false,
        },
      },
    ],
    slidesToScroll: ITEMS_PER_PAGE,
    slidesToShow: ITEMS_PER_PAGE,
    speed: 1000,
    swipe: false,
    variableWidth: false,
  };

  const slidesToShow =
    windowWidth >= SLIDER_COMPACT_BREAKPOINT
      ? ITEMS_PER_PAGE
      : ITEMS_PER_PAGE_COMPACT;

  const hasPrevious = currentIndex > 0;
  const hasNext = currentIndex + slidesToShow < authors.length;

  const handlePrevious = useCallback(() => {
    sliderRef.current?.slickPrev();
  }, []);

  const handleNext = useCallback(() => {
    sliderRef.current?.slickNext();
  }, []);

  return (
    <div
      aria-labelledby="travel-experts-heading"
      className="flex w-full flex-col gap-8"
      role="region"
    >
      <div className="flex w-full flex-row items-center justify-between">
        <h2
          className="font-inter text-2xl font-semibold text-gray-900"
          id="travel-experts-heading"
        >
          Meet the&nbsp;
          <span className="font-playfair font-medium italic text-gray-900">
            travel experts
          </span>
        </h2>
        <div className={clsx('hidden items-center gap-2 md:hidden lg:flex')}>
          <button
            aria-disabled={!hasPrevious}
            aria-label="Previous authors"
            className={clsx(
              'size-8 flex-none rounded-full border bg-white stroke-gray-200 p-1',
              !hasPrevious && 'opacity-40',
            )}
            disabled={!hasPrevious}
            onClick={handlePrevious}
            type="button"
          >
            <FontAwesomeIcon icon={faChevronLeft} />
          </button>

          <button
            aria-disabled={!hasNext}
            aria-label="Next authors"
            className={clsx(
              'size-8 flex-none rounded-full border bg-white stroke-gray-200 p-1',
              !hasNext && 'opacity-40',
            )}
            disabled={!hasNext}
            onClick={handleNext}
            type="button"
          >
            <FontAwesomeIcon icon={faChevronRight} />
          </button>
        </div>
      </div>
      {/* Slider for Desktop */}
      <div
        className={clsx(
          'hidden lg:block lg:w-full xl:overflow-hidden',
          styles.slickWrapper,
          { [styles.sliderWrapper]: authors.length < ITEMS_PER_PAGE },
        )}
      >
        <Slider ref={sliderRef} {...sliderSettings}>
          {authors.map((author) => (
            <AuthorCard author={author} key={author.id} />
          ))}
        </Slider>
      </div>
      {/* Slider for Mobile */}
      <div className="-mr-4 flex flex-row gap-4 overflow-x-auto overflow-y-hidden lg:hidden">
        {authors.map((author) => (
          <AuthorCard author={author} key={author.id} />
        ))}
      </div>
    </div>
  );
}

export default MeetTheTeam;
