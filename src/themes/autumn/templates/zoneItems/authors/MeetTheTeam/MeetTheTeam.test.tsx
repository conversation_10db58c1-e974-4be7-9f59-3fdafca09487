/* eslint-disable @stylistic/max-len */
/* eslint-disable react/jsx-props-no-spreading */
import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper } from 'util/jest';

import MeetTheTeam from '.';

import type { AuthorsZoneItem } from 'types/ZoneItems';

describe('MeetTheTeam component', () => {
  const mockAuthors = [
    {
      bio: 'Travel writer and photographer with 10 years of experience exploring hidden gems around the world.',
      facebook: 'https://facebook.com/author1',
      id: 1,
      instagram: 'https://instagram.com/author1',
      linkedin: 'https://linkedin.com/in/author1',
      mugshot: '/images/authors/author1.jpg',
      name: '<PERSON>',
      position: 'Senior Travel Writer',
      tiktok: '',
      twitter: 'https://twitter.com/author1',
      web: 'https://sarahjohnson.com',
      youtube: '',
    },
    {
      bio: 'Adventure travel specialist focusing on sustainable tourism and cultural immersion.',
      facebook: '',
      id: 2,
      instagram: 'https://instagram.com/author2',
      linkedin: '',
      mugshot: '/images/authors/author2.jpg',
      name: 'Michael Chen',
      position: 'Adventure Travel Editor',
      tiktok: 'https://tiktok.com/@author2',
      twitter: '',
      web: '',
      youtube: 'https://youtube.com/author2',
    },
    {
      bio: 'Luxury travel expert with expertise in premium accommodations and fine dining.',
      facebook: 'https://facebook.com/author3',
      id: 3,
      instagram: '',
      linkedin: 'https://linkedin.com/in/author3',
      mugshot: '/images/authors/author3.jpg',
      name: 'Emma Rodriguez',
      position: 'Luxury Travel Correspondent',
      tiktok: '',
      twitter: 'https://twitter.com/author3',
      web: 'https://emmarodriguez.travel',
      youtube: '',
    },
    {
      bio: 'Budget travel enthusiast helping travelers explore the world without breaking the bank.',
      facebook: '',
      id: 4,
      instagram: 'https://instagram.com/author4',
      linkedin: '',
      mugshot: '/images/authors/author4.jpg',
      name: 'David Thompson',
      position: 'Budget Travel Writer',
      tiktok: '',
      twitter: 'https://twitter.com/author4',
      web: '',
      youtube: 'https://youtube.com/author4',
    },
    {
      bio: 'Family travel expert specializing in kid-friendly destinations and activities.',
      facebook: 'https://facebook.com/author5',
      id: 5,
      instagram: 'https://instagram.com/author5',
      linkedin: 'https://linkedin.com/in/author5',
      mugshot: '/images/authors/author5.jpg',
      name: 'Lisa Williams',
      position: 'Family Travel Editor',
      tiktok: 'https://tiktok.com/@author5',
      twitter: '',
      web: 'https://familytraveladventures.com',
      youtube: '',
    },
    {
      bio: 'Food and travel writer exploring culinary destinations across the globe.',
      facebook: '',
      id: 6,
      instagram: 'https://instagram.com/author6',
      linkedin: '',
      mugshot: '/images/authors/author6.jpg',
      name: 'James Park',
      position: 'Food & Travel Writer',
      tiktok: '',
      twitter: 'https://twitter.com/author6',
      web: 'https://culinaryjourney.com',
      youtube: 'https://youtube.com/author6',
    },
  ];

  it('renders with multiple authors', () => {
    expect.assertions(1);

    const props: AuthorsZoneItem = {
      elementId: 123,
      index: 0,
      order: 1,
      zoneItemData: {
        authors: mockAuthors,
        template: 'meet-the-team',
      },
      zoneItemId: 123,
      zoneItemType: ZoneItemType.Authors,
      zoneName: ZoneName.MAIN,
    };

    const store = createStore();

    const { container } = render(
      <TestWrapper store={store}>
        <MeetTheTeam {...props} />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
