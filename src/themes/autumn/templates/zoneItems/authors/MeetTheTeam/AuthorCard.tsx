import { useEffect, useMemo, useRef, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import AuthorImage from 'themes/autumn/components/storyElements/common/AuthorImage';
import { getAuthorBioHtml } from 'util/author/author';
import { htmlToText } from 'util/device';

import type { StoryAuthor } from 'types/Story';

function AuthorCard({ author }: { author: StoryAuthor }) {
  const authorBioRef = useRef<HTMLDivElement>(null);
  const [authorBioHeight, setAuthorBioHeight] = useState<number>(0);
  const { transformUrl } = useAppSelector((state) => state.settings);
  const authorBioText = useMemo(
    () => htmlToText(getAuthorBioHtml(author, true)),
    [author],
  );
  const authorPath = `/profile/${author.id}`;

  useEffect(() => {
    if (authorBioRef.current) {
      setAuthorBioHeight(authorBioRef.current.clientHeight);
    }
  }, [authorBioRef]);

  return (
    <div className="flex h-[424px] w-[292px] flex-col rounded-lg border-[0.36px] border-gray-200 shadow-md">
      <div className="h-[153px] w-[292px] rounded-t-lg">
        <Link href={authorPath} noStyle>
          <AuthorImage
            author={author}
            className="rounded-t-lg"
            height={153}
            transformUrl={transformUrl}
            useBannerImage
            width={292}
          />
        </Link>
      </div>
      <div className="flex w-full flex-col justify-start gap-4 p-4">
        <div className="flex flex-col justify-start gap-1">
          <h3
            className="font-inter text-base font-semibold text-gray-900 hover:underline"
            id={`author-${author.id}`}
          >
            <Link href={authorPath} noStyle>
              {author.name}
            </Link>
          </h3>
          <span className="font-inter text-xs font-normal text-gray-500 empty:h-3">
            {author.position}
          </span>
        </div>
        <div className="h-[90px] w-full">
          <div
            aria-labelledby={`author-${author.id}`}
            className="line-clamp-4 font-inter text-xs font-normal text-gray-600"
            dangerouslySetInnerHTML={{ __html: authorBioText }}
            ref={authorBioRef}
          />
          {(authorBioRef.current?.scrollHeight ?? 0) > authorBioHeight && (
            <Link
              className="font-inter text-xs font-normal text-gray-600 underline hover:text-gray-800"
              href={authorPath}
              noStyle
            >
              Read More
            </Link>
          )}
        </div>
        <div className="h-1 border-t-1 border-gray-300" />
        {author.latestStory && (
          <Link
            className="line-clamp-2 font-playfair text-sm/[26px] font-medium text-gray-900 hover:underline"
            href={author.latestStory.storyUrl}
            noStyle
          >
            {author.latestStory.title}
          </Link>
        )}
      </div>
    </div>
  );
}

export default AuthorCard;
