// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InfoWithCTA Component should render basic component correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="flex flex-col gap-7 bg-stone-200 px-4 py-6 font-inter md:flex-row md:items-center md:gap-6 md:p-8 lg:rounded-xl"
    >
      <div
        class="flex flex-1 flex-col gap-3"
      >
        <h2
          class="text-xs font-bold uppercase tracking-wider text-gray-900"
        >
          Still have questions?
        </h2>
        <div
          class="text-base font-normal leading-6 text-gray-900 lg:whitespace-pre-line"
        >
          Can't find the answer you're looking for? Feel free to contact our customer support team.
        </div>
      </div>
      <a
        class="flex w-full items-center justify-center gap-2 rounded bg-black px-4 py-3 text-sm font-medium text-white md:w-auto"
        href="https://austcommunitymedia.my.site.com/faq/s/contactsupport"
      >
        Contact Customer Support
      </a>
    </div>
  </div>
</DocumentFragment>
`;

exports[`InfoWithCTA Component should render description normally when no phone number is present 1`] = `
<DocumentFragment>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="flex flex-col gap-7 bg-stone-200 px-4 py-6 font-inter md:flex-row md:items-center md:gap-6 md:p-8 lg:rounded-xl"
    >
      <div
        class="flex flex-1 flex-col gap-3"
      >
        <h2
          class="text-xs font-bold uppercase tracking-wider text-gray-900"
        >
          Still have questions?
        </h2>
        <div
          class="text-base font-normal leading-6 text-gray-900 lg:whitespace-pre-line"
        >
          This is a normal description without any phone numbers
        </div>
      </div>
      <a
        class="flex w-full items-center justify-center gap-2 rounded bg-black px-4 py-3 text-sm font-medium text-white md:w-auto"
        href="https://austcommunitymedia.my.site.com/faq/s/contactsupport"
      >
        Contact Customer Support
      </a>
    </div>
  </div>
</DocumentFragment>
`;
