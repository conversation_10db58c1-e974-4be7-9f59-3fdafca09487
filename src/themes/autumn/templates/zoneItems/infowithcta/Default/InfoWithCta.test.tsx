import { render, screen } from '@testing-library/react';

import { createStore } from 'store/store';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { TestWrapper } from 'util/jest';

import InfoWithCTA from '.';

import type { InfoWithCTAZoneItem } from 'types/ZoneItems';

const defaultData = {
  buttonText: 'Contact Customer Support',
  description:
    // eslint-disable-next-line @stylistic/max-len
    "Can't find the answer you're looking for? Feel free to contact our customer support team.",
  template: 'Default',
  title: 'Still have questions?',
  url: 'https://austcommunitymedia.my.site.com/faq/s/contactsupport',
};

describe('InfoWithCTA Component', () => {
  const store = createStore();
  const createMockProps = (
    data: Partial<InfoWithCTAZoneItem['zoneItemData']> = {},
  ): InfoWithCTAZoneItem => ({
    editOnly: false,
    elementId: 123,
    index: 0,
    order: 1,
    zoneItemData: {
      ...defaultData,
      ...data,
    },
    zoneItemId: 456,
    zoneItemType: ZoneItemType.InfoWithCTA,
    zoneName: ZoneName.MAIN,
  });

  it('should render basic component correctly', () => {
    const props = createMockProps();
    const { asFragment } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <InfoWithCTA {...props} />
      </TestWrapper>,
    );
    expect(screen.getByText(defaultData.title)).toBeInTheDocument();
    expect(screen.getByText(defaultData.description)).toBeInTheDocument();

    const button = screen.getByRole('link', { name: defaultData.buttonText });
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('href', defaultData.url);
    expect(asFragment()).toMatchSnapshot();
  });

  // eslint-disable-next-line @stylistic/max-len
  it('should handle phone number in description correctly on mobile and desktop', () => {
    const props = createMockProps({
      description: 'Monday to Friday 9am to 5pm or call on 1300 131 095',
    });

    render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <InfoWithCTA {...props} />
      </TestWrapper>,
    );

    const mobilePhoneLink = screen.getByRole('link', { name: '1300 131 095' });
    expect(mobilePhoneLink).toBeInTheDocument();
    expect(mobilePhoneLink).toHaveAttribute('href', 'tel:+611300131095');
    expect(mobilePhoneLink).toHaveClass('lg:hidden');

    const allPhoneTexts = screen.getAllByText('1300 131 095');
    const desktopPhoneText = allPhoneTexts.find((el) => el.tagName === 'SPAN');
    expect(desktopPhoneText).toHaveClass('hidden', 'lg:inline');
  });

  it('should handle phone number with spaces correctly', () => {
    const props = createMockProps({
      description: 'Please contact 1300131095 today',
    });

    render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <InfoWithCTA {...props} />
      </TestWrapper>,
    );

    const phoneLink = screen.getByRole('link', { name: '1300 131 095' });
    expect(phoneLink).toHaveAttribute('href', 'tel:+611300131095');
  });

  // eslint-disable-next-line @stylistic/max-len
  it('should render description normally when no phone number is present', () => {
    const props = createMockProps({
      description: 'This is a normal description without any phone numbers',
    });

    const { asFragment } = render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <InfoWithCTA {...props} />
      </TestWrapper>,
    );

    expect(
      screen.getByText(
        'This is a normal description without any phone numbers',
      ),
    ).toBeInTheDocument();
    expect(screen.queryByText('1300 131 095')).not.toBeInTheDocument();
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render with custom styling classes', () => {
    const props = createMockProps();

    render(
      <TestWrapper store={store}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <InfoWithCTA {...props} />
      </TestWrapper>,
    );

    const container = screen
      .getByText(defaultData.title)
      .closest('div')?.parentElement;
    expect(container).toHaveClass('flex', 'flex-col', 'gap-7', 'bg-stone-200');

    const title = screen.getByText(defaultData.title);
    expect(title).toHaveClass(
      'text-xs',
      'font-bold',
      'uppercase',
      'tracking-wider',
      'text-gray-900',
    );

    const button = screen.getByRole('link', { name: defaultData.buttonText });
    expect(button).toHaveClass(
      'flex',
      'w-full',
      'items-center',
      'justify-center',
      'gap-2',
      'rounded',
      'bg-black',
    );
  });
});
