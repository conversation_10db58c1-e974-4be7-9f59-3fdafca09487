import clsx from 'clsx';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { isExternalLink } from 'util/page';

import type { InfoWithCTAZoneItem } from 'types/ZoneItems';

interface Props extends InfoWithCTAZoneItem {
  className?: string;
}

export default function InfoWithCTA({
  className = '',
  zoneItemData: { buttonText, description, title, url },
}: Props): React.ReactElement {
  const domain = useAppSelector((state) => state.conf.domain);
  const finalUrl = isExternalLink(url) ? url : `https://${domain}${url}`;
  // Split description to handle phone number separately
  const parts = description.split(/1300\s?131\s?095/);
  const hasPhoneNumber = parts.length > 1;

  return (
    <div
      className={clsx(
        'flex flex-col gap-7 bg-stone-200 px-4 py-6 font-inter md:flex-row md:items-center md:gap-6 md:p-8 lg:rounded-xl',
        className,
      )}
    >
      <div className="flex flex-1 flex-col gap-3">
        <h2 className="text-xs font-bold uppercase tracking-wider text-gray-900">
          {title}
        </h2>
        {hasPhoneNumber ? (
          <p className="text-base font-normal leading-6 text-gray-900 lg:whitespace-pre-line">
            {parts[0]}
            {/* Mobile: Clickable link */}
            <Link
              className="text-blue-600 underline hover:text-blue-800 lg:hidden"
              href="tel:+611300131095"
            >
              1300 131 095
            </Link>
            {/* Desktop: Regular text */}
            <span className="hidden lg:inline">1300 131 095</span>
            {parts[1]}
          </p>
        ) : (
          <div
            className="text-base font-normal leading-6 text-gray-900 lg:whitespace-pre-line"
            dangerouslySetInnerHTML={{ __html: description }}
          />
        )}
      </div>
      <Link
        className="flex w-full items-center justify-center gap-2 rounded bg-black px-4 py-3 text-sm font-medium text-white md:w-auto"
        href={finalUrl}
        noStyle
      >
        {buttonText}
      </Link>
    </div>
  );
}
