import type { HeadingZoneItem } from 'types/ZoneItems';

export default function ExplorePageNoLinkHeading({
  zoneItemData: { anchorId, heading, subtitle },
}: HeadingZoneItem): React.ReactElement {
  return (
    <div className="mx-auto font-inter md:w-[840px]">
      <h2
        className="justify-start text-2xl font-semibold !leading-[30px] text-gray-900"
        id={anchorId}
      >
        {heading}
        {subtitle && (
          <div className="text-base font-normal leading-6 text-gray-600">
            {subtitle}
          </div>
        )}
      </h2>
    </div>
  );
}
