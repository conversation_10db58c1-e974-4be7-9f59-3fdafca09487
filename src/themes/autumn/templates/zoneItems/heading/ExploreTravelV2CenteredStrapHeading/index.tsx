import type { HeadingZoneItem } from 'types/ZoneItems';

export default function ExplorePageNoLinkHeading({
  zoneItemData: { anchorId, heading, subtitle },
}: HeadingZoneItem): React.ReactElement {
  return (
    <div className="container mx-auto font-inter lg:max-w-[840px]">
      <h1
        className="mx-auto mt-6 max-w-screen-md flex-wrap justify-center whitespace-nowrap text-wrap text-[28px] font-semibold leading-tight text-gray-900 md:mt-0 md:text-center md:text-4xl"
        id={anchorId}
      >
        {heading}
      </h1>
      {subtitle && (
        <div className="mx-auto max-w-screen-md flex-wrap pt-4 font-inter text-sm font-normal text-gray-800 md:text-center">
          {subtitle}
        </div>
      )}
    </div>
  );
}
