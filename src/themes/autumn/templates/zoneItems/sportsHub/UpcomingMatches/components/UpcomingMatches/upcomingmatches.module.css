.slickWrapper {
  :global {
    .slick-track {
      display: flex;
    }
    .slick-track ul li {
      list-style-type: disc;
      padding-top: 8px;
      padding-bottom: 8px;
    }
    .slick-track ul {
      height: 150px;
    }
    .slick-track .slick-slide > div {
      margin-left: 4px;
      margin-right: 4px;
    }
    @media only screen and (max-width: 360px) {
      .slick-track .slick-slide > div {
        width: 300px;
      }
    }
    @media only screen and (min-width: 361px) and (max-width: 389px) {
      .slick-track .slick-slide > div {
        width: 318px;
      }
    }
    @media only screen and (min-width: 390px) and (max-width: 410px) {
      .slick-track .slick-slide > div {
        width: 330px;
      }
    }
    @media only screen and (min-width: 411px) and (max-width: 444px) {
      .slick-track .slick-slide > div {
        width: 378px;
      }
    }
    @media only screen and (min-width: 445px) and (max-width: 1024px) {
      .slick-track .slick-slide > div {
        width: 290px;
      }
    }
    @media only screen and (min-width: 1025px) and (max-width: 1199px) {
      .slick-track .slick-slide > div {
        width: 300px;
      }
    }
    @media only screen and (min-width: 1200px) {
      .slick-track .slick-slide > div {
        width: 328px;
      }
      .story .slick-track .slick-slide > div {
        width: 355px;
      }
      .takeover-ad .slick-track .slick-slide > div {
        width: 315px;
      }
    }
  }
}
