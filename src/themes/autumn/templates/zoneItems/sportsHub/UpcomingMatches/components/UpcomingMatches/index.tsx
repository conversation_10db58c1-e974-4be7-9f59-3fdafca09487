'use client';

import {
  faChevronLeft,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';
import Slider from 'react-slick';

import { useAppSelector } from 'store/hooks';
import { GameInsight } from 'themes/autumn/components/SportsHub/Component/GameInsight';
import { GameInsightColorPalette } from 'themes/autumn/components/SportsHub/Component/GameInsight/enums';
import MatchWidget from 'themes/autumn/components/SportsHub/Match/widget';
import {
  getCurrentPeriod,
  getMatchSeason,
  getSportPageByCompLevelId,
} from 'themes/autumn/components/SportsHub/utils';
import Sponsor from 'themes/autumn/components/generic/Sponsor';
import { Competition, MatchStatus, SportPage } from 'types/SportsHub';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { ResponsiveType } from 'util/device';
import { sendToGtm, setGtmDataLayer } from 'util/gtm';
import { useResponsiveTypeFromWidth } from 'util/hooks';
import { fetchCurrentRoundSportMatches } from 'util/organization/suzuka';
import { useDate } from 'util/time';

import styles from './upcomingmatches.module.css';

import type { MatchWidgetProps } from 'types/SportsHub';
import type { CurrentRoundSportMatchesResponse } from 'util/organization/suzuka';

const NAV_BACKGROUND = (
  <svg fill="none" height="33" viewBox="0 0 43 43" width="33">
    <g filter="url(#filter0_d_2427_10098)">
      <circle cx="21.5" cy="20.5" fill="white" r="16.5" />
      <circle cx="21.5" cy="20.5" r="16" stroke="#D1D5DB" />
    </g>
    <defs>
      <filter
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
        height="43"
        id="filter0_d_2427_10098"
        width="43"
        x="0"
        y="0"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="2.5" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.11 0"
        />
        <feBlend
          in2="BackgroundImageFix"
          mode="normal"
          result="effect1_dropShadow_2427_10098"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_2427_10098"
          mode="normal"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

interface UpcomingMatchesProps {
  sportPages?: SportPage[];
}

function UpcomingMatches({
  sportPages = [SportPage.AFL, SportPage.NRL, SportPage.CRICKET],
}: UpcomingMatchesProps): React.ReactElement | null {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [initialized, setInitialized] = useState(false);
  const [futureMatches, setFutureMatches] = useState<MatchWidgetProps[]>([]);
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const sliderRef = useRef<Slider>(null);
  const now = useDate();
  const responsiveType = useResponsiveTypeFromWidth();
  const slidesToScroll =
    responsiveType === ResponsiveType.TABLET_NARROW ? 3 : 1;
  const sportsHubSponsor = useAppSelector(
    (state) => state.features.sportsHubSponsor,
  );

  const resizeSlider = () => {
    let currentSlider = document.querySelector('.slick-list .slick-current');
    if (currentSlider) {
      let innerHeight = currentSlider.clientHeight;
      if (responsiveType === ResponsiveType.TABLET_NARROW) {
        for (let i = 0; i < slidesToScroll; i++) {
          currentSlider = currentSlider.nextSibling as Element;
          if (!currentSlider) {
            break;
          }
          innerHeight = Math.max(innerHeight, currentSlider.clientHeight);
        }
      }
      if (innerHeight) {
        (
          document.querySelector('.slick-list') as HTMLDivElement
        ).style.height = `${innerHeight + 5}px`;
      }
    }
  };

  const fetchFutureMatches = useCallback(() => {
    const promises: Promise<CurrentRoundSportMatchesResponse>[] = [];
    const sportMatches: MatchWidgetProps[] = [];
    sportPages.forEach((sportPage) => {
      const competition = Competition[sportPage];
      const matchSeason = getMatchSeason(sportPage, now).name;
      promises.push(
        fetchCurrentRoundSportMatches({
          allFinals: true,
          compLevelId: competition.compLevelId,
          matchSeason,
          matchStatusIn: [
            MatchStatus.MATCH_STATUS_SCHEDULED,
            MatchStatus.MATCH_STATUS_PREMATCH,
          ],
        }),
      );
    });
    Promise.all(promises)
      .then((res) => {
        res.forEach((sportMatchesResponse) => {
          const matches = sportMatchesResponse.results?.matches;
          if (matches) {
            matches.forEach((sportMatchResponse) => {
              const compLevelId = sportMatchResponse.match?.comp.compLevelId;
              if (compLevelId) {
                const sportPage = getSportPageByCompLevelId(compLevelId);
                if (sportPage) {
                  const sportMatch = {
                    ...sportMatchResponse.data,
                    awaySquadPosition:
                      sportMatchResponse.ladderPositions?.awaySquad,
                    extra: sportMatchResponse.match?.extra,
                    homeSquadPosition:
                      sportMatchResponse.ladderPositions?.homeSquad,
                    id: sportMatchResponse.id,
                    sportPage,
                  };
                  sportMatches.push(sportMatch);
                }
              }
            });
          }
        });
        if (sportMatches.length > 0) {
          sportMatches.sort(
            (a, b) =>
              new Date(a.utcStartTime).getTime() -
              new Date(b.utcStartTime).getTime(),
          );
        }
        setFutureMatches(sportMatches);
        if (sportMatches.length) {
          sendToGtm({
            action: 'impression',
            label: 'upcoming_matches_widget_impressions',
            trigger: 'upcoming_matches_widget_impressions_trigger',
          });
          if (sportsHubSponsor.enabled && sportsHubSponsor.data) {
            const sponsorData =
              sportsHubSponsor.data.sponsorData[
                sportMatches[0].sportPage ?? ''
              ];
            if (sponsorData) {
              setGtmDataLayer({
                data: {
                  action: 'impression',
                  section: 'upcoming_matches',
                  type: sponsorData.name.toLowerCase(),
                },
                event: 'widget_sponsorship',
              });
            }
          }
        }
      })
      .catch(console.error);
  }, [sportPages, now, sportsHubSponsor]);

  useEffect(() => {
    if (!initialized) {
      setInitialized(true);
      fetchFutureMatches();
      setTimeout(() => {
        setTimeout(() => resizeSlider(), 50);
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialized]);

  const handlePrevClick = () => {
    if (sliderRef.current) {
      sendToGtm({
        action: `page_num_${currentSlide - 1}`,
        label: 'upcoming_matches_widget_left_click',
        trigger: 'upcoming_matches_widget_nav_click_trigger',
        value: 'upcoming_matches_widget_left_click',
      });
      sliderRef.current.slickPrev();
    }
  };

  const handleNextClick = () => {
    if (sliderRef.current) {
      sendToGtm({
        action: `page_num_${currentSlide + 1}`,
        label: 'upcoming_matches_widget_right_click',
        trigger: 'upcoming_matches_widget_nav_click_trigger',
        value: 'upcoming_matches_widget_right_click',
      });
      sliderRef.current.slickNext();
    }
  };

  const onClickWidget = (event: React.MouseEvent<HTMLAnchorElement>) => {
    sendToGtm({
      action: event.currentTarget.href,
      label: 'upcoming_matches_widget_click',
      trigger: 'upcoming_matches_widget_click_trigger',
    });
  };

  const settings = {
    afterChange: (index: number) => {
      setTimeout(() => resizeSlider(), 50);
      if (sportsHubSponsor.enabled && sportsHubSponsor.data) {
        const sponsorData =
          sportsHubSponsor.data.sponsorData[
            futureMatches[index].sportPage ?? ''
          ];
        if (sponsorData) {
          setGtmDataLayer({
            data: {
              action: 'impression',
              section: 'upcoming_matches',
              type: sponsorData.name.toLowerCase(),
            },
            event: 'widget_sponsorship',
          });
        }
      }
    },
    arrows: false,
    beforeChange: (_: number, next: number) => setCurrentSlide(next),
    dots: false,
    easing: 'easeInOut',
    infinite: false,
    responsive: [
      {
        breakpoint: 981,
        settings: {
          slidesToScroll: 3,
        },
      },
      {
        breakpoint: 767,
        settings: {
          slidesToScroll: 1,
        },
      },
    ],
    slidesToScroll: 1,
    slidesToShow: 1,
    speed: 300,
    variableWidth: true,
  };

  const numItems = futureMatches.length;
  const numSlidesToShow = settings.slidesToShow;
  const showPrevArrow = currentSlide > 0;
  const showNextArrow = currentSlide + numSlidesToShow < numItems;

  if (numItems <= 0) {
    return null;
  }

  return (
    <div className="flex flex-col">
      <div className="grid place-content-between text-2xl font-extrabold text-slate-750 lg:grid-cols-3 lg:text-xl xl:grid-cols-4 xl:text-2xl">
        <h2 className="pb-4 lg:col-span-2 xl:col-span-3">
          Upcoming matches
          <FontAwesomeIcon
            className="ml-2 text-lg lg:hidden"
            icon={faChevronRight}
          />
        </h2>
        {sliderRef && (
          <div
            className={clsx(
              'hidden text-right text-xs font-medium lg:block',
              {
                'mx-2': !hasTakeoverAd,
              },
              {
                block: initialized,
                hidden: !initialized,
              },
            )}
          >
            <button
              className="cursor-pointer"
              disabled={!showPrevArrow}
              onClick={handlePrevClick}
              type="button"
            >
              {NAV_BACKGROUND}
              <FontAwesomeIcon
                className={clsx('relative -top-6 text-sm', {
                  'text-gray-300': !showPrevArrow,
                })}
                icon={faChevronLeft}
              />
            </button>

            <button
              className="cursor-pointer"
              disabled={!showNextArrow}
              onClick={handleNextClick}
              type="button"
            >
              {NAV_BACKGROUND}
              <FontAwesomeIcon
                className={clsx('relative -top-6 text-sm', {
                  'text-gray-300': !showNextArrow,
                })}
                icon={faChevronRight}
              />
            </button>
          </div>
        )}
      </div>
      <div
        className={clsx({
          flex: initialized,
          hidden: !initialized,
        })}
      >
        <div className={clsx('mb-4 w-full', styles.slickWrapper)}>
          <Slider
            className={clsx('flex flex-row', viewType, {
              'takeover-ad': hasTakeoverAd,
            })}
            ref={sliderRef}
            /* eslint-disable-next-line react/jsx-props-no-spreading */
            {...settings}
          >
            {futureMatches.map(
              (match) =>
                match.sportPage && (
                  <div
                    className="rounded-md border-1 border-gray-300 hover:border-green-550"
                    key={match.utcStartTime}
                  >
                    <MatchWidget
                      awaySquadId={match.awaySquadId}
                      awaySquadName={match.awaySquadName}
                      awaySquadNickname={match.awaySquadNickname}
                      awaySquadPosition={match.awaySquadPosition}
                      awaySquadScore={match.awaySquadScore}
                      borderClassName="border-0"
                      compName={
                        match.sportPage === SportPage.CRICKET
                          ? match.compName
                          : undefined
                      }
                      currentPeriod={getCurrentPeriod(
                        Competition[match.sportPage].sport,
                        match.periodCompleted,
                      )}
                      homeSquadId={match.homeSquadId}
                      homeSquadName={match.homeSquadName}
                      homeSquadNickname={match.homeSquadNickname}
                      homeSquadPosition={match.homeSquadPosition}
                      homeSquadScore={match.homeSquadScore}
                      matchId={match.id}
                      matchName={match.matchName}
                      matchStatus={match.matchStatus}
                      onClickWidget={(event) => {
                        onClickWidget(event);
                      }}
                      periodCompleted={match.periodCompleted}
                      periodSeconds={match.periodSeconds}
                      showDisplayBoard={false}
                      showVenue={false}
                      sportPage={match.sportPage}
                      utcStartTime={match.utcStartTime}
                      venueName={match.venueName}
                    />
                    {match.extra && (
                      <div className="border-t-1 border-gray-300 p-4 pt-0 lg:p-2 xl:p-4">
                        <GameInsight
                          awaySquadName={match.awaySquadNickname}
                          colorPalette={GameInsightColorPalette.PALETTE_GREEN}
                          enableReadMore
                          extra={match.extra}
                          homeSquadName={match.homeSquadNickname}
                          listClassName="list-decimal text-sm text-slate-750"
                          matchId={match.id}
                          showHeading={false}
                          sportPage={match.sportPage}
                        />
                      </div>
                    )}
                    <Sponsor
                      gameType={match.sportPage}
                      section="upcoming_matches"
                    />
                  </div>
                ),
            )}
          </Slider>
        </div>
      </div>
    </div>
  );
}

export default UpcomingMatches;
