import { faCamera } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import React from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import Link from 'themes/autumn/components/generic/Link';

import type { ImageZoneItem } from 'types/ZoneItems';

function Default({
  zoneItemData: {
    alt,
    desktopImage,
    mobileImage,
    openNewWindow,
    tabletImage,
    title,
    url,
  },
}: ImageZoneItem): React.ReactElement {
  const description = alt || title;
  return (
    <div>
      <Link
        className={clsx('flex items-center gap-x-3', {
          'pointer-events-none': url === '',
        })}
        href={url}
        noStyle
        target={openNewWindow ? '_blank' : undefined}
      >
        <div className="overflow-hidden rounded-md">
          <picture>
            <source media="(min-width: 835px)" srcSet={desktopImage} />
            {tabletImage && (
              <source
                media="(min-width: 768px) and (max-width: 834px)"
                srcSet={tabletImage}
              />
            )}
            {mobileImage && (
              <source media="(max-width: 767px)" srcSet={mobileImage} />
            )}
            <img alt={alt} src={desktopImage} title={title} />
          </picture>
        </div>
      </Link>
      {description && (
        <div className="pt-3 text-left font-inter text-sm font-normal leading-5 text-gray-500">
          <FontAwesomeIcon className="mr-2" icon={faCamera} />
          {description}
        </div>
      )}
    </div>
  );
}

export default Default;
