// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Pill Component should have proper ARIA attributes 1`] = `
<DocumentFragment>
  <button
    aria-controls="Adventure-stories"
    aria-selected="false"
    class="flex items-center justify-center px-3 py-2 rounded-full border border-solid border-gray-300 opacity-100 transition duration-300 ease-out hover:opacity-100 focus:opacity-100"
    role="tab"
    type="button"
  >
    <span
      class="font-inter text-xs font-medium leading-[14px] whitespace-nowrap text-left text-gray-900"
    >
      Adventure
    </span>
  </button>
</DocumentFragment>
`;

exports[`Pill Component should render correctly when active 1`] = `
<DocumentFragment>
  <button
    aria-controls="Adventure-stories"
    aria-selected="true"
    class="flex items-center justify-center px-3 py-2 rounded-full border border-solid border-gray-900 opacity-100 transition duration-300 ease-out hover:opacity-100 focus:opacity-100 custom-class"
    disabled=""
    role="tab"
    type="button"
  >
    <span
      class="font-inter text-xs font-medium leading-[14px] whitespace-nowrap text-left text-gray-900"
    >
      Adventure
    </span>
  </button>
</DocumentFragment>
`;

exports[`Pill Component should render correctly when not active 1`] = `
<DocumentFragment>
  <button
    aria-controls="Adventure-stories"
    aria-selected="false"
    class="flex items-center justify-center px-3 py-2 rounded-full border border-solid border-gray-300 opacity-100 transition duration-300 ease-out hover:opacity-100 focus:opacity-100"
    role="tab"
    type="button"
  >
    <span
      class="font-inter text-xs font-medium leading-[14px] whitespace-nowrap text-left text-gray-900"
    >
      Adventure
    </span>
  </button>
</DocumentFragment>
`;
