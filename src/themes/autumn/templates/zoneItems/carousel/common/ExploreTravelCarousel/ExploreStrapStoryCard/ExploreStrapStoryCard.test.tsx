/* eslint-disable jest/no-conditional-expect */
import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { TestWrapper, genMockStories } from 'util/jest';

import { CardType } from '../types';

import ExploreStrapStoryCard from '.';

describe('ExploreStrapStoryCard Component', () => {
  const store = createStore((state) => ({
    ...state,
    accessToken: 'test_token',
    conf: {
      ...state.conf,
      mode: RenderMode.NORMAL,
    },
  }));

  it('should render story with correct elements and ARIA attributes', () => {
    const mockStories = genMockStories({ length: 1 });
    const story = mockStories[0];

    const { asFragment, getByLabelText, getByText, getByTitle } = render(
      <TestWrapper store={store}>
        <ExploreStrapStoryCard
          containerClassName="custom-container"
          story={story}
          storyListId={123}
          type={CardType.CONTENT}
        />
      </TestWrapper>,
    );

    // Verify snapshot of the rendered component
    expect(asFragment()).toMatchSnapshot();

    // Check image
    const image = getByTitle(`${story.leadImage?.description}`);
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src');

    // Check title
    const title = getByText(`${story.title}`);
    expect(title).toBeInTheDocument();

    // Check summary
    if (story.summary) {
      const summary = getByText(`${story.summary}`);
      expect(summary).toBeInTheDocument();
    }

    // Check author
    if (story.byline) {
      const author = getByLabelText(`Author for ${story.title}`);
      expect(author).toBeInTheDocument();
    }
  });

  it('should call onClick when any clickable element is clicked', () => {
    const mockStories = genMockStories({ length: 1 });
    const story = mockStories[0];

    const { getByLabelText, getByText, getByTitle } = render(
      <TestWrapper store={store}>
        <ExploreStrapStoryCard
          containerClassName="custom-container"
          story={story}
          storyListId={123}
          type={CardType.CONTENT}
        />
      </TestWrapper>,
    );
    // Click title
    const title = getByText(`${story.title}`);
    expect(title).toBeInTheDocument();
    expect(title.tagName).toBe('H3');

    // Click image
    const image = getByTitle(`${story.leadImage?.description}`);
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src');
    expect(image).toHaveAttribute('alt', `${story.leadImage?.description}`);

    // Click summary
    if (story.summary) {
      const summary = getByText(`${story.summary}`);
      expect(summary).toBeInTheDocument();
      expect(summary).toHaveAttribute('href', '/story/0/?cs=7');
    }

    // Click author
    if (story.byline) {
      const author = getByLabelText(`Author for ${story.title}`);
      expect(author).toBeInTheDocument();
      expect(author).toHaveAttribute('href', '/story/0/?cs=7');
    }
  });
});
