import clsx from 'clsx';
import React from 'react';

import { ImagePosition } from 'types/ZoneItems';

import ExploreStrapStoryCard from '../ExploreStrapStoryCard';
import { type CardType, type StoryOrNull } from '../types';

import type { ColorPalette } from 'themes/autumn/components/stories/Signpost/constants';

interface MobileExploreTravelListProps {
  getCardType: (story: StoryOrNull) => CardType;
  hasMore: boolean;
  onSeeMore: () => Promise<void>;
  showDivider?: boolean;
  showPills?: boolean;
  signPostClassName?: string;
  signPostColorPalette?: ColorPalette;
  stories: StoryOrNull[];
  storyListId: number;
}

export default function MobileExploreTravelList({
  getCardType,
  hasMore,
  onSeeMore,
  showDivider = false,
  showPills,
  signPostClassName,
  signPostColorPalette,
  stories,
  storyListId,
}: MobileExploreTravelListProps): React.ReactElement {
  const [heroStory, ...remainingStories] = stories;
  return (
    <div className="flex flex-col gap-7">
      {!showPills && (
        <div className={clsx({ 'divide-y divide-gray-300': showDivider })}>
          {stories.map((story, index) => (
            <ExploreStrapStoryCard
              containerClassName="pb-7 w-full"
              imageHeight={262}
              imageWidth={393}
              key={story?.id ?? `no-story-${index}`}
              leadingImageClassName="mb-2"
              signPostClassName={signPostClassName}
              signPostColorPalette={signPostColorPalette}
              story={story || undefined}
              storyListId={storyListId}
              type={getCardType(story)}
            />
          ))}
        </div>
      )}
      {showPills && (
        <div className="divide-y divide-gray-300">
          <div className="pb-5">
            <ExploreStrapStoryCard
              containerClassName="pb-0 w-full"
              imageHeight={262}
              imageWidth={393}
              signPostClassName={clsx('mt-3', signPostClassName)}
              signPostColorPalette={signPostColorPalette}
              story={heroStory || undefined}
              storyListId={storyListId}
              type={getCardType(heroStory)}
            />
          </div>
          {remainingStories.map((story, index) => (
            <ExploreStrapStoryCard
              containerClassName="pb-5 pt-5"
              enforceSummary={false}
              imageHeight={89}
              imagePosition={ImagePosition.RIGHT_BEGIN}
              imageWidth={120}
              imageWrapperClassName="h-full items-start"
              key={story?.id ?? `no-story-${index}`}
              leadingImageClassName="h-[89px] w-[120px]"
              showSummaryInStoryList={false}
              signPostClassName={signPostClassName}
              signPostColorPalette={signPostColorPalette}
              sourceClassName="text-xs"
              story={story || undefined}
              storyListId={storyListId}
              titleSize="text-base pb-0"
              type={getCardType(story)}
            />
          ))}
        </div>
      )}

      {hasMore && (
        <button
          aria-label="Load more stories"
          className="mx-auto h-12 w-64 rounded-lg border border-gray-300 px-6 py-3 font-inter text-base font-medium text-gray-700 shadow-sm"
          onClick={onSeeMore}
          type="button"
        >
          See more
        </button>
      )}
    </div>
  );
}
