// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ExploreStrapStoryCard Component should render story with correct elements and ARIA attributes 1`] = `
<DocumentFragment>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="group text-gray-900 custom-container"
      id="-story-100000"
      role="article"
    >
      <div
        class="flex flex-col md:w-72"
        data-testid="headline"
      >
        <div
          class="flex w-full min-w-0 flex-col pr-4"
        />
        <div
          class="flex flex-col-reverse w-full"
        >
          <a
            class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
            href="/story/0/?cs=7"
          >
            <h3>
              test title 0
            </h3>
          </a>
          <div
            class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
          >
            <a
              class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full xl:w-72 lg:min-w-[231px]"
              href="/story/0/?cs=7"
            >
              <picture>
                <source
                  srcset="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w288_h192_fmax.webp 1x, transform/v1/crop/frm/10000.jpg/r0_33_800_566_w576_h384_fmax.webp 2x"
                  type="image/webp"
                />
                <img
                  alt="test description 0"
                  class="mx-auto w-full"
                  loading="lazy"
                  src="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w288_h192_fmax.jpg"
                  srcset="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w576_h384_fmax.jpg 2x"
                  style="height: auto;"
                  title="test description 0"
                />
              </picture>
            </a>
          </div>
        </div>
        <a
          class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
          href="/story/0/?cs=7"
        />
        <div
          class="flex w-full mt-3 flex-col"
        >
          <div
            class="flex font-inter text-xs text-sm font-medium text-gray-500"
          >
            <span
              data-testid="headline-author"
            >
              <a
                aria-label="Author for test title 0"
                class="mt-3"
                href="/story/0/?cs=7"
              >
                Callum Godde
              </a>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
