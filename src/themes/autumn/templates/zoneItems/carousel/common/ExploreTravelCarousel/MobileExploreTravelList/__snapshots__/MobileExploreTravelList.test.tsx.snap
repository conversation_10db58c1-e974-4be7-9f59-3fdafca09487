// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MobileExploreTravelList Component should handle cases with fewer than 4 stories 1`] = `
<DocumentFragment>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="flex flex-col gap-7"
    >
      <div
        class=""
      >
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100000"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/0/?cs=7"
              >
                <h3>
                  test title 0
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/0/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10000.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 0"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 0"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/0/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 0"
                    class="mt-3"
                    href="/story/0/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100001"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/1/?cs=7"
              >
                <h3>
                  test title 1
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/1/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10001.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10001.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 1"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10001.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10001.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 1"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/1/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 1"
                    class="mt-3"
                    href="/story/1/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100002"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/2/?cs=7"
              >
                <h3>
                  test title 2
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/2/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10002.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10002.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 2"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10002.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10002.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 2"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/2/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 2"
                    class="mt-3"
                    href="/story/2/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MobileExploreTravelList Component should load additional stories when See More is clicked 1`] = `
<DocumentFragment>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="flex flex-col gap-7"
    >
      <div
        class=""
      >
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100000"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/0/?cs=7"
              >
                <h3>
                  test title 0
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/0/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10000.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 0"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 0"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/0/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 0"
                    class="mt-3"
                    href="/story/0/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100001"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/1/?cs=7"
              >
                <h3>
                  test title 1
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/1/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10001.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10001.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 1"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10001.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10001.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 1"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/1/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 1"
                    class="mt-3"
                    href="/story/1/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100002"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/2/?cs=7"
              >
                <h3>
                  test title 2
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/2/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10002.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10002.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 2"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10002.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10002.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 2"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/2/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 2"
                    class="mt-3"
                    href="/story/2/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100003"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/3/?cs=7"
              >
                <h3>
                  test title 3
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/3/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10003.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10003.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 3"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10003.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10003.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 3"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/3/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 3"
                    class="mt-3"
                    href="/story/3/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100004"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/4/?cs=7"
              >
                <h3>
                  test title 4
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/4/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10004.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10004.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 4"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10004.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10004.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 4"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/4/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 4"
                    class="mt-3"
                    href="/story/4/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100005"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/5/?cs=7"
              >
                <h3>
                  test title 5
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/5/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10005.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10005.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 5"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10005.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10005.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 5"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/5/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 5"
                    class="mt-3"
                    href="/story/5/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MobileExploreTravelList Component should render the initial 4 stories and the See More button 1`] = `
<DocumentFragment>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="flex flex-col gap-7"
    >
      <div
        class=""
      >
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100000"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/0/?cs=7"
              >
                <h3>
                  test title 0
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/0/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10000.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 0"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10000.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 0"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/0/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 0"
                    class="mt-3"
                    href="/story/0/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100001"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/1/?cs=7"
              >
                <h3>
                  test title 1
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/1/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10001.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10001.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 1"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10001.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10001.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 1"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/1/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 1"
                    class="mt-3"
                    href="/story/1/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100002"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/2/?cs=7"
              >
                <h3>
                  test title 2
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/2/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10002.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10002.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 2"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10002.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10002.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 2"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/2/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 2"
                    class="mt-3"
                    href="/story/2/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="group text-gray-900 pb-7 w-full"
          id="-story-100003"
          role="article"
        >
          <div
            class="flex flex-col md:w-72"
            data-testid="headline"
          >
            <div
              class="flex w-full min-w-0 flex-col pr-4"
            />
            <div
              class="flex flex-col-reverse w-full"
            >
              <a
                class="break-words text-gray-900 visited:text-gray-500 font-merriweather font-bold line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline text-xl"
                href="/story/3/?cs=7"
              >
                <h3>
                  test title 3
                </h3>
              </a>
              <div
                class="flex shrink-0 flex-col items-center justify-center !mb-0 mb-3"
              >
                <a
                  class="relative float-left w-full max-w-full overflow-hidden rounded-md !rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full mb-2"
                  href="/story/3/?cs=7"
                >
                  <picture>
                    <source
                      srcset="transform/v1/crop/frm/10003.jpg/r0_33_800_566_w393_h262_fmax.webp 1x, transform/v1/crop/frm/10003.jpg/r0_33_800_566_w786_h524_fmax.webp 2x"
                      type="image/webp"
                    />
                    <img
                      alt="test description 3"
                      class="mx-auto w-full"
                      loading="lazy"
                      src="transform/v1/crop/frm/10003.jpg/r0_33_800_566_w393_h262_fmax.jpg"
                      srcset="transform/v1/crop/frm/10003.jpg/r0_33_800_566_w786_h524_fmax.jpg 2x"
                      style="height: auto;"
                      title="test description 3"
                    />
                  </picture>
                </a>
              </div>
            </div>
            <a
              class="self-start font-normal text-gray-600 font-merriweather font-bold mt-3 text-sm !font-inter !line-clamp-2 line-clamp-3"
              href="/story/3/?cs=7"
            />
            <div
              class="flex w-full mt-3 flex-col"
            >
              <div
                class="flex font-inter text-xs text-sm font-medium text-gray-500"
              >
                <span
                  data-testid="headline-author"
                >
                  <a
                    aria-label="Author for test title 3"
                    class="mt-3"
                    href="/story/3/?cs=7"
                  >
                    Callum Godde
                  </a>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-label="Load more stories"
        class="mx-auto h-12 w-64 rounded-lg border border-gray-300 px-6 py-3 font-inter text-base font-medium text-gray-700 shadow-sm"
        type="button"
      >
        See more
      </button>
    </div>
  </div>
</DocumentFragment>
`;
