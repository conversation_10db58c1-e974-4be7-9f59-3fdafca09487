import { fireEvent, render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { TestWrapper, genMockStories } from 'util/jest';

import { CardType } from '../types';

import MobileExploreTravelList from '.';

describe('MobileExploreTravelList Component', () => {
  const mockOnSeeMore = jest.fn();
  const store = createStore((state) => ({
    ...state,
    accessToken: 'test_token',
    conf: {
      ...state.conf,
      mode: RenderMode.NORMAL,
    },
  }));

  it('should render the initial 4 stories and the See More button', () => {
    const mockStories = genMockStories({ length: 6 });

    const { asFragment, getAllByRole, getByRole } = render(
      <TestWrapper store={store}>
        <MobileExploreTravelList
          getCardType={() => CardType.CONTENT}
          hasMore
          onSeeMore={mockOnSeeMore}
          stories={mockStories.slice(0, 4)}
          storyListId={123}
        />
      </TestWrapper>,
    );

    // Verify snapshot of the initial render
    expect(asFragment()).toMatchSnapshot();

    // Check that 4 stories are rendered
    const articles = getAllByRole('article');
    expect(articles).toHaveLength(4);

    // Check that the See More button is displayed
    const seeMoreButton = getByRole('button', { name: /load more stories/i });
    expect(seeMoreButton).toBeInTheDocument();
  });

  it('should load additional stories when See More is clicked', () => {
    const mockStories = genMockStories({ length: 6 });

    const { asFragment, getAllByRole, getByRole, queryByRole, rerender } =
      render(
        <TestWrapper store={store}>
          <MobileExploreTravelList
            getCardType={() => CardType.CONTENT}
            hasMore
            onSeeMore={mockOnSeeMore}
            stories={mockStories.slice(0, 4)}
            storyListId={123}
          />
        </TestWrapper>,
      );

    // Simulate clicking the See More button
    const seeMoreButton = getByRole('button', { name: /load more stories/i });
    fireEvent.click(seeMoreButton);

    // Re-render with all stories (6 in total)
    rerender(
      <TestWrapper store={store}>
        <MobileExploreTravelList
          getCardType={() => CardType.CONTENT}
          hasMore={false} // All stories are now loaded
          onSeeMore={mockOnSeeMore}
          stories={mockStories} // Pass all 6 stories
          storyListId={123}
        />
      </TestWrapper>,
    );

    // Verify snapshot after loading additional stories
    expect(asFragment()).toMatchSnapshot();

    // Check that all 6 stories are rendered
    const articles = getAllByRole('article');
    expect(articles).toHaveLength(6);

    // Check that the See More button is no longer displayed
    const noMoreButton = queryByRole('button', { name: /load more stories/i });
    expect(noMoreButton).not.toBeInTheDocument();
  });

  it('should handle cases with fewer than 4 stories', () => {
    const mockStories = genMockStories({ length: 3 });

    const { asFragment, getAllByRole, queryByRole } = render(
      <TestWrapper store={store}>
        <MobileExploreTravelList
          getCardType={() => CardType.CONTENT}
          hasMore={false}
          onSeeMore={mockOnSeeMore}
          stories={mockStories} // Pass all 3 stories
          storyListId={123}
        />
      </TestWrapper>,
    );

    // Verify snapshot for fewer than 4 stories
    expect(asFragment()).toMatchSnapshot();

    // Check that all 3 stories are rendered
    const articles = getAllByRole('article');
    expect(articles).toHaveLength(3);

    // Check that the See More button is not displayed
    const noMoreButton = queryByRole('button', { name: /load more stories/i });
    expect(noMoreButton).not.toBeInTheDocument();
  });
});
