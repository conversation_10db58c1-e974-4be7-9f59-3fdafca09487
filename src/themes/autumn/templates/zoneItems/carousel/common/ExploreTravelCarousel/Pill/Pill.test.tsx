import { fireEvent, render } from '@testing-library/react';

import Pill from '.';

describe('Pill Component', () => {
  const mockOnSelect = jest.fn();

  it('should render correctly when active', () => {
    const { asFragment, getByRole } = render(
      <Pill
        isSelected
        name="Adventure"
        onSelect={mockOnSelect}
        pillClassName="custom-class"
      />,
    );

    const pill = getByRole('tab', { name: /adventure/i });

    expect(pill).toHaveAttribute('aria-selected', 'true');
    expect(pill).toHaveAttribute('aria-controls', 'Adventure-stories');
    expect(pill).toHaveClass('custom-class');

    // Snapshot test
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render correctly when not active', () => {
    const { asFragment, getByRole } = render(
      <Pill isSelected={false} name="Adventure" onSelect={mockOnSelect} />,
    );

    const pill = getByRole('tab', { name: /adventure/i });

    expect(pill).toHaveAttribute('aria-selected', 'false');
    expect(pill).toHaveAttribute('aria-controls', 'Adventure-stories');
    expect(pill).not.toHaveClass('custom-class');

    // Snapshot test
    expect(asFragment()).toMatchSnapshot();
  });

  it('should call onSelect when clicked', () => {
    const { getByRole } = render(
      <Pill isSelected={false} name="Adventure" onSelect={mockOnSelect} />,
    );

    const pill = getByRole('tab', { name: /adventure/i });
    fireEvent.click(pill);

    expect(mockOnSelect).toHaveBeenCalledWith();
  });

  it('should have proper ARIA attributes', () => {
    const { asFragment, getByRole } = render(
      <Pill isSelected={false} name="Adventure" onSelect={mockOnSelect} />,
    );

    const pill = getByRole('tab', { name: /adventure/i });

    expect(pill).toHaveAttribute('role', 'tab');
    expect(pill).toHaveAttribute('aria-selected', 'false');
    expect(pill).toHaveAttribute('aria-controls', 'Adventure-stories');

    // Snapshot test
    expect(asFragment()).toMatchSnapshot();
  });
});
