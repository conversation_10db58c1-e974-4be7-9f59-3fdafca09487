import clsx from 'clsx';
import slugify from 'slugify';

interface PillProps {
  isSelected: boolean;
  name: string;
  onSelect: () => void;
  pillClassName?: string;
}

export default function Pill({
  isSelected,
  name,
  onSelect,
  pillClassName,
}: PillProps) {
  return (
    <button
      aria-controls={`${slugify(name)}-stories`}
      aria-selected={isSelected}
      className={clsx(
        'flex items-center justify-center',
        'px-3 py-2',
        'rounded-full',
        'border border-solid',
        isSelected ? 'border-gray-900' : 'border-gray-300',
        'opacity-100',
        'transition duration-300 ease-out',
        'hover:opacity-100 focus:opacity-100',
        pillClassName,
      )}
      disabled={isSelected}
      onClick={() => onSelect()}
      role="tab"
      type="button"
    >
      <span
        className={clsx(
          'font-inter',
          'text-xs font-medium leading-[14px]',
          'whitespace-nowrap text-left text-gray-900',
        )}
      >
        {name}
      </span>
    </button>
  );
}
