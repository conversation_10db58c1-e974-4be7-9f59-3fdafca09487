import {
  faChevronLeft,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import Slider, { type SwipeDirection } from 'react-slick';

import { useAppSelector } from 'store/hooks';
import { ColorPalette } from 'themes/autumn/components/stories/Signpost/constants';
import { sendGtmInteractionEvent } from 'themes/autumn/templates/Classifieds/utils';
import StoryListContext from 'util/ZoneItems/context/storylist-context';
import { SummaryOption } from 'util/constants';
import { ResponsiveType } from 'util/device';
import { useResponsiveTypeFromWidth } from 'util/hooks';
import { fetchStoriesForStoryList } from 'util/organization/suzuka';

import ExploreStrapStoryCard from './ExploreStrapStoryCard';
import styles from './ExploreTravelCarousel.module.css';
import MobileExploreTravelList from './MobileExploreTravelList';
import Pill from './Pill';
import { ArrowLabel } from './enums';
import { CardType, type StoryOrNull } from './types';

import type { CollectionStoryList, StoryList } from 'types/ZoneItems';

interface ExploreTravelCarouselProps {
  initialSelectedStoryListId?: number;
  limit: number | null;
  offset?: number;
  showPills?: boolean;
  signPostClassName?: string;
  signPostColorPalette?: ColorPalette;
  storylists: CollectionStoryList[] | StoryList[];
  tags?: string;
  title: string;
  useCanonicalUrl?: boolean;
  zoneItemId: number;
}

const ITEMS_PER_PAGE = 4;

export default function ExploreTravelCarousel({
  initialSelectedStoryListId,
  limit: storiesLimit,
  offset = 0,
  showPills = true,
  signPostClassName = '!mb-1 md:!mb-0',
  signPostColorPalette = ColorPalette.PALETTE_EXPLORE,
  storylists,
  tags,
  title,
  useCanonicalUrl = false,
  zoneItemId,
}: ExploreTravelCarouselProps): React.ReactElement | null {
  const initialStoryList = useMemo(() => {
    let initial = storylists[0];

    if (showPills && initialSelectedStoryListId) {
      const collectionStoryList = storylists.find(
        (storylist) => storylist.storyListId === initialSelectedStoryListId,
      );

      if (collectionStoryList) {
        initial = collectionStoryList;
      }
    }

    return initial;
  }, [initialSelectedStoryListId, showPills, storylists]);

  const limit = storiesLimit ?? ITEMS_PER_PAGE;
  const { siteId } = useAppSelector((state) => state.settings);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentOffset, setCurrentOffset] = useState(offset);
  const [hasReachedEnd, setHasReachedEnd] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [stories, setStories] = useState<StoryOrNull[]>([
    ...Array<StoryOrNull>(ITEMS_PER_PAGE).fill(null),
  ]);
  const responsiveType = useResponsiveTypeFromWidth();
  const [selectedStoryList, setSelectedStoryList] = useState<
    CollectionStoryList | StoryList
  >(initialStoryList);
  const sliderRef = useRef<Slider>(null);
  const ref = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const mobileVisibleStories = stories.slice(
    0,
    (currentIndex + 1) * ITEMS_PER_PAGE,
  );

  const sendPillClickGTMEvent = useCallback(
    (storylist: StoryList | CollectionStoryList) => {
      sendGtmInteractionEvent(
        {
          action: 'pill_click',
          label: storylist.label || storylist.storyListTitle,
          section: title,
        },
        'pill_interaction',
      );
    },
    [title],
  );

  const sendArrowClickGTMEvent = useCallback(
    (storylist: StoryList | CollectionStoryList, direction: ArrowLabel) => {
      sendGtmInteractionEvent(
        {
          action: 'arrow_click',
          category: storylist.label || storylist.storyListTitle,
          label: direction,
          section: title,
        },
        'carousel_interaction',
      );
    },
    [title],
  );

  const fetchStories = useCallback(
    async (newOffset: number, storyListId: number) => {
      abortControllerRef.current?.abort();
      // eslint-disable-next-line compat/compat
      const controller = new AbortController();
      abortControllerRef.current = controller;
      const { signal } = controller;

      setIsLoading(true);
      try {
        const newStories = await fetchStoriesForStoryList({
          extraTags: tags,
          limit,
          offset: newOffset,
          signal,
          siteId,
          storyListId,
          useCanonicalUrl,
        });
        setStories((prevStories) =>
          // If it's a new category, replace stories
          newOffset === 0
            ? [...newStories]
            : [
                ...prevStories.filter((story) => story !== null),
                ...newStories,
              ],
        );

        setCurrentOffset(newOffset);
        setHasReachedEnd(newStories.length < limit);
      } catch (error) {
        console.error(error);
      }
      setIsLoading(false);
    },
    [limit, siteId, tags, useCanonicalUrl],
  );

  useEffect(() => {
    fetchStories(offset, initialStoryList.storyListId).catch(() => {});
  }, [initialStoryList, offset, showPills, fetchStories]);

  const getCardType = (story: StoryOrNull): CardType => {
    if (story) {
      return CardType.CONTENT;
    }
    if (isLoading) {
      return CardType.LOADING;
    }
    return CardType.PADDING;
  };

  const handlePillSelect = async (
    storylist: CollectionStoryList | StoryList,
  ) => {
    if (!showPills) return;
    setCurrentIndex(0);
    setCurrentOffset(0);
    setSelectedStoryList(storylist);
    setStories([...Array<StoryOrNull>(ITEMS_PER_PAGE).fill(null)]);
    sendPillClickGTMEvent(storylist);
    await fetchStories(0, storylist.storyListId);
  };

  const handlePrevious = () => {
    sendArrowClickGTMEvent(selectedStoryList, ArrowLabel.LEFT);
    sliderRef.current?.slickPrev();
  };

  const handleNext = () => {
    sendArrowClickGTMEvent(selectedStoryList, ArrowLabel.RIGHT);
    if ((currentIndex + ITEMS_PER_PAGE) % limit !== 0) {
      fetchStories(currentOffset + limit, selectedStoryList.storyListId).catch(
        () => {},
      );
    }
    sliderRef.current?.slickNext();
  };

  const sliderSettings = {
    arrows: false,
    beforeChange: (_: number, next: number) => {
      setCurrentIndex(next);
    },
    dots: false,
    easing: 'easeInOut',
    infinite: false,
    responsive: [
      {
        breakpoint: 1023, // Tablet
        settings: {
          slidesToScroll: 2,
          slidesToShow: 2,
          swipe: true,
          swipeEvent: (swipeDirection: SwipeDirection) =>
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            handleSwipe(swipeDirection),
          touchThreshold: 100,
          variableWidth: true,
        },
      },
      {
        breakpoint: 1025, // iPad pro
        settings: {
          slidesToScroll: 4,
          slidesToShow: 4,
          swipe: true,
          swipeEvent: (swipeDirection: SwipeDirection) =>
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            handleSwipe(swipeDirection),
          touchThreshold: 100,
          variableWidth: false,
        },
      },
    ],
    slidesToScroll: ITEMS_PER_PAGE,
    slidesToShow: ITEMS_PER_PAGE,
    speed: 1000,
    swipe: false,
    variableWidth: false,
  };

  const hasPrevious = currentIndex > 0;
  const hasNext =
    !hasReachedEnd ||
    currentIndex + sliderSettings.slidesToShow < stories.length;
  const disableNextButton = !hasNext || isLoading;

  const handleSwipe = (swipeDirection: SwipeDirection) => {
    if (swipeDirection === 'left') {
      if (
        hasNext &&
        !isLoading &&
        (currentIndex + ITEMS_PER_PAGE) % limit !== 0
      ) {
        fetchStories(
          currentOffset + limit,
          selectedStoryList.storyListId,
        ).catch(() => {});
      }
    }
  };

  const handleSeeMore = async () => {
    const nextIndex = currentIndex + 1;
    const totalPagesMobile = Math.ceil(stories.length / ITEMS_PER_PAGE);
    if (nextIndex >= totalPagesMobile && hasNext) {
      await fetchStories(
        currentOffset + limit,
        selectedStoryList?.storyListId,
      );
    }
    setCurrentIndex(nextIndex);
  };

  // Add padding to make the list a multiple of x
  // to create a consistent number of slides for
  // smoother transitions
  const paddedStoryList = [...stories];
  const remainder = stories.length % ITEMS_PER_PAGE;
  if (remainder !== 0) {
    paddedStoryList.push(
      ...Array<StoryOrNull>(ITEMS_PER_PAGE - remainder).fill(null),
    );
  }
  // To avoid swiping on blank slides in tablet modes
  const storyListToUse =
    responsiveType === ResponsiveType.TABLET_NARROW
      ? stories
      : paddedStoryList;

  const value = useMemo(
    () => ({
      enableOrder: false,
      label: selectedStoryList.label,
      storyListId: selectedStoryList.storyListId,
      storyListTitle: selectedStoryList.storyListTitle,
      summaryOptions: SummaryOption.NEWSNOW_ENABLED,
      useCanonicalUrl,
      zoneItemId,
    }),
    [
      selectedStoryList.label,
      selectedStoryList.storyListId,
      selectedStoryList.storyListTitle,
      useCanonicalUrl,
      zoneItemId,
    ],
  );

  return (
    <StoryListContext.Provider value={value}>
      <div
        className={clsx('md:min-h-[387px]', {
          'md:-mt-7 lg:-mt-16': !showPills,
        })}
      >
        <div
          className={clsx('relative flex items-center justify-between', {
            'mb-0 md:mb-6 lg:justify-end': !showPills,
            'mb-6': showPills,
          })}
        >
          {showPills && (
            <div
              className="-mr-4 -mt-1 flex snap-x flex-nowrap gap-2 overflow-x-auto scrollbar-hide md:-mr-14 lg:snap-none lg:flex-wrap"
              role="tablist"
            >
              {storylists.map((storylist) => (
                <Pill
                  isSelected={
                    selectedStoryList.storyListId === storylist.storyListId
                  }
                  key={storylist.storyListId}
                  name={storylist?.label || storylist?.storyListTitle}
                  onSelect={() => handlePillSelect(storylist)}
                />
              ))}
            </div>
          )}
          <div
            className={clsx('hidden items-center gap-2 md:hidden lg:flex', {
              'lg:pb-5': !showPills,
            })}
          >
            <button
              aria-disabled={!hasPrevious}
              aria-label="Previous stories"
              className={clsx(
                'size-8 flex-none rounded-full border bg-white stroke-gray-200 p-1',
                !hasPrevious && 'opacity-40',
              )}
              disabled={!hasPrevious}
              onClick={handlePrevious}
              type="button"
            >
              <FontAwesomeIcon icon={faChevronLeft} />
            </button>

            <button
              aria-disabled={disableNextButton}
              aria-label="Load more stories"
              className={clsx(
                'size-8 flex-none rounded-full border bg-white stroke-gray-200 p-1',
                disableNextButton && 'opacity-40',
              )}
              disabled={disableNextButton}
              onClick={handleNext}
              type="button"
            >
              <FontAwesomeIcon icon={faChevronRight} />
            </button>
          </div>
        </div>

        <div className="md:hidden lg:hidden">
          <MobileExploreTravelList
            getCardType={getCardType}
            hasMore={!hasReachedEnd}
            onSeeMore={handleSeeMore}
            showPills={showPills}
            signPostClassName={signPostClassName}
            signPostColorPalette={signPostColorPalette}
            stories={mobileVisibleStories}
            storyListId={selectedStoryList.storyListId}
          />
        </div>

        <div
          aria-live="polite"
          className={clsx(
            'hidden md:-mr-14 md:block md:min-h-[356px] lg:block lg:w-full xl:overflow-hidden',
            styles.slickWrapper,
            !showPills && 'lg:-mt-4',
            { [styles.sliderWrapper]: stories.length < ITEMS_PER_PAGE },
          )}
          ref={ref}
        >
          <Slider
            ref={sliderRef}
            /* eslint-disable-next-line react/jsx-props-no-spreading */
            {...sliderSettings}
          >
            {storyListToUse.map((story, index) => (
              <ExploreStrapStoryCard
                containerClassName="xl:w-72 lg:w-[231px]"
                headlineWrapperClassName="md:w-72 lg:w-[231px] xl:w-72"
                key={story?.id ?? `no-story-${index}`}
                leadingImageClassName="xl:w-72 lg:w-[231px] mb-2"
                signPostClassName={signPostClassName}
                signPostColorPalette={signPostColorPalette}
                story={story || undefined}
                storyListId={selectedStoryList.storyListId}
                type={getCardType(story)}
              />
            ))}
          </Slider>
        </div>
      </div>
    </StoryListContext.Provider>
  );
}
