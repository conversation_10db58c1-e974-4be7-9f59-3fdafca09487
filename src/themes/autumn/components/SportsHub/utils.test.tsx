import fs from 'fs';
import path from 'path';

import { Sport, SportPage } from 'types/SportsHub';

import {
  daysToGo,
  formatMinutesLabel,
  getAflGoalAndBehind,
  getAflGoals,
  getAflScores,
  getCurrentPeriod,
  getMatchSeason,
  getMinsPerPeriod,
  getMinutesInGame,
  getMinutesInGamePeriod,
  getNumOfPeriods,
  getOrdinalIndicator,
  getScoreBySquadId,
  getSportPageByCompLevelId,
} from './utils';

import type { SportMatchDetailResponse } from 'util/organization/suzuka';

describe('daysToGo function', () => {
  const tests = [
    {
      expected: 14,
      now: new Date('2023-03-02T19:50:00+11:00'),
      utcStartTime: '2023-03-16T19:50:00+11:00',
    },
  ];

  it('calculate days to go', () => {
    expect.assertions(1);
    tests.forEach(({ expected, now, utcStartTime }) => {
      expect(daysToGo(utcStartTime, now)).toStrictEqual(expected);
    });
  });
});

describe('getMinutesInGame function', () => {
  it('return 0 if periodCompleted is larger than period', () => {
    expect.assertions(1);
    expect(getMinutesInGame(1, 2, 2000, 40)).toBe(0);
  });

  it('return minutes in a game', () => {
    expect.assertions(1);
    expect(getMinutesInGame(2, 1, 2000, 40)).toBe(33);
  });
});

describe('getMinutesInGamePeriod function', () => {
  it('return 0 if periodCompleted is larger than period', () => {
    expect.assertions(1);
    expect(getMinutesInGamePeriod(1, 2, 2000, 40)).toBe(40);
  });

  it('return minutes in a game', () => {
    expect.assertions(1);
    expect(getMinutesInGame(2, 1, 2000, 40)).toBe(33);
  });
});

describe('formatMinutesLabel function', () => {
  it('format when minutes are larger than 10', () => {
    expect.assertions(1);
    expect(formatMinutesLabel(20)).toBe('20');
  });

  it('format when minutes are less than 10', () => {
    expect.assertions(1);
    expect(formatMinutesLabel(8)).toBe('08');
  });
});

describe('getMinsPerPeriod function', () => {
  it('soccer', () => {
    expect.assertions(1);
    expect(getMinsPerPeriod(Sport.SOCCER)).toBe(45);
  });

  it('afl', () => {
    expect.assertions(1);
    expect(getMinsPerPeriod(Sport.AFL)).toBe(20);
  });
});

describe('getMatchSeason function', () => {
  it('a-league - date in previous season', () => {
    expect.assertions(1);
    expect(
      getMatchSeason(
        SportPage.A_LEAGUE,
        new Date('2023-03-16T19:50:00+11:00'),
      ),
    ).toStrictEqual({ label: '2022/23', name: '2022-2023' });
  });

  it('a-league - date in next season', () => {
    expect.assertions(1);
    expect(
      getMatchSeason(
        SportPage.A_LEAGUE,
        new Date('2023-11-16T19:50:00+11:00'),
      ),
    ).toStrictEqual({ label: '2023/24', name: '2023-2024' });
  });

  it('afl', () => {
    expect.assertions(1);
    expect(
      getMatchSeason(SportPage.AFL, new Date('2023-11-16T19:50:00+11:00')),
    ).toStrictEqual({ label: '2023', name: '2023' });
  });
});

describe('getSportPageByCompLevelId function', () => {
  it('afl comp level id', () => {
    expect.assertions(1);
    expect(getSportPageByCompLevelId(14)).toStrictEqual(SportPage.AFL);
  });

  it('a-league comp level id', () => {
    expect.assertions(1);
    expect(getSportPageByCompLevelId(73)).toStrictEqual(SportPage.A_LEAGUE);
  });
});

describe('getScoreBySquadId function', () => {
  const mockScoresData = JSON.parse(
    fs
      .readFileSync(path.join(__dirname, './mocks/leagueStats.json'))
      .toString(),
  ) as SportMatchDetailResponse;
  const scores = mockScoresData.stats?.data.matchScores?.totalScores ?? [];
  it('get squad Id', () => {
    expect.assertions(1);
    expect(getScoreBySquadId(scores, 336)).toBe(54);
  });
});

describe('getAflGoals function', () => {
  const mockAflData = JSON.parse(
    fs.readFileSync(path.join(__dirname, './mocks/aflStats.json')).toString(),
  ) as SportMatchDetailResponse;
  const aflScoreFlows = mockAflData.stats?.data.scoreFlow?.score ?? [];
  it('sort the goals', () => {
    expect.assertions(1);
    expect(getAflGoals(aflScoreFlows, 1010)).toStrictEqual([
      ['Jesse Hogan', 3],
      ['Finn Callaghan', 1],
      ["Xavier O'Halloran", 1],
      ['Lachie Ash', 1],
      ['Josh Kelly', 1],
    ]);
  });
});

describe('getAflScores function', () => {
  const mockAflData = JSON.parse(
    fs.readFileSync(path.join(__dirname, './mocks/aflStats.json')).toString(),
  ) as SportMatchDetailResponse;
  const aflPeriodScores =
    mockAflData.stats?.data.matchScores?.periodScores ?? [];

  const expected = [
    { behinds: 4, goals: 2, period: 1, score: 16, squadId: 1010 },
    { behinds: 4, goals: 1, period: 2, score: 10, squadId: 1010 },
    { behinds: 1, goals: 3, period: 3, score: 19, squadId: 1010 },
    { behinds: 4, goals: 1, period: 4, score: 10, squadId: 1010 },
  ];
  it('sort the goals', () => {
    expect.assertions(1);
    expect(getAflScores(aflPeriodScores, 1010)).toStrictEqual(expected);
  });
});

describe('getAflGoalAndBehind function', () => {
  const mockAflData = JSON.parse(
    fs.readFileSync(path.join(__dirname, './mocks/aflStats.json')).toString(),
  ) as SportMatchDetailResponse;
  const aflPeriodScores =
    mockAflData.stats?.data.matchScores?.periodScores ?? [];

  it('afl goal and behind', () => {
    expect.assertions(1);
    expect(getAflGoalAndBehind(1, aflPeriodScores, 1010)).toBe('2.4');
  });
});

describe('getOrdinalIndicator function', () => {
  it('first indicator', () => {
    expect.assertions(1);
    expect(getOrdinalIndicator(1)).toBe('st');
  });

  it('second indicator', () => {
    expect.assertions(1);
    expect(getOrdinalIndicator(2)).toBe('nd');
  });

  it('third indicator', () => {
    expect.assertions(1);
    expect(getOrdinalIndicator(3)).toBe('rd');
  });

  it('others indicator', () => {
    expect.assertions(1);
    expect(getOrdinalIndicator(11)).toBe('th');
  });
});

describe('getNumOfPeriods function', () => {
  it('afl', () => {
    expect.assertions(1);
    expect(getNumOfPeriods(Sport.AFL)).toBe(4);
  });

  it('others', () => {
    expect.assertions(1);
    expect(getNumOfPeriods(Sport.LEAGUE)).toBe(2);
  });
});

describe('getCurrentPeriod function', () => {
  it('afl with the correct period', () => {
    expect.assertions(1);
    expect(getCurrentPeriod(Sport.AFL, 2)).toBe(3);
  });

  it('afl with the wrong period', () => {
    expect.assertions(1);
    expect(getCurrentPeriod(Sport.AFL, 5)).toBe(4);
  });
});
