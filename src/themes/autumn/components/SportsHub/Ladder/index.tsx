import React from 'react';

import { SportPage } from 'types/SportsHub';

import AflLadder from './aflLadder';
import CricketLadder from './cricketLadder';
import LeagueLadder from './leagueLadder';
import SoccerLadder from './soccerLadder';

import type {
  Afl,
  CricketGroup,
  League,
  Soccer,
  TeamsType,
} from 'types/SportsHub';

interface RoundProps {
  sportPage: SportPage;
  teams: TeamsType;
}

function Ladder({ sportPage, teams }: RoundProps): React.ReactElement | null {
  const ladderComponent = (indexToShow: number) => {
    switch (sportPage) {
      case SportPage.NRL:
        return (
          <LeagueLadder indexToShow={indexToShow} teams={teams as League[]} />
        );
      case SportPage.AFL:
        return <AflLadder indexToShow={indexToShow} teams={teams as Afl[]} />;
      case SportPage.A_LEAGUE:
        return (
          <SoccerLadder indexToShow={indexToShow} teams={teams as Soccer[]} />
        );
      case SportPage.CRICKET:
        return <CricketLadder teams={teams as CricketGroup[]} />;
      default:
        return null;
    }
  };

  return ladderComponent(teams.length - 1);
}

export default Ladder;
