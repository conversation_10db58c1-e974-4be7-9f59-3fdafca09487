import React from 'react';

import { useAppSelector } from 'store/hooks';
import { SportPage } from 'types/SportsHub';

import Avatar from '../Component/Avatar';

import type { CricketGroup } from 'types/SportsHub';

interface CricketLadderProps {
  teams: CricketGroup[];
}

function CricketLadder({ teams }: CricketLadderProps): React.ReactElement {
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  return (
    <div className="my-7 flow-root overflow-auto">
      <div className="mx-auto max-w-7xl">
        <div className="flex flex-row">
          <div className="absolute z-10 w-28 bg-white">
            <table className="w-full divide-y divide-gray-300 text-xs font-bold">
              <thead>
                <tr className="divide-x divide-gray-200 text-center uppercase text-orange-650">
                  <th className="pb-3.5 text-center" scope="col">
                    Pos
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    Team
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white text-center text-xs font-bold">
                {teams.map((teamGroup) => {
                  const teamRecords = teamGroup.teamRecord.filter(
                    (team) =>
                      team.teamName &&
                      !team.teamName.match(/^([A-Z]\d+|TBC)$/),
                  );
                  return (
                    <>
                      {!!teamGroup.groupName && !!teamRecords.length && (
                        <tr
                          className="divide-x divide-gray-200 text-xs text-slate-750"
                          key={teamGroup.groupName}
                        >
                          <td className="px-3 py-4 font-medium text-gray-900">
                            &nbsp;
                          </td>
                          <td className="px-3 py-4 font-medium text-gray-900">
                            &nbsp;
                          </td>
                        </tr>
                      )}
                      {teamRecords.map((team) => (
                        <tr
                          className="divide-x divide-gray-200 text-sm text-slate-750"
                          key={team.teamId}
                        >
                          <td className="p-3 font-medium text-gray-900 md:py-4">
                            {team.position}
                          </td>
                          <td className="p-1">
                            <Avatar
                              className="m-auto h-7 object-contain shadow-lg"
                              name={team.teamName || ''}
                              sportPage={SportPage.CRICKET}
                              squadId={team.teamId || 0}
                              staticUrl={staticUrl}
                            />
                          </td>
                        </tr>
                      ))}
                    </>
                  );
                })}
              </tbody>
            </table>
          </div>
          <div className="relative left-28 mr-28 grow">
            <table className="w-full divide-y divide-gray-300 text-xs font-bold">
              <thead>
                <tr className="divide-x divide-gray-200 text-center uppercase text-orange-650">
                  <th
                    aria-hidden
                    className="px-3 pb-3.5 text-left lg:w-1/2"
                    scope="col"
                  >
                    &nbsp;
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    GP
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    W
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    NR
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    L
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    NRR
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    PTS
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white text-center text-xs font-bold">
                {teams.map((teamGroup) => {
                  const teamRecords = teamGroup.teamRecord.filter(
                    (team) =>
                      team.teamName &&
                      !team.teamName.match(/^([A-Z]\d+|TBC)$/),
                  );
                  return (
                    <>
                      {!!teamGroup.groupName && !!teamRecords.length && (
                        <tr
                          className="divide-x divide-gray-200 text-xs text-slate-750"
                          key={teamGroup.groupName}
                        >
                          <td className="px-3 py-4 font-semibold uppercase text-gray-900">
                            {teamGroup.groupName}
                          </td>
                          <td className="px-3">&nbsp;</td>
                          <td className="px-3">&nbsp;</td>
                          <td className="px-3">&nbsp;</td>
                          <td className="px-3">&nbsp;</td>
                          <td className="px-3">&nbsp;</td>
                          <td className="px-3">&nbsp;</td>
                        </tr>
                      )}
                      {teamRecords.map((team) => (
                        <tr
                          className="divide-x divide-gray-200 text-sm text-slate-750"
                          key={team.teamId}
                        >
                          <td className="whitespace-nowrap p-3 text-left md:py-4">
                            {team.teamName}
                          </td>
                          <td className="p-3 text-orange-650">
                            {team.played}
                          </td>
                          <td className="p-3">{team.won}</td>
                          <td className="p-3">{team.noResult}</td>
                          <td className="p-3">{team.lost}</td>
                          <td className="p-3">{team.netRunRate}</td>
                          <td className="p-3">{team.points}</td>
                        </tr>
                      ))}
                    </>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CricketLadder;
