import fs from 'fs';
import path from 'path';

import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { SportPage } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import Ladder from './index';

import type { Afl, League, Soccer } from 'types/SportsHub';

describe('<Ladder />', () => {
  it('renders with NRL ladder', () => {
    expect.assertions(1);

    const mockTeamsData = JSON.parse(
      fs
        .readFileSync(path.join(__dirname, '../mocks/leagueLadder.json'))
        .toString(),
    ) as League[];

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Ladder sportPage={SportPage.NRL} teams={mockTeamsData} />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with AFL ladder', () => {
    expect.assertions(1);

    const mockTeamsData = JSON.parse(
      fs
        .readFileSync(path.join(__dirname, '../mocks/aflLadder.json'))
        .toString(),
    ) as Afl[];

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Ladder sportPage={SportPage.AFL} teams={mockTeamsData} />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with A-league ladder', () => {
    expect.assertions(1);

    const mockTeamsData = JSON.parse(
      fs
        .readFileSync(path.join(__dirname, '../mocks/soccerLadder.json'))
        .toString(),
    ) as Soccer[];

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Ladder sportPage={SportPage.A_LEAGUE} teams={mockTeamsData} />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
