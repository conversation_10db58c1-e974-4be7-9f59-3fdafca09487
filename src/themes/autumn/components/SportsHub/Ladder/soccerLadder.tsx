import React from 'react';

import { useAppSelector } from 'store/hooks';

import type { Soccer } from 'types/SportsHub';

interface SoccerLadderProps {
  indexToShow: number;
  teams: Soccer[];
}

function SoccerLadder({
  indexToShow,
  teams,
}: SoccerLadderProps): React.ReactElement {
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  return (
    <div className="my-7 flow-root overflow-auto">
      <div className="mx-auto max-w-7xl">
        <div className="flex flex-row">
          <div className="absolute z-10 w-28 bg-white">
            <table className="w-full divide-y divide-gray-300 border-r-1 border-gray-300 text-xs font-bold">
              <thead>
                <tr className="divide-x divide-gray-200 text-center text-orange-650">
                  <th className="pb-3.5 text-center" scope="col">
                    POS
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    CLUB
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white text-center text-xs font-bold">
                {teams.map((team, index) => {
                  if (index <= indexToShow) {
                    return (
                      <tr
                        className="divide-x divide-gray-200 text-sm text-slate-750"
                        key={team.squadId}
                      >
                        <td className="p-3 font-medium text-gray-900 md:py-4">
                          {team.position}
                        </td>
                        <td className="p-1">
                          <img
                            alt={team.squadName}
                            className="m-auto h-7 object-contain"
                            loading="lazy"
                            src={
                              // eslint-disable-next-line @stylistic/max-len
                              `${staticUrl}images/sports-hub/squads/squad-${team.squadId}.svg`
                            }
                          />
                        </td>
                      </tr>
                    );
                  }
                  return null;
                })}
              </tbody>
            </table>
          </div>
          <div className="relative left-28 mr-28 grow">
            <table className="w-full divide-y divide-gray-300 text-xs font-bold">
              <thead>
                <tr className="divide-x divide-gray-200 text-center text-orange-650">
                  <th className="px-3 pb-3.5 text-left lg:w-1/2" scope="col">
                    TEAM
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    POINTS
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    PLAYED
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    WON
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    LOST
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    DRAWN
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    GF
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    GA
                  </th>
                  <th className="px-3 pb-3.5" scope="col">
                    GD
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white text-center text-xs font-bold">
                {teams.map((team, index) => {
                  if (index <= indexToShow) {
                    return (
                      <tr
                        className="divide-x divide-gray-200 text-sm text-slate-750"
                        key={team.squadId}
                      >
                        <td className="whitespace-nowrap p-3 text-left md:py-4">
                          {team.squadName}
                        </td>
                        <td className="p-3 text-orange-650">{team.points}</td>
                        <td className="p-3">{team.played}</td>
                        <td className="p-3">{team.won}</td>
                        <td className="p-3">{team.lost}</td>
                        <td className="p-3">{team.drawn}</td>
                        <td className="p-3">{team.for}</td>
                        <td className="p-3">{team.against}</td>
                        <td className="p-3">{team.diff}</td>
                      </tr>
                    );
                  }
                  return null;
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SoccerLadder;
