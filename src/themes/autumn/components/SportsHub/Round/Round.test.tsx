import fs from 'fs';
import path from 'path';

import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { CompleteProfileEnrichment } from 'store/slices/features';
import { createStore } from 'store/store';
import { SportPage } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import Round from './index';

import type { MatchResponse } from 'util/organization/suzuka';

describe('<Round />', () => {
  const currentRoundMatchesMockData = JSON.parse(
    fs
      .readFileSync(path.join(__dirname, '../mocks/currentMatches.json'))
      .toString(),
  ) as MatchResponse[];

  it('renders with paywall', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
      features: {
        ...state.features,
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.NONE,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: true,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: true,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: false,
            supportLoginFacebook: false,
            supportLoginGoogle: false,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
      piano: {
        ...state.piano,
        hasPaywall: false,
        loadingPaywall: false,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Round
          currentRoundMatches={currentRoundMatchesMockData}
          sportPage={SportPage.AFL}
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders without paywall', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
      features: {
        ...state.features,
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.NONE,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: true,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: true,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: false,
            supportLoginFacebook: false,
            supportLoginGoogle: false,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
      piano: {
        ...state.piano,
        hasPaywall: true,
        loadingPaywall: false,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Round
          currentRoundMatches={currentRoundMatchesMockData}
          sportPage={SportPage.AFL}
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
