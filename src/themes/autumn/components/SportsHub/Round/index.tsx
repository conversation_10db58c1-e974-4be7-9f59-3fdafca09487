import { Fragment } from 'react';

import { useAppSelector } from 'store/hooks';
import { Competition, SportPage } from 'types/SportsHub';

import PianoPaywall from '../../generic/PianoPaywall';
import MatchWidget from '../Match/widget';
import { getCurrentPeriod } from '../utils';

import type { MatchResponse } from 'util/organization/suzuka';

interface RoundProps {
  currentRoundMatches: MatchResponse[];
  sportPage: SportPage;
}

function Round({
  currentRoundMatches,
  sportPage,
}: RoundProps): React.ReactElement | null {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const loadingPaywall = useAppSelector((state) => state.piano.loadingPaywall);
  const hasPaywall = useAppSelector((state) => state.piano.hasPaywall);
  const hasPianoPaywall =
    pianoFeature.enabled && (loadingPaywall || hasPaywall);

  if (currentRoundMatches.length === 0) {
    return null;
  }

  return (
    <>
      {currentRoundMatches.map((matchResponse: MatchResponse, index) => {
        const match = matchResponse.data;
        const matchComponent = match && (
          <div className="my-3" key={match.id}>
            <MatchWidget
              awaySquadId={match.awaySquadId}
              awaySquadName={match.awaySquadName}
              awaySquadNickname={match.awaySquadNickname}
              awaySquadPosition={matchResponse.ladderPositions?.awaySquad || 0}
              awaySquadScore={match.awaySquadScore}
              awaySummary={match.awaySummary}
              currentPeriod={getCurrentPeriod(
                Competition[sportPage].sport,
                match.periodCompleted,
              )}
              homeSquadId={match.homeSquadId}
              homeSquadName={match.homeSquadName}
              homeSquadNickname={match.homeSquadNickname}
              homeSquadPosition={matchResponse.ladderPositions?.homeSquad || 0}
              homeSquadScore={match.homeSquadScore}
              homeSummary={match.homeSummary}
              matchId={matchResponse.id}
              matchName={match.matchName}
              matchStatus={match.matchStatus}
              matchSummary={match.matchSummary}
              periodCompleted={match.periodCompleted}
              periodSeconds={match.periodSeconds}
              sportPage={sportPage}
              utcStartTime={match.utcStartTime}
              venueCountry={match.venueCountry}
              venueName={match.venueName}
            />
          </div>
        );

        let component = matchComponent;

        if (sportPage !== SportPage.CRICKET && hasPianoPaywall) {
          if (index === 0) {
            component = (
              <Fragment key={match.id}>
                <div className="pointer-events-none gradient-mask-b-0">
                  {matchComponent}
                </div>
                <div className="mx-auto mb-6 max-w-sm px-4 md:px-0">
                  <PianoPaywall />
                </div>
              </Fragment>
            );
          } else {
            component = (
              <div className="hidden" key={match.id}>
                {matchComponent}
              </div>
            );
          }
        }

        return component;
      })}
    </>
  );
}

export default Round;
