// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<MatchDetailBanner /> renders with complete match 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="-mx-4 rounded-none border-1 border-gray-300 bg-gray-850 pb-5 pt-2 hover:border-orange-650 md:mx-0 md:rounded-lg md:py-5"
  >
    <div
      class="mt-3 grid w-full pl-4 text-white md:hidden"
    >
      <a
        class="mb-5 cursor-pointer text-sm font-normal md:mt-0"
        href="/sport/afl/matches/"
      >
        <svg
          class="float-left mr-3"
          fill="none"
          height="16"
          viewBox="0 0 9 16"
          width="9"
        >
          <path
            d="M8 1L1 8L8 15"
            stroke="white"
            stroke-width="2"
          />
        </svg>
         Matches
      </a>
    </div>
    <div
      class="grid w-full cursor-pointer grid-cols-3 content-center gap-2 px-2 text-white md:grid-cols-5 md:gap-4"
    >
      <div
        class="grid justify-items-center md:col-span-2 md:justify-items-end"
      >
        <div
          class="flex flex-row items-center justify-center gap-4 text-2xl font-bold"
        >
          <div
            class="mt-1 hidden text-center md:block"
          >
            Home Squad Nick Name
          </div>
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Home Squad Name"
              class="size-20 p-0.5 rounded-full bg-white"
              loading="lazy"
              src="images/sports-hub/squads/squad-1010.svg"
            />
          </div>
          <div
            class="mt-1 hidden flex-col gap-y-1 text-center md:flex"
          >
            5
            <div
              class="text-sm"
            >
              2.4
            </div>
          </div>
        </div>
      </div>
      <div
        class="content-center justify-items-center gap-y-1 hidden md:grid"
      >
        <div
          class="inline-flex h-6 shrink-0 items-center rounded-full bg-white px-5 text-sm font-bold uppercase text-gray-850"
        >
          Full time
        </div>
      </div>
      <div
        class="grid place-items-center text-2xl font-extrabold md:hidden"
      >
        vs
      </div>
      <div
        class="grid justify-items-center md:col-span-2 md:justify-items-start"
      >
        <div
          class="flex flex-row items-center justify-center gap-4 text-2xl font-bold"
        >
          <div
            class="mt-1 hidden flex-col gap-y-1 text-center md:flex"
          >
            10
            <div
              class="text-sm"
            >
              5.1
            </div>
          </div>
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Away Squad Name"
              class="size-20 p-0.5 rounded-full bg-white"
              loading="lazy"
              src="images/sports-hub/squads/squad-40.svg"
            />
          </div>
          <div
            class="mt-1 hidden text-center md:block"
          >
            Away Squad Nick Name
          </div>
        </div>
      </div>
    </div>
    <div
      class="mt-3 grid w-full grid-cols-3 content-center gap-2 px-2 text-white md:hidden md:grid-cols-5 md:gap-4"
    >
      <div
        class="grid content-center justify-items-center"
      >
        <div
          class="text-2xl font-normal"
        >
          5
        </div>
        <div>
          2.4
        </div>
      </div>
      <div
        class="grid justify-items-center gap-y-1 md:col-span-3"
      >
        <div
          class="inline-flex h-6 shrink-0 items-center rounded-full bg-white px-5 text-sm font-bold uppercase text-gray-850"
        >
          Full time
        </div>
      </div>
      <div
        class="grid content-center justify-items-center"
      >
        <div
          class="text-2xl font-bold"
        >
          10
        </div>
        <div>
          5.1
        </div>
      </div>
    </div>
    <div
      class="mt-2 grid justify-items-center gap-y-1 px-4 text-white"
    >
      <div
        class="text-center text-sm font-normal"
      >
        Fri 1 Sept
      </div>
      <div
        class="text-center text-sm font-normal"
      >
        Venue
      </div>
      <div
        class="mx-4 mt-3 grid grid-cols-[1fr_auto_1fr] items-center gap-x-2 text-2xl font-bold"
      >
        <div
          class="overflow-hidden break-words text-right"
        >
          Home Squad Nick Name
        </div>
        <div
          class="text-center"
        >
          VS
        </div>
        <div
          class="overflow-hidden break-words text-left"
        >
          Away Squad Nick Name
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<MatchDetailBanner /> renders with the playing match 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="-mx-4 rounded-none border-1 border-gray-300 bg-gray-850 pb-5 pt-2 hover:border-orange-650 md:mx-0 md:rounded-lg md:py-5"
  >
    <div
      class="mt-3 grid w-full pl-4 text-white md:hidden"
    >
      <a
        class="mb-5 cursor-pointer text-sm font-normal md:mt-0"
        href="/sport/afl/matches/"
      >
        <svg
          class="float-left mr-3"
          fill="none"
          height="16"
          viewBox="0 0 9 16"
          width="9"
        >
          <path
            d="M8 1L1 8L8 15"
            stroke="white"
            stroke-width="2"
          />
        </svg>
         Matches
      </a>
    </div>
    <div
      class="grid w-full cursor-pointer grid-cols-3 content-center gap-2 px-2 text-white md:grid-cols-5 md:gap-4"
    >
      <div
        class="grid justify-items-center md:col-span-2 md:justify-items-end"
      >
        <div
          class="flex flex-row items-center justify-center gap-4 text-2xl font-bold"
        >
          <div
            class="mt-1 hidden text-center md:block"
          >
            Home Squad Nick Name
          </div>
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Home Squad Name"
              class="size-20 p-0.5 rounded-full bg-white"
              loading="lazy"
              src="images/sports-hub/squads/squad-1010.svg"
            />
          </div>
          <div
            class="mt-1 hidden flex-col gap-y-1 text-center md:flex"
          >
            5
            <div
              class="text-sm"
            >
              2.4
            </div>
          </div>
        </div>
      </div>
      <div
        class="content-center justify-items-center gap-y-1 hidden md:grid"
      >
        <div
          class="grid content-center"
        >
          <div
            class="inline-flex shrink-0 items-center rounded-full bg-orange-650 px-3 py-1 text-xs font-medium normal-case leading-4 text-white transition-colors duration-600 ease-default focus:transition-none"
          >
            <span>
              Q
              1
            </span>
             
            20
            :
            00
          </div>
        </div>
      </div>
      <div
        class="grid place-items-center text-2xl font-extrabold md:hidden"
      >
        vs
      </div>
      <div
        class="grid justify-items-center md:col-span-2 md:justify-items-start"
      >
        <div
          class="flex flex-row items-center justify-center gap-4 text-2xl font-bold"
        >
          <div
            class="mt-1 hidden flex-col gap-y-1 text-center md:flex"
          >
            10
            <div
              class="text-sm"
            >
              5.1
            </div>
          </div>
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Away Squad Name"
              class="size-20 p-0.5 rounded-full bg-white"
              loading="lazy"
              src="images/sports-hub/squads/squad-40.svg"
            />
          </div>
          <div
            class="mt-1 hidden text-center md:block"
          >
            Away Squad Nick Name
          </div>
        </div>
      </div>
    </div>
    <div
      class="mt-3 grid w-full grid-cols-3 content-center gap-2 px-2 text-white md:hidden md:grid-cols-5 md:gap-4"
    >
      <div
        class="grid content-center justify-items-center"
      >
        <div
          class="text-2xl font-normal"
        >
          5
        </div>
        <div>
          2.4
        </div>
      </div>
      <div
        class="grid justify-items-center gap-y-1 md:col-span-3"
      >
        <div
          class="grid content-center"
        >
          <div
            class="inline-flex shrink-0 items-center rounded-full bg-orange-650 px-3 py-1 text-xs font-medium normal-case leading-4 text-white transition-colors duration-600 ease-default focus:transition-none"
          >
            <span>
              Q
              1
            </span>
             
            20
            :
            00
          </div>
        </div>
      </div>
      <div
        class="grid content-center justify-items-center"
      >
        <div
          class="text-2xl font-bold"
        >
          10
        </div>
        <div>
          5.1
        </div>
      </div>
    </div>
    <div
      class="mt-2 grid justify-items-center gap-y-1 px-4 text-white"
    >
      <div
        class="text-center text-sm font-normal"
      >
        Venue
      </div>
      <div
        class="mx-4 mt-3 grid grid-cols-[1fr_auto_1fr] items-center gap-x-2 text-2xl font-bold"
      >
        <div
          class="overflow-hidden break-words text-right"
        >
          Home Squad Nick Name
        </div>
        <div
          class="text-center"
        >
          VS
        </div>
        <div
          class="overflow-hidden break-words text-left"
        >
          Away Squad Nick Name
        </div>
      </div>
      <div
        class="mt-1 text-xs font-normal md:text-lg"
      >
        <span
          class="font-bold uppercase"
        >
          Away Squad Nick Name
        </span>
         
        by 
        5
      </div>
    </div>
  </div>
</div>
`;

exports[`<MatchDetailBanner /> renders with the scheduled match 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="-mx-4 rounded-none border-1 border-gray-300 bg-gray-850 pb-5 pt-2 hover:border-orange-650 md:mx-0 md:rounded-lg md:py-5"
  >
    <div
      class="mt-3 grid w-full pl-4 text-white md:hidden"
    >
      <a
        class="mb-5 cursor-pointer text-sm font-normal md:mt-0"
        href="/sport/afl/matches/"
      >
        <svg
          class="float-left mr-3"
          fill="none"
          height="16"
          viewBox="0 0 9 16"
          width="9"
        >
          <path
            d="M8 1L1 8L8 15"
            stroke="white"
            stroke-width="2"
          />
        </svg>
         Matches
      </a>
    </div>
    <div
      class="grid w-full cursor-pointer grid-cols-3 content-center gap-2 px-2 text-white md:grid-cols-5 md:gap-4"
    >
      <div
        class="grid justify-items-center md:col-span-2 md:justify-items-end"
      >
        <div
          class="flex flex-row items-center justify-center gap-4 text-2xl font-bold"
        >
          <div
            class="mt-1 hidden text-center md:block"
          >
            Home Squad Nick Name
          </div>
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Home Squad Name"
              class="size-20 p-0.5 rounded-full bg-white"
              loading="lazy"
              src="images/sports-hub/squads/squad-1010.svg"
            />
          </div>
          <div
            class="mt-1 hidden flex-col gap-y-1 text-center md:flex"
          >
            <div
              class="text-sm"
            >
              2.4
            </div>
          </div>
        </div>
      </div>
      <div
        class="content-center justify-items-center gap-y-1 hidden md:grid"
      >
        <div
          class="mb-2 grid text-center text-xs font-normal md:hidden"
        >
          Round 20
           - 
          Fri 1 Sept
        </div>
        <div
          class="inline-flex h-6 shrink-0 items-center rounded-full bg-green-550 px-5 text-sm font-bold text-white"
        >
          8:00 am
        </div>
        <div
          class="mt-2 hidden text-center text-xs font-normal md:grid"
        >
          Round 20
           - 
          Fri 1 Sept
        </div>
      </div>
      <div
        class="grid place-items-center text-2xl font-extrabold md:hidden"
      >
        vs
      </div>
      <div
        class="grid justify-items-center md:col-span-2 md:justify-items-start"
      >
        <div
          class="flex flex-row items-center justify-center gap-4 text-2xl font-bold"
        >
          <div
            class="mt-1 hidden flex-col gap-y-1 text-center md:flex"
          >
            <div
              class="text-sm"
            >
              5.1
            </div>
          </div>
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Away Squad Name"
              class="size-20 p-0.5 rounded-full bg-white"
              loading="lazy"
              src="images/sports-hub/squads/squad-40.svg"
            />
          </div>
          <div
            class="mt-1 hidden text-center md:block"
          >
            Away Squad Nick Name
          </div>
        </div>
      </div>
    </div>
    <div
      class="mt-3 grid w-full grid-cols-3 content-center gap-2 px-2 text-white md:hidden md:grid-cols-5 md:gap-4"
    >
      <div
        class="grid content-center justify-items-center"
      />
      <div
        class="grid justify-items-center gap-y-1 md:col-span-3"
      >
        <div
          class="mb-2 grid text-center text-xs font-normal md:hidden"
        >
          Round 20
           - 
          Fri 1 Sept
        </div>
        <div
          class="inline-flex h-6 shrink-0 items-center rounded-full bg-green-550 px-5 text-sm font-bold text-white"
        >
          8:00 am
        </div>
        <div
          class="mt-2 hidden text-center text-xs font-normal md:grid"
        >
          Round 20
           - 
          Fri 1 Sept
        </div>
      </div>
      <div
        class="grid content-center justify-items-center"
      />
    </div>
    <div
      class="mt-2 grid justify-items-center gap-y-1 px-4 text-white"
    >
      <div
        class="text-center text-sm font-normal"
      >
        Venue
      </div>
      <div
        class="mx-4 mt-3 grid grid-cols-[1fr_auto_1fr] items-center gap-x-2 text-2xl font-bold"
      >
        <div
          class="overflow-hidden break-words text-right"
        >
          Home Squad Nick Name
        </div>
        <div
          class="text-center"
        >
          VS
        </div>
        <div
          class="overflow-hidden break-words text-left"
        >
          Away Squad Nick Name
        </div>
      </div>
    </div>
  </div>
</div>
`;
