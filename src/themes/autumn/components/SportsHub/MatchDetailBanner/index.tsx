import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { SPORT_NAV_THEME } from 'themes/autumn/templates/index/IndexPageSport/theme';
import {
  SportPage,
  matchCompleteStatuses,
  matchDelayedStatuses,
  matchNotScheduledStatuses,
  matchNotStartedStatuses,
  matchPlayingStatuses,
} from 'types/SportsHub';
import { matchDateFormatted, matchTimeFormatted } from 'util/time';

import AflScoreAndBehind from '../Component/AflScoreAndBehind';
import Avatar from '../Component/Avatar';
import MatchLiveTime from '../Component/MatchLiveTime';
import TeamScore from '../Component/TeamScore';

import type { MatchDetailBannerProps } from 'types/SportsHub';

function MatchDetailBanner({
  awaySquadId,
  awaySquadName,
  awaySquadNickname,
  awaySquadOvers,
  awaySquadScore,
  awaySummary,
  currentPeriod,
  homeSquadId,
  homeSquadName,
  homeSquadNickname,
  homeSquadOvers,
  homeSquadScore,
  homeSummary,
  matchName,
  matchStatus,
  matchSummary,
  periodCompleted,
  periodScores,
  periodSeconds,
  roundNumber,
  sportPage,
  utcStartTime,
  venueCountry,
  venueName,
}: MatchDetailBannerProps): React.ReactElement | null {
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  if (!sportPage) {
    return null;
  }

  const venueComponent = (
    <div className={clsx('text-center text-sm font-normal')}>
      {venueName}
      {!!venueCountry && <>, {venueCountry}</>}
    </div>
  );

  const matchDateComponent = utcStartTime !== '' &&
    matchCompleteStatuses.includes(matchStatus) && (
      <div className="text-center text-sm font-normal">
        {matchDateFormatted(utcStartTime)}
      </div>
    );

  const matchNameComponent = !!matchName && (
    <div className="overflow-hidden break-words text-center text-sm font-bold text-white">
      {matchName}
    </div>
  );

  const matchLiveTimeComponent = matchPlayingStatuses.includes(
    matchStatus,
  ) && (
    <>
      {sportPage === SportPage.CRICKET ? (
        <div className="inline-flex h-6 shrink-0 items-center gap-2 rounded-full bg-orange-650 px-5 text-sm font-bold uppercase text-white">
          Live
          {SPORT_NAV_THEME.live.selectedIcon && (
            <span
              dangerouslySetInnerHTML={{
                __html: SPORT_NAV_THEME.live.selectedIcon,
              }}
            />
          )}
        </div>
      ) : (
        <MatchLiveTime
          currentPeriod={currentPeriod}
          periodCompleted={periodCompleted}
          periodSeconds={periodSeconds}
          sportPage={sportPage}
        />
      )}
    </>
  );

  const matchCompleteComponent = matchCompleteStatuses.includes(
    matchStatus,
  ) && (
    <div className="inline-flex h-6 shrink-0 items-center rounded-full bg-white px-5 text-sm font-bold uppercase text-gray-850">
      {sportPage === SportPage.CRICKET ? 'Completed' : 'Full time'}
    </div>
  );

  const matchDelayedComponent = matchDelayedStatuses.includes(matchStatus) && (
    <div className="inline-flex h-6 shrink-0 items-center rounded-full bg-white px-5 text-sm font-bold uppercase text-gray-850">
      Delayed
    </div>
  );

  const roundLabel =
    roundNumber === 0 ? 'Opening Round' : `Round ${roundNumber}`;

  const matchFutureComponent = matchNotStartedStatuses.includes(
    matchStatus,
  ) && (
    <>
      <div className="mb-2 grid text-center text-xs font-normal md:hidden">
        {sportPage !== SportPage.CRICKET && <>{roundLabel} - </>}
        {matchDateFormatted(utcStartTime)}
      </div>
      <div className="inline-flex h-6 shrink-0 items-center rounded-full bg-green-550 px-5 text-sm font-bold text-white">
        {matchTimeFormatted(utcStartTime)}
      </div>
      <div className="mt-2 hidden text-center text-xs font-normal md:grid">
        {sportPage !== SportPage.CRICKET && <>{roundLabel} - </>}
        {matchDateFormatted(utcStartTime)}
      </div>
    </>
  );

  const matchTeamsComponent = (
    <div
      className={clsx(
        'mx-4 mt-3 grid grid-cols-[1fr_auto_1fr] items-center gap-x-2',
        sportPage === SportPage.CRICKET
          ? 'text-lg font-semibold md:hidden'
          : 'text-2xl font-bold',
      )}
    >
      <div className="overflow-hidden break-words text-right">
        {sportPage === SportPage.CRICKET ? homeSquadName : homeSquadNickname}
      </div>
      <div className="text-center">VS</div>
      <div className="overflow-hidden break-words text-left">
        {sportPage === SportPage.CRICKET ? awaySquadName : awaySquadNickname}
      </div>
    </div>
  );

  const matchCurrentStatsComponent = awaySquadScore !== undefined &&
    homeSquadScore !== undefined &&
    awaySquadScore !== homeSquadScore &&
    matchPlayingStatuses.includes(matchStatus) && (
      <div className="mt-1 text-xs font-normal md:text-lg">
        <span className="font-bold uppercase">
          {awaySquadScore > homeSquadScore
            ? awaySquadNickname
            : homeSquadNickname}
        </span>{' '}
        by {Math.abs(awaySquadScore - homeSquadScore)}
      </div>
    );

  const matchScoreComponent = (
    <div className="mt-3 grid w-full grid-cols-3 content-center gap-2 px-2 text-white md:hidden md:grid-cols-5 md:gap-4">
      <div className="grid content-center justify-items-center">
        {matchNotScheduledStatuses.includes(matchStatus) &&
          (sportPage === SportPage.CRICKET ? (
            <>
              {!!homeSummary && (
                <div
                  className={
                    homeSummary.toLowerCase() === 'yet to bat'
                      ? 'whitespace-nowrap text-lg font-bold'
                      : 'text-2xl font-extrabold'
                  }
                >
                  {homeSummary}
                </div>
              )}
              {typeof homeSquadOvers !== 'undefined' && (
                <div className="mt-1 text-base font-normal">
                  ({homeSquadOvers})
                </div>
              )}
            </>
          ) : (
            <>
              {homeSquadScore !== undefined &&
                awaySquadScore !== undefined && (
                  <>
                    <TeamScore
                      className="text-2xl"
                      score={homeSquadScore}
                      win={homeSquadScore > awaySquadScore}
                    />
                    {periodScores && (
                      <AflScoreAndBehind
                        currentPeriod={currentPeriod}
                        periodScores={periodScores}
                        sportPage={sportPage}
                        squadId={homeSquadId}
                      />
                    )}
                  </>
                )}
            </>
          ))}
      </div>
      <div className="grid justify-items-center gap-y-1 md:col-span-3">
        {sportPage !== SportPage.CRICKET && (
          <>
            {matchLiveTimeComponent}
            {matchDelayedComponent}
            {matchCompleteComponent}
            {matchFutureComponent}
          </>
        )}
      </div>
      <div className="grid content-center justify-items-center">
        {matchNotScheduledStatuses.includes(matchStatus) &&
          (sportPage === SportPage.CRICKET ? (
            <>
              {!!awaySummary && (
                <div
                  className={
                    awaySummary.toLowerCase() === 'yet to bat'
                      ? 'whitespace-nowrap text-lg font-bold'
                      : 'text-2xl font-extrabold'
                  }
                >
                  {awaySummary}
                </div>
              )}
              {typeof awaySquadOvers !== 'undefined' && (
                <div className="mt-1 text-base font-normal">
                  ({awaySquadOvers})
                </div>
              )}
            </>
          ) : (
            <>
              {homeSquadScore !== undefined &&
                awaySquadScore !== undefined && (
                  <>
                    <TeamScore
                      className="text-2xl"
                      score={awaySquadScore}
                      win={awaySquadScore > homeSquadScore}
                    />
                    {periodScores && (
                      <AflScoreAndBehind
                        currentPeriod={currentPeriod}
                        periodScores={periodScores}
                        sportPage={sportPage}
                        squadId={awaySquadId}
                      />
                    )}
                  </>
                )}
            </>
          ))}
      </div>
    </div>
  );

  return (
    <div className="-mx-4 rounded-none border-1 border-gray-300 bg-gray-850 pb-5 pt-2 hover:border-orange-650 md:mx-0 md:rounded-lg md:py-5">
      <div className="mt-3 grid w-full pl-4 text-white md:hidden">
        <Link
          className="mb-5 cursor-pointer text-sm font-normal md:mt-0"
          href={`/sport/${sportPage}/matches/`}
          noStyle
        >
          <svg
            className="float-left mr-3"
            fill="none"
            height="16"
            viewBox="0 0 9 16"
            width="9"
          >
            <path d="M8 1L1 8L8 15" stroke="white" strokeWidth="2" />
          </svg>
          {sportPage === SportPage.CRICKET && <>ICC</>} Matches
        </Link>
      </div>
      <div
        className={clsx(
          'grid w-full cursor-pointer grid-cols-3 content-center gap-2 px-2 text-white md:grid-cols-5 md:gap-4',
        )}
      >
        <div className="grid justify-items-center md:col-span-2 md:justify-items-end">
          <div className="flex flex-row items-center justify-center gap-4 text-2xl font-bold">
            <div
              className={clsx('mt-1 hidden text-center md:block', {
                'text-lg uppercase': sportPage === SportPage.CRICKET,
              })}
            >
              {sportPage === SportPage.CRICKET
                ? homeSquadName
                : homeSquadNickname}
            </div>
            <div className="flex justify-center text-sm font-medium">
              <Avatar
                className={clsx('size-20 p-0.5', {
                  'rounded-full bg-white': sportPage !== SportPage.CRICKET,
                })}
                name={homeSquadName}
                sportPage={sportPage}
                squadId={homeSquadId}
                staticUrl={staticUrl}
              />
            </div>
            <div className="mt-1 hidden flex-col gap-y-1 text-center md:flex">
              {matchNotScheduledStatuses.includes(matchStatus) &&
                (sportPage === SportPage.CRICKET ? (
                  <>
                    {!!homeSummary && (
                      <div
                        className={
                          homeSummary.toLowerCase() === 'yet to bat'
                            ? 'whitespace-nowrap text-sm font-normal'
                            : 'text-2xl font-extrabold'
                        }
                      >
                        {homeSummary}
                      </div>
                    )}
                    {typeof homeSquadOvers !== 'undefined' && (
                      <div className="text-base font-normal">
                        ({homeSquadOvers})
                      </div>
                    )}
                  </>
                ) : (
                  <>{homeSquadScore}</>
                ))}

              {periodScores && (
                <AflScoreAndBehind
                  className="text-sm"
                  currentPeriod={currentPeriod}
                  periodScores={periodScores}
                  sportPage={sportPage}
                  squadId={homeSquadId}
                />
              )}
            </div>
          </div>
        </div>
        <div
          className={clsx(
            'content-center justify-items-center gap-y-1',
            sportPage === SportPage.CRICKET ? 'grid' : 'hidden md:grid',
          )}
        >
          {matchLiveTimeComponent}
          {matchCompleteComponent}
          {matchFutureComponent}
          {sportPage === SportPage.CRICKET && (
            <>
              {matchNameComponent}
              {venueComponent}
            </>
          )}
        </div>
        {sportPage !== SportPage.CRICKET && (
          <div className="grid place-items-center text-2xl font-extrabold md:hidden">
            vs
          </div>
        )}
        <div className="grid justify-items-center md:col-span-2 md:justify-items-start">
          <div className="flex flex-row items-center justify-center gap-4 text-2xl font-bold">
            <div className="mt-1 hidden flex-col gap-y-1 text-center md:flex">
              {matchNotScheduledStatuses.includes(matchStatus) &&
                (sportPage === SportPage.CRICKET ? (
                  <>
                    {!!awaySummary && (
                      <div
                        className={
                          awaySummary.toLowerCase() === 'yet to bat'
                            ? 'whitespace-nowrap text-sm font-normal'
                            : 'text-2xl font-extrabold'
                        }
                      >
                        {awaySummary}
                      </div>
                    )}
                    {typeof awaySquadOvers !== 'undefined' && (
                      <div className="text-base font-normal">
                        ({awaySquadOvers})
                      </div>
                    )}
                  </>
                ) : (
                  <>{awaySquadScore}</>
                ))}

              {periodScores && (
                <AflScoreAndBehind
                  className="text-sm"
                  currentPeriod={currentPeriod}
                  periodScores={periodScores}
                  sportPage={sportPage}
                  squadId={awaySquadId}
                />
              )}
            </div>
            <div className="flex justify-center text-sm font-medium">
              <Avatar
                className={clsx('size-20 p-0.5', {
                  'rounded-full bg-white': sportPage !== SportPage.CRICKET,
                })}
                name={awaySquadName}
                sportPage={sportPage}
                squadId={awaySquadId}
                staticUrl={staticUrl}
              />
            </div>
            <div
              className={clsx('mt-1 hidden text-center md:block', {
                'text-lg uppercase': sportPage === SportPage.CRICKET,
              })}
            >
              {sportPage === SportPage.CRICKET
                ? awaySquadName
                : awaySquadNickname}
            </div>
          </div>
        </div>
      </div>
      {matchScoreComponent}
      <div className="mt-2 grid justify-items-center gap-y-1 px-4 text-white">
        {matchDateComponent}
        {sportPage !== SportPage.CRICKET && venueComponent}
        {matchTeamsComponent}
        {matchCurrentStatsComponent}
        {sportPage === SportPage.CRICKET &&
          !!matchSummary &&
          matchNotScheduledStatuses.includes(matchStatus) && (
            <>{matchSummary}</>
          )}
      </div>
    </div>
  );
}

export default MatchDetailBanner;
