import React from 'react';

import { SportPage } from 'types/SportsHub';

interface AvatarProps {
  className?: string;
  name: string;
  sportPage: SportPage;
  squadId: number;
  staticUrl: string;
}

function Avatar({
  className = 'h-12 w-12 p-0.5',
  name,
  sportPage,
  squadId,
  staticUrl,
}: AvatarProps): React.ReactElement {
  const namespace = sportPage === SportPage.CRICKET ? 'cricket/' : '';
  const filename = `squad-${squadId}.svg`;
  const src =
    sportPage === SportPage.CRICKET && name.match(/^([A-Z]\d+|TBC)$/)
      ? `${staticUrl}images/sports-hub/logos/icc-logo-comp-3347.svg`
      : `${staticUrl}images/sports-hub/squads/${namespace}${filename}`;
  return <img alt={name} className={className} loading="lazy" src={src} />;
}

export default Avatar;
