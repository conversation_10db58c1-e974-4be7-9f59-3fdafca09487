import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { SportPage } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import Avatar from './index';

describe('<Avatar />', () => {
  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Avatar
          name="avatar"
          sportPage={SportPage.AFL}
          squadId={1}
          staticUrl="/"
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
