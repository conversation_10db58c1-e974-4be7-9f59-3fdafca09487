/* eslint-disable import/prefer-default-export */
import { GameInsightColorPalette } from './enums';

import type { PalettesType } from './types.js';

export const PALETTES: Record<GameInsightColorPalette, PalettesType> = {
  [GameInsightColorPalette.PALETTE_GREEN]: {
    border: 'border-green-550',
    text: 'text-green-550',
  },
  [GameInsightColorPalette.PALETTE_ORANGE]: {
    border: 'border-orange-650',
    text: 'text-orange-650',
  },
};
