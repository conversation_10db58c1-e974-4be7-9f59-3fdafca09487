import fs from 'fs';
import path from 'path';

import { fireEvent, render, screen } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { SportPage } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import { GameInsight } from './index';

import type { MatchExtraProps } from 'types/SportsHub';

describe('<GameInsight />', () => {
  const mockExtraData = JSON.parse(
    fs.readFileSync(path.join(__dirname, '../../mocks/extra.json')).toString(),
  ) as MatchExtraProps;

  it('renders with one tab', () => {
    expect.assertions(3);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <GameInsight
          awaySquadName="awaySquadName"
          enableReadMore
          extra={mockExtraData}
          homeSquadName="homeSquadName"
          matchId={1}
          showHeading
          sportPage={SportPage.NRL}
        />
      </TestWrapper>,
    );
    expect(screen.queryByTestId('away-team')).toBeNull();
    expect(screen.getByTestId('home-team')).not.toBeNull();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with two tab', () => {
    expect.assertions(4);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <GameInsight
          awaySquadName="awaySquadName"
          enableReadMore
          extra={mockExtraData}
          homeSquadName="homeSquadName"
          matchId={1}
          showHeading
          sportPage={SportPage.AFL}
        />
      </TestWrapper>,
    );
    const awayTeamSection = screen.queryByTestId('away-team');
    const homeTeamSection = screen.queryByTestId('home-team');
    expect(awayTeamSection).not.toBeNull();
    expect(homeTeamSection).not.toBeNull();

    const homeTeamButton = screen.getByTestId('home-team-button');
    fireEvent.click(homeTeamButton);
    expect(awayTeamSection?.classList.contains('hidden')).toBe(true);

    expect(container.firstChild).toMatchSnapshot();
  });
});
