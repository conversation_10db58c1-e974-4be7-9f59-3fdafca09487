/* eslint-disable import/prefer-default-export */

'use client';

import clsx from 'clsx';
import React, { useState } from 'react';

import Link from 'themes/autumn/components/generic/Link';
import { SportPage } from 'types/SportsHub';

import { PALETTES } from './constants';
import { GameInsightColorPalette } from './enums';

import type { PalettesType } from './types';
import type { MatchExtraProps } from 'types/SportsHub';

interface GameInsightProps {
  awaySquadName: string;
  colorPalette?: GameInsightColorPalette;
  enableReadMore?: boolean;
  extra: MatchExtraProps;
  homeSquadName: string;
  listClassName?: string;
  matchId?: number;
  showHeading?: boolean;
  sportPage: SportPage;
}

interface ReadMoreProps {
  matchId: number;
  sportPage: SportPage;
}

function ReadMore({ matchId, sportPage }: ReadMoreProps) {
  return (
    <Link
      className="pl-5 text-sm text-green-650 underline"
      href={`/sport/${sportPage}/match/${matchId}/`}
      noStyle
    >
      Read more
    </Link>
  );
}

interface TwoTabsProps {
  awaySquadName: string;
  awayTeamInsight: string;
  enableReadMore: boolean;
  homeSquadName: string;
  homeTeamInsight: string;
  listClassName: string;
  selectedColorPalette: PalettesType;
}

function TwoTabs({
  awaySquadName,
  awayTeamInsight,
  enableReadMore,
  homeSquadName,
  homeTeamInsight,
  listClassName,
  selectedColorPalette,
}: TwoTabsProps): React.ReactElement {
  const [selectHomeTeam, setSelectHomeTeam] = useState(true);
  const selectedTheme = selectHomeTeam
    ? // eslint-disable-next-line @stylistic/max-len
      `${selectedColorPalette.border} ${selectedColorPalette.text} text-sm font-bold`
    : 'text-sm font-normal';
  const unSelectedTheme = !selectHomeTeam
    ? // eslint-disable-next-line @stylistic/max-len
      `${selectedColorPalette.border} ${selectedColorPalette.text} text-sm font-bold`
    : 'text-sm font-normal';

  const component = (
    <>
      <div
        className={clsx({
          hidden: !selectHomeTeam,
        })}
        data-testid="home-team"
      >
        <div
          className={clsx('pt-4', listClassName)}
          dangerouslySetInnerHTML={{ __html: homeTeamInsight }}
        />
      </div>
      <div
        className={clsx({
          hidden: selectHomeTeam,
        })}
        data-testid="away-team"
      >
        <div
          className={clsx('pt-4', listClassName)}
          dangerouslySetInnerHTML={{ __html: awayTeamInsight }}
        />
      </div>
    </>
  );

  return (
    <>
      <div className="mt-4 grid grid-cols-2 content-center">
        <button
          className={clsx(
            'grid cursor-pointer justify-items-start border-b-3 pb-4 pl-5',
            selectedTheme,
          )}
          data-testid="home-team-button"
          onClick={() => {
            setSelectHomeTeam(true);
          }}
          type="button"
        >
          {homeSquadName.toUpperCase()}
        </button>
        <button
          className={clsx(
            'grid cursor-pointer justify-items-end border-b-3 pb-4 pr-5',
            unSelectedTheme,
          )}
          data-testid="away-team-button"
          onClick={() => {
            setSelectHomeTeam(false);
          }}
          type="button"
        >
          {awaySquadName.toUpperCase()}
        </button>
      </div>
      {enableReadMore && (
        <div className="pointer-events-none pl-5 gradient-mask-b-80">
          {component}
        </div>
      )}
      {!enableReadMore && component}
    </>
  );
}

interface OneTabProps {
  enableReadMore: boolean;
  homeTeamInsight: string;
}

function OneTab({
  enableReadMore,
  homeTeamInsight,
}: OneTabProps): React.ReactElement {
  const component = (
    <div
      className={clsx('pt-4 text-sm')}
      dangerouslySetInnerHTML={{ __html: homeTeamInsight }}
      data-testid="home-team"
    />
  );
  return (
    <>
      {enableReadMore && (
        <div className="pointer-events-none pl-5 gradient-mask-b-80">
          {component}
        </div>
      )}
      {!enableReadMore && component}
    </>
  );
}

export function GameInsight({
  awaySquadName,
  colorPalette = GameInsightColorPalette.PALETTE_ORANGE,
  enableReadMore = false,
  extra,
  homeSquadName,
  listClassName = 'list-disc text-sm text-slate-750',
  matchId,
  showHeading = true,
  sportPage,
}: GameInsightProps): React.ReactElement {
  const selectedColorPalette = PALETTES[colorPalette];
  return (
    <>
      {showHeading && (
        <div className="text-lg font-extrabold">Pre-Game Insights</div>
      )}
      {sportPage === SportPage.AFL && (
        <TwoTabs
          awaySquadName={awaySquadName}
          awayTeamInsight={extra.awaySquadPreview}
          enableReadMore={enableReadMore}
          homeSquadName={homeSquadName}
          homeTeamInsight={extra.homeSquadPreview}
          listClassName={listClassName}
          selectedColorPalette={selectedColorPalette}
        />
      )}
      {sportPage === SportPage.NRL && (
        <OneTab
          enableReadMore={enableReadMore}
          homeTeamInsight={extra.homeSquadPreview}
        />
      )}
      {enableReadMore && !!matchId && (
        <ReadMore matchId={matchId} sportPage={sportPage} />
      )}
    </>
  );
}
