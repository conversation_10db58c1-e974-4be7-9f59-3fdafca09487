import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { Sport } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import ScoreRecord from './index';

describe('<ScoreRecord />', () => {
  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <ScoreRecord
          firstname="first name"
          period={1}
          periodSeconds={1200}
          sport={Sport.AFL}
          surname="surname"
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
