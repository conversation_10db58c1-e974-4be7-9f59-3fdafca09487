import React from 'react';

import { getMinsPerPeriod } from '../../utils';

import type { Sport } from 'types/SportsHub';

interface ScoreRecordProps {
  firstname: string;
  period: number;
  periodSeconds: number;
  sport: Sport;
  surname: string;
}

function ScoreRecord({
  firstname,
  period,
  periodSeconds,
  sport,
  surname,
}: ScoreRecordProps) {
  const minsPerPeriod = getMinsPerPeriod(sport);
  const minutes =
    (period - 1) * minsPerPeriod + Math.round(periodSeconds / 60);
  return (
    <div className="pb-2 text-sm font-normal">
      {firstname} {surname} {minutes}&apos;
    </div>
  );
}

export default ScoreRecord;
