import clsx from 'clsx';
import React from 'react';

interface TeamScoreProps {
  className?: string;
  score: number;
  win: boolean;
}

function TeamScore({
  className = 'text-base',
  score,
  win,
}: TeamScoreProps): React.ReactElement {
  return (
    <div
      className={clsx(
        className,
        { 'font-normal': !win },
        { 'font-bold': win },
      )}
    >
      {score}
    </div>
  );
}

export default TeamScore;
