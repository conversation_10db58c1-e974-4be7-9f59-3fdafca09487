import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import TeamScore from './index';

describe('<TeamScore />', () => {
  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <TeamScore score={3} win />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
