import fs from 'fs';
import path from 'path';

import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { SportPage } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import AflScoreAndBehind from './index';

import type { SportMatchDetailResponse } from 'util/organization/suzuka';

describe('<AflScoreAndBehind />', () => {
  const mockData = JSON.parse(
    fs
      .readFileSync(path.join(__dirname, '../../mocks/aflStats.json'))
      .toString(),
  ) as SportMatchDetailResponse;
  const periodScores = mockData.stats?.data.matchScores?.periodScores ?? [];

  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <AflScoreAndBehind
          className="text-sm"
          currentPeriod={1}
          periodScores={periodScores}
          sportPage={SportPage.AFL}
          squadId={1010}
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
