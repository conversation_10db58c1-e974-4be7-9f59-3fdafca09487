import React from 'react';

import { SportPage } from 'types/SportsHub';

import { getAflGoalAndBehind } from '../../utils';

import type { PeriodScoresProps } from 'types/SportsHub';

interface AflScoreAndBehindProps {
  className?: string;
  currentPeriod: number;
  periodScores: PeriodScoresProps[];
  sportPage: SportPage;
  squadId: number;
}

function Index({
  className,
  currentPeriod,
  periodScores,
  sportPage,
  squadId,
}: AflScoreAndBehindProps): React.ReactElement | null {
  if (sportPage !== SportPage.AFL) {
    return null;
  }

  const scoreAndBehind = getAflGoalAndBehind(
    currentPeriod,
    periodScores,
    squadId,
  );

  if (!scoreAndBehind) {
    return null;
  }

  return <div className={className}>{scoreAndBehind}</div>;
}

export default Index;
