import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import { sendToGtm } from 'util/gtm';

interface SponsorButtonProps {
  channel?: string;
  className?: string;
}

function KayoSportsButton({
  channel = 'non_widget',
  className = 'mt-4',
}: SponsorButtonProps): React.ReactElement {
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  const src = `${staticUrl}images/sports-hub/sponsors/kayo-sports.svg`;
  const href = `https://kayosports.com.au/?campaign=kayo&channel=${channel}`;
  const gtmSuffix = channel === 'widget' ? 'upcoming_matches' : 'others';
  return (
    <a
      className={clsx(
        'inline-block h-9 w-[178px] rounded-full bg-[#40F87F] text-center text-sm font-semibold text-black',
        className,
      )}
      href={href}
      onClick={() => {
        sendToGtm({
          label: `kayo_impression_${gtmSuffix}`,
          trigger: `kayo_impression_${gtmSuffix}_trigger`,
        });
      }}
      rel="noopener"
      target="_blank"
    >
      Watch it on
      <img
        alt="Watch it on Kayo Sports"
        className="mx-[-2px] !inline-block"
        src={src}
        width="52"
      />
      <svg
        className="inline-block"
        fill="none"
        height="14"
        viewBox="0 0 14 14"
      >
        <path
          d="M0.833374 11.0534C0.858193 12.1703 1.96265 12.8473 2.93062 12.3151L10.7115 7.73861C11.1348 7.49042 11.423 7.03401 11.423 6.49764C11.423 5.96128 11.1348 5.50486 10.7115 5.2553L2.93062 0.68587C1.96265 0.153635 0.858189 0.823755 0.833374 1.94201V11.0534Z"
          fill="black"
        />
      </svg>
    </a>
  );
}

export default KayoSportsButton;
