import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import KayoSportsButton from '.';

describe('<KayoSportsButton />', () => {
  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
      settings: {
        ...state.settings,
        staticUrl: '/',
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <KayoSportsButton />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
