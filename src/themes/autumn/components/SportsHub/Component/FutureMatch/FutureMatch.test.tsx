import { screen } from '@testing-library/dom';
import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { MatchStatus } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import * as utils from '../../utils';

import FutureMatch from './index';

describe('<FutureMatch />', () => {
  it('renders with days is larger than 1', () => {
    expect.assertions(2);

    const daysToGoMock = jest.spyOn(utils, 'daysToGo');
    daysToGoMock.mockImplementation(() => 3);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <FutureMatch
          matchStatus={MatchStatus.MATCH_STATUS_SCHEDULED}
          utcStartTime=""
        />
      </TestWrapper>,
    );
    expect(screen.getByText(/days/i)).toBeInTheDocument();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with days is equal to 1', () => {
    expect.assertions(2);

    const daysToGoMock = jest.spyOn(utils, 'daysToGo');
    daysToGoMock.mockImplementation(() => 1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <FutureMatch
          matchStatus={MatchStatus.MATCH_STATUS_SCHEDULED}
          utcStartTime=""
        />
      </TestWrapper>,
    );
    expect(screen.queryByText(/days/i)).toBeNull();
    expect(container.firstChild).toMatchSnapshot();
  });
});
