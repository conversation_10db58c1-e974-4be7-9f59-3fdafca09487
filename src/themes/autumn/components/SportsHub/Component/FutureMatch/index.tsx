import React from 'react';

import { MatchStatus } from 'types/SportsHub';
import { useDate } from 'util/time';

import { daysToGo } from '../../utils';

interface FutureMatchProps {
  matchStatus: MatchStatus;
  utcStartTime: string;
}

function FutureMatch({
  matchStatus,
  utcStartTime,
}: FutureMatchProps): React.ReactElement | null {
  const days = daysToGo(utcStartTime, useDate());

  if (
    (matchStatus !== MatchStatus.MATCH_STATUS_SCHEDULED &&
      matchStatus !== MatchStatus.MATCH_STATUS_PREMATCH) ||
    days <= 0
  ) {
    return null;
  }

  const daysLabel = `${days} day${days > 1 ? 's' : ''}`;
  return (
    <div className="-mx-4 mb-4 grid bg-teal-150 py-4 md:mx-0 md:mt-4 md:place-content-center">
      <div className="grid w-full grid-cols-1 content-center md:w-[400px]">
        <div className="text-center text-xs text-black">
          <span>Game in</span>&nbsp;
          <span className="text-sm font-extrabold">{daysLabel}</span>
        </div>
      </div>
    </div>
  );
}

export default FutureMatch;
