import React from 'react';

import { Competition, SportPage } from 'types/SportsHub';

import {
  formatMinutesLabel,
  getMinsPerPeriod,
  getMinutesInGamePeriod,
  getNumOfPeriods,
  getOrdinalIndicator,
  getSeconds,
} from '../../utils';

interface MatchLiveTimeProps {
  className?: string;
  currentPeriod: number;
  periodCompleted: number;
  periodSeconds: number;
  sportPage: SportPage;
}

interface PeriodLabelProps {
  currentPeriod: number;
  sportPage: SportPage;
}

function PeriodLabel({
  currentPeriod,
  sportPage,
}: PeriodLabelProps): React.ReactElement {
  if (sportPage === SportPage.AFL) {
    return <span>Q{currentPeriod}</span>;
  }
  const label = `${currentPeriod}${getOrdinalIndicator(
    currentPeriod,
  ).toUpperCase()}`;
  return (
    <>
      {label}&nbsp;<span className="hidden min-[370px]:inline">HALF</span>
      <span className="min-[370px]:hidden">HL</span>
    </>
  );
}

function MatchLiveTime({
  className = 'grid content-center',
  currentPeriod,
  periodCompleted,
  periodSeconds,
  sportPage,
}: MatchLiveTimeProps): React.ReactElement {
  const { sport } = Competition[sportPage];
  const numOfPeriods = getNumOfPeriods(sport);
  const minsPerPeriod = getMinsPerPeriod(sport);
  const minutes = getMinutesInGamePeriod(
    numOfPeriods,
    periodCompleted,
    periodSeconds,
    minsPerPeriod,
  );
  const seconds = getSeconds(
    numOfPeriods,
    periodCompleted,
    periodSeconds,
    minsPerPeriod,
  );

  return (
    <div className={className}>
      <div className="inline-flex shrink-0 items-center rounded-full bg-orange-650 px-3 py-1 text-xs font-medium normal-case leading-4 text-white transition-colors duration-600 ease-default focus:transition-none">
        <PeriodLabel currentPeriod={currentPeriod} sportPage={sportPage} />
        &nbsp;
        {formatMinutesLabel(minutes)}:{seconds}
      </div>
    </div>
  );
}

export default MatchLiveTime;
