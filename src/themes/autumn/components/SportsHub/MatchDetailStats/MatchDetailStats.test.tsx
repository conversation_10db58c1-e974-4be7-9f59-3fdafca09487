import fs from 'fs';
import path from 'path';

import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { CompleteProfileEnrichment } from 'store/slices/features';
import { createStore } from 'store/store';
import { MatchStatus, SportPage } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import MatchDetailStats from './index';

import type { EnhancedStore } from '@reduxjs/toolkit';
import type { RootState } from 'store/store';
import type { SportMatchDetailResponse } from 'util/organization/suzuka';

describe('<MatchDetailStats />', () => {
  let store: EnhancedStore<RootState>;

  beforeEach(() => {
    store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
      features: {
        ...state.features,
        piano: {
          data: {
            aid: 'test',
            apiToken: 'test',
            articlePaywallHeadingText: '',
            betaResourceId: 'test',
            completeProfileEnrichments: CompleteProfileEnrichment.NONE,
            ctaVariant: 'test',
            enterpriseSubscriptions: [],
            hasSocialScreen: true,
            header: 'test',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: true,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: true,
            isPianoSsoConfirmationDisabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: true,
            registrationOnly: false,
            siteId: '1',
            subColour: '1',
            subHeader: 'test',
            supportAuthServer: false,
            supportAuthServerPaywall: false,
            supportLoginApple: false,
            supportLoginFacebook: false,
            supportLoginGoogle: false,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
      piano: {
        ...state.piano,
        hasPaywall: false,
        loadingPaywall: false,
      },
    }));
  });

  const mockAflData = JSON.parse(
    fs.readFileSync(path.join(__dirname, '../mocks/aflStats.json')).toString(),
  ) as SportMatchDetailResponse;
  const aflPeriodScores =
    mockAflData.stats?.data.matchScores?.periodScores ?? [];
  const aflScoreFlows = mockAflData.stats?.data.scoreFlow?.score ?? [];

  it('renders with afl match detail page', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <MatchDetailStats
          awaySquadId={1010}
          awaySquadName="Giants"
          awaySquadNickname="Giants"
          currentPeriod={1}
          homeSquadId={40}
          homeSquadName="Magpies"
          homeSquadNickname="Magpies"
          matchStatus={MatchStatus.MATCH_STATUS_COMPLETE}
          periodScores={aflPeriodScores}
          scoreFlows={aflScoreFlows}
          showScorecard={false}
          sportPage={SportPage.AFL}
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  const mockLeagueData = JSON.parse(
    fs
      .readFileSync(path.join(__dirname, '../mocks/leagueStats.json'))
      .toString(),
  ) as SportMatchDetailResponse;
  const leagueScoreFlows = mockLeagueData.stats?.data.scoreFlow?.score ?? [];

  it('renders with NRL match detail page', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <MatchDetailStats
          awaySquadId={334}
          awaySquadName="Wests Tigers"
          awaySquadNickname="Wests Tigers"
          currentPeriod={2}
          homeSquadId={336}
          homeSquadName="Sea Eagles"
          homeSquadNickname="Sea Eagles"
          matchStatus={MatchStatus.MATCH_STATUS_COMPLETE}
          scoreFlows={leagueScoreFlows}
          showScorecard={false}
          sportPage={SportPage.NRL}
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  const mockSoccerData = JSON.parse(
    fs
      .readFileSync(path.join(__dirname, '../mocks/soccerStats.json'))
      .toString(),
  ) as SportMatchDetailResponse;
  const soccerScoreFlows = mockSoccerData.stats?.data.scoreFlow?.score ?? [];

  it('renders with A-League match detail page', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <MatchDetailStats
          awaySquadId={8846}
          awaySquadName="Wests Tigers"
          awaySquadNickname="Wests Tigers"
          currentPeriod={2}
          homeSquadId={7041}
          homeSquadName="Western United FC"
          homeSquadNickname="Western United FC"
          matchStatus={MatchStatus.MATCH_STATUS_COMPLETE}
          scoreFlows={soccerScoreFlows}
          showScorecard={false}
          sportPage={SportPage.A_LEAGUE}
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
