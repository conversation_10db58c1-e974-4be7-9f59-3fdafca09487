'use client';

import clsx from 'clsx';
import React, { useState } from 'react';

import {
  InningsEntryCode,
  matchNotScheduledStatuses,
  matchPlayingStatuses,
} from 'types/SportsHub';

import type { CricketStatsProps } from 'types/SportsHub';

function CricketStats({
  inningNumber,
  inningsSummary,
  matchStatus,
  showScorecard,
}: CricketStatsProps): React.ReactElement | null {
  const [inningsSelected, setInningsSelected] = useState(0);

  const scoreDetailComponent = showScorecard &&
    inningsSummary &&
    matchNotScheduledStatuses.includes(matchStatus) && (
      <div className="mb-6">
        <div className="inline-block">
          <div className="flex items-stretch rounded-md bg-gray-100 p-0.5">
            {inningsSummary.innings.map((innings, index) => (
              <button
                className={clsx(
                  'px-5 py-4 font-inter text-xs font-medium md:px-10 md:text-sm',
                  {
                    'rounded-md border bg-white': index === inningsSelected,
                  },
                )}
                key={innings.battingSquadId}
                onClick={() => setInningsSelected(index)}
                type="button"
              >
                {innings.battingSquadName} Innings
              </button>
            ))}
          </div>
        </div>
        {!!inningsSummary.innings.length && (
          <div className="mt-4">
            {inningsSummary.innings.map((innings, index) => (
              <div
                className={clsx('mt-10', {
                  hidden: index !== inningsSelected,
                })}
                key={innings.battingSquadId}
              >
                <h2 className="mb-6 text-heading4 font-bold">Batting</h2>

                <table className="w-full divide-y divide-gray-300 border-b border-gray-300 text-xs">
                  <thead>
                    <tr className="divide-gray-300 text-center text-orange-650 md:divide-x">
                      <th
                        className="pb-3.5 pr-3 text-left lg:w-1/2"
                        scope="col"
                      >
                        Batters
                      </th>
                      <th className="px-3 pb-3.5" scope="col">
                        R
                      </th>
                      <th className="px-3 pb-3.5" scope="col">
                        B
                      </th>
                      <th className="px-3 pb-3.5" scope="col">
                        4s
                      </th>
                      <th className="px-3 pb-3.5" scope="col">
                        6s
                      </th>
                      <th
                        className="hidden px-3 pb-3.5 md:table-cell"
                        scope="col"
                      >
                        S/R
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-300 bg-white text-center text-xs">
                    {innings.entry
                      ?.filter(
                        (entry) =>
                          entry.code ===
                            InningsEntryCode.INNINGS_ENTRY_CODE_BATTER &&
                          entry.status,
                      )
                      .map((entry) => (
                        <tr
                          className="divide-gray-300 text-xs text-slate-750 md:divide-x"
                          key={`${entry.firstname}${entry.surname}`}
                        >
                          <td className="py-3 pr-3 text-left">
                            <div className="grid-cols-2 gap-x-4 md:grid">
                              <div className="text-xs font-medium md:text-sm">
                                {entry.firstname}{' '}
                                <span className="font-semibold">
                                  {entry.surname}
                                </span>
                              </div>
                              <div
                                className={clsx(
                                  'text-xs md:text-sm',
                                  entry.status === 'not out'
                                    ? 'text-green-600'
                                    : 'text-gray-500',
                                )}
                              >
                                {entry.status === 'not out'
                                  ? 'Not out'
                                  : entry.status}
                                {!!entry.onStrike && <> &#127951;</>}
                              </div>
                            </div>
                          </td>
                          <td className="p-3 font-bold">{entry.runs}</td>
                          <td className="p-3">{entry.ballsfaced}</td>
                          <td className="p-3">{entry.fours}</td>
                          <td className="p-3">{entry.sixes}</td>
                          <td className="hidden p-3 md:table-cell">
                            {entry.strikeRate}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>

                {innings.entry?.find(
                  (entry) =>
                    entry.code ===
                      InningsEntryCode.INNINGS_ENTRY_CODE_BATTER &&
                    !entry.status,
                ) && (
                  <>
                    <h2 className="mb-2 mt-6 text-sm font-bold">
                      {matchPlayingStatuses.includes(matchStatus) &&
                      innings.number >= (inningNumber || 1) ? (
                        <>Yet to bat</>
                      ) : (
                        <>Did not bat</>
                      )}
                      :
                    </h2>
                    <div className="text-xs text-gray-500">
                      {innings.entry
                        ?.filter(
                          (entry) =>
                            entry.code ===
                              InningsEntryCode.INNINGS_ENTRY_CODE_BATTER &&
                            !entry.status,
                        )
                        .sort((a, b) =>
                          a.battingOrder && b.battingOrder
                            ? a.battingOrder - b.battingOrder
                            : 0,
                        )
                        .map((entry, index2) => (
                          <>
                            {!!index2 && <span className="px-1">&#8226;</span>}
                            <span key={`${entry.firstname}${entry.surname}`}>
                              {entry.firstname} {entry.surname}
                            </span>
                          </>
                        ))}
                    </div>
                  </>
                )}

                <div className="mt-6 bg-gray-50 p-4">
                  <div className="flex justify-between">
                    <div className="text-xs uppercase text-gray-500">
                      Extras
                    </div>
                    <div className="justify-self-end text-xs text-gray-500">
                      {innings.entry
                        ?.filter(
                          (entry) =>
                            entry.code ===
                            InningsEntryCode.INNINGS_ENTRY_CODE_EXTRAS,
                        )
                        ?.reduce((acc, it) => acc + (it.runs || 0), 0) || 0}
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <div className="text-sm font-semibold uppercase">
                      Total
                    </div>
                    <div className="justify-self-end text-sm font-semibold">
                      {innings.runs} ({innings.wickets} wickets,{' '}
                      {innings.overs} overs)
                    </div>
                  </div>
                </div>

                <h2 className="mb-4 mt-14 text-heading4 font-bold">Bowling</h2>

                <table className="w-full divide-y divide-gray-300 border-b border-gray-300 text-xs">
                  <thead>
                    <tr className="divide-gray-300 text-center text-orange-650 md:divide-x">
                      <th
                        className="pb-3.5 pr-3 text-left lg:w-1/2"
                        scope="col"
                      >
                        Bowlers
                      </th>
                      <th className="px-3 pb-3.5" scope="col">
                        O
                      </th>
                      <th className="px-3 pb-3.5" scope="col">
                        M
                      </th>
                      <th className="px-3 pb-3.5" scope="col">
                        R
                      </th>
                      <th className="px-3 pb-3.5" scope="col">
                        W
                      </th>
                      <th
                        className="hidden px-3 pb-3.5 md:table-cell"
                        scope="col"
                      >
                        Eco
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-300 bg-white text-center text-xs">
                    {innings.entry
                      ?.filter(
                        (entry) =>
                          entry.code ===
                          InningsEntryCode.INNINGS_ENTRY_CODE_BOWLER,
                      )
                      .map((entry) => (
                        <tr
                          className="divide-gray-300 text-xs text-slate-750 md:divide-x"
                          key={`${entry.firstname}${entry.surname}`}
                        >
                          <td className="py-3 pr-3 text-left text-xs font-medium md:text-sm">
                            {entry.firstname}{' '}
                            <span className="font-semibold">
                              {entry.surname}
                            </span>
                          </td>
                          <td className="p-3">{entry.overs}</td>
                          <td className="p-3">{entry.maidens}</td>
                          <td className="p-3">{entry.runs}</td>
                          <td className="p-3">{entry.wickets}</td>
                          <td className="hidden p-3 md:table-cell">
                            {entry.economy}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            ))}
          </div>
        )}
      </div>
    );

  const lineupComponent = !showScorecard &&
    inningsSummary &&
    matchNotScheduledStatuses.includes(matchStatus) && (
      <div className="my-6 flex flex-col gap-y-12 md:grid md:flex-none md:grid-cols-2 md:gap-x-12">
        {inningsSummary.innings?.map((innings) => (
          <div key={innings.bowlingSquadId}>
            <div className="rounded-md border-1 border-gray-300">
              <div className="flex items-center rounded-t-md border-b border-gray-300 bg-teal-150 px-6 py-2">
                <div className="font-semibold">{innings.bowlingSquadName}</div>
              </div>
              {innings.entry
                ?.filter(
                  (entry) =>
                    entry.code === InningsEntryCode.INNINGS_ENTRY_CODE_BATTER,
                )
                .sort((a, b) =>
                  a.battingOrder && b.battingOrder
                    ? a.battingOrder - b.battingOrder
                    : 0,
                )
                .map((entry) => (
                  <div
                    className="border-b-1 border-gray-300 px-6 py-4"
                    key={`${entry.firstname}${entry.surname}`}
                  >
                    {entry.firstname}{' '}
                    <span className="font-semibold">{entry.surname}</span>
                  </div>
                ))}
            </div>
          </div>
        ))}
      </div>
    );

  return (
    <>
      {scoreDetailComponent}
      {lineupComponent}
    </>
  );
}

export default CricketStats;
