import React from 'react';

import { useAppSelector } from 'store/hooks';
import { MatchStatus, ScoreNameType, Sport } from 'types/SportsHub';

import PianoPaywall from '../../generic/PianoPaywall';
import ScoreRecord from '../Component/ScoreRecord';
import TeamScore from '../Component/TeamScore';

import type { NrlStatsProps, ScoreFlow } from 'types/SportsHub';

function getNrlTries(scoreFlows: ScoreFlow[], squadId: number): ScoreFlow[] {
  return scoreFlows.filter(
    (scoreFlow) =>
      scoreFlow.squadId === squadId &&
      scoreFlow.scoreName === ScoreNameType.TRY,
  );
}

function LeagueStats({
  awaySquadId,
  awaySquadNickname,
  homeSquadId,
  homeSquadNickname,
  matchStatus,
  scoreFlows,
}: NrlStatsProps): React.ReactElement | null {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const loadingPaywall = useAppSelector((state) => state.piano.loadingPaywall);
  const hasPaywall = useAppSelector((state) => state.piano.hasPaywall);
  const hasPianoPaywall =
    pianoFeature.enabled && (loadingPaywall || hasPaywall);
  const homeScore = getNrlTries(scoreFlows, homeSquadId);
  const awayScore = getNrlTries(scoreFlows, awaySquadId);

  const nrlScoreComponent = (
    <div className="-mx-4 grid bg-teal-150 py-4 md:mx-0 md:mt-4 md:place-content-center">
      <div className="grid w-full grid-cols-3 content-center md:w-[400px]">
        <div className="grid justify-items-start pl-5 md:pl-0">
          <TeamScore
            score={homeScore.length}
            win={homeScore.length > awayScore.length}
          />
        </div>
        <div className="grid justify-items-center text-black">TRIES</div>
        <div className="grid justify-items-end pr-5 md:pr-0">
          <TeamScore
            score={awayScore.length}
            win={awayScore.length > homeScore.length}
          />
        </div>
      </div>
    </div>
  );

  const scoreComponent = (matchStatus === MatchStatus.MATCH_STATUS_PLAYING ||
    matchStatus === MatchStatus.MATCH_STATUS_POSTMATCH ||
    matchStatus === MatchStatus.MATCH_STATUS_COMPLETE) && (
    <>{nrlScoreComponent}</>
  );

  const scoreDetailComponent = (matchStatus ===
    MatchStatus.MATCH_STATUS_PLAYING ||
    matchStatus === MatchStatus.MATCH_STATUS_POSTMATCH ||
    matchStatus === MatchStatus.MATCH_STATUS_COMPLETE) &&
    scoreFlows && (
      <div className="grid md:place-content-center">
        <div className="mt-4 grid w-full grid-cols-2 content-center divide-x md:w-[400px]">
          <div className="grid justify-items-start">
            <div className="flex flex-col">
              <div className="mb-4 text-sm font-bold">{homeSquadNickname}</div>
              {homeScore.map((scoreFlow) => (
                <ScoreRecord
                  firstname={scoreFlow.firstname}
                  key={scoreFlow.periodSeconds}
                  period={scoreFlow.period}
                  periodSeconds={scoreFlow.periodSeconds}
                  sport={Sport.LEAGUE}
                  surname={scoreFlow.surname}
                />
              ))}
            </div>
          </div>
          <div className="grid justify-items-end">
            <div className="flex flex-col text-right">
              <div className="mb-4 text-sm font-bold">{awaySquadNickname}</div>
              {awayScore.map((scoreFlow) => (
                <ScoreRecord
                  firstname={scoreFlow.firstname}
                  key={scoreFlow.periodSeconds}
                  period={scoreFlow.period}
                  periodSeconds={scoreFlow.periodSeconds}
                  sport={Sport.LEAGUE}
                  surname={scoreFlow.surname}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    );

  return (
    <>
      {hasPianoPaywall && (
        <>
          <div className="pointer-events-none gradient-mask-b-0">
            {scoreComponent}
          </div>
          <div className="mx-auto my-6 max-w-sm px-4 md:px-0">
            <PianoPaywall />
          </div>
        </>
      )}
      {!hasPianoPaywall && (
        <>
          {scoreComponent}
          {scoreDetailComponent}
        </>
      )}
    </>
  );
}

export default LeagueStats;
