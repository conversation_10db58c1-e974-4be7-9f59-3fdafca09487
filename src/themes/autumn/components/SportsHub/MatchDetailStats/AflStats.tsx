import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import { MatchStatus, SportPage } from 'types/SportsHub';

import PianoPaywall from '../../generic/PianoPaywall';
import Avatar from '../Component/Avatar';
import GoalRecord from '../Component/GoalRecord';
import { getAflGoals, getAflScores } from '../utils';

import type { AflStatsProps, PeriodScoresProps } from 'types/SportsHub';

interface AflPeriodScoresProps {
  currentPeriod: number;
  matchStatus: MatchStatus;
  periodScores: PeriodScoresProps[];
}

function AflPeriodScores({
  currentPeriod,
  matchStatus,
  periodScores,
}: AflPeriodScoresProps): React.ReactElement {
  let scoreTotal = 0;
  let periodGoalsTotal = 0;
  let periodBehindTotal = 0;
  return (
    <>
      {periodScores.map((periodScore, index) => {
        scoreTotal += periodScore.score;
        periodGoalsTotal += periodScore.goals;
        periodBehindTotal += periodScore.behinds;
        return (
          <div
            className={clsx(
              'grid content-center justify-items-center border-r-1 py-1.5 text-xs font-bold',
              {
                'border-orange-650':
                  (currentPeriod === index + 1 ||
                    currentPeriod === index + 2) &&
                  matchStatus === MatchStatus.MATCH_STATUS_PLAYING,
              },
            )}
            key={`${periodScore.squadId}-${periodScore.period}`}
          >
            {currentPeriod > index && (
              <div className="flex flex-col">
                <div className="text-center text-lg font-bold">
                  {scoreTotal}
                </div>
                <div className="text-center text-xs font-normal">
                  {periodGoalsTotal}.{periodBehindTotal}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </>
  );
}

function AflStats({
  awaySquadId,
  awaySquadNickname,
  currentPeriod,
  homeSquadId,
  homeSquadNickname,
  matchStatus,
  periodScores,
  scoreFlows,
}: AflStatsProps): React.ReactElement | null {
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const loadingPaywall = useAppSelector((state) => state.piano.loadingPaywall);
  const hasPaywall = useAppSelector((state) => state.piano.hasPaywall);
  const hasPianoPaywall =
    pianoFeature.enabled && (loadingPaywall || hasPaywall);
  const homePeriodScores = getAflScores(periodScores, homeSquadId);
  const awayPeriodScores = getAflScores(periodScores, awaySquadId);

  const homeGoals = getAflGoals(scoreFlows, homeSquadId);
  const awayGoals = getAflGoals(scoreFlows, awaySquadId);

  const aflScoreComponent = (
    <div className="grid py-4 md:mt-4 md:place-content-center">
      <div className="grid w-full grid-cols-5 content-center border-1 md:w-[400px]">
        <div className="grid justify-items-center border-r-1 bg-gray-250" />
        {[1, 2, 3, 4].map((period) => (
          <div
            className={clsx(
              'grid justify-items-center border-r-1 py-1.5 text-xs font-bold',
              {
                'border-orange-650 bg-orange-650 text-white':
                  currentPeriod === period &&
                  matchStatus === MatchStatus.MATCH_STATUS_PLAYING,
              },
              {
                'bg-gray-250':
                  currentPeriod !== period ||
                  matchStatus !== MatchStatus.MATCH_STATUS_PLAYING,
              },
            )}
            key={period}
          >
            Q{period}
          </div>
        ))}
      </div>
      <div className="grid w-full grid-cols-5 content-center border-b-1 border-l-1 md:w-[400px]">
        <div className="grid justify-items-center border-r-1">
          <Avatar
            className="my-4 size-6"
            name={homeSquadNickname}
            sportPage={SportPage.AFL}
            squadId={homeSquadId}
            staticUrl={staticUrl}
          />
        </div>
        <AflPeriodScores
          currentPeriod={currentPeriod}
          matchStatus={matchStatus}
          periodScores={homePeriodScores}
        />
      </div>
      <div className="grid w-full grid-cols-5 content-center border-b-1 border-l-1 md:w-[400px]">
        <div className="grid justify-items-center border-r-1">
          <Avatar
            className="my-4 size-6"
            name={awaySquadNickname}
            sportPage={SportPage.AFL}
            squadId={awaySquadId}
            staticUrl={staticUrl}
          />
        </div>
        <AflPeriodScores
          currentPeriod={currentPeriod}
          matchStatus={matchStatus}
          periodScores={awayPeriodScores}
        />
      </div>
    </div>
  );

  const scoreComponent = (matchStatus === MatchStatus.MATCH_STATUS_PLAYING ||
    matchStatus === MatchStatus.MATCH_STATUS_POSTMATCH ||
    matchStatus === MatchStatus.MATCH_STATUS_COMPLETE) &&
    periodScores && <>{aflScoreComponent}</>;

  const scoreDetailComponent = (matchStatus ===
    MatchStatus.MATCH_STATUS_PLAYING ||
    matchStatus === MatchStatus.MATCH_STATUS_POSTMATCH ||
    matchStatus === MatchStatus.MATCH_STATUS_COMPLETE) &&
    scoreFlows && (
      <>
        <div className="-mx-4 grid bg-teal-150 py-4 md:mx-0 md:mt-4 md:place-content-center">
          <div className="w-full text-center md:w-[400px]">GOAL KICKERS</div>
        </div>
        <div className="grid md:place-content-center">
          <div className="mt-4 grid w-full grid-cols-2 content-center divide-x md:w-[400px]">
            <div className="grid justify-items-start">
              <div className="flex flex-col">
                <div className="mb-6 text-sm font-bold">
                  {homeSquadNickname}
                </div>
                {homeGoals.map((homeGoal, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <React.Fragment key={index}>
                    <GoalRecord goals={homeGoal[1]} name={homeGoal[0]} />
                  </React.Fragment>
                ))}
              </div>
            </div>
            <div className="grid justify-items-end">
              <div className="flex flex-col text-right">
                <div className="mb-6 text-sm font-bold">
                  {awaySquadNickname}
                </div>
                {awayGoals.map((awayGoal, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <React.Fragment key={index}>
                    <GoalRecord goals={awayGoal[1]} name={awayGoal[0]} />
                  </React.Fragment>
                ))}
              </div>
            </div>
          </div>
        </div>
      </>
    );

  return (
    <>
      {hasPianoPaywall && (
        <>
          <div className="pointer-events-none gradient-mask-b-0">
            {scoreComponent}
          </div>
          <div className="mx-auto mb-6 max-w-sm px-4 md:px-0">
            <PianoPaywall />
          </div>
        </>
      )}
      {!hasPianoPaywall && (
        <>
          {scoreComponent}
          {scoreDetailComponent}
        </>
      )}
    </>
  );
}

export default AflStats;
