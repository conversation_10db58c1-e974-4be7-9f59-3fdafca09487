import React from 'react';

import { SportPage } from 'types/SportsHub';

import AflStats from './AflStats';
import CricketStats from './CricketStats';
import LeagueStats from './LeagueStats';
import SoccerStats from './SoccerStats';

import type { MatchStatsProps } from 'types/SportsHub';

function MatchDetailStats({
  awaySquadId,
  awaySquadName,
  awaySquadNickname,
  currentPeriod,
  homeSquadId,
  homeSquadName,
  homeSquadNickname,
  inningsNumber,
  inningsSummary,
  matchStatus,
  periodScores,
  scoreFlows,
  showScorecard,
  sportPage,
}: MatchStatsProps): React.ReactElement | null {
  return (
    <>
      {sportPage === SportPage.NRL && scoreFlows && (
        <LeagueStats
          awaySquadId={awaySquadId}
          awaySquadNickname={awaySquadNickname}
          homeSquadId={homeSquadId}
          homeSquadNickname={homeSquadNickname}
          matchStatus={matchStatus}
          scoreFlows={scoreFlows}
        />
      )}
      {sportPage === SportPage.AFL && (scoreFlows || periodScores) && (
        <AflStats
          awaySquadId={awaySquadId}
          awaySquadNickname={awaySquadNickname}
          currentPeriod={currentPeriod}
          homeSquadId={homeSquadId}
          homeSquadNickname={homeSquadNickname}
          matchStatus={matchStatus}
          periodScores={periodScores}
          scoreFlows={scoreFlows}
        />
      )}
      {sportPage === SportPage.A_LEAGUE && scoreFlows && (
        <SoccerStats
          awaySquadId={awaySquadId}
          awaySquadNickname={awaySquadNickname}
          homeSquadId={homeSquadId}
          homeSquadNickname={homeSquadNickname}
          matchStatus={matchStatus}
          scoreFlows={scoreFlows}
        />
      )}
      {sportPage === SportPage.CRICKET && inningsSummary && (
        <CricketStats
          awaySquadId={awaySquadId}
          awaySquadName={awaySquadName}
          awaySquadNickname={awaySquadNickname}
          homeSquadId={homeSquadId}
          homeSquadName={homeSquadName}
          homeSquadNickname={homeSquadNickname}
          inningNumber={inningsNumber}
          inningsSummary={inningsSummary}
          matchStatus={matchStatus}
          showScorecard={showScorecard}
        />
      )}
    </>
  );
}

export default MatchDetailStats;
