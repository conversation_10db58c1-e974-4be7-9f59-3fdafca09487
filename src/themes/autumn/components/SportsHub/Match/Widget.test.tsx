import { render } from '@testing-library/react';
import React from 'react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { MatchStatus, SportPage } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import MatchWidget from './widget';

describe('<MatchWidget />', () => {
  it('renders with the scheduled match', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <MatchWidget
          awaySquadId={0}
          awaySquadName="Away Squad Name"
          awaySquadNickname="Away Squad Nick Name"
          awaySquadScore={10}
          currentPeriod={1}
          homeSquadId={1}
          homeSquadName="Home Squad Name"
          homeSquadNickname="Home Squad Nick Name"
          homeSquadScore={5}
          matchId={1}
          matchStatus={MatchStatus.MATCH_STATUS_SCHEDULED}
          periodCompleted={1}
          periodSeconds={1200}
          sportPage={SportPage.AFL}
          utcStartTime="2023-09-01T08:00:00Z"
          venueName="Venue"
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with the playing match', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <MatchWidget
          awaySquadId={0}
          awaySquadName="Away Squad Name"
          awaySquadNickname="Away Squad Nick Name"
          awaySquadScore={10}
          currentPeriod={1}
          homeSquadId={1}
          homeSquadName="Home Squad Name"
          homeSquadNickname="Home Squad Nick Name"
          homeSquadScore={5}
          matchId={1}
          matchStatus={MatchStatus.MATCH_STATUS_PLAYING}
          periodCompleted={1}
          periodSeconds={1200}
          sportPage={SportPage.AFL}
          utcStartTime="2023-09-01T08:00:00Z"
          venueName="Venue"
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with complete match', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <MatchWidget
          awaySquadId={0}
          awaySquadName="Away Squad Name"
          awaySquadNickname="Away Squad Nick Name"
          awaySquadScore={10}
          currentPeriod={1}
          homeSquadId={1}
          homeSquadName="Home Squad Name"
          homeSquadNickname="Home Squad Nick Name"
          homeSquadScore={5}
          matchId={1}
          matchStatus={MatchStatus.MATCH_STATUS_COMPLETE}
          periodCompleted={1}
          periodSeconds={1200}
          sportPage={SportPage.AFL}
          utcStartTime="2023-09-01T08:00:00Z"
          venueName="Venue"
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
