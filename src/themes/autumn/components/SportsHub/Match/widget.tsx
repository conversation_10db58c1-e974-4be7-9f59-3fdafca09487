import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { SPORT_NAV_THEME } from 'themes/autumn/templates/index/IndexPageSport/theme';
import {
  SportPage,
  matchCompleteStatuses,
  matchDelayedStatuses,
  matchNotCompleteStatuses,
  matchNotScheduledStatuses,
  matchPlayingStatuses,
  matchScheduledStatuses,
} from 'types/SportsHub';
import { matchDateFormatted, matchTimeFormatted } from 'util/time';

import MatchLiveTime from '../Component/MatchLiveTime';
import { getOrdinalIndicator } from '../utils';

import type { MatchWidgetProps } from 'types/SportsHub';

interface LogoProps {
  name: string;
  sportPage: SportPage;
  squadId: number;
  staticUrl: string;
}

function Logo({ name, sportPage, squadId, staticUrl }: LogoProps) {
  const namespace = sportPage === SportPage.CRICKET ? 'cricket/' : '';
  const filename = `squad-${squadId}.svg`;
  const src =
    sportPage === SportPage.CRICKET && name.match(/^([A-Z]\d+|TBC)$/)
      ? `${staticUrl}images/sports-hub/logos/icc-logo-comp-3347.svg`
      : `${staticUrl}images/sports-hub/squads/${namespace}${filename}`;
  return (
    <img
      alt={name}
      className="size-16 object-contain"
      loading="lazy"
      src={src}
    />
  );
}

interface MatchDisplayBoardProps {
  className: string;
  icon?: React.ReactElement;
  title: string;
}

function MatchDisplayBoard({
  className,
  icon,
  title,
}: MatchDisplayBoardProps) {
  return (
    <div className="grid content-start text-gray-450">
      <div
        className={clsx(
          'inline-flex shrink-0 items-center gap-1 rounded-full px-3 py-1 text-xs font-medium uppercase leading-4 text-white transition-colors duration-600 ease-default focus:transition-none',
          className,
        )}
      >
        {icon}
        {title}
      </div>
    </div>
  );
}

function MatchWidget({
  awaySquadId,
  awaySquadName,
  awaySquadNickname,
  awaySquadPosition,
  awaySquadScore,
  awaySummary,
  // eslint-disable-next-line @stylistic/max-len
  borderClassName = 'rounded-md border-1 border-gray-300 hover:border-orange-650',
  compName,
  currentPeriod,
  homeSquadId,
  homeSquadName,
  homeSquadNickname,
  homeSquadPosition,
  homeSquadScore,
  homeSummary,
  matchId,
  matchName,
  matchStatus,
  matchSummary,
  onClickWidget,
  periodCompleted,
  periodSeconds,
  showDisplayBoard = true,
  showVenue = true,
  sportPage,
  utcStartTime,
  venueCountry,
  venueName,
}: MatchWidgetProps): React.ReactElement | null {
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  if (!matchId || !sportPage) {
    return null;
  }

  const matchDateComponent = utcStartTime !== '' && (
    <div className="grid content-end text-base font-normal text-slate-750 opacity-80">
      {matchDateFormatted(utcStartTime)}
    </div>
  );

  const matchCompleteComponent = matchCompleteStatuses.includes(
    matchStatus,
  ) && (
    <MatchDisplayBoard
      className="bg-slate-750"
      title={sportPage === SportPage.CRICKET ? 'Completed' : 'Full time'}
    />
  );

  const matchDelayedComponent = matchDelayedStatuses.includes(matchStatus) && (
    <MatchDisplayBoard className="bg-slate-750" title="Delayed" />
  );

  const matchScheduledComponent = showDisplayBoard &&
    matchScheduledStatuses.includes(matchStatus) && (
      <MatchDisplayBoard className="bg-green-550" title="Prematch" />
    );

  const venueComponent = showVenue &&
    matchNotCompleteStatuses.includes(matchStatus) && (
      <div className="overflow-hidden break-words text-center text-xs font-normal text-slate-750 opacity-80">
        {venueName}
        {!!venueCountry && <>, {venueCountry}</>}
      </div>
    );

  const matchNameComponent = !!matchName &&
    sportPage === SportPage.CRICKET && (
      <div className="overflow-hidden break-words text-center text-sm font-bold text-slate-750 opacity-80">
        {matchName}
      </div>
    );

  const matchSummaryComponent = !!matchSummary &&
    sportPage === SportPage.CRICKET &&
    matchNotScheduledStatuses.includes(matchStatus) && (
      <div className="overflow-hidden break-words text-center text-xs font-medium text-green-550 opacity-80">
        {matchSummary}
      </div>
    );

  const matchLiveTimeComponent = matchPlayingStatuses.includes(
    matchStatus,
  ) && (
    <>
      {sportPage === SportPage.CRICKET ? (
        <MatchDisplayBoard
          className="bg-orange-650"
          icon={
            SPORT_NAV_THEME.live.selectedIcon ? (
              <span
                dangerouslySetInnerHTML={{
                  __html: SPORT_NAV_THEME.live.selectedIcon,
                }}
              />
            ) : undefined
          }
          title="Live"
        />
      ) : (
        <MatchLiveTime
          currentPeriod={currentPeriod}
          periodCompleted={periodCompleted}
          periodSeconds={periodSeconds}
          sportPage={sportPage}
        />
      )}
    </>
  );

  const matchTimeToStartComponent = matchScheduledStatuses.includes(
    matchStatus,
  ) && (
    <div className="grid content-start text-center text-lg font-bold text-slate-750 lg:text-sm xl:text-lg">
      {matchTimeFormatted(utcStartTime)}
    </div>
  );

  const teamPositionComponent = (position: number) => {
    if (position === 0) {
      return null;
    }
    const positionLabel = `${position}${getOrdinalIndicator(position)}`;
    return (
      <div className="mt-1 text-center text-sm font-medium text-gray-450">
        {positionLabel}
      </div>
    );
  };

  const teamScoreComponent = (score: number, win: boolean) => (
    <div
      className={clsx(
        'mt-1 text-center text-heading5 text-slate-750',
        { 'font-normal': !win },
        { 'font-bold': win },
      )}
    >
      {score}
    </div>
  );

  return (
    <Link
      className="no-underline"
      href={`/sport/${sportPage}/match/${matchId}/`}
      noStyle
      onClick={onClickWidget}
    >
      <div
        className={clsx(
          'grid w-full cursor-pointer grid-cols-3 content-center py-4',
          borderClassName,
        )}
      >
        <div className="grid justify-items-center">
          <div className="flex flex-col">
            <div className="flex justify-center text-sm font-medium">
              <Logo
                name={homeSquadName}
                sportPage={sportPage}
                squadId={homeSquadId}
                staticUrl={staticUrl}
              />
            </div>
            <div className="mt-1 grid min-h-[40px] overflow-hidden break-words text-center text-sm font-medium">
              {sportPage === SportPage.CRICKET
                ? homeSquadName
                : homeSquadNickname}
            </div>
            {matchNotScheduledStatuses.includes(matchStatus) &&
              (sportPage === SportPage.CRICKET ? (
                <>
                  {!!homeSummary && (
                    <div className="text-center text-lg font-bold">
                      {homeSummary}
                    </div>
                  )}
                </>
              ) : (
                <>
                  {awaySquadScore !== undefined &&
                    homeSquadScore !== undefined &&
                    teamScoreComponent(
                      homeSquadScore,
                      homeSquadScore > awaySquadScore,
                    )}
                </>
              ))}
            {matchNotCompleteStatuses.includes(matchStatus) &&
              !!homeSquadPosition &&
              teamPositionComponent(homeSquadPosition)}
          </div>
        </div>
        <div className="grid justify-items-center gap-y-1">
          {matchDateComponent}
          {matchLiveTimeComponent}
          {matchTimeToStartComponent}
          {matchCompleteComponent}
          {matchScheduledComponent}
          {matchNameComponent}
          {matchDelayedComponent}
          {venueComponent}
          {matchSummaryComponent}
        </div>
        <div className="grid justify-items-center">
          <div className="flex flex-col">
            <div className="flex justify-center text-sm font-medium">
              <Logo
                name={awaySquadName}
                sportPage={sportPage}
                squadId={awaySquadId}
                staticUrl={staticUrl}
              />
            </div>
            <div className="mt-1 grid min-h-[40px] overflow-hidden break-words text-center text-sm font-medium">
              {sportPage === SportPage.CRICKET
                ? awaySquadName
                : awaySquadNickname}
            </div>
            {matchNotScheduledStatuses.includes(matchStatus) &&
              (sportPage === SportPage.CRICKET ? (
                <>
                  {!!awaySummary && (
                    <div className="text-center text-lg font-bold">
                      {awaySummary}
                    </div>
                  )}
                </>
              ) : (
                <>
                  {awaySquadScore !== undefined &&
                    homeSquadScore !== undefined &&
                    teamScoreComponent(
                      awaySquadScore,
                      awaySquadScore > homeSquadScore,
                    )}
                </>
              ))}
            {matchNotCompleteStatuses.includes(matchStatus) &&
              !!awaySquadPosition &&
              teamPositionComponent(awaySquadPosition)}
          </div>
        </div>
        {compName && (
          <div className="col-span-3 flex justify-center overflow-hidden break-words text-sm text-gray-600">
            {compName}
          </div>
        )}
      </div>
    </Link>
  );
}

export default MatchWidget;
