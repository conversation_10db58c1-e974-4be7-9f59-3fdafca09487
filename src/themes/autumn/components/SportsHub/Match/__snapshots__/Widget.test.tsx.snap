// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<MatchWidget /> renders with complete match 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <a
    class="no-underline"
    href="/sport/afl/match/1/"
  >
    <div
      class="grid w-full cursor-pointer grid-cols-3 content-center py-4 rounded-md border-1 border-gray-300 hover:border-orange-650"
    >
      <div
        class="grid justify-items-center"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Home Squad Name"
              class="size-16 object-contain"
              loading="lazy"
              src="images/sports-hub/squads/squad-1.svg"
            />
          </div>
          <div
            class="mt-1 grid min-h-[40px] overflow-hidden break-words text-center text-sm font-medium"
          >
            Home Squad Nick Name
          </div>
          <div
            class="mt-1 text-center text-heading5 text-slate-750 font-normal"
          >
            5
          </div>
        </div>
      </div>
      <div
        class="grid justify-items-center gap-y-1"
      >
        <div
          class="grid content-end text-base font-normal text-slate-750 opacity-80"
        >
          Fri 1 Sept
        </div>
        <div
          class="grid content-start text-gray-450"
        >
          <div
            class="inline-flex shrink-0 items-center gap-1 rounded-full px-3 py-1 text-xs font-medium uppercase leading-4 text-white transition-colors duration-600 ease-default focus:transition-none bg-slate-750"
          >
            Full time
          </div>
        </div>
      </div>
      <div
        class="grid justify-items-center"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Away Squad Name"
              class="size-16 object-contain"
              loading="lazy"
              src="images/sports-hub/squads/squad-0.svg"
            />
          </div>
          <div
            class="mt-1 grid min-h-[40px] overflow-hidden break-words text-center text-sm font-medium"
          >
            Away Squad Nick Name
          </div>
          <div
            class="mt-1 text-center text-heading5 text-slate-750 font-bold"
          >
            10
          </div>
        </div>
      </div>
    </div>
  </a>
</div>
`;

exports[`<MatchWidget /> renders with the playing match 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <a
    class="no-underline"
    href="/sport/afl/match/1/"
  >
    <div
      class="grid w-full cursor-pointer grid-cols-3 content-center py-4 rounded-md border-1 border-gray-300 hover:border-orange-650"
    >
      <div
        class="grid justify-items-center"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Home Squad Name"
              class="size-16 object-contain"
              loading="lazy"
              src="images/sports-hub/squads/squad-1.svg"
            />
          </div>
          <div
            class="mt-1 grid min-h-[40px] overflow-hidden break-words text-center text-sm font-medium"
          >
            Home Squad Nick Name
          </div>
          <div
            class="mt-1 text-center text-heading5 text-slate-750 font-normal"
          >
            5
          </div>
        </div>
      </div>
      <div
        class="grid justify-items-center gap-y-1"
      >
        <div
          class="grid content-end text-base font-normal text-slate-750 opacity-80"
        >
          Fri 1 Sept
        </div>
        <div
          class="grid content-center"
        >
          <div
            class="inline-flex shrink-0 items-center rounded-full bg-orange-650 px-3 py-1 text-xs font-medium normal-case leading-4 text-white transition-colors duration-600 ease-default focus:transition-none"
          >
            <span>
              Q
              1
            </span>
             
            20
            :
            00
          </div>
        </div>
        <div
          class="overflow-hidden break-words text-center text-xs font-normal text-slate-750 opacity-80"
        >
          Venue
        </div>
      </div>
      <div
        class="grid justify-items-center"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Away Squad Name"
              class="size-16 object-contain"
              loading="lazy"
              src="images/sports-hub/squads/squad-0.svg"
            />
          </div>
          <div
            class="mt-1 grid min-h-[40px] overflow-hidden break-words text-center text-sm font-medium"
          >
            Away Squad Nick Name
          </div>
          <div
            class="mt-1 text-center text-heading5 text-slate-750 font-bold"
          >
            10
          </div>
        </div>
      </div>
    </div>
  </a>
</div>
`;

exports[`<MatchWidget /> renders with the scheduled match 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <a
    class="no-underline"
    href="/sport/afl/match/1/"
  >
    <div
      class="grid w-full cursor-pointer grid-cols-3 content-center py-4 rounded-md border-1 border-gray-300 hover:border-orange-650"
    >
      <div
        class="grid justify-items-center"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Home Squad Name"
              class="size-16 object-contain"
              loading="lazy"
              src="images/sports-hub/squads/squad-1.svg"
            />
          </div>
          <div
            class="mt-1 grid min-h-[40px] overflow-hidden break-words text-center text-sm font-medium"
          >
            Home Squad Nick Name
          </div>
        </div>
      </div>
      <div
        class="grid justify-items-center gap-y-1"
      >
        <div
          class="grid content-end text-base font-normal text-slate-750 opacity-80"
        >
          Fri 1 Sept
        </div>
        <div
          class="grid content-start text-center text-lg font-bold text-slate-750 lg:text-sm xl:text-lg"
        >
          8:00 am
        </div>
        <div
          class="grid content-start text-gray-450"
        >
          <div
            class="inline-flex shrink-0 items-center gap-1 rounded-full px-3 py-1 text-xs font-medium uppercase leading-4 text-white transition-colors duration-600 ease-default focus:transition-none bg-green-550"
          >
            Prematch
          </div>
        </div>
        <div
          class="overflow-hidden break-words text-center text-xs font-normal text-slate-750 opacity-80"
        >
          Venue
        </div>
      </div>
      <div
        class="grid justify-items-center"
      >
        <div
          class="flex flex-col"
        >
          <div
            class="flex justify-center text-sm font-medium"
          >
            <img
              alt="Away Squad Name"
              class="size-16 object-contain"
              loading="lazy"
              src="images/sports-hub/squads/squad-0.svg"
            />
          </div>
          <div
            class="mt-1 grid min-h-[40px] overflow-hidden break-words text-center text-sm font-medium"
          >
            Away Squad Nick Name
          </div>
        </div>
      </div>
    </div>
  </a>
</div>
`;
