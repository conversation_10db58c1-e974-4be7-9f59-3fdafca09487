// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Finals /> renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mt-5 overflow-auto font-inter"
  >
    <div
      class="grid select-none auto-cols-max grid-flow-col gap-x-12 gap-y-4"
    >
      <div
        class="col-start-1 mb-2 text-xl font-semibold"
      >
        Finals week 1
      </div>
      <div
        class="col-start-2 mb-2 text-xl font-semibold"
      >
        Semi Finals
      </div>
      <div
        class="col-start-3 mb-2 text-xl font-semibold"
      >
        Preliminary Finals
      </div>
      <div
        class="col-start-4 mb-2 text-xl font-semibold"
      >
        Grand Final
      </div>
      <div
        class="row-span-2 grid gap-y-4"
      >
        <div
          class="relative flex w-full min-w-[210px] flex-col justify-center self-stretch"
        >
          <div
            class="z-0"
          />
          <div
            class="relative z-10 rounded-md border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="px-3 py-2 text-xs font-medium"
            >
              <span>
                 
              </span>
            </div>
            <div
              class="flex bg-gray-100"
            >
              <div
                class="flex grow px-2 py-1"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Home
                </div>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="flex grow flex-row content-center px-2 py-1 align-middle"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Away
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="relative flex w-full min-w-[210px] flex-col justify-center self-stretch"
        >
          <div
            class="z-0"
          />
          <div
            class="relative z-10 rounded-md border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="px-3 py-2 text-xs font-medium"
            >
              <span>
                 
              </span>
            </div>
            <div
              class="flex bg-gray-100"
            >
              <div
                class="flex grow px-2 py-1"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Home
                </div>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="flex grow flex-row content-center px-2 py-1 align-middle"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Away
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="row-span-2 row-start-5 grid gap-y-4"
      >
        <div
          class="relative flex w-full min-w-[210px] flex-col justify-center self-stretch"
        >
          <div
            class="z-0"
          />
          <div
            class="relative z-10 rounded-md border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="px-3 py-2 text-xs font-medium"
            >
              <span>
                 
              </span>
            </div>
            <div
              class="flex bg-gray-100"
            >
              <div
                class="flex grow px-2 py-1"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Home
                </div>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="flex grow flex-row content-center px-2 py-1 align-middle"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Away
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="relative flex w-full min-w-[210px] flex-col justify-center self-stretch"
        >
          <div
            class="z-0"
          />
          <div
            class="relative z-10 rounded-md border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="px-3 py-2 text-xs font-medium"
            >
              <span>
                 
              </span>
            </div>
            <div
              class="flex bg-gray-100"
            >
              <div
                class="flex grow px-2 py-1"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Home
                </div>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="flex grow flex-row content-center px-2 py-1 align-middle"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Away
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="relative col-span-2 col-start-2 row-start-4"
      >
        <div
          class="absolute left-1/2 z-0"
        >
          <div
            class="absolute -left-3 -top-3 size-6 rounded-full text-center text-xs font-semibold leading-6 text-white bg-green-650"
          >
            W
          </div>
          <div
            class="absolute -left-16 -top-16 -z-10 fill-gray-500 stroke-gray-500 stroke-1"
          >
            <svg
              height="130"
              viewBox="0 0 130 130"
              width="130"
            >
              <line
                x1="0"
                x2="130"
                y1="0"
                y2="130"
              />
              <polygon
                points="130,130 125,128 128,125"
              />
              <line
                x1="0"
                x2="130"
                y1="130"
                y2="0"
              />
              <polygon
                points="130,0 125,2 128,5"
              />
            </svg>
          </div>
        </div>
      </div>
      <div
        class="row-span-2 grid content-center"
      >
        <div
          class="relative flex w-full min-w-[210px] flex-col justify-center self-stretch"
        >
          <div
            class="z-0"
          >
            <div
              class="absolute right-full mr-2 w-4 border-l border-gray-400 bottom-1/2 top-0 rounded-bl border-b"
            >
              <div
                class="absolute right-full size-5 border-r border-gray-400 -top-1.5 rounded-tr border-t"
              />
              <div
                class="absolute -right-1.5 -bottom-[3px] -rotate-90"
              >
                <div
                  class="fill-gray-400 stroke-1"
                >
                  <svg
                    height="5"
                    viewBox="0 0 11 11"
                    width="11"
                  >
                    <polygon
                      points="0,0 11,0 5,11.0"
                    />
                  </svg>
                </div>
              </div>
              <div
                class="absolute size-6 rounded-full text-center text-xs font-semibold leading-6 text-white -ml-3 bg-gray-500 bottom-1/3"
              >
                L
              </div>
            </div>
            <div
              class="absolute right-full mr-2 w-4 border-l border-gray-400 bottom-0 top-1/2 rounded-tl"
            >
              <div
                class="absolute right-full size-5 border-r border-gray-400 -bottom-1.5 rounded-br border-b"
              />
              <div
                class="absolute size-6 rounded-full text-center text-xs font-semibold leading-6 text-white -ml-3 bg-green-650 top-1/3"
              >
                W
              </div>
            </div>
          </div>
          <div
            class="relative z-10 rounded-md border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="px-3 py-2 text-xs font-medium"
            >
              <span>
                 
              </span>
            </div>
            <div
              class="flex bg-gray-100"
            >
              <div
                class="flex grow px-2 py-1"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Home
                </div>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="flex grow flex-row content-center px-2 py-1 align-middle"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Away
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="row-span-2 grid content-center"
      >
        <div
          class="relative flex w-full min-w-[210px] flex-col justify-center self-stretch"
        >
          <div
            class="z-0"
          >
            <div
              class="absolute right-full mr-2 w-4 border-l border-gray-400 bottom-1/2 top-0 rounded-bl border-b"
            >
              <div
                class="absolute right-full size-5 border-r border-gray-400 -top-1.5 rounded-tr border-t"
              />
              <div
                class="absolute -right-1.5 -bottom-[3px] -rotate-90"
              >
                <div
                  class="fill-gray-400 stroke-1"
                >
                  <svg
                    height="5"
                    viewBox="0 0 11 11"
                    width="11"
                  >
                    <polygon
                      points="0,0 11,0 5,11.0"
                    />
                  </svg>
                </div>
              </div>
              <div
                class="absolute size-6 rounded-full text-center text-xs font-semibold leading-6 text-white -ml-3 bg-green-650 bottom-1/3"
              >
                W
              </div>
            </div>
            <div
              class="absolute right-full mr-2 w-4 border-l border-gray-400 bottom-0 top-1/2 rounded-tl"
            >
              <div
                class="absolute right-full size-5 border-r border-gray-400 -bottom-1.5 rounded-br border-b"
              />
              <div
                class="absolute size-6 rounded-full text-center text-xs font-semibold leading-6 text-white -ml-3 bg-gray-500 top-1/3"
              >
                L
              </div>
            </div>
          </div>
          <div
            class="relative z-10 rounded-md border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="px-3 py-2 text-xs font-medium"
            >
              <span>
                 
              </span>
            </div>
            <div
              class="flex bg-gray-100"
            >
              <div
                class="flex grow px-2 py-1"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Home
                </div>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="flex grow flex-row content-center px-2 py-1 align-middle"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Away
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="row-span-2 grid content-center"
      >
        <div
          class="relative flex w-full min-w-[210px] flex-col justify-center self-stretch"
        >
          <div
            class="z-0"
          >
            <div
              class="absolute right-1/2 -z-10 h-7 w-[600px] border-r border-gray-400 bottom-full mb-1 rounded-tr border-t"
            >
              <div
                class="absolute -right-1.5 bottom-0"
              >
                <div
                  class="fill-gray-400 stroke-1"
                >
                  <svg
                    height="5"
                    viewBox="0 0 11 11"
                    width="11"
                  >
                    <polygon
                      points="0,0 11,0 5,11.0"
                    />
                  </svg>
                </div>
              </div>
              <div
                class="absolute size-6 rounded-full text-center text-xs font-semibold leading-6 text-white -mt-3 bg-green-650 right-1/3"
              >
                W
              </div>
            </div>
          </div>
          <div
            class="relative z-10 rounded-md border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="px-3 py-2 text-xs font-medium"
            >
              <span>
                 
              </span>
            </div>
            <div
              class="flex bg-gray-100"
            >
              <div
                class="flex grow px-2 py-1"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Home
                </div>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="flex grow flex-row content-center px-2 py-1 align-middle"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Away
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="row-span-2 grid content-center"
      >
        <div
          class="relative flex w-full min-w-[210px] flex-col justify-center self-stretch"
        >
          <div
            class="z-0"
          >
            <div
              class="absolute right-1/2 -z-10 h-7 w-[600px] border-r border-gray-400 top-full mt-1 rounded-br border-b"
            >
              <div
                class="absolute -right-1.5 top-0 rotate-180"
              >
                <div
                  class="fill-gray-400 stroke-1"
                >
                  <svg
                    height="5"
                    viewBox="0 0 11 11"
                    width="11"
                  >
                    <polygon
                      points="0,0 11,0 5,11.0"
                    />
                  </svg>
                </div>
              </div>
              <div
                class="absolute size-6 rounded-full text-center text-xs font-semibold leading-6 text-white -bottom-3 bg-green-650 right-1/3"
              >
                W
              </div>
            </div>
          </div>
          <div
            class="relative z-10 rounded-md border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="px-3 py-2 text-xs font-medium"
            >
              <span>
                 
              </span>
            </div>
            <div
              class="flex bg-gray-100"
            >
              <div
                class="flex grow px-2 py-1"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Home
                </div>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="flex grow flex-row content-center px-2 py-1 align-middle"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-gray-500"
                >
                  Away
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="row-span-5 grid content-center"
      >
        <div
          class="relative flex w-full min-w-[210px] flex-col justify-center self-stretch"
        >
          <div
            class="z-0"
          >
            <div
              class="absolute right-1/2 -z-10 h-16 w-[300px] border-r border-gray-400 bottom-full mb-1 rounded-tr border-t"
            >
              <div
                class="absolute -right-1.5 bottom-0"
              >
                <div
                  class="fill-gray-400 stroke-1"
                >
                  <svg
                    height="5"
                    viewBox="0 0 11 11"
                    width="11"
                  >
                    <polygon
                      points="0,0 11,0 5,11.0"
                    />
                  </svg>
                </div>
              </div>
              <div
                class="absolute size-6 rounded-full text-center text-xs font-semibold leading-6 text-white -top-3.5 bg-green-650 right-1/4"
              >
                W
              </div>
            </div>
            <div
              class="absolute right-1/2 -z-10 h-16 w-[300px] border-r border-gray-400 top-full mt-1 rounded-br border-b"
            >
              <div
                class="absolute -right-1.5 top-0 rotate-180"
              >
                <div
                  class="fill-gray-400 stroke-1"
                >
                  <svg
                    height="5"
                    viewBox="0 0 11 11"
                    width="11"
                  >
                    <polygon
                      points="0,0 11,0 5,11.0"
                    />
                  </svg>
                </div>
              </div>
              <div
                class="absolute size-6 rounded-full text-center text-xs font-semibold leading-6 text-white -bottom-3 bg-green-650 right-1/4"
              >
                W
              </div>
            </div>
          </div>
          <div
            class="relative z-10 rounded-md border border-gray-200 bg-white shadow-sm"
          >
            <div
              class="px-3 py-2 text-xs font-medium border-b"
            >
              <span>
                 
              </span>
            </div>
            <div
              class="flex"
            >
              <div
                class="ml-4 flex grow px-2 py-6"
              >
                <div
                  class="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle"
                />
                <div
                  class="grow align-middle text-sm font-semibold leading-8 text-black"
                >
                  GRAND FINAL
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
