import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import { Competition, SportPage } from 'types/SportsHub';
import { matchDateFormatted, matchTimeFormatted } from 'util/time';

import PianoPaywall from '../../generic/PianoPaywall';
import { getOrdinalIndicator } from '../utils';

import type { FinalsMatches } from 'util/organization/suzuka';

const PAYWALL_VISIBLE_INDEXES = [0, 1, 4, 6];

interface ArrowProps {
  bottom?: boolean;
  right?: boolean;
  top?: boolean;
}

interface SignProps {
  long?: boolean;
  medium?: boolean;
  top?: boolean;
  win?: boolean;
}

interface SignDirectProps {
  win?: boolean;
}

interface JoinProps {
  arrow?: boolean;
  top?: boolean;
  win?: boolean;
}

interface JoinLongProps {
  top?: boolean;
  win?: boolean;
}

interface JoinMediumProps {
  top?: boolean;
  win?: boolean;
}

interface TeamLogoProps {
  name?: string;
  squadId?: number;
}

interface TeamPositionProps {
  position?: number;
}

interface TeamNameProps {
  home?: boolean;
  name?: string;
}

interface GameProps {
  away?: string;
  awayPosition?: number;
  awaySquadId?: number;
  children?: React.ReactNode;
  home?: string;
  homePosition?: number;
  homeSquadId?: number;
  isGrandFinal?: boolean;
  matchId?: number;
  sportPage: SportPage;
  utcStartTime: string;
}

interface FinalsProps {
  matches: FinalsMatches;
  sportPage: SportPage;
}

interface FinalsGameProps {
  children?: React.ReactNode;
  hasPianoPaywall: boolean;
  index: number;
  isGrandFinal?: boolean;
  matches: FinalsMatches;
  sportPage: SportPage;
}

function Arrow({
  bottom = false,
  right = false,
  top = false,
}: ArrowProps): React.ReactElement {
  return (
    <div
      className={clsx('absolute -right-1.5', {
        '-bottom-[3px] -rotate-90': right,
        'bottom-0': top,
        'top-0 rotate-180': bottom,
      })}
    >
      <div className="fill-gray-400 stroke-1">
        <svg height="5" viewBox="0 0 11 11" width="11">
          <polygon points="0,0 11,0 5,11.0" />
        </svg>
      </div>
    </div>
  );
}

function Sign({
  long = false,
  medium = false,
  top = false,
  win = false,
}: SignProps): React.ReactElement {
  return (
    <div
      className={clsx(
        'absolute size-6 rounded-full text-center text-xs font-semibold leading-6 text-white',
        {
          '-bottom-3': (medium || long) && !top,
          '-ml-3': !long && !medium,
          '-mt-3': long && top,
          '-top-3.5': medium && top,
          'bg-gray-500': !win,
          'bg-green-650': win,
          'bottom-1/3': top && !long && !medium,
          'right-1/3': long,
          'right-1/4': medium,
          'top-1/3': !top && !long && !medium,
        },
      )}
    >
      {win ? 'W' : 'L'}
    </div>
  );
}

function SignDirect({ win = false }: SignDirectProps): React.ReactElement {
  return (
    <div
      className={clsx(
        'absolute -left-3 -top-3 size-6 rounded-full text-center text-xs font-semibold leading-6 text-white',
        {
          'bg-gray-500': !win,
          'bg-green-650': win,
        },
      )}
    >
      {win ? 'W' : 'L'}
    </div>
  );
}

function Join({
  arrow = false,
  top = false,
  win = false,
}: JoinProps): React.ReactElement {
  return (
    <div
      className={clsx(
        'absolute right-full mr-2 w-4 border-l border-gray-400',
        {
          'bottom-0 top-1/2 rounded-tl': !top,
          'bottom-1/2 top-0 rounded-bl border-b': top,
        },
      )}
    >
      <div
        className={clsx(
          'absolute right-full size-5 border-r border-gray-400',
          {
            '-bottom-1.5 rounded-br border-b': !top,
            '-top-1.5 rounded-tr border-t': top,
          },
        )}
      />
      {arrow && <Arrow right />}
      <Sign top={top} win={win} />
    </div>
  );
}

function JoinLong({
  top = false,
  win = false,
}: JoinLongProps): React.ReactElement {
  return (
    <div
      className={clsx(
        'absolute right-1/2 -z-10 h-7 w-[600px] border-r border-gray-400',
        {
          'bottom-full mb-1 rounded-tr border-t': top,
          'top-full mt-1 rounded-br border-b': !top,
        },
      )}
    >
      <Arrow bottom={!top} top={top} />
      <Sign long top={top} win={win} />
    </div>
  );
}

function JoinMedium({
  top = false,
  win = false,
}: JoinMediumProps): React.ReactElement {
  return (
    <div
      className={clsx(
        'absolute right-1/2 -z-10 h-16 w-[300px] border-r border-gray-400',
        {
          'bottom-full mb-1 rounded-tr border-t': top,
          'top-full mt-1 rounded-br border-b': !top,
        },
      )}
    >
      <Arrow bottom={!top} top={top} />
      <Sign medium top={top} win={win} />
    </div>
  );
}

function TeamPosition({
  position,
}: TeamPositionProps): React.ReactElement | null {
  if (!position) return null;
  const positionLabel = `${position}${getOrdinalIndicator(position)}`;
  return (
    <div className="w-14 flex-none border-r border-gray-300 p-2 text-center text-base font-semibold">
      {positionLabel}
    </div>
  );
}

function TeamLogo({ name = '', squadId }: TeamLogoProps): React.ReactElement {
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  if (!squadId) {
    return (
      <div className="mr-2 inline-block size-8 rounded-full bg-gray-300 align-middle" />
    );
  }
  const src = `${staticUrl}images/sports-hub/squads/squad-${squadId}.svg`;
  return (
    <img
      alt={name}
      className="mr-2 inline-block size-8 object-contain align-middle"
      loading="lazy"
      src={src}
    />
  );
}

function TeamName({
  home = false,
  name = '',
}: TeamNameProps): React.ReactElement {
  return (
    <div
      className={clsx('grow align-middle text-sm font-semibold leading-8', {
        'text-black': name,
        'text-gray-500': !name,
      })}
    >
      {name.toUpperCase() || (home ? 'Home' : 'Away')}
    </div>
  );
}

function Game({
  away = '',
  awayPosition,
  awaySquadId,
  children,
  home = '',
  homePosition,
  homeSquadId,
  isGrandFinal = false,
  matchId,
  sportPage,
  utcStartTime,
}: GameProps): React.ReactElement {
  let grandFinalName: string | undefined;
  if (!awaySquadId && !homeSquadId && isGrandFinal) {
    if (sportPage === SportPage.AFL) {
      grandFinalName = 'MCG';
    } else if (
      sportPage === SportPage.A_LEAGUE ||
      sportPage === SportPage.NRL
    ) {
      grandFinalName = 'Grand Final';
    }
  }
  const card = (
    <div
      className={clsx(
        'relative z-10 rounded-md border border-gray-200 bg-white shadow-sm',
        {
          'hover:border-orange-650': matchId !== undefined,
        },
      )}
    >
      <div
        className={clsx('px-3 py-2 text-xs font-medium', {
          'border-b': isGrandFinal,
        })}
      >
        {utcStartTime ? (
          `${matchDateFormatted(utcStartTime)}, ${matchTimeFormatted(
            utcStartTime,
          )}`
        ) : (
          <span>&nbsp;</span>
        )}
      </div>
      {grandFinalName ? (
        <div className="flex">
          <div className="ml-4 flex grow px-2 py-6">
            <TeamLogo />
            <TeamName name={grandFinalName} />
          </div>
        </div>
      ) : (
        <>
          <div className="flex bg-gray-100">
            <TeamPosition position={homePosition} />
            <div className="flex grow px-2 py-1">
              <TeamLogo name={home} squadId={homeSquadId} />
              <TeamName home name={home} />
            </div>
          </div>
          <div className="flex">
            <TeamPosition position={awayPosition} />
            <div className="flex grow flex-row content-center px-2 py-1 align-middle">
              <TeamLogo name={away} squadId={awaySquadId} />
              <TeamName name={away} />
            </div>
          </div>
        </>
      )}
    </div>
  );
  return (
    <div className="relative flex w-full min-w-[210px] flex-col justify-center self-stretch">
      <div className="z-0">{children}</div>
      {matchId !== undefined ? (
        <a href={`/sport/${sportPage}/match/${matchId}`}>{card}</a>
      ) : (
        card
      )}
    </div>
  );
}

function FinalsGame({
  children,
  hasPianoPaywall,
  index,
  isGrandFinal = false,
  matches,
  sportPage,
}: FinalsGameProps): React.ReactElement {
  const baseFinalsRound = (Competition[sportPage]?.homeAwayRounds ?? 0) + 1;
  let matchKey;
  if (index < 4) {
    matchKey = `${baseFinalsRound}-${index + 1}`;
  } else if (index < 6) {
    matchKey = `${baseFinalsRound + 1}-${index - 3}`;
  } else if (index < 8) {
    matchKey = `${baseFinalsRound + 2}-${index - 5}`;
  } else {
    matchKey = `${baseFinalsRound + 3}-${index - 7}`;
  }
  const game = (
    <Game
      away={matches[matchKey]?.data?.awaySquadNickname}
      awayPosition={matches[matchKey]?.ladderPositions?.awaySquad}
      awaySquadId={matches[matchKey]?.data?.awaySquadId}
      home={matches[matchKey]?.data?.homeSquadNickname}
      homePosition={matches[matchKey]?.ladderPositions?.homeSquad}
      homeSquadId={matches[matchKey]?.data?.homeSquadId}
      isGrandFinal={isGrandFinal}
      matchId={matches[matchKey]?.id}
      sportPage={sportPage}
      utcStartTime={matches[matchKey]?.data?.utcStartTime}
    >
      {children}
    </Game>
  );

  const component =
    hasPianoPaywall && !PAYWALL_VISIBLE_INDEXES.includes(index) ? (
      <div className="hidden">{game}</div>
    ) : (
      game
    );
  return component;
}

function Finals({
  matches,
  sportPage,
}: FinalsProps): React.ReactElement | null {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const loadingPaywall = useAppSelector((state) => state.piano.loadingPaywall);
  const hasPaywall = useAppSelector((state) => state.piano.hasPaywall);
  const hasPianoPaywall =
    pianoFeature.enabled && (loadingPaywall || hasPaywall);

  return (
    <div className="mt-5 overflow-auto font-inter">
      <div
        className={clsx(
          'grid select-none auto-cols-max grid-flow-col gap-x-12 gap-y-4',
          {
            'pointer-events-none max-h-48 overflow-hidden gradient-mask-b-0':
              hasPianoPaywall,
          },
        )}
      >
        <div className="col-start-1 mb-2 text-xl font-semibold">
          Finals week 1
        </div>
        <div className="col-start-2 mb-2 text-xl font-semibold">
          Semi Finals
        </div>
        <div className="col-start-3 mb-2 text-xl font-semibold">
          Preliminary Finals
        </div>
        <div className="col-start-4 mb-2 text-xl font-semibold">
          Grand Final
        </div>
        <div className="row-span-2 grid gap-y-4">
          <FinalsGame
            hasPianoPaywall={hasPianoPaywall}
            index={0}
            matches={matches}
            sportPage={sportPage}
          />
          <FinalsGame
            hasPianoPaywall={hasPianoPaywall}
            index={1}
            matches={matches}
            sportPage={sportPage}
          />
        </div>
        <div className="row-span-2 row-start-5 grid gap-y-4">
          <FinalsGame
            hasPianoPaywall={hasPianoPaywall}
            index={2}
            matches={matches}
            sportPage={sportPage}
          />
          <FinalsGame
            hasPianoPaywall={hasPianoPaywall}
            index={3}
            matches={matches}
            sportPage={sportPage}
          />
        </div>
        <div className="relative col-span-2 col-start-2 row-start-4">
          <div
            className={clsx('absolute left-1/2 z-0', {
              hidden: hasPianoPaywall,
            })}
          >
            <SignDirect win />
            <div className="absolute -left-16 -top-16 -z-10 fill-gray-500 stroke-gray-500 stroke-1">
              <svg height="130" viewBox="0 0 130 130" width="130">
                <line x1="0" x2="130" y1="0" y2="130" />
                <polygon points="130,130 125,128 128,125" />
                <line x1="0" x2="130" y1="130" y2="0" />
                <polygon points="130,0 125,2 128,5" />
              </svg>
            </div>
          </div>
        </div>

        <div className="row-span-2 grid content-center">
          <FinalsGame
            hasPianoPaywall={hasPianoPaywall}
            index={4}
            matches={matches}
            sportPage={sportPage}
          >
            <Join arrow top />
            <Join win />
          </FinalsGame>
        </div>
        <div className="row-span-2 grid content-center">
          <FinalsGame
            hasPianoPaywall={hasPianoPaywall}
            index={5}
            matches={matches}
            sportPage={sportPage}
          >
            <Join arrow top win />
            <Join />
          </FinalsGame>
        </div>
        <div className="row-span-2 grid content-center">
          <FinalsGame
            hasPianoPaywall={hasPianoPaywall}
            index={6}
            matches={matches}
            sportPage={sportPage}
          >
            <JoinLong top win />
          </FinalsGame>
        </div>
        <div className="row-span-2 grid content-center">
          <FinalsGame
            hasPianoPaywall={hasPianoPaywall}
            index={7}
            matches={matches}
            sportPage={sportPage}
          >
            <JoinLong win />
          </FinalsGame>
        </div>
        <div className="row-span-5 grid content-center">
          <FinalsGame
            hasPianoPaywall={hasPianoPaywall}
            index={8}
            isGrandFinal
            matches={matches}
            sportPage={sportPage}
          >
            <JoinMedium top win />
            <JoinMedium win />
          </FinalsGame>
        </div>
      </div>
      {hasPianoPaywall && (
        <div className="mx-auto mb-6 max-w-sm px-4 md:px-0">
          <PianoPaywall />
        </div>
      )}
    </div>
  );
}

export default Finals;
