import fs from 'fs';
import path from 'path';

import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { SportPage } from 'types/SportsHub';
import { TestWrapper } from 'util/jest';

import Finals from './index';

import type { FinalsMatches } from 'util/organization/suzuka';

describe('<Finals />', () => {
  const mockMatchesData = JSON.parse(
    fs
      .readFileSync(path.join(__dirname, '../mocks/nrlFinals.json'))
      .toString(),
  ) as FinalsMatches;

  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Finals matches={mockMatchesData} sportPage={SportPage.NRL} />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
