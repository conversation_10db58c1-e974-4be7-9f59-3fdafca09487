import {
  ALeagueDate,
  Competition,
  CompetitionLevelIds,
  ScoreNameType,
  Sport,
  SportPage,
} from 'types/SportsHub';

import type {
  PeriodScoresProps,
  ScoreFlow,
  TotalScoreType,
} from 'types/SportsHub';

export function daysToGo(utcStartTime: string, now: Date): number {
  return Math.round(
    (new Date(utcStartTime).getTime() - now.getTime()) / 86400000,
  );
}

export function getMinutesInGame(
  period: number,
  periodCompleted: number,
  periodSeconds: number,
  minsPerPeriod: number,
): number {
  if (
    !Number.isInteger(periodSeconds) ||
    !Number.isInteger(period) ||
    !Number.isInteger(periodCompleted)
  ) {
    return 0;
  }

  if (periodCompleted >= period) {
    return 0;
  }

  const minsInPeriod = Math.round(periodSeconds / 60);
  if (minsInPeriod > minsPerPeriod) {
    return minsPerPeriod;
  }

  return minsInPeriod;
}

export function getMinutesInGamePeriod(
  period: number,
  periodCompleted: number,
  periodSeconds: number,
  minsPerPeriod: number,
): number {
  if (
    !Number.isInteger(periodSeconds) ||
    !Number.isInteger(period) ||
    !Number.isInteger(periodCompleted)
  ) {
    return 0;
  }

  if (periodCompleted >= period) {
    return minsPerPeriod;
  }

  const minsInPeriod = Math.round(periodSeconds / 60);
  if (minsInPeriod > minsPerPeriod) {
    return minsPerPeriod;
  }

  return minsInPeriod;
}

export function formatMinutesLabel(minutes: number): string {
  return minutes < 10 ? `0${minutes}` : `${minutes}`;
}

export function getSeconds(
  period: number,
  periodCompleted: number,
  periodSeconds: number,
  minsPerPeriod: number,
): string {
  if (
    !Number.isInteger(periodSeconds) ||
    !Number.isInteger(period) ||
    !Number.isInteger(periodCompleted)
  ) {
    return '00';
  }

  if (periodCompleted >= period) {
    return '00';
  }

  const remainingSeconds = period * minsPerPeriod * 60 - periodSeconds;
  if (remainingSeconds < 0) {
    return '00';
  }
  const seconds = periodSeconds % 60;
  if (seconds < 10) {
    return `0${seconds}`;
  }
  return seconds.toString();
}

export function getMinsPerPeriod(sport: Sport) {
  switch (sport) {
    case Sport.SOCCER:
      return 45;
    case Sport.AFL:
      return 20;
    case Sport.LEAGUE:
      return 40;
    default:
      return 0;
  }
}

interface MatchSeasonType {
  label: string;
  name: string;
}

export function getMatchSeason(
  sportPage: SportPage,
  now: Date,
): MatchSeasonType {
  const currentYear = new Date(now).getFullYear();
  if (sportPage === SportPage.A_LEAGUE) {
    let from: string;
    let to: string;
    const aleagueStartDate = new Date(
      currentYear,
      ALeagueDate.startMonth - 1,
      ALeagueDate.startDay,
    );
    if (new Date(now) >= aleagueStartDate) {
      from = currentYear.toString();
      to = (currentYear + 1).toString();
    } else {
      from = (currentYear - 1).toString();
      to = currentYear.toString();
    }
    const prefixFrom = from.substring(0, 2);
    const prefixTo = to.substring(0, 2);
    if (prefixFrom === prefixTo) {
      return {
        label: `${from}/${to.substring(2, 4)}`,
        name: `${from}-${to}`,
      };
    }
    return {
      label: `${from}/${to}`,
      name: `${from}-${to}`,
    };
  }
  return {
    label: currentYear.toString(),
    name: currentYear.toString(),
  };
}

export function getSportPageByCompLevelId(
  compLevelId: number,
): SportPage | undefined {
  const mapping: Record<number, SportPage> = {};
  Object.keys(Competition).forEach((sportPage) => {
    mapping[Competition[sportPage as SportPage].compLevelId] =
      sportPage as SportPage;
  });
  if (CompetitionLevelIds.includes(compLevelId)) {
    return mapping[compLevelId];
  }
  return undefined;
}

export function getScoreBySquadId(
  scores: TotalScoreType[] | undefined,
  squadId: number,
): number | undefined {
  return scores?.find((score) => score.squadId === squadId)?.score;
}

export function getAflGoals(
  scoreFlows: ScoreFlow[] | undefined,
  squadId: number,
): [string, number][] {
  if (!scoreFlows) {
    return [];
  }
  const goalsRecord: Record<string, number> = {};
  scoreFlows
    .filter(
      (scoreFlow) =>
        scoreFlow.squadId === squadId &&
        scoreFlow.scoreName === ScoreNameType.GOAL,
    )
    .forEach((scoreFlow) => {
      const key = `${scoreFlow.firstname} ${scoreFlow.surname}`;
      if (key in goalsRecord) {
        goalsRecord[key] += 1;
      } else {
        goalsRecord[key] = 1;
      }
    });
  return Object.entries(goalsRecord).sort(([, a], [, b]) => b - a);
}

export function getAflScores(
  periodScores: PeriodScoresProps[] | undefined,
  squadId: number,
): PeriodScoresProps[] {
  if (!periodScores) {
    return [];
  }
  return periodScores.filter((periodScore) => periodScore.squadId === squadId);
}

export function getAflGoalAndBehind(
  currentPeriod: number,
  periodScores: PeriodScoresProps[],
  squadId: number,
): string | null {
  const scores = getAflScores(periodScores, squadId);
  let goals = 0;
  let behinds = 0;

  for (let i = 0; i < scores.length; i++) {
    if (scores[i].period > currentPeriod) break;
    goals += scores[i].goals;
    behinds += scores[i].behinds;
  }
  return `${goals}.${behinds}`;
}

export function getOrdinalIndicator(num: number): string {
  return num < 10 || num > 20
    ? (['st', 'nd', 'rd'][(num % 10) - 1] ?? 'th')
    : 'th';
}

export function getNumOfPeriods(sport: Sport) {
  return sport === Sport.AFL ? 4 : 2;
}

export function getCurrentPeriod(
  sport: Sport,
  periodCompleted: number,
): number {
  const currentPeriod = periodCompleted + 1;
  const maxPeriod = getNumOfPeriods(sport);
  return currentPeriod > maxPeriod ? maxPeriod : currentPeriod;
}
