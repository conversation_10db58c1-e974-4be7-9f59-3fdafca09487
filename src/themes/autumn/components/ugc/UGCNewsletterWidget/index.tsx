import clsx from 'clsx';
import React, { useCallback, useEffect, useState } from 'react';
import slugify from 'slugify';

import { useAppSelector } from 'store/hooks';
import Button from 'themes/autumn/components/generic/Button';
import { fetchMailGroups } from 'util/mail';
import {
  subSuccessToast,
  useNewsletterData,
  useNewsletterWithSubFlow,
  useSubmitNewsletter,
} from 'util/newsletter';

const NOTICEBOARD_NEWSLETTER_ID = 'Whats On';

interface Props {
  isRHC?: boolean;
}

export default function UGCNewsletterWidget({
  isRHC = false,
}: Props): React.ReactElement | null {
  const [buttonClicked, setButtonClicked] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const user = useAppSelector((state) => state.piano.user);
  const newsletters = useNewsletterData();
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const hasAccess = useAppSelector((state) => state.piano.hasAccess);
  const email = user?.email || '';
  const noticeboardNewsletter =
    newsletters?.local?.[NOTICEBOARD_NEWSLETTER_ID.toLowerCase()] ?? null;
  const isAllowedAccess = !noticeboardNewsletter?.subscriberOnly || hasAccess;

  const onSubmit = useSubmitNewsletter({
    groupId: NOTICEBOARD_NEWSLETTER_ID,
    isNational: false,
  });

  const setEnabled = useCallback(
    (groupId: string, enabled: boolean) => {
      if (!isAllowedAccess) {
        return;
      }
      setButtonClicked(true);
      onSubmit(enabled);
    },
    [isAllowedAccess, onSubmit],
  );

  const handleClick = useNewsletterWithSubFlow(
    setEnabled,
    NOTICEBOARD_NEWSLETTER_ID,
    false,
    email,
    noticeboardNewsletter?.subscriberOnly || false,
    slugify(noticeboardNewsletter?.name || '', {
      lower: true,
      remove: /['']/g,
    }),
  );

  useEffect(() => {
    if (!pianoInitialized) {
      return;
    }

    if (window.localStorage) {
      const queuedGroupId = window.localStorage.getItem('queuedNewsletter');
      if (queuedGroupId !== NOTICEBOARD_NEWSLETTER_ID) {
        return;
      }

      if (user) {
        setEnabled(NOTICEBOARD_NEWSLETTER_ID, true);
      }
      window.localStorage.removeItem('queuedNewsletter');
    }
  }, [user, pianoInitialized, setEnabled]);

  useEffect(() => {
    if (buttonClicked && email && isAllowedAccess) {
      setButtonClicked(false);
      subSuccessToast(
        <div className="flex flex-col">
          <p className="text-sm font-medium leading-5 text-green-800">
            {`${noticeboardNewsletter?.name} added to`}
          </p>
          <p className="text-sm font-medium leading-5 text-green-800">
            {email}
          </p>
        </div>,
      );
      setIsSubscribed(true);
    }
  }, [buttonClicked, email, isAllowedAccess, noticeboardNewsletter?.name]);

  useEffect(() => {
    if (email) {
      fetchMailGroups()
        .then((data) => {
          if (data?.subscribedGroups[NOTICEBOARD_NEWSLETTER_ID]) {
            setIsSubscribed(true);
          }
        })
        .catch(console.error);
    }
  }, [email]);

  if (!noticeboardNewsletter || isSubscribed) {
    return null;
  }

  return (
    <div
      className={clsx(
        'relative mb-4 flex flex-col items-center space-y-4 border-b border-gray-300 pb-6 text-center text-gray-900',
        {
          'border-t pt-6': isRHC,
        },
      )}
    >
      <h2
        className={clsx('text-xl font-bold', {
          'md:text-2xl md:font-extrabold': !isRHC,
        })}
      >
        Subscribe to the What&apos;s On Newsletter
      </h2>
      <div
        className={clsx('text-xs', {
          'md:w-3/4 md:text-base': !isRHC,
          'px-2.5 md:text-sm': isRHC,
        })}
      >
        Stay in the know! Get a weekly roundup of local events, community
        notices, and community stories—delivered straight to your inbox.
      </div>
      <Button
        bgColor="bg-red-600"
        buttonClassName="md:px-8 md:py-6"
        className="relative"
        fontSize="text-sm md:text-base"
        hoverColor="hover:bg-red-700 active:bg-red-700"
        onClick={handleClick}
        text="Add me to the newsletter"
        textColor="text-white font-medium leading-6"
        type="button"
      />
    </div>
  );
}
