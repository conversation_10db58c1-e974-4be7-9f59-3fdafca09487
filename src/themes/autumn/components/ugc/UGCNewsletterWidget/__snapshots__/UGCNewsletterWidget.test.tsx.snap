// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<UGCNewsletterWidget /> renders correctly in RHC mode 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="relative mb-4 flex flex-col items-center space-y-4 border-b border-gray-300 pb-6 text-center text-gray-900 border-t pt-6"
  >
    <h2
      class="text-xl font-bold"
    >
      Subscribe to the What's On Newsletter
    </h2>
    <div
      class="text-xs px-2.5 md:text-sm"
    >
      Stay in the know! Get a weekly roundup of local events, community notices, and community stories—delivered straight to your inbox.
    </div>
    <div
      class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end relative"
    >
      <button
        class="flex items-center justify-center rounded-md bg-red-600 md:h-10 lg:h-10 hover:bg-red-700 active:bg-red-700 md:px-8 md:py-6 md:w-auto"
        type="button"
      >
        <span
          class="py-2 font-medium leading-6 text-sm md:text-base px-4 text-white font-medium leading-6"
        >
          Add me to the newsletter
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`<UGCNewsletterWidget /> renders correctly in default mode 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="relative mb-4 flex flex-col items-center space-y-4 border-b border-gray-300 pb-6 text-center text-gray-900"
  >
    <h2
      class="text-xl font-bold md:text-2xl md:font-extrabold"
    >
      Subscribe to the What's On Newsletter
    </h2>
    <div
      class="text-xs md:w-3/4 md:text-base"
    >
      Stay in the know! Get a weekly roundup of local events, community notices, and community stories—delivered straight to your inbox.
    </div>
    <div
      class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end relative"
    >
      <button
        class="flex items-center justify-center rounded-md bg-red-600 md:h-10 lg:h-10 hover:bg-red-700 active:bg-red-700 md:px-8 md:py-6 md:w-auto"
        type="button"
      >
        <span
          class="py-2 font-medium leading-6 text-sm md:text-base px-4 text-white font-medium leading-6"
        >
          Add me to the newsletter
        </span>
      </button>
    </div>
  </div>
</div>
`;
