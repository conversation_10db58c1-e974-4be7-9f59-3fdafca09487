import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';
import { fetchMailGroups } from 'util/mail';

import UGCNewsletterWidget from '.';

import type { EnhancedStore } from '@reduxjs/toolkit';
import type { RootState } from 'store/store';

jest.mock('util/newsletter', () => ({
  subSuccessToast: jest.fn(),
  useNewsletterClick: () => jest.fn(),
  useNewsletterData: () => ({
    local: {
      'whats on': {
        description: 'Test description',
        frequency: 'Weekly',
        icon: 'test-icon.png',
        name: 'Whats On',
        subscriberOnly: false,
      },
    },
    national: {},
  }),
  useNewsletterWithSubFlow: () => jest.fn(),
  useSubmitNewsletter: () => jest.fn(),
}));

jest.mock('util/mail', () => ({
  fetchMailGroups: jest.fn().mockResolvedValue({
    subscribedGroups: {},
  }),
}));

describe('<UGCNewsletterWidget />', () => {
  let store: EnhancedStore<RootState>;

  beforeEach(() => {
    store = createStore((state) => ({
      ...state,
      piano: {
        ...state.piano,
        initialized: true,
        user: null,
      },
    }));
  });

  it('renders correctly in default mode', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <UGCNewsletterWidget isRHC={false} />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders correctly in RHC mode', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <UGCNewsletterWidget isRHC />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('does not render when user is already subscribed', async () => {
    expect.assertions(1);

    jest.mocked(fetchMailGroups).mockResolvedValue({
      defaultInterests: [],
      groups: {},
      isSubscribed: true,
      nationalLists: {},
      requiresManualResubscribe: false,
      subscribedGroups: {
        'Whats On': true,
      },
      subscribedNational: {},
    });

    store = createStore((state) => ({
      ...state,
      piano: {
        ...state.piano,
        hasDPEAccess: false,
        user: {
          aud: 'test',
          confirmed: true,
          email: 'test',
          email_confirmation_required: false,
          exp: 123,
          family_name: 'test',
          firstName: 'test',
          given_name: 'test',
          iat: 123,
          iss: 'test',
          jti: 'test',
          lastName: 'test',
          login_timestamp: 'test',
          ls: 'test',
          passwordType: 'test',
          r: true,
          sub: 'test',
          uid: 'test',
          valid: true,
        },
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <UGCNewsletterWidget />
      </TestWrapper>,
    );

    await new Promise((resolve) => {
      setTimeout(resolve, 100);
    });

    expect(
      container.querySelector('.relative.mb-4.flex.flex-col.items-center'),
    ).toBeNull();
  });
});
