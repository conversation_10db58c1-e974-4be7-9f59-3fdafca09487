// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ShareWidget /> renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="flex h-96 flex-col justify-center rounded-lg bg-teal-50"
  >
    <div
      class="mx-7 flex flex-col space-y-4 text-start sm:items-start md:items-center lg:items-start"
    >
      <div>
        <div>
          Icon
        </div>
      </div>
      <div
        class="text-xl font-semibold text-green-900 md:text-2xl"
      >
        Test Title
      </div>
      <div
        class="text-sm text-green-900"
      >
        Test Subtitle
      </div>
      <a
        class="h-12 w-full rounded-lg bg-green-700 p-3 pb-7 text-center font-medium text-white no-underline transition-colors hover:bg-green-500"
        href="/test-link"
        target="_blank"
      >
        Test Link
      </a>
    </div>
  </div>
</div>
`;
