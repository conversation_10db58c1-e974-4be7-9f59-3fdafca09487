import { render } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import ShareWidget from '.';

describe('<ShareWidget />', () => {
  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        mode: RenderMode.NORMAL,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <ShareWidget
          icon={<div>Icon</div>}
          linkHref="/test-link"
          linkText="Test Link"
          subtitle="Test Subtitle"
          title="Test Title"
        />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
