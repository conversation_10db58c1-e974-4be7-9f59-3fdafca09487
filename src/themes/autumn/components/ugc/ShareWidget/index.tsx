import React from 'react';

import Link from 'themes/autumn/components/generic/Link';

export interface UGChareWidgetProps {
  icon: React.ReactNode;
  linkHref: string;
  linkText: string;
  subtitle: string;
  title: string;
}

export default function ShareWidget({
  icon,
  linkHref,
  linkText,
  subtitle,
  title,
}: UGChareWidgetProps): React.ReactElement {
  return (
    <div className="flex h-96 flex-col justify-center rounded-lg bg-teal-50">
      <div className="mx-7 flex flex-col space-y-4 text-start sm:items-start md:items-center lg:items-start">
        <div>{icon}</div>
        <div className="text-xl font-semibold text-green-900 md:text-2xl">
          {title}
        </div>
        <div className="text-sm text-green-900">{subtitle}</div>
        <Link
          className="h-12 w-full rounded-lg bg-green-700 p-3 pb-7 text-center font-medium text-white no-underline transition-colors hover:bg-green-500"
          href={linkHref}
          noStyle
          target="_blank"
        >
          {linkText}
        </Link>
      </div>
    </div>
  );
}
