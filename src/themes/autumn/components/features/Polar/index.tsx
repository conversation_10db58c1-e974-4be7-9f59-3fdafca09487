import React from 'react';

import { useAppSelector } from 'store/hooks';
import { ThemeVariant } from 'store/slices/conf';
import { AdSize } from 'util/ads';

import Ad from '../../ads/Ad';

import styles from './index.module.css';

function Polar(): React.ReactElement | null {
  const polar = useAppSelector((state) => state.features.polar);
  const themeVariant = useAppSelector((state) => state.conf.themeVariant);

  if (!polar.enabled) {
    return null;
  }

  return (
    <>
      <Ad
        className={
          themeVariant === ThemeVariant.MOP ? styles.mop_overrides : undefined
        }
        isBrandedContent
        position={1}
        // TODO Consider defaulting to `slotId` if not set
        publiftName="polar"
        sizes={AdSize.polar}
        slotId="polar"
        withLabel={false}
        withPlaceholder={false}
        withPlaceholderBackground={false}
      />
      {/* Polar is adding `display: none` to next closest div so
          a dummy div is necessary to avoid hiding storylist items.
      */}
      <div className="h-0 overflow-hidden" />
    </>
  );
}

export default Polar;
