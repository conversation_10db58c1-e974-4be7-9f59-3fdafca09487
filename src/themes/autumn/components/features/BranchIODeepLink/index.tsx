'use client';

import Script from 'next/script';
import React from 'react';

import { useAppSelector } from 'store/hooks';

function BranchIODeepLink(): React.ReactElement | null {
  const mobileAppFeature = useAppSelector((state) => state.features.mobileApp);
  const story = useAppSelector((state) => state.story);
  const isStory = !!story.id;

  if (
    !isStory ||
    !mobileAppFeature.enabled ||
    !mobileAppFeature.data.branchIoPublicKey
  )
    return null;

  const { branchIoPublicKey } = mobileAppFeature.data;

  return (
    <Script
      id="branch-deeplink-script"
      onLoad={() => {
        window.branch.init(branchIoPublicKey);
      }}
      src="https://cdn.branch.io/branch-latest.min.js"
      strategy="lazyOnload"
    />
  );
}

export default BranchIODeepLink;
