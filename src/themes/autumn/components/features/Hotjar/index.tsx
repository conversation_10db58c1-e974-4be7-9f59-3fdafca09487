'use client';

import { useEffect, useState } from 'react';
import { hotjar } from 'react-hotjar';

import { useAppSelector } from 'store/hooks';

import type React from 'react';

export interface HotJarProps {
  hjid: number;
  hjsv: number;
}

/* TODO: enable hotjar for single page apps on hotjar server */
function HotJar(): React.ReactElement | null {
  const [initialized, setInitialized] = useState(false);
  const hotJarFeature = useAppSelector((state) => state.features.hotjar);

  function convertHotJarSettings(idString: string): HotJarProps {
    const properties = idString.split(/,/g);
    const hotJarSettings: Record<string, number> = {};
    properties.forEach((property) => {
      const tup = property.split(':');

      hotJarSettings[tup[0]] = parseInt(tup[1], 10);
    });
    return hotJarSettings as unknown as HotJarProps;
  }

  useEffect(() => {
    if (hotJarFeature?.enabled && !initialized) {
      setInitialized(true);
      const { hjid, hjsv } = convertHotJarSettings(hotJarFeature.data.id);
      if (hjid && hjsv) {
        hotjar.initialize({ id: hjid, sv: hjsv });
      }
    }
  }, [hotJarFeature, initialized]);

  return null;
}

export default HotJar;
