import Logo from '../Logo';

import { alt, src } from './constants';

interface Props {
  hideIcon?: boolean;
  iconClassName?: string;
  imgClassName?: string;
}

export default function CarExpertLogo({
  hideIcon = false,
  iconClassName,
  imgClassName,
}: Props): React.ReactElement {
  return (
    <Logo
      alt={alt}
      hideIcon={hideIcon}
      iconClassName={iconClassName}
      imgClassName={imgClassName}
      src={src}
    />
  );
}
