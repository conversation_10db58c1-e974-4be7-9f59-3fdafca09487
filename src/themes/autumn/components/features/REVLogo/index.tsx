import Logo from '../Logo';

const alt = 'View';
const srcPrefix = 'https://cdn.newsnow.io/209997574/';
const src = `${srcPrefix}76e5113c-0f81-4f71-9440-78aab9288ce4.svg`;

interface Props {
  hideIcon?: boolean;
  iconClassName?: string;
  imgClassName?: string;
}

export default function REVLogo({
  hideIcon = false,
  iconClassName,
  imgClassName,
}: Props): React.ReactElement {
  return (
    <Logo
      alt={alt}
      hideIcon={hideIcon}
      iconClassName={iconClassName}
      imgClassName={imgClassName}
      src={src}
    />
  );
}
