import Script from 'next/script';
import React from 'react';

import { useAppSelector } from 'store/hooks';

function MicrosoftClarity(): React.ReactElement | null {
  const feature = useAppSelector((state) => state.features.microsoftClarity);

  if (!feature.enabled) return null;

  return (
    <Script id="clarity-script" strategy="lazyOnload">
      {`(function(c,l,a,r,i,t,y){
          c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
          t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
          y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "${feature.data.clarityId}");
      `}
    </Script>
  );
}

export default MicrosoftClarity;
