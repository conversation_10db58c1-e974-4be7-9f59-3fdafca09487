import { faExternalLinkAlt } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

interface Props {
  alt: string;
  hideIcon?: boolean;
  iconClassName?: string;
  imgClassName?: string;
  mdSrc?: string;
  src: string;
}

export default function Logo({
  alt,
  hideIcon = false,
  iconClassName,
  imgClassName,
  mdSrc,
  src,
}: Props): React.ReactElement {
  return (
    <>
      <picture>
        {mdSrc !== undefined && (
          <source media="(min-width: 768px)" srcSet={mdSrc} />
        )}
        <img
          alt={alt}
          className={clsx('inline h-8 md:h-auto', imgClassName)}
          loading="lazy"
          src={src}
        />
      </picture>
      {!hideIcon && (
        <FontAwesomeIcon
          className={clsx('text-sm', iconClassName)}
          color="rgba(29, 29, 29, 0.54)"
          icon={faExternalLinkAlt}
        />
      )}
    </>
  );
}
