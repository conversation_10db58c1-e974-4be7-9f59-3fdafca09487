import clsx from 'clsx';
import { ParallaxBanner } from 'react-scroll-parallax';

import Link from '../generic/Link';

interface ParallaxProps {
  bgOpacity?: string;
  btnText?: string;
  className?: string;
  description?: string;
  imageUrl: string;
  openNewWindow?: boolean;
  speed?: number;
  title: string;
  url?: string;
}

export default function Parallax({
  bgOpacity = 'opacity-30',
  btnText,
  // eslint-disable-next-line @stylistic/max-len
  className = 'aspect-square h-112 md:aspect-[1.8/1] lg:aspect-[2.5/1] lg:h-128 xl:aspect-[3.5/1]',
  description,
  imageUrl,
  openNewWindow = false,
  speed = -30,
  title,
  url,
}: ParallaxProps): React.ReactElement {
  const opacity = (
    <div
      className={clsx(
        'absolute left-0 top-0 block size-full bg-neutral-900',
        bgOpacity,
      )}
    />
  );

  const content = (
    <div className="z-10 mx-auto flex size-full max-w-sm flex-col items-center justify-center text-center font-medium md:max-w-lg md:px-6 xl:px-0">
      <div className="mx-2 line-clamp-3 text-2xl tracking-wide text-white md:text-heading2">
        {title}
      </div>
      {description && (
        <div className="mx-4 mt-4 line-clamp-3 max-w-2xl text-center text-sm tracking-wide text-white md:mx-auto md:text-base">
          {description}
        </div>
      )}
      {url && btnText && (
        <Link
          className="mt-4 rounded border-2 border-white/20 bg-white/10 px-28 py-2 font-inter text-base text-white shadow-sm hover:bg-white/30 md:px-12 md:py-3.5"
          href={url}
          noStyle
          target={openNewWindow ? '_blank' : undefined}
        >
          {btnText}
        </Link>
      )}
    </div>
  );

  return (
    <ParallaxBanner
      className={className}
      layers={[
        {
          children: opacity,
          image: imageUrl,
          speed,
        },
        {
          children: content,
        },
      ]}
    />
  );
}
