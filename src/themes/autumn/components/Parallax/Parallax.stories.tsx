import React from 'react';
import { ParallaxProvider } from 'react-scroll-parallax';

import { getRandomImageUrl, loremIpsumGenerator } from 'util/storybook';

import Parallax from '.';

import type { Meta, StoryFn } from '@storybook/nextjs-vite';

export default {
  component: Parallax,
  title: 'Components/Parallax',
} as Meta<typeof Parallax>;

/* eslint-disable react/jsx-props-no-spreading */
const TemplateDefault: StoryFn<typeof Parallax> = (args) => (
  <ParallaxProvider>
    <div className="flex h-96 w-full items-center justify-center bg-blue-50">
      Content
    </div>
    <Parallax {...args} />
    <div className="flex h-96 w-full items-center justify-center bg-blue-50">
      Content
    </div>
  </ParallaxProvider>
);
/* eslint-enable react/jsx-props-no-spreading */

export const Default = TemplateDefault.bind({});

Default.args = {
  btnText: 'Explore',
  description: loremIpsumGenerator(),
  imageUrl: getRandomImageUrl(1080, 1920),
  openNewWindow: true,
  title: loremIpsumGenerator(),
  url: 'https://google.com',
};
