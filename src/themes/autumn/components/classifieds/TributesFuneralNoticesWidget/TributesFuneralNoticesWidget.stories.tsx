import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import TributesFuneralNoticesWidget from '.';

import type { Meta } from '@storybook/nextjs-vite';
import type { ClassifiedAd } from 'types/Classified';

export default {
  component: TributesFuneralNoticesWidget,
  title: 'Notice board/Tributes & Funeral Notices widget',
} as Meta<typeof TributesFuneralNoticesWidget>;

function mockAd(options?: Partial<ClassifiedAd>): ClassifiedAd {
  return {
    canonicalUrl: '/',
    category: {
      id: 0,
      name: '',
      slug: '',
    },
    categoryText: '',
    customerEmail: '',
    customerName: '',
    customerPhone: '',
    customerPostcode: '',
    customerState: '',
    customerTown: '',
    dateBorn: '1/3/1970',
    dateDeceased: '2/3/2025',
    enableComments: false,
    expirationDate: '',
    funeralDate: '',
    funeralHomeAddress: '',
    funeralHomeCity: '',
    funeralHomeName: '',
    funeralHomePostcode: '',
    funeralHomeState: '',
    funeralStartTime: '',
    funeralVenueAddress: '',
    funeralVenueCity: '',
    funeralVenueName: '',
    funeralVenuePostcode: '',
    funeralVenueState: '',
    id: 1,
    images: [],
    location: 'Sydney, NSW',
    logo: null,
    publicationDate: '2025-01-01',
    quoteText: '',
    text: '',
    title: 'Mr Smith',
    url: '',
    yearBorn: null,
    yearDeceased: null,
    ...options,
  };
}

const classifieds = [mockAd(), mockAd(), mockAd(), mockAd()];

export const Default = () => (
  <TestWrapper store={createStore()}>
    <div className="@container">
      <TributesFuneralNoticesWidget
        classifieds={classifieds}
        title="Tributes & Funerals"
      />
    </div>
  </TestWrapper>
);

export const NarrowContainer = () => (
  <TestWrapper store={createStore()}>
    <div className="border @container" style={{ width: 320 }}>
      <TributesFuneralNoticesWidget
        classifieds={classifieds}
        title="Tributes & Funerals"
      />
    </div>
  </TestWrapper>
);
