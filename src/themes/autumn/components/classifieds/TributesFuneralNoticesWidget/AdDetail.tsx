'use client';

import React, { useCallback } from 'react';

import Link from 'themes/autumn/components/generic/Link';
import {
  getTributeAgeDetails,
  sendGtmEvent,
} from 'themes/autumn/templates/Classifieds/utils';
import { capitalize, formatLocationShort } from 'util/string';

import type { ClassifiedAd } from 'types/Classified';

interface Props {
  ad: ClassifiedAd;
  image: React.JSX.Element | null;
  onClick?: () => void;
  ref?: React.Ref<HTMLDivElement>;
}

function formatDate(date: string) {
  const day = new Date(date).toLocaleDateString('en-US', {
    day: 'numeric',
  });
  const month = new Date(date).toLocaleDateString('en-US', {
    month: 'short',
  });
  return `Published ${day} ${month}`;
}

export default function AdDetail({ ad, image, onClick, ref }: Props) {
  const handleLinkClick = useCallback(() => {
    onClick?.();
    sendGtmEvent(ad, 'ad_click');
  }, [ad, onClick]);

  if (!image) {
    return null;
  }

  return (
    <div
      className="flex h-full flex-col items-center justify-center rounded-md border border-gray-300 p-4 text-center md:rounded-none md:border-0"
      ref={ref}
    >
      <Link
        className="flex w-[100px] justify-center"
        href={ad.canonicalUrl}
        onClick={handleLinkClick}
      >
        {image}
      </Link>
      <h3 className="grow">
        <Link
          className="mb-4 mt-2 inline-block text-sm font-semibold text-gray-900 no-underline visited:text-gray-900 hover:text-gray-900 hover:opacity-70"
          href={ad.canonicalUrl}
          onClick={handleLinkClick}
        >
          {capitalize(ad.title)}
          <br />
          {getTributeAgeDetails(ad)[0]}
        </Link>
      </h3>
      {ad.location && (
        <div className="text-sm font-normal text-gray-600">
          {formatLocationShort(ad.location)}
        </div>
      )}
      {ad.publicationDate && (
        <div className="mt-1 text-xs font-normal text-gray-600">
          {formatDate(ad.publicationDate)}
        </div>
      )}
    </div>
  );
}
