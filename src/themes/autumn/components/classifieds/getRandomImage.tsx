import type { ClassifiedAd } from 'types/Classified';

const imageCount = 24;

export default function getRandomImage(ad: ClassifiedAd) {
  // Combine the ID and date for a decently sized seed
  const input = ad.id * new Date(ad.publicationDate).getTime();

  // mulberry32 PRNG for reproducable random images based on ad ID to avoid
  // changes between SSR and CSR
  /* eslint-disable no-bitwise */
  const seed = ((input | 0) + 0x9e3779b9) | 0;
  let random = seed ^ (seed >>> 15);
  random = Math.imul(random, 0x85ebca6b);
  random ^= random >>> 13;
  random = Math.imul(random, 0xc2b2ae35);
  random = ((random ^= random >>> 16) >>> 0) / 4294967296;
  /* eslint-enable no-bitwise */

  const index = Math.floor(random * imageCount) + 1;
  return `tributes-funerals/tributes-placeholder-${index}.jpg`;
}
