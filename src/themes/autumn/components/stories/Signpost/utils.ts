import { withinLast } from 'util/time';

export const TAGS_MAX_NUMBER = -1;

export function getVisibleTags(
  now: Date,
  tags: string[],
  publishFrom: string,
  hideSubscriberSignposts: boolean,
  hideFreeSignposts: boolean,
  max = 1,
): string[] {
  if (!tags) {
    return [];
  }

  const signPostTags = tags.filter((t) => {
    if (t === 'signpost-breaking') {
      return withinLast(now, new Date(publishFrom), 1000 * 60 * 60 * 12);
    }
    if (/^signpost-subscriber/.test(t)) {
      return !hideSubscriberSignposts;
    }
    if (/^signpost-free/.test(t)) {
      return !hideFreeSignposts;
    }
    return t.startsWith('signpost-');
  });

  return max === TAGS_MAX_NUMBER ? signPostTags : signPostTags.splice(0, max);
}
