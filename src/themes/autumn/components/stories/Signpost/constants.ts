enum SignpostColor {
  GRAY = 'text-gray-800 bg-gray-100',
  GRAY_2 = 'text-gray-800 bg-gray-200',
  RED = 'text-red-800 bg-red-100',
  BLUE = 'text-blue-800 bg-blue-100',
  GREEN = 'text-green-800 bg-green-100',
  GREEN_NO_BACKGROUND = 'text-green-550 bg-transparent',
  YELLOW = 'text-yellow-800 bg-yellow-100',
  INDIGO = 'text-indigo-800 bg-indigo-100',
  ECHIDNA = 'text-white bg-gray-900',
}

export enum ColorPalette {
  PALETTE_DEFAULT = 'palette_default',
  PALETTE_GRAY_BG = 'palette_gray_bg',
  PALETTE_ECHIDNA = 'palette_echidna',
  PALETTE_EXPLORE = 'palette_explore',
  PALETTE_SPORT = 'palette_sport',
}
type PaletteColors = Record<string, SignpostColor>;

export const PALETTES: Record<ColorPalette, PaletteColors> = {
  [ColorPalette.PALETTE_DEFAULT]: {
    breaking: SignpostColor.RED,
    default: SignpostColor.GRAY,
    exclusive: SignpostColor.RED,
    free: SignpostColor.GREEN,
    live: SignpostColor.RED,
    'real-australia': SignpostColor.GREEN,
    subscriber: SignpostColor.BLUE,
    warning: SignpostColor.YELLOW,
  },
  [ColorPalette.PALETTE_GRAY_BG]: {
    breaking: SignpostColor.RED,
    default: SignpostColor.GRAY_2,
    exclusive: SignpostColor.RED,
    free: SignpostColor.GREEN,
    live: SignpostColor.RED,
    'real-australia': SignpostColor.GREEN,
    subscriber: SignpostColor.BLUE,
    warning: SignpostColor.YELLOW,
  },
  [ColorPalette.PALETTE_ECHIDNA]: {
    default: SignpostColor.ECHIDNA,
  },
  [ColorPalette.PALETTE_EXPLORE]: {
    breaking: SignpostColor.RED,
    default: SignpostColor.YELLOW,
  },
  [ColorPalette.PALETTE_SPORT]: {
    default: SignpostColor.GREEN_NO_BACKGROUND,
  },
};
