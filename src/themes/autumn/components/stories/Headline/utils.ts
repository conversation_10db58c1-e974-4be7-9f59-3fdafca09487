/* eslint-disable import/prefer-default-export */

import { SummaryOption } from 'util/constants';

export const showSummary = (
  showSummaryInStoryList: boolean,
  storySummary: string,
  storyShowSummary: boolean | undefined,
  summaryOption: SummaryOption,
): boolean =>
  showSummaryInStoryList &&
  !!storySummary &&
  ((storyShowSummary && summaryOption === SummaryOption.NEWSNOW_ENABLED) ||
    summaryOption === SummaryOption.ALWAYS_SHOW);
