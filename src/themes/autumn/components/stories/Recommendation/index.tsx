import React from 'react';

import { useAppSelector } from 'store/hooks';

export default function Recommendation({
  className,
}: {
  className?: string;
}): React.ReactElement {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const hasPianoPaywall = useAppSelector((state) => state.piano.hasPaywall);

  return (
    <>
      {pianoFeature.enabled && !hasPianoPaywall && (
        <div
          className={`${className} min-h-px empty:hidden`}
          id="exploreTravelRecommendationArticle"
        />
      )}
    </>
  );
}
