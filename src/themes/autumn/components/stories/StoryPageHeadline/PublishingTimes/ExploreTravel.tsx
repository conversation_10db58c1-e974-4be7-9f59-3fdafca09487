import { isEqual, isSameDay } from 'date-fns';
import React from 'react';

import { toDisplayDate, toDisplayDateTime, toDisplayTime } from 'util/time';

import type { PublishingTimesProps } from './common';

export default function ExploreTravelPublishingTimes({
  publishFrom,
  updatedOn,
}: PublishingTimesProps): React.ReactElement {
  const areDatesSameDay = isSameDay(updatedOn, publishFrom);
  const areDatesEqual = isEqual(publishFrom, updatedOn);

  let updatedOnText: string | undefined = toDisplayDate(updatedOn);
  let publishFromText = toDisplayDate(publishFrom);

  if (areDatesEqual) {
    updatedOnText = undefined;
  } else if (areDatesSameDay) {
    publishFromText = toDisplayTime(publishFrom);
    updatedOnText = toDisplayDateTime(updatedOn);
  }

  return (
    <div className="mt-2">
      {!!updatedOnText && (
        <>
          <span className="text-xs/[18px] font-bold text-gray-500">
            Updated&nbsp;
          </span>
          <span className="text-xs/[18px] font-normal text-gray-500">
            {updatedOnText},&nbsp;first&nbsp;published&nbsp;
          </span>
        </>
      )}
      <span className="text-xs/[18px] font-normal text-gray-500">
        {publishFromText}
      </span>
    </div>
  );
}
