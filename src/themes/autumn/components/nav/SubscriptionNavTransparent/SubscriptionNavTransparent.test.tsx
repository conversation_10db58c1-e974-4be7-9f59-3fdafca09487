import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import SubscriptionNavTransparent from '.';

describe('subscription nav transparent', () => {
  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        logoSvgOnly: 'logo.svg',
      },
      page: {
        ...state.page,
        name: 'Page Name',
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <SubscriptionNavTransparent />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
