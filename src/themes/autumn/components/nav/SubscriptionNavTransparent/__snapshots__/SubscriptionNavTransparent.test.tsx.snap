// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`subscription nav transparent renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="h-0 w-full"
  />
  <div
    class="top-0 z-50 flex h-16 w-full items-center justify-center transition duration-200 relative bg-transparent text-white"
  >
    <div
      class="mx-auto flex w-full max-w-[960px] items-center justify-between px-10 lg:px-0"
    >
      <div
        class="flex items-center"
      >
        <a
          class="block h-6 w-28 md:h-9 md:w-48 brightness-200 contrast-200 invert text-blue-600 underline decoration-blue-400 hover:text-blue-600 hover:decoration-blue-600 visited:text-gray-500 visited:decoration-gray-500"
          data-testid="logo"
          href="/"
        >
          <img
            alt=""
            class="size-full max-h-full max-w-full object-contain"
          />
        </a>
        <div
          class="ml-2 truncate border-l border-solid pl-4 font-merriweather capitalize border-white/50"
        >
          Page Name
        </div>
      </div>
      <div
        class="hidden flex-row md:flex"
      >
        <a
          class="mr-6 text-sm no-underline"
          href="/contact/"
        >
          Contact
        </a>
        <a
          class="text-sm no-underline"
          href="/help-centre/"
          target="_blank"
        >
          Help
        </a>
      </div>
    </div>
  </div>
</div>
`;
