import React from 'react';
import { createPortal } from 'react-dom';

import { useAppSelector } from 'store/hooks';
import { DeviceType } from 'util/device';
import { useDeviceTypeFromWidth } from 'util/hooks';

function MostViewedNavBar(): React.ReactElement | null {
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);

  const pianoEnabled = useAppSelector((state) => state.features.piano.enabled);
  const hasBottomAnchorAd = useAppSelector(
    (state) => state.adServing.hasBottomAnchorAd,
  );
  const isPipEnabledMobile = useAppSelector(
    (state) => state.conf.dailymotionPipEnabledMobile,
  );
  const isMobile = useDeviceTypeFromWidth() === DeviceType.MOBILE;

  if (
    !isClientSide ||
    !pianoEnabled ||
    (!isMobile && hasBottomAnchorAd) ||
    (isMobile && isPipEnabledMobile)
  ) {
    return null;
  }

  const mobileContainer = document.querySelector('#mostViewedNavBarMobile');
  const desktopContainer = document.querySelector('#mostViewedNavBarDesktop');

  if (!mobileContainer || !desktopContainer) {
    return null;
  }

  return (
    <>
      {createPortal(
        <div className="w-full" id="mostViewedNavBar" />,
        isMobile ? mobileContainer : desktopContainer,
      )}
    </>
  );
}

export default MostViewedNavBar;
