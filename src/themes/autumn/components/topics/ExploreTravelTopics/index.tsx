import clsx from 'clsx';
import React from 'react';
import slugify from 'slugify';

import { getTopicsFromStoryTags } from 'util/story';

import Link from '../../generic/Link';

export interface ExploreTravelTopicsProps {
  tags: string[];
}

const ExploreTravelTopics = ({
  tags,
}: ExploreTravelTopicsProps): React.ReactElement | null => {
  const topicTags: string[] = getTopicsFromStoryTags(tags);

  if (topicTags.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-row items-center gap-6">
      <div className="font-inter text-sm text-gray-900">Topics:</div>
      <div className="flex flex-row flex-wrap gap-3">
        {topicTags.map((topicTag) => (
          <div className="rounded-full bg-gray-100 px-4 py-1" key={topicTag}>
            <Link
              className={clsx(
                'items-center justify-center text-sm font-medium',
              )}
              href={`/topic/${slugify(topicTag).toLocaleLowerCase()}`}
              noStyle
              target="_blank"
            >
              {topicTag}
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExploreTravelTopics;
