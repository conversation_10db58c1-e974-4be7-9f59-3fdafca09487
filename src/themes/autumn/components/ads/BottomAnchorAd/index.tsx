'use client';

import { useEffect } from 'react';

import { useAppDispatch, useAppSelector } from 'store/hooks';
import adServingSlice from 'store/slices/adServing';
import Ad from 'themes/autumn/components/ads/Ad';
import { DeviceType } from 'util/device';
import { useDeviceTypeFromWidth } from 'util/hooks';

function watchAnchorElement(element: HTMLElement) {
  const observer = new MutationObserver(() => {
    const status = element.getAttribute('data-anchor-status');
    document.body.style.paddingBottom = status === 'dismissed' ? '0' : '95px';
  });

  observer.observe(element, {
    attributeFilter: ['data-anchor-status'],
    attributes: true,
  });

  return observer;
}

export default function BottomAnchorAd(): React.ReactElement | null {
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  const deviceType = useDeviceTypeFromWidth();
  const viewType = useAppSelector((state) => state.settings.viewType);
  const dispatch = useAppDispatch();
  const enable = useAppSelector(
    (state) =>
      state.features.adServing.enabled &&
      state.features.adServing.data.useBottomAnchorAd,
  );
  const isGalleryOpen = useAppSelector((state) => state.page.isGalleryOpen);
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const position = useAppSelector((state) =>
    state.features.adServing.enabled
      ? state.features.adServing.data.bottomAnchorAdPosition
      : 1,
  );

  const show =
    enable &&
    isClientSide &&
    deviceType === DeviceType.DESKTOP &&
    !isGalleryOpen &&
    !hasTakeoverAd &&
    viewType === 'story';

  useEffect(() => {
    dispatch(adServingSlice.actions.setBottomAnchorAd(show));
  }, [dispatch, show]);

  useEffect(() => {
    if (!show) {
      return undefined;
    }

    let element = Array.prototype.find.call(
      document.documentElement.children,
      (child: HTMLElement) =>
        child.tagName === 'INS' && child.id.startsWith('gpt_unit'),
    ) as HTMLElement | undefined;

    let observer: MutationObserver;
    let insObserver: MutationObserver;
    if (element) {
      insObserver = watchAnchorElement(element);
    } else {
      observer = new MutationObserver(() => {
        // eslint-disable-next-line prefer-destructuring
        element = document.getElementsByTagName('ins')[0];

        if (!element?.getAttribute('id')?.startsWith('gpt_unit')) {
          return;
        }

        insObserver = watchAnchorElement(element);

        observer.disconnect();
      });

      observer.observe(document.documentElement, {
        childList: true,
      });
    }

    return () => {
      observer?.disconnect();
      insObserver?.disconnect();
    };
  }, [show]);

  if (!show) {
    return null;
  }

  return (
    <Ad
      position={position}
      // TODO Not yet defined
      publiftName="anchor"
      renderOutOfThePage
      sizes={[1, 1]}
      slotId="BOTTOM_ANCHOR"
      withLabel={false}
      withPlaceholder={false}
      withPlaceholderBackground={false}
    />
  );
}
