/*
 Gutter ads (also called takeover ads) load into the 1x11 slot. The `body` gets
 a `.has-gutters` class and two elements are prepended to `#main`:
 `div.gutter.gutter-left` and `div.gutter.gutter-right` which both contain
 anchors.

 If this ad renders, `page.hasGutterAd` becomes `true` in the global state. The
 news well collapses into the tablet view to make room for the gutter ads, and
 the billboard ad moves from being above the nav, to being below the nav,
 surrounding the news well in ads.

 All this behaviour is expected on desktop only.
 */
import clsx from 'clsx';

import { useAppDispatch } from 'store/hooks';
import pageSlice from 'store/slices/page';
import { AdSize, GUTTER_AD_CONTAINER_ID } from 'util/ads';
import { useLazyLoadComponentState } from 'util/hooks';

import Ad from '../Ad';

import styles from './styles.module.css';

export default function GutterAd() {
  const { showComponentPlaceholder } = useLazyLoadComponentState();
  const dispatch = useAppDispatch();

  if (!showComponentPlaceholder) {
    return null;
  }

  return (
    <>
      <div
        className={clsx(
          'relative mx-auto hidden w-full max-w-sm justify-center md:max-w-lg lg:flex',
          styles.wrapper,
        )}
        id={GUTTER_AD_CONTAINER_ID}
      />
      <Ad
        lgSizes={AdSize.gutter}
        onRender={({ event }) => {
          if (event.size) {
            dispatch(pageSlice.actions.setHasGutterAd());
          }
        }}
        position={1}
        // TODO Not yet defined
        publiftName="gutter"
        sizes={[]}
        slotId="gutter"
        withLabel={false}
        withPlaceholder={false}
        withPlaceholderBackground={false}
      />
    </>
  );
}
