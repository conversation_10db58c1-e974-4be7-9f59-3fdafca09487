import Script from 'next/script';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import { usePremiumSubscription } from 'util/hooks';
import { isSponsoredPage } from 'util/page';

export default function HindSight(): React.ReactElement | null {
  const hindSight = useAppSelector((state) => state.features.hindSight);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const {
    hasPremiumExtended,
    hasValidatedPremium,
    supportPremiumExtended,
    supportPremiumSubscription,
  } = usePremiumSubscription();
  const waitingForPremiumValidation =
    supportPremiumSubscription &&
    supportPremiumExtended &&
    !hasValidatedPremium;
  const hideForPremiumExtended =
    supportPremiumSubscription && supportPremiumExtended && hasPremiumExtended;

  if (
    !hindSight.enabled ||
    isSponsoredPage(viewType) ||
    waitingForPremiumValidation ||
    hideForPremiumExtended
  ) {
    return null;
  }

  return (
    <Script
      async
      id="hindsight-script"
      src={
        'https://static.solutionshindsight.net/' +
        'teju-webclient/hindsight-webclient.min.js'
      }
    />
  );
}
