'use client';

import Script from 'next/script';
import { useEffect } from 'react';

import { useAppDispatch, useAppSelector } from 'store/hooks';
import adServingSlice from 'store/slices/adServing';
import DFPManager from 'themes/autumn/components/ads/GAM/dfp/DFPManager';

import type { RegisteredSlot } from 'themes/autumn/components/ads/GAM/dfp/DFPManager';

function enqueue(c: 'f' | 'i', r: unknown[]) {
  // eslint-disable-next-line no-underscore-dangle
  window.apstag?._Q.push([c, r]);
}

// Exclude any special slots e.g. 6x1 Polar ad
// Supported sizes: 320x50, 120x240, 336x280, 728x90, 250x250, 300x250,
// 300x600, 300x100, 300x300, 300x50, 300x75, 970x90, 300x1050, 468x60,
// 970x250, 400x300, 320x100, 120x600, 160x60
function isAllowedSlotSize(size: googletag.SingleSizeArray) {
  return size[0] !== 996 && size[0] !== 940 && size[1] !== 1;
}

function slotToApsData(slot: RegisteredSlot) {
  return {
    // Exclude unsupported sizes from the bid request
    sizes: slot.sizes.filter(isAllowedSlotSize),
    slotID: slot.slotId,
    slotName: `${slot.dfpNetworkId}${slot.adUnit ? `/${slot.adUnit}` : ''}/${
      slot.slotId
    }`,
  };
}

export default function AmazonPublisherServices() {
  const dispatch = useAppDispatch();
  const publisherId = useAppSelector(
    (state) =>
      state.features.adServing.enabled &&
      state.features.adServing.data.apsPublisherId,
  );

  useEffect(() => {
    if (!publisherId) {
      return undefined;
    }

    window.apstag = {
      _Q: [],
      fetchBids(...args) {
        enqueue('f', args);
      },
      init(...args) {
        enqueue('i', args);
      },
      setDisplayBids() {},
      targetingKeys() {
        return [];
      },
    };

    window.apstag.init({
      adServer: 'googletag',
      pubID: publisherId,
      simplerGPT: true,
    });

    const slots = Object.values(DFPManager.getRegisteredSlots())
      .map(slotToApsData)
      .filter(({ sizes }) => sizes.length !== 0);

    if (slots.length) {
      window.apstag.fetchBids(
        {
          slots,
          timeout: 2e3,
        },
        () => {
          googletag.cmd.push(() => {
            window.apstag?.setDisplayBids();
            dispatch(adServingSlice.actions.setAmazonPublisherServicesReady());
          });
        },
      );
    } else {
      dispatch(adServingSlice.actions.setAmazonPublisherServicesReady());
    }

    function fetchForDynamicSlot(slot: RegisteredSlot) {
      const slotData = slotToApsData(slot);

      if (!slotData.sizes.length) {
        return;
      }

      window.apstag?.fetchBids(
        {
          slots: [slotData],
          timeout: 2e3,
        },
        () => {
          googletag.cmd.push(() => {
            window.apstag?.setDisplayBids();
          });
        },
      );
    }
    DFPManager.on('slotRegistered', fetchForDynamicSlot);

    return () => {
      DFPManager.off('slotRegistered', fetchForDynamicSlot);
    };
  }, [dispatch, publisherId]);

  if (!publisherId) {
    return null;
  }

  return <Script async src="https://c.amazon-adsystem.com/aax2/apstag.js" />;
}
