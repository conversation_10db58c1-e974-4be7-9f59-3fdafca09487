import React from 'react';

import DFPManager from 'themes/autumn/components/ads/GAM/dfp/DFPManager';

import type { LazyLoadConfig } from 'themes/autumn/components/ads/GAM/dfp/DFPManager';

export interface ContextType {
  dfpAdUnit?: string;
  dfpNetworkId?: string;
  dfpSizeMapping?: {
    sizes: googletag.GeneralSize;
    viewport: googletag.SingleSizeArray;
  }[];
  dfpTargetingArguments?: Record<string, string>;
  newSlotCallback?: () => void;
}
const context: ContextType = {
  dfpAdUnit: undefined,
  dfpNetworkId: undefined,
  dfpSizeMapping: undefined,
  dfpTargetingArguments: undefined,
  newSlotCallback: undefined,
};
export const Context = React.createContext(context);

type Props = React.PropsWithChildren<{
  adSenseAttributes?: Record<string, string>;
  adUnit?: string;
  autoLoad?: boolean;
  autoReload?: {
    adSenseAttributes?: boolean;
    adUnit?: boolean;
    collapseEmptyDivs?: boolean;
    dfpNetworkId?: boolean;
    disableInitialLoad?: boolean;
    lazyLoad?: boolean;
    personalizedAds?: boolean;
    singleRequest?: boolean;
    sizeMapping?: boolean;
    targetingArguments?: boolean;
  };
  collapseEmptyDivs: boolean;
  dfpNetworkId: string;
  disableInitialLoad: boolean;
  lazyLoad?: false | LazyLoadConfig;
  limitedAds: boolean;
  personalizedAds: boolean;
  singleRequest: boolean;
  sizeMapping?: {
    sizes: googletag.GeneralSize;
    viewport: googletag.SingleSizeArray;
  }[];
  targetingArguments?: Record<string, string>;
}>;

export default class DFPSlotsProvider extends React.Component<Props> {
  /* eslint-disable react/default-props-match-prop-types */
  static defaultProps = {
    autoLoad: true,
    autoReload: {
      adSenseAttributes: false,
      adUnit: false,
      collapseEmptyDivs: false,
      dfpNetworkId: false,
      disableInitialLoad: false,
      lazyLoad: false,
      personalizedAds: false,
      singleRequest: false,
      sizeMapping: false,
      targetingArguments: false,
    },
    collapseEmptyDivs: false,
    disableInitialLoad: false,
    lazyLoad: false,
    limitedAds: false,
    personalizedAds: true,
    singleRequest: true,
  };
  /* eslint-enable react/default-props-match-prop-types */

  contextValue: ContextType = {};

  loadAlreadyCalled = false;

  loadCallbackAttached = false;

  shouldReloadAds = false;

  totalSlots = 0;

  componentDidMount() {
    this.applyConfigs();
    const { autoLoad } = this.props;
    if (autoLoad && !this.loadAdsIfPossible()) {
      this.attachLoadCallback();
    }
  }

  shouldComponentUpdate(nextProps: Props) {
    this.shouldReloadAds = this.shouldReloadConfig(nextProps);

    const { autoLoad, children } = this.props;
    if (nextProps.children !== children) {
      return true;
    }
    if (nextProps.autoLoad && !autoLoad) {
      return true;
    }

    return this.shouldReloadAds;
  }

  componentDidUpdate() {
    this.applyConfigs();
    const { autoLoad } = this.props;
    if (autoLoad) {
      if (this.loadAlreadyCalled) {
        if (this.shouldReloadAds) {
          DFPManager.reload().catch(() => {});
        }
      } else if (!this.loadAdsIfPossible()) {
        this.attachLoadCallback();
      }
    }
    this.shouldReloadAds = false;
  }

  getContextValue() {
    const {
      contextValue: {
        dfpAdUnit: ctxDfpAdUnit,
        dfpNetworkId: ctxDfpNetworkId,
        dfpSizeMapping: ctxDfpSizeMapping,
        dfpTargetingArguments: ctxDfpTargetingArguments,
      },
      props: {
        adUnit: dfpAdUnit,
        dfpNetworkId,
        sizeMapping: dfpSizeMapping,
        targetingArguments: dfpTargetingArguments,
      },
    } = this;
    // performance: update context value object only when any of its
    // props is updated.
    if (
      dfpNetworkId !== ctxDfpNetworkId ||
      dfpAdUnit !== ctxDfpAdUnit ||
      dfpSizeMapping !== ctxDfpSizeMapping ||
      dfpTargetingArguments !== ctxDfpTargetingArguments
    ) {
      this.contextValue = {
        dfpAdUnit,
        dfpNetworkId,
        dfpSizeMapping,
        dfpTargetingArguments,
        newSlotCallback: this.newSlotCallback,
      };
    }
    return this.contextValue;
  }

  applyConfigs = () => {
    const {
      adSenseAttributes,
      collapseEmptyDivs,
      disableInitialLoad,
      lazyLoad,
      limitedAds,
      personalizedAds,
      singleRequest,
    } = this.props;

    DFPManager.configurePersonalizedAds(personalizedAds);
    DFPManager.configureSingleRequest(singleRequest);
    DFPManager.configureDisableInitialLoad(disableInitialLoad);
    DFPManager.configureLazyLoad(
      !!lazyLoad,
      typeof lazyLoad === 'boolean' ? undefined : lazyLoad,
    );
    if (adSenseAttributes) {
      DFPManager.setAdSenseAttributes(adSenseAttributes);
    }
    DFPManager.setCollapseEmptyDivs(collapseEmptyDivs);
    DFPManager.configureLimitedAds(limitedAds);
  };

  attachLoadCallback = () => {
    if (this.loadCallbackAttached) {
      return false;
    }

    DFPManager.on('slotRegistered', this.loadAdsIfPossible);
    this.loadCallbackAttached = true;
    return true;
  };

  // Checks all the mounted children ads have been already registered
  // in the DFPManager before trying to call the gpt load scripts.
  // This is helpful when trying to fetch ads with a single request.
  loadAdsIfPossible = () => {
    let r = false;
    if (
      Object.keys(DFPManager.getRegisteredSlots()).length >= this.totalSlots
    ) {
      DFPManager.removeListener('slotRegistered', this.loadAdsIfPossible);
      DFPManager.load();
      this.loadAlreadyCalled = true;
      this.loadCallbackAttached = false;
      r = true;
    }
    return r;
  };

  // pretty straightforward interface that children ad slots use to register
  // with their DFPSlotProvider parent node.
  newSlotCallback = () => {
    this.totalSlots += 1;
  };

  shouldReloadConfig = (nextProps: Props) => {
    const { props } = this;
    const { autoLoad, autoReload } = props;
    const reloadConfig = nextProps.autoReload || autoReload;
    if (autoLoad || nextProps.autoLoad) {
      if (typeof reloadConfig === 'object') {
        return (
          Object.entries(reloadConfig) as [
            keyof Props['autoReload'],
            boolean,
          ][]
        ).some(
          ([propName, value]) =>
            value && props[propName] !== nextProps[propName],
        );
      }
    }
    return false;
  };

  render() {
    const { children } = this.props;
    return (
      <Context.Provider value={this.getContextValue()}>
        {children}
      </Context.Provider>
    );
  }
}
