const GPT_SRC = {
  limitedAds: 'pagead2.googlesyndication.com',
  standard: 'securepubads.g.doubleclick.net',
};

// eslint-disable-next-line import/prefer-default-export
export function loadGPTScript(
  limitedAds = false,
): Promise<typeof window.googletag> {
  return new Promise((resolve, reject) => {
    window.googletag = window.googletag || {};
    // @ts-expect-error Google tag init
    window.googletag.cmd = window.googletag.cmd || [];

    /* eslint-disable no-underscore-dangle */
    if (
      window.googletag.apiReady ||
      // @ts-expect-error Undocumented property
      window.googletag._loadStarted_ ||
      // @ts-expect-error Undocumented property
      window.googletag._loaded_
    ) {
      resolve(window.googletag);
      return;
    }
    /* eslint-enable no-underscore-dangle */

    const scriptTag = document.createElement('script');
    scriptTag.src = `https://${
      limitedAds ? GPT_SRC.limitedAds : GPT_SRC.standard
    }/tag/js/gpt.js`;
    scriptTag.async = true;
    scriptTag.type = 'text/javascript';
    scriptTag.onerror = () => {
      reject(new Error('Could not load gpt.js'));
    };
    scriptTag.onload = function scriptTagOnLoad() {
      resolve(window.googletag);
    };
    document.getElementsByTagName('head')[0].appendChild(scriptTag);
  });
}
