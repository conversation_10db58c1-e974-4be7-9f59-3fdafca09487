/*
This is a modified version of `react-dfp` converted into TypeScript and with
added support for out-of-page ads (bottom/top anchor). DFP deprecation warnings
are also resolved. The ad slot HTML is simplified by removing a wrapper.
*/
export { default as AdSlot } from './AdSlot';
export { default as DFPManager } from './DFPManager';
export type { SizeMapping } from './AdSlot';
export type {
  SlotIsViewableEvent,
  SlotRegisterEvent,
  SlotRenderEndedEvent,
  SlotVisibilityChangedEvent,
  TargetingArguments,
} from './DFPManager';
export { default as DFPSlotsProvider } from './DFPSlotsProvider';
