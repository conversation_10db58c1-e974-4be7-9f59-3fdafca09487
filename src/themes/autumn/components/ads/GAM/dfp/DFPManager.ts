import { EventEmitter } from 'events';

import * as Utils from 'themes/autumn/components/ads/GAM/dfp/utils';

import type React from 'react';

export interface LazyLoadConfig {
  fetchMarginPercent?: number;
  mobileScaling?: number;
  renderMarginPercent?: number;
}

export type TargetingArguments = Record<string, string>;

export interface AdSlotEvent<GoogleTagEvent extends googletag.events.Event> {
  adElementRef: React.RefObject<HTMLElement | null>;
  event: GoogleTagEvent;
  slotId: string;
}

export type SlotRenderEndedEvent =
  AdSlotEvent<googletag.events.SlotRenderEndedEvent>;

export type SlotIsViewableEvent =
  AdSlotEvent<googletag.events.ImpressionViewableEvent>;

export interface SlotRegisterEvent {
  adElementRef: React.RefObject<HTMLElement | null>;
  sizes?: googletag.GeneralSize;
  slotCount: number;
  slotId: string;
}

export type SlotVisibilityChangedEvent =
  AdSlotEvent<googletag.events.SlotVisibilityChangedEvent>;

export interface Slot {
  adSenseAttributes?: AdSenseAttributes;
  adUnit?: string;
  dfpNetworkId: string;
  renderOutOfThePage: boolean;
  sizeMapping: {
    sizes: googletag.GeneralSize;
    viewport: googletag.SingleSizeArray;
  }[];
  sizes: googletag.SingleSizeArray[];
  slotId: string;
  slotShouldRefresh: () => boolean;
  targetingArguments: TargetingArguments;
}

export interface RegisteredSlot extends Slot {
  gptSlot?: googletag.Slot;
  loading: boolean;
}

export type AdSenseAttributes = Partial<
  Record<googletag.adsense.AttributeName, string>
>;

class DFPManager extends EventEmitter {
  disableInitialLoadEnabled = false;

  initialLoadComplete = true;

  globalAdSenseAttributes: AdSenseAttributes = {};

  globalTargetingArguments: TargetingArguments = {};

  googleGPTScriptLoadPromise: undefined | Promise<typeof window.googletag> =
    undefined;

  lazyLoadConfig: undefined | LazyLoadConfig = undefined;

  lazyLoadEnabled = false;

  limitedAds = false;

  loadPromise: undefined | Promise<void> = undefined;

  managerAlreadyInitialized = false;

  registeredSlots: Record<string, RegisteredSlot> = {};

  servePersonalizedAds = true;

  singleRequestEnabled = true;

  constructor() {
    super();
    this.setMaxListeners(0);
  }

  attachSlotIsViewable(cb: (eventData: SlotIsViewableEvent) => void) {
    this.on('impressionViewable', cb);
  }

  attachSlotRenderEnded(cb: (eventData: SlotRenderEndedEvent) => void) {
    this.on('slotRenderEnded', cb);
  }

  attachSlotVisibilityChanged(
    cb: (eventData: SlotVisibilityChangedEvent) => void,
  ) {
    this.on('slotVisibilityChanged', cb);
  }

  collapseEmptyDivs = false;

  configureDisableInitialLoad(value: boolean) {
    this.disableInitialLoadEnabled = value;
  }

  // configure those gpt parameters that need to be set before pubads service
  // initialization.
  configureInitialOptions(googletag: typeof window.googletag) {
    googletag.cmd.push(() => {
      if (this.disableInitialLoadIsEnabled()) {
        this.initialLoadComplete = false;
        googletag.pubads().disableInitialLoad();
      }
    });
  }

  configureLazyLoad(enable = true, config?: LazyLoadConfig) {
    let conf;

    if (config) {
      conf = { ...config };
    }

    this.lazyLoadEnabled = enable;
    this.lazyLoadConfig = conf;
  }

  configureLimitedAds(value: boolean) {
    this.limitedAds = value;
  }

  configureOptions(googletag: typeof window.googletag) {
    googletag.cmd.push(() => {
      const pubadsService = googletag.pubads();
      pubadsService.setPrivacySettings({
        limitedAds: this.limitedAdsIsEnabled(),
        nonPersonalizedAds: !this.personalizedAdsEnabled(),
      });
      const targetingArguments = this.getTargetingArguments();
      // set global targeting arguments
      Object.keys(targetingArguments).forEach((varName) => {
        if (pubadsService && targetingArguments[varName]) {
          pubadsService.setTargeting(varName, targetingArguments[varName]);
        }
      });
      // set global adSense attributes
      const adSenseAttributes = this.getAdSenseAttributes();
      (
        Object.entries(adSenseAttributes) as [
          keyof AdSenseAttributes,
          string,
        ][]
      ).forEach(([key, value]) => {
        pubadsService.set(key, value);
      });
      if (this.lazyLoadIsEnabled()) {
        const config = this.getLazyLoadConfig();
        if (config) {
          pubadsService.enableLazyLoad(config);
        } else {
          pubadsService.enableLazyLoad();
        }
      }
      if (this.singleRequestIsEnabled()) {
        pubadsService.enableSingleRequest();
      }
      // Collapsing empty divs does not work with lazy loading enabled
      // Causes some ad slots to not be rendered
      if (!this.lazyLoadEnabled && this.collapseEmptyDivs) {
        pubadsService.collapseEmptyDivs(true);
      }
    });
  }

  configurePersonalizedAds(value: boolean) {
    this.servePersonalizedAds = value;
  }

  configureSingleRequest(value: boolean) {
    this.singleRequestEnabled = value;
  }

  destroyGPTSlots(...slotsToDestroy: string[]) {
    if (slotsToDestroy.length === 0) {
      // eslint-disable-next-line no-param-reassign
      slotsToDestroy = Object.keys(this.registeredSlots);
    }
    const slots: RegisteredSlot[] = [];
    const gptSlots: googletag.Slot[] = [];
    slotsToDestroy.forEach((slotId) => {
      const slot = this.registeredSlots[slotId];
      slots.push(slot);
      delete this.registeredSlots[slotId];
    });
    return new Promise((resolve) => {
      this.getGoogletag()
        .then((googletag) => {
          googletag.cmd.push(() => {
            if (this.managerAlreadyInitialized) {
              if (slotsToDestroy.length > 0) {
                slots.forEach((slot) => {
                  if (slot.gptSlot) {
                    gptSlots.push(slot.gptSlot);
                    // eslint-disable-next-line no-param-reassign
                    delete slot.gptSlot;
                  }
                });
                googletag.destroySlots(gptSlots);
              } else {
                googletag.destroySlots();
              }
            }
            resolve(slotsToDestroy);
          });
        })
        .catch(() => {});
    });
  }

  detachSlotIsViewable(cb: (eventData: SlotIsViewableEvent) => void) {
    this.removeListener('impressionViewable', cb);
  }

  detachSlotRenderEnded(cb: (eventData: SlotRenderEndedEvent) => void) {
    this.removeListener('slotRenderEnded', cb);
  }

  detachSlotVisibilityChanged(
    cb: (eventData: SlotVisibilityChangedEvent) => void,
  ) {
    this.removeListener('slotVisibilityChanged', cb);
  }

  disableInitialLoadIsEnabled() {
    return this.disableInitialLoadEnabled;
  }

  doLoad(...slots: string[]) {
    this.init();
    let availableSlots: string[];

    if (slots.length > 0) {
      availableSlots = slots.filter((slotId) =>
        Object.prototype.hasOwnProperty.call(this.registeredSlots, slotId),
      );
    } else {
      availableSlots = Object.keys(this.registeredSlots);
    }
    availableSlots = availableSlots.filter(
      (id) =>
        !this.registeredSlots[id].loading && !this.registeredSlots[id].gptSlot,
    );
    availableSlots.forEach((slotId) => {
      this.registeredSlots[slotId].loading = true;
    });
    return this.gptLoadAds(availableSlots);
  }

  getAdSenseAttribute(key: googletag.adsense.AttributeName) {
    return this.globalAdSenseAttributes[key];
  }

  getAdSenseAttributes() {
    return { ...this.globalAdSenseAttributes };
  }

  getGoogletag() {
    if (!this.googleGPTScriptLoadPromise) {
      this.googleGPTScriptLoadPromise = Utils.loadGPTScript(this.limitedAds);
    }

    return this.googleGPTScriptLoadPromise;
  }

  getLazyLoadConfig() {
    return this.lazyLoadConfig;
  }

  getRefreshableSlots(...slotsArray: string[]) {
    const slots: Record<string, RegisteredSlot> = {};
    if (slotsArray.length === 0) {
      const slotsToRefresh = Object.keys(this.registeredSlots).map(
        (k) => this.registeredSlots[k],
      );
      return slotsToRefresh.reduce((last, slot) => {
        if (slot.slotShouldRefresh()) {
          slots[slot.slotId] = slot;
        }
        return slots;
      }, slots);
    }
    return slotsArray.reduce((last, slotId) => {
      const slot = this.registeredSlots[slotId];
      if (typeof slot !== 'undefined') {
        slots[slotId] = slot;
      }
      return slots;
    }, slots);
  }

  getRegisteredSlots() {
    return this.registeredSlots;
  }

  getSlotAdSenseAttributes(slotId: string) {
    const propValue = this.getSlotProperty(slotId, 'adSenseAttributes');
    return propValue ? { ...propValue } : undefined;
  }

  getSlotProperty<T extends keyof Slot>(
    slotId: string,
    propName: T,
  ): Slot[T] | undefined {
    const slot = this.getRegisteredSlots()[slotId];
    let ret: Slot[T] | undefined;

    if (slot !== undefined) {
      ret = slot[propName] || ret;
    }

    return ret;
  }

  getSlotTargetingArguments(slotId: string) {
    const propValue = this.getSlotProperty(slotId, 'targetingArguments');
    return propValue ? { ...propValue } : undefined;
  }

  getTargetingArguments() {
    return { ...this.globalTargetingArguments };
  }

  gptLoadAds(slotsToInitialize: string[]) {
    return new Promise<void>((resolve) => {
      this.getGoogletag()
        .then((googletag) => {
          slotsToInitialize.forEach((currentSlotId) => {
            this.registeredSlots[currentSlotId].loading = false;

            googletag.cmd.push(() => {
              const slot = this.registeredSlots[currentSlotId];
              let gptSlot: null | googletag.Slot;
              let adUnit = slot.dfpNetworkId;
              if (slot.adUnit) {
                adUnit = `${adUnit}/${slot.adUnit}`;
              }
              if (slot.renderOutOfThePage) {
                gptSlot = googletag.defineOutOfPageSlot(
                  adUnit,
                  /* eslint-disable @typescript-eslint/no-unsafe-argument */
                  // @ts-expect-error Incorrect enum type
                  googletag.enums.OutOfPageFormat[currentSlotId] ||
                    currentSlotId,
                  /* eslint-enable @typescript-eslint/no-unsafe-argument */
                );
              } else {
                gptSlot = googletag.defineSlot(
                  adUnit,
                  slot.sizes,
                  currentSlotId,
                );
              }
              if (gptSlot !== null) {
                slot.gptSlot = gptSlot;
                const slotTargetingArguments =
                  this.getSlotTargetingArguments(currentSlotId);
                if (slotTargetingArguments !== undefined) {
                  Object.keys(slotTargetingArguments).forEach((varName) => {
                    if (slot && slot.gptSlot) {
                      slot.gptSlot.setTargeting(
                        varName,
                        slotTargetingArguments[varName],
                      );
                    }
                  });
                }
                const slotAdSenseAttributes =
                  this.getSlotAdSenseAttributes(currentSlotId);
                if (slotAdSenseAttributes !== undefined) {
                  (
                    Object.entries(slotAdSenseAttributes) as [
                      keyof AdSenseAttributes,
                      string,
                    ][]
                  ).forEach(([key, value]) => {
                    slot.gptSlot?.set(key, value);
                  });
                }
                slot.gptSlot.addService(googletag.pubads());
                if (slot.sizeMapping) {
                  let builder = googletag.sizeMapping();
                  slot.sizeMapping.forEach((mapping) => {
                    builder = builder.addSize(mapping.viewport, mapping.sizes);
                  });
                  slot.gptSlot.defineSizeMapping(builder.build());
                }
              }
            });
          });
          googletag.cmd.push(() => {
            googletag.enableServices();
            slotsToInitialize.forEach((theSlotId) => {
              const slot = this.registeredSlots[theSlotId];
              if (slot.renderOutOfThePage && slot.gptSlot) {
                googletag.display(slot.gptSlot);
              } else {
                googletag.display(theSlotId);
              }

              // If the ad is registered after the initial page load and call
              // to `refresh()`, it must be refreshed manually
              if (
                this.disableInitialLoadIsEnabled() &&
                this.initialLoadComplete &&
                slot.gptSlot
              ) {
                googletag.pubads().refresh([slot.gptSlot]);
              }
            });
            resolve();
          });
        })
        .catch(() => {});
    });
  }

  gptRefreshAds(slots: string[]) {
    return this.getGoogletag()
      .then((googletag) => {
        googletag.cmd.push(() => {
          const pubadsService = googletag.pubads();
          if (slots.length === 0) {
            // Refresh all ads; must be called without any arguments
            pubadsService.refresh();
            this.initialLoadComplete = true;
          } else {
            pubadsService.refresh(
              slots
                .map((slotId) => this.registeredSlots[slotId].gptSlot)
                .filter((slot) => slot !== undefined),
            );
          }
        });
      })
      .catch(() => {});
  }

  init() {
    if (!this.managerAlreadyInitialized) {
      this.managerAlreadyInitialized = true;
      this.getGoogletag()
        .then((googletag) => {
          this.configureInitialOptions(googletag);
          this.configureOptions(googletag);
          googletag.cmd.push(() => {
            const pubadsService = googletag.pubads();
            pubadsService.addEventListener('slotRenderEnded', (event) => {
              const slotId = event.slot.getSlotElementId();
              this.emit('slotRenderEnded', { event, slotId });
            });
            pubadsService.addEventListener('impressionViewable', (event) => {
              const slotId = event.slot.getSlotElementId();
              this.emit('impressionViewable', { event, slotId });
            });
            pubadsService.addEventListener(
              'slotVisibilityChanged',
              (event) => {
                const slotId = event.slot.getSlotElementId();
                this.emit('slotVisibilityChanged', { event, slotId });
              },
            );
            pubadsService.setPrivacySettings({
              nonPersonalizedAds: !this.personalizedAdsEnabled(),
            });
          });
        })
        .catch(() => {});
    }
  }

  lazyLoadIsEnabled() {
    return this.lazyLoadEnabled;
  }

  limitedAdsIsEnabled() {
    return this.limitedAds;
  }

  load(...slots: string[]) {
    if (this.loadPromise === undefined) {
      this.loadPromise = this.doLoad(...slots);
    } else {
      this.loadPromise = this.loadPromise.then(() => this.doLoad(...slots));
    }
  }

  personalizedAdsEnabled() {
    return this.servePersonalizedAds;
  }

  refresh(...slots: string[]) {
    if (this.loadPromise === undefined) {
      this.load();
      if (this.disableInitialLoadIsEnabled()) {
        this.gptRefreshAds(
          slots.length === 0
            ? []
            : Object.keys(this.getRefreshableSlots(...slots)),
        ).catch(() => {});
      }
    } else {
      this.loadPromise
        .then(() =>
          this.gptRefreshAds(
            slots.length === 0
              ? []
              : Object.keys(this.getRefreshableSlots(...slots)),
          ),
        )
        .catch(() => {});
    }
  }

  registerSlot(
    {
      adSenseAttributes,
      adUnit,
      dfpNetworkId,
      renderOutOfThePage,
      sizeMapping,
      sizes,
      slotId,
      slotShouldRefresh,
      targetingArguments,
    }: Slot,
    autoLoad = true,
  ) {
    if (!Object.prototype.hasOwnProperty.call(this.registeredSlots, slotId)) {
      this.registeredSlots[slotId] = {
        adSenseAttributes,
        adUnit,
        dfpNetworkId,
        loading: false,
        renderOutOfThePage,
        sizeMapping,
        sizes,
        slotId,
        slotShouldRefresh,
        targetingArguments,
      };
      this.emit('slotRegistered', this.registeredSlots[slotId]);
      if (autoLoad && this.loadPromise !== undefined) {
        this.loadPromise = this.loadPromise.catch().then(() => {
          const slot = this.registeredSlots[slotId];
          if (typeof slot !== 'undefined') {
            const { gptSlot, loading } = slot;
            if (!loading && !gptSlot) {
              this.load(slotId);
            }
          }
        });
      }
    }
  }

  reload(...slots: string[]) {
    return this.destroyGPTSlots(...slots).then(() => this.load());
  }

  setAdSenseAttributes(attrs: AdSenseAttributes) {
    Object.assign(this.globalAdSenseAttributes, attrs);
    if (this.managerAlreadyInitialized) {
      this.getGoogletag()
        .then((googletag) => {
          googletag.cmd.push(() => {
            const pubadsService = googletag.pubads();
            (
              Object.entries(this.globalAdSenseAttributes) as [
                keyof AdSenseAttributes,
                string,
              ][]
            ).forEach(([key, value]) => {
              pubadsService.set(key, value);
            });
          });
        })
        .catch(() => {});
    }
  }

  setCollapseEmptyDivs(collapse: boolean) {
    this.collapseEmptyDivs = collapse;
  }

  setTargetingArguments(data: typeof this.globalTargetingArguments) {
    Object.assign(this.globalTargetingArguments, data);
    if (this.managerAlreadyInitialized) {
      this.getGoogletag()
        .then((googletag) => {
          googletag.cmd.push(() => {
            const pubadsService = googletag.pubads();
            Object.keys(this.globalTargetingArguments).forEach((varName) => {
              if (pubadsService && this.globalTargetingArguments[varName]) {
                pubadsService.setTargeting(
                  varName,
                  this.globalTargetingArguments[varName],
                );
              }
            });
          });
        })
        .catch(() => {});
    }
  }

  singleRequestIsEnabled() {
    return this.singleRequestEnabled;
  }

  unregisterSlot({ slotId }: Slot) {
    this.destroyGPTSlots(slotId).catch(() => {});
  }
}

const manager = new DFPManager();

export default manager;
