import React from 'react';

import DFPManager from 'themes/autumn/components/ads/GAM/dfp/DFPManager';
import { Context } from 'themes/autumn/components/ads/GAM/dfp/DFPSlotsProvider';

import type {
  AdSenseAttributes,
  Slot,
  SlotIsViewableEvent,
  SlotRegisterEvent,
  SlotRenderEndedEvent,
  SlotVisibilityChangedEvent,
  TargetingArguments,
} from 'themes/autumn/components/ads/GAM/dfp/DFPManager';

export type SizeMapping = {
  sizes: googletag.GeneralSize;
  viewport: googletag.SingleSizeArray;
};

type Props = React.PropsWithChildren<{
  // Props are spread to slot components leading to false positives
  /* eslint-disable react/no-unused-prop-types */
  adSenseAttributes?: AdSenseAttributes;
  adUnit?: string;
  dfpNetworkId?: string;
  fetchNow?: boolean;
  onSlotIsViewable?: (event: SlotIsViewableEvent) => void;
  onSlotRegister?: (event: SlotRegisterEvent) => void;
  onSlotRender?: (event: SlotRenderEndedEvent) => void;
  onSlotVisibilityChanged?: (event: SlotVisibilityChangedEvent) => void;
  renderOutOfThePage?: boolean;
  shouldRefresh?: (data: Props) => boolean;
  sizeMapping?: SizeMapping[];
  sizes?: googletag.GeneralSize;
  slotId?: string;
  targetingArguments?: TargetingArguments;
  /* eslint-enable react/no-unused-prop-types */
}>;

interface State {
  slotId: string;
}

interface AdSlotProps {
  adUnit?: string;
  dfpNetworkId?: string;
  sizeMapping?: Props['sizeMapping'];
  targetingArguments?: Record<string, string>;
}

export default class AdSlot extends React.Component<Props, State> {
  static defaultProps = {
    fetchNow: false,
    renderOutOfThePage: false,
  };

  static dynamicAdCount = 0;

  static contextType = Context;

  static generateSlotId() {
    AdSlot.dynamicAdCount += 1;
    return `adSlot-${AdSlot.dynamicAdCount}`;
  }

  adElementRef = React.createRef<HTMLDivElement>();

  // https://github.com/jsx-eslint/eslint-plugin-react/issues/3467
  // eslint-disable-next-line react/static-property-placement
  context!: React.ContextType<typeof Context>;

  constructor(props: Props) {
    super(props);

    const { slotId } = this.props;
    this.state = {
      slotId: slotId || AdSlot.generateSlotId(),
    };
  }

  componentDidMount() {
    // register this ad-unit in the <DFPSlotProvider>, when available.
    const { newSlotCallback } = this.context;

    newSlotCallback?.();
    this.registerSlot();
  }

  componentWillUnmount() {
    this.unregisterSlot();
  }

  getSlotId = () => {
    const { slotId } = this.props;
    const { slotId: stateSlotId } = this.state;
    return slotId || stateSlotId;
  };

  mapContextToAdSlotProps = () => {
    const mappedProps: AdSlotProps = {};
    const { dfpAdUnit, dfpNetworkId, dfpSizeMapping, dfpTargetingArguments } =
      this.context;

    if (dfpNetworkId !== undefined) {
      mappedProps.dfpNetworkId = dfpNetworkId;
    }
    if (dfpAdUnit !== undefined) {
      mappedProps.adUnit = dfpAdUnit;
    }
    if (dfpSizeMapping !== undefined) {
      mappedProps.sizeMapping = dfpSizeMapping;
    }
    if (dfpTargetingArguments !== undefined) {
      mappedProps.targetingArguments = dfpTargetingArguments;
    }

    return mappedProps;
  };

  registerSlot = () => {
    const { slotId: slotIdState } = this.state;
    if (slotIdState && DFPManager.getRegisteredSlots()[slotIdState]) {
      // If the ad slot is already in use, show an error, but generate another
      // ID so the ads still work
      const slotId = `${slotIdState}-${AdSlot.generateSlotId()}`;
      console.error(
        `Duplicate ad slot ID ${slotIdState}. Using ${slotId} instead`,
      );
      this.setState(
        {
          slotId,
        },
        this.registerSlot,
      );
      return;
    }

    const slot = {
      ...this.mapContextToAdSlotProps(),
      ...this.props,
      ...this.state,
      slotShouldRefresh: this.slotShouldRefresh,
    } as Slot;
    DFPManager.registerSlot(slot);

    const { fetchNow } = this.props;

    if (fetchNow) {
      DFPManager.load(this.getSlotId());
    }

    DFPManager.attachSlotRenderEnded(this.slotRenderEnded);
    DFPManager.attachSlotIsViewable(this.slotIsViewable);
    DFPManager.attachSlotVisibilityChanged(this.slotVisibilityChanged);

    this.slotRegisterCallback();
  };

  unregisterSlot = () => {
    DFPManager.unregisterSlot({
      ...this.mapContextToAdSlotProps(),
      ...this.props,
      ...this.state,
    } as Slot);
    DFPManager.detachSlotRenderEnded(this.slotRenderEnded);
    DFPManager.detachSlotIsViewable(this.slotIsViewable);
    DFPManager.detachSlotVisibilityChanged(this.slotVisibilityChanged);
  };

  slotRenderEnded = (eventData: SlotRenderEndedEvent) => {
    const { onSlotRender } = this.props;
    if (eventData.slotId === this.getSlotId() && onSlotRender) {
      // now that slot has rendered we have access to the ref
      const params = {
        ...eventData,
        adElementRef: this.adElementRef,
      };
      onSlotRender(params);
    }
  };

  slotRegisterCallback = () => {
    const { onSlotRegister, sizes } = this.props;
    if (typeof onSlotRegister === 'function') {
      onSlotRegister({
        adElementRef: this.adElementRef,
        sizes,
        slotCount: AdSlot.dynamicAdCount,
        slotId: this.getSlotId(),
      });
    }
  };

  slotIsViewable = (eventData: SlotIsViewableEvent) => {
    if (eventData.slotId === this.getSlotId()) {
      const { onSlotIsViewable } = this.props;
      onSlotIsViewable?.(eventData);
    }
  };

  slotVisibilityChanged = (eventData: SlotVisibilityChangedEvent) => {
    if (eventData.slotId === this.getSlotId()) {
      const { onSlotVisibilityChanged } = this.props;
      onSlotVisibilityChanged?.(eventData);
    }
  };

  slotShouldRefresh = () => {
    const { shouldRefresh } = this.props;

    if (shouldRefresh !== undefined) {
      return shouldRefresh({
        ...this.mapContextToAdSlotProps(),
        ...this.props,
        slotId: this.getSlotId(),
      });
    }

    return true;
  };

  render() {
    const { renderOutOfThePage } = this.props;

    if (renderOutOfThePage) {
      return null;
    }

    const { slotId } = this.state;

    return <div id={slotId || undefined} ref={this.adElementRef} />;
  }
}
