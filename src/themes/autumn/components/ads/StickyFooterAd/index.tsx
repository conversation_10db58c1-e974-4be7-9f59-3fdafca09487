'use client';

import clsx from 'clsx';
import { useEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import { AdSize } from 'util/ads';
import { useLazyLoadComponentState } from 'util/hooks';
import { isSponsoredStoryTag } from 'util/page';

import Ad from '../Ad';

interface StickyFooterAdProps {
  className?: string;
}

export default function StickyFooterAd({
  className,
}: StickyFooterAdProps): React.ReactElement | null {
  const [hidden, setHidden] = useState(false);

  const [windowHeight, setWindowHeight] = useState(0);
  const [isToolbarHidden, setHiddenToolbar] = useState(false);
  const hasTakeoverAd = useAppSelector((state) => state.page.hasTakeoverAd);
  const hasPaywall = useAppSelector((state) => state.piano.hasPaywall);
  const storyTags = useAppSelector((state) => state.story.tags);
  const featuresBehindPaywallEnabled = useAppSelector(
    (state) => state.piano.featuresBehindPaywallEnabled,
  );
  const { showComponentPlaceholder } = useLazyLoadComponentState();

  useEffect(() => {
    const { userAgent } = window.navigator;
    const isiOS = !!/iPad/i.exec(userAgent) || !!/iPhone/i.exec(userAgent);
    const isWebKit = !!/WebKit/i.exec(userAgent);
    const isiOSSafari = isiOS && isWebKit && !/CriOS/i.exec(userAgent);

    if (!isiOSSafari) {
      return;
    }

    // Check for a change in the window height to detect if Safari's bottom
    // toolbar is shown, so we can add extra padding to push the ad above the
    // home swipe bar
    function listener() {
      if (windowHeight) {
        setHiddenToolbar(window.innerHeight >= windowHeight);
      } else {
        setWindowHeight(window.innerHeight);
      }
    }

    window.addEventListener('resize', listener);

    // eslint-disable-next-line consistent-return
    return () => {
      window.removeEventListener('resize', listener);
    };
  });

  // To hide the ad, `<Ad />` must not be rendered (as opposed to hidden via
  // CSS), otherwise it could count as an invalid ad view
  if (
    hidden ||
    (!featuresBehindPaywallEnabled && hasPaywall) ||
    !showComponentPlaceholder ||
    isSponsoredStoryTag(storyTags)
  ) {
    return null;
  }

  return (
    <div
      className={clsx('z-10 bg-gray-100 md:hidden', className, {
        hidden: hasTakeoverAd,
      })}
    >
      <button
        aria-label="Close"
        className="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        onClick={() => setHidden(true)}
        type="button"
      >
        <span className="flex size-6 place-items-center">
          <svg
            className="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div className={clsx('relative', { 'pb-6': isToolbarHidden })}>
        <Ad
          mdSizes={[]}
          position={1}
          // TODO Consider defaulting to `slotId` if not set
          publiftName="sticky-footer"
          sizes={AdSize.mobileBanner}
          slotId="sticky-footer"
          withLabel={false}
        />
      </div>
    </div>
  );
}
