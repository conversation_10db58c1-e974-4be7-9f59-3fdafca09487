'use client';

import Script from 'next/script';
import { useEffect } from 'react';

import { useAppSelector } from 'store/hooks';
import { useAdTargeting } from 'util/ads';

import type { CreateAdOptions } from 'util/ads';

export default function Provider({ children }: React.PropsWithChildren) {
  const targeting = useAdTargeting();
  const adServing = useAppSelector((state) => state.features.adServing);
  const doubleClickSite = adServing.enabled
    ? adServing.data.doubleClickSite
    : '';
  const fuseLibraryVersion = adServing.enabled
    ? adServing.data.fuseLibraryVersion
    : '2001';

  useEffect(() => {
    Provider.setTargetingArguments(targeting);
  }, [targeting]);

  if (!doubleClickSite) {
    return null;
  }

  const fuseScriptSrc =
    'https://acm-media.cdn.fuseplatform.net/' +
    `${fuseLibraryVersion}/fuse.js`;

  return (
    <>
      {children}
      <Script src={fuseScriptSrc} />
    </>
  );
}

Provider.refresh = function refresh(...slots: string[]) {
  window.fusetag = window.fusetag || { que: [] };

  window.fusetag.que.push(() => {
    slots.forEach((slot) => {
      const slotCode = document
        .getElementById(slot)
        ?.getAttribute('data-fuse-code');
      if (!slotCode) {
        console.error(`Could not find ad to refresh for slot ${slot}`);
        return;
      }

      (window.fusetag as Fuse).refreshSlotByCode(slotCode);
    });
  });
};

Provider.setTargetingArguments = function setTargetingArguments(
  kv: Record<string, string>,
) {
  window.fusetag = window.fusetag || { que: [] };
  window.fusetag.que.push(() => {
    Object.entries(kv).forEach(([key, value]) => {
      if (!value) {
        return;
      }

      (window.fusetag as Fuse).setTargeting(key, value);
    });
  });
};

Provider.createAd = function createAd({
  publiftName,
  slotId,
  targetingArguments,
}: CreateAdOptions) {
  const element = document.getElementById(slotId);

  if (!element) {
    return;
  }

  element.setAttribute('data-fuse', publiftName);

  if (targetingArguments) {
    Object.entries(targetingArguments).forEach(([key, value]) => {
      element.setAttribute(`data-targeting-${key}`, value);
    });
  }

  window.fusetag = window.fusetag || { que: [] };

  window.fusetag.que.push(() => {
    (window.fusetag as Fuse).registerZone(slotId);
  });

  Provider.refresh(slotId);
};
