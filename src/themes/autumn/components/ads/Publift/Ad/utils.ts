/* eslint-disable import/prefer-default-export */

'use client';

import { type AdSizeType } from 'util/ads';
import { ResponsiveType } from 'util/device';
import { hasAdSize } from 'util/scrollx';

const devices = [
  ResponsiveType.MOBILE,
  ResponsiveType.TABLET_NARROW,
  ResponsiveType.TABLET_WIDE,
  ResponsiveType.DESKTOP,
];

export function deviceShouldHaveAd(
  device: ResponsiveType,
  props: {
    lgSizes?: AdSizeType;
    mdSizes?: AdSizeType;
    sizes?: AdSizeType;
    xlSizes?: AdSizeType;
  },
) {
  const deviceIndex = devices.indexOf(device);
  const sizes = [props.sizes, props.mdSizes, props.lgSizes, props.xlSizes].map(
    hasAdSize,
  );

  // If a breakpoint has an explicit ad, or any of the sizes before it do, it
  // should display an ad
  if (sizes[deviceIndex] !== null) {
    return sizes[deviceIndex];
  }

  const sizeSpec = sizes.slice(0, deviceIndex).filter((s) => s !== null);
  return sizeSpec[sizeSpec.length - 1];
}
