import { AdSize } from 'util/ads';
import { ResponsiveType } from 'util/device';

import { deviceShouldHaveAd } from './utils';

describe('deviceShouldHaveAd()', () => {
  it('works for a variety of scenarios', () => {
    // Allow sizes to be in the logical order
    /* eslint-disable sort-keys */
    const tests = [
      {
        deviceType: ResponsiveType.MOBILE,
        expected: true,
        props: {
          sizes: AdSize.mrec,
        },
      },
      {
        deviceType: ResponsiveType.MOBILE,
        expected: false,
        props: {
          sizes: [],
        },
      },
      {
        deviceType: ResponsiveType.MOBILE,
        expected: true,
        props: {
          sizes: AdSize.mrec,
          mdSizes: [],
        },
      },
      {
        deviceType: ResponsiveType.TABLET_NARROW,
        expected: false,
        props: {
          sizes: AdSize.mrec,
          mdSizes: [],
        },
      },
      {
        deviceType: ResponsiveType.TABLET_WIDE,
        expected: true,
        props: {
          sizes: [],
          mdSizes: AdSize.mrec,
        },
      },
      {
        deviceType: ResponsiveType.DESKTOP,
        expected: true,
        props: {
          sizes: [],
          mdSizes: AdSize.mrec,
          lgSizes: [],
          xlSizes: AdSize.mrec,
        },
      },
      {
        deviceType: ResponsiveType.DESKTOP,
        expected: true,
        props: {
          sizes: [],
          mdSizes: AdSize.mrec,
          xlSizes: AdSize.mrec,
        },
      },
      {
        deviceType: ResponsiveType.TABLET_NARROW,
        expected: false,
        props: {
          sizes: [],
          lgSizes: AdSize.mrec,
        },
      },
      {
        deviceType: ResponsiveType.TABLET_WIDE,
        expected: true,
        props: {
          sizes: [],
          lgSizes: AdSize.mrec,
        },
      },
    ];
    /* eslint-enable sort-keys */

    tests.forEach(({ deviceType, expected, props }) => {
      expect(deviceShouldHaveAd(deviceType, props)).toBe(expected);
    });
  });
});
