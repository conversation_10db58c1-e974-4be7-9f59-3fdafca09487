'use client';

import { useState } from 'react';

import ScrollXAd from 'themes/autumn/components/ads/ScrollXAd';
import Polar from 'themes/autumn/components/features/Polar';

export default function PolarOrScrollX() {
  const [hasScrollX, setScrollX] = useState(false);
  return (
    <>
      {!hasScrollX && <Polar />}
      <ScrollXAd
        onRender={({ adElementRef, event }) => {
          if (event.size) {
            const adsRef = adElementRef.current as HTMLElement;
            const observer = new MutationObserver(() => {
              if (adsRef.className.includes('bz-viewability-container')) {
                setScrollX(true);
              }
            });

            observer.observe(adsRef, {
              attributes: true,
              childList: true,
              subtree: true,
            });
          }
        }}
      />
    </>
  );
}
