'use client';

import { render } from '@testing-library/react';
import React from 'react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import PolarOrScrollX from './index';

import type { EnhancedStore } from '@reduxjs/toolkit';
import type { RootState } from 'store/store';

// eslint-disable-next-line @typescript-eslint/consistent-type-imports
jest.mock<typeof import('react')>('react', () => ({
  ...jest.requireActual('react'),
  useState: jest.fn(),
}));

const useStateMock = jest.spyOn(React, 'useState');
const setState = jest.fn();

describe('<PolarOrScrollX />', () => {
  let store: EnhancedStore<RootState>;

  beforeEach(() => {
    store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        adServing: {
          data: {
            apsPublisherId: '',
            autoRefreshInterval: 0,
            bottomAnchorAdPosition: 1,
            doubleClickCat: 'a',
            doubleClickRegion: 'b',
            doubleClickSite: 'c',
            doubleClickState: 'd',
            doubleClickZone: 'e',
            enableLazyLoadAbTest: false,
            fetchMarginPercent: 100,
            fuseLibraryVersion: '2001',
            mobileScaling: 2,
            renderMarginPercent: 100,
            useBottomAnchorAd: false,
            useMantis: true,
            usePublift: false,
          },
          enabled: true,
        },
        polar: {
          enabled: true,
        },
      },
    }));

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore:next-line
    useStateMock.mockImplementation((init: unknown) => [init, setState]);
  });

  it('renders with hasScrollX is equal to true', () => {
    expect.assertions(1);
    const { container } = render(
      <TestWrapper store={store}>
        <PolarOrScrollX />
      </TestWrapper>,
    );

    useStateMock.mockImplementationOnce(() => [true, setState]);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with hasScrollX is equal to false', () => {
    expect.assertions(1);
    const { container } = render(
      <TestWrapper store={store}>
        <PolarOrScrollX />
      </TestWrapper>,
    );

    useStateMock.mockImplementationOnce(() => [false, setState]);
    expect(container.firstChild).toMatchSnapshot();
  });
});
