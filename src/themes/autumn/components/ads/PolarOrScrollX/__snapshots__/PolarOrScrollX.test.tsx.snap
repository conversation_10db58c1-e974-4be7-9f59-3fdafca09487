// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PolarOrScrollX /> renders with hasScrollX is equal to false 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="md:hidden block space-y-2"
    id="scrollx-container"
  >
    <div
      class="items-center justify-center flex overflow-hidden"
    >
      <div
        class=""
      >
        <div
          id="scrollx"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<PolarOrScrollX /> renders with hasScrollX is equal to true 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="block space-y-2"
    id="polar-container"
  >
    <div
      class="items-center justify-center flex overflow-hidden"
    >
      <div
        class="relative w-full overflow-hidden"
      >
        <div
          id="polar"
        />
      </div>
    </div>
  </div>
  <div
    class="h-0 overflow-hidden"
  />
  <div
    class="md:hidden block space-y-2"
    id="scrollx-container"
  >
    <div
      class="items-center justify-center flex overflow-hidden"
    >
      <div
        class=""
      >
        <div
          id="scrollx"
        />
      </div>
    </div>
  </div>
</div>
`;
