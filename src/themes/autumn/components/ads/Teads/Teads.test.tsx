import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import Teads from '.';

describe('<Teads />', () => {
  it('renders when disabled', () => {
    expect.assertions(1);

    const store = createStore();

    const { container } = render(
      <TestWrapper store={store}>
        <Teads />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders when enabled', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        teads: {
          enabled: true,
        },
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Teads />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
