// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Ad /> renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="block space-y-2 bg-gray-100 py-2"
    id="test-slot-container"
  >
    <p
      class="text-center font-sans text-xxs font-medium uppercase leading-2 text-gray-400"
    >
      Advertisement
    </p>
    <div
      class="items-center justify-center min-h-[250px] flex overflow-hidden"
    >
      <span
        class="absolute rounded-sm border border-gray-400 px-1 text-center font-sans text-xxs leading-4 text-gray-400"
      >
        Ad
      </span>
      <div
        class="relative w-full overflow-hidden"
      >
        <div
          id="test-slot"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<Ad /> renders the ad slot 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="block space-y-2 bg-gray-100 py-2"
    id="test-slot-container"
  >
    <p
      class="text-center font-sans text-xxs font-medium uppercase leading-2 text-gray-400"
    >
      Advertisement
    </p>
    <div
      class="items-center justify-center min-h-[250px] flex overflow-hidden"
    >
      <span
        class="absolute rounded-sm border border-gray-400 px-1 text-center font-sans text-xxs leading-4 text-gray-400"
      >
        Ad
      </span>
      <div
        class="relative w-full overflow-hidden"
      >
        <div
          id="test-slot"
        />
      </div>
    </div>
  </div>
</div>
`;
