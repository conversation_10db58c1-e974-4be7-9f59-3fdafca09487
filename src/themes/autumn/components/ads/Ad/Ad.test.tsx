import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { AdSize } from 'util/ads';
import { TestWrapper } from 'util/jest';

import Ad from '.';

describe('<Ad />', () => {
  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        adServing: {
          data: {
            apsPublisherId: '',
            autoRefreshInterval: 0,
            bottomAnchorAdPosition: 1,
            doubleClickCat: 'a',
            doubleClickRegion: 'b',
            doubleClickSite: 'c',
            doubleClickState: 'd',
            doubleClickZone: 'e',
            enableLazyLoadAbTest: false,
            fetchMarginPercent: 100,
            fuseLibraryVersion: '2001',
            mobileScaling: 2,
            renderMarginPercent: 100,
            useBottomAnchorAd: false,
            useMantis: false,
            usePublift: false,
          },
          enabled: true,
        },
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Ad
          position={1}
          publiftName="test-slot"
          sizes={AdSize.mrec}
          slotId="test-slot"
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders the ad slot', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        adServing: {
          data: {
            apsPublisherId: '',
            autoRefreshInterval: 0,
            bottomAnchorAdPosition: 1,
            doubleClickCat: 'a',
            doubleClickRegion: 'b',
            doubleClickSite: 'c',
            doubleClickState: 'd',
            doubleClickZone: 'e',
            enableLazyLoadAbTest: false,
            fetchMarginPercent: 100,
            fuseLibraryVersion: '2001',
            mobileScaling: 2,
            renderMarginPercent: 100,
            useBottomAnchorAd: false,
            useMantis: true,
            usePublift: false,
          },
          enabled: true,
        },
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Ad
          position={1}
          publiftName="test-slot"
          sizes={AdSize.mrec}
          slotId="test-slot"
        />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
