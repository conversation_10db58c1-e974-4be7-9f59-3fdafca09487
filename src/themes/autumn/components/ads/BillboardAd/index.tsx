'use client';

import { useEffect, useState } from 'react';

import { useAppDispatch } from 'store/hooks';
import pageSlice from 'store/slices/page';
import Ad from 'themes/autumn/components/ads/Ad';
import { AdSize } from 'util/ads';

import type { Props as AdProps } from 'themes/autumn/components/ads/Ad';

type Props = Pick<AdProps, 'className' | 'onRender' | 'sticky'>;

export default function BillboardAd(props: Props) {
  const [isTakeoverAd, setIsTakeoverAd] = useState(false);
  const dispatch = useAppDispatch();

  // Cannot use the ad render callback to check for TruSkin, as the ads can
  // load indirectly through an ad partner
  useEffect(() => {
    const markTakeover = () => {
      const firstChild = document.body.children[0];
      const hasTakeoverAd = // Desktop
        firstChild.className === 'bz-custom-container' ||
        document.getElementById('celtra-skin-container-background') ||
        // Mobile
        document.getElementById('bz-topfiller') ||
        !!document.body.querySelector('.celtra-mobile-skin-wrapper');

      if (hasTakeoverAd) {
        dispatch(pageSlice.actions.setHasTakeoverAd());
        setIsTakeoverAd(true);
        return true;
      }
      return false;
    };

    // Initial pass in case the ad is already on the page
    if (markTakeover()) return undefined;

    const observer = new MutationObserver(() => {
      if (markTakeover()) observer.disconnect();
    });

    observer.observe(document.body, {
      childList: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [dispatch]);

  return (
    <Ad
      autoRefresh={false}
      isTakeoverAd={isTakeoverAd}
      mdSizes={AdSize.leaderboard as [number, number]}
      moveWhenGutterAdPresent
      placeholderPadding="py-3"
      position={1}
      publiftName="header-1"
      sizes={[]}
      slotId="billboard"
      withLabel={false}
      xlSizes={
        [
          [996, 120],
          [996, 250],
          [970, 250],
          [940, 250],
          [940, 120],
          // TypeScript infers this as an array, not a tuple
        ] as [number, number][]
      }
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
    />
  );
}
