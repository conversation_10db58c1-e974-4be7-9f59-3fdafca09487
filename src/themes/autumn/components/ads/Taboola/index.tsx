import Script from 'next/script';

import { useAppSelector } from 'store/hooks';
import { isSponsoredPage } from 'util/page';

// eslint-disable-next-line @stylistic/max-len
// https://developers.taboola.com/web-integrations/docs/js-tag?utm_medium=email&utm_campaign=code_instruction

export default function Taboola() {
  const publisherId = useAppSelector(
    (state) =>
      state.features.taboola.enabled &&
      state.features.taboola.data.publisherId,
  );
  const viewType = useAppSelector((state) => state.settings.viewType);

  if (!publisherId || isSponsoredPage(viewType)) {
    return null;
  }

  const loader = (
    <Script id="taboola-script">
      {`window._taboola = window._taboola || [];
        _taboola.push({article:'auto'});
        !function (e, f, u, i) {
          if (!document.getElementById(i)){
            e.async = 1;
            e.src = u;
            e.id = i;
            f.parentNode.insertBefore(e, f);
          }
        }(document.createElement('script'),
        document.getElementsByTagName('script')[0],
        '//cdn.taboola.com/libtrc/${publisherId}/loader.js',
        'tb_loader_script');
        if(window.performance && typeof window.performance.mark == 'function')
          {window.performance.mark('tbl_ic');}
      `}
    </Script>
  );

  const placement = (
    <>
      <div id="taboola-below-article-feed" />
      <Script id="taboola-article-placement-tag">
        {`window._taboola = window._taboola || [];
          _taboola.push({
            mode: 'thumbnails-a',
            container: 'taboola-below-article-feed',
            placement: 'below article feed',
            target_type: 'mix'
          });`}
      </Script>
    </>
  );

  const flush = (
    <Script id="taboola-flush-tag" strategy="lazyOnload">
      {`window._taboola = window._taboola || [];
        _taboola.push({flush: true});`}
    </Script>
  );

  return (
    <>
      {loader}
      {placement}
      {flush}
    </>
  );
}
