import Script from 'next/script';

import { useAppSelector } from 'store/hooks';
import { useLazyLoadComponentState } from 'util/hooks';
import { isSponsoredPage } from 'util/page';

import styles from './index.module.css';

export default function OwnLocal(): React.ReactElement | null {
  const ownLocal = useAppSelector((state) => state.features.ownLocal);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const { showComponent } = useLazyLoadComponentState();

  if (!ownLocal.enabled || isSponsoredPage(viewType) || !showComponent) {
    return null;
  }

  return (
    <>
      <div
        className={styles.wrapper}
        data-nosnippet
        id="origami-ad-container"
      />
      <Script
        async
        data-partner-id={ownLocal.data.partnerId}
        id="origami-ad-widget"
        src="https://origami.secure.ownlocal.com/origami-widget.js"
      />
    </>
  );
}
