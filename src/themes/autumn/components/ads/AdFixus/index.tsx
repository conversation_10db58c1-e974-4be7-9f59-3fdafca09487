import Script from 'next/script';
import ReactDOM from 'react-dom';

import { useAppSelector } from 'store/hooks';

export default function AdFixus() {
  const domain = useAppSelector((state) => state.conf.domain);
  const adFixusFeature = useAppSelector((state) => state.features.adFixus);

  if (!adFixusFeature.enabled) {
    return null;
  }

  const { licenseKey, version, versionUrl } = adFixusFeature.data;

  if (!licenseKey || !version || !versionUrl) {
    return null;
  }

  const url =
    `https://${domain}/afx_prid/${version}` +
    `/auth/ps/p${versionUrl}.js?lcsid=${licenseKey}`;

  ReactDOM.preload(url, {
    as: 'script',
  });

  return (
    <>
      <Script id="adfixus-inline">var afx_start = Date.now();</Script>
      <Script src={url} />
    </>
  );
}
