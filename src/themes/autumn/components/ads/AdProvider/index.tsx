'use client';

import { useEffect } from 'react';

import LiveRamp from 'components/LiveRamp';
import { useAppSelector } from 'store/hooks';
import GAMProvider from 'themes/autumn/components/ads/GAM/Provider';
import DFPManager from 'themes/autumn/components/ads/GAM/dfp/DFPManager';
import PubliftProvider from 'themes/autumn/components/ads/Publift/Provider';
import { useAdTargeting } from 'util/ads';

import type { CreateAdOptions } from 'util/ads';

export default function AdProvider({ children }: React.PropsWithChildren) {
  const adServing = useAppSelector((state) => state.features.adServing);
  const targeting = useAdTargeting();

  let Provider: typeof PubliftProvider | typeof GAMProvider | undefined;

  if (adServing.enabled) {
    Provider = adServing.data.usePublift ? PubliftProvider : GAMProvider;
  }

  useEffect(() => {
    Provider?.setTargetingArguments(targeting);
  }, [Provider, targeting]);

  if (!adServing.enabled || !Provider) {
    return null;
  }

  return (
    <>
      <LiveRamp />
      <Provider>{children}</Provider>
    </>
  );
}

// Refresh ads for the given element IDs
AdProvider.refresh = function refresh(...slots: string[]) {
  const store = window.getStore();
  const { adServing } = store.getState().features;

  if (!adServing.enabled) {
    return undefined;
  }

  const Provider = adServing.data.usePublift ? PubliftProvider : DFPManager;
  return Provider.refresh(...slots);
};

// Set global targeting arguments
AdProvider.setTargetingArguments = function setTargetingArguments(
  kv: Record<string, string>,
) {
  const store = window.getStore();
  const { adServing } = store.getState().features;

  if (!adServing.enabled) {
    return undefined;
  }

  const Provider = adServing.data.usePublift ? PubliftProvider : DFPManager;
  return Provider.setTargetingArguments(kv);
};

// Create an ad in an existing element with the ID in `options.slotId`
AdProvider.createAd = function createAd(options: CreateAdOptions) {
  const store = window.getStore();
  const { adServing } = store.getState().features;

  if (!adServing.enabled) {
    return undefined;
  }

  const Provider = adServing.data.usePublift ? PubliftProvider : GAMProvider;
  return Provider.createAd(options);
};
