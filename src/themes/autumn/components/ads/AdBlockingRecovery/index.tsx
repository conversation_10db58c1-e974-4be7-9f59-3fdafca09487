'use client';

import Script from 'next/script';
import { useEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import { getCookie, setCookie } from 'util/cookies';

const url =
  'https://fundingchoicesmessages.google.com/i/pub-9739190967354013?ers=1';
const blockCheckCookieName = 'dismissCheck';

export default function AdBlockingRecovery() {
  const hasAccess = useAppSelector((state) => state.piano.hasAccess);
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (getCookie(blockCheckCookieName)) {
      return undefined;
    }

    window.googlefc = window.googlefc || {};
    window.googlefc.ccpa = window.googlefc.ccpa || {};
    // @ts-expect-error Incorrect read-only flag on the below property
    window.googlefc.callbackQueue = window.googlefc.callbackQueue || [];

    // Queue the callback on the callbackQueue.
    googlefc.callbackQueue.push({
      AD_BLOCK_DATA_READY: () => {
        const status = googlefc.getAdBlockerStatus();
        if (
          status === googlefc.AdBlockerStatusEnum.EXTENSION_LEVEL_AD_BLOCKER ||
          status === googlefc.AdBlockerStatusEnum.NETWORK_LEVEL_AD_BLOCKER
        ) {
          setShow(true);
          setCookie({
            cookieName: blockCheckCookieName,
            cookiePath: '/',
            cookieValue: '1',
            // Do not show the message again for 14 days for subscribers
            // and 3 days for everyone else
            expireSeconds: (hasAccess ? 14 : 3) * 24 * 60 * 60,
          });
        }
      },
    });

    setShow(true);

    return () => {
      googlefc.callbackQueue.length = 0;
    };
  }, [hasAccess, setShow]);

  useEffect(() => {
    if (!show) {
      return;
    }

    // Below generated by GAM
    function signalGooglefcPresent() {
      if (!('googlefcPresent' in window.frames)) {
        if (document.body) {
          const iframe = document.createElement('iframe');
          iframe.style.border = 'none';
          iframe.style.display = 'none';
          iframe.style.height = '0';
          iframe.style.left = '-1000px';
          iframe.style.top = '-1000px';
          iframe.style.width = '0';
          iframe.style.zIndex = '-1000';
          iframe.name = 'googlefcPresent';
          document.body.appendChild(iframe);
        } else {
          setTimeout(signalGooglefcPresent, 0);
        }
      }
    }

    signalGooglefcPresent();
  }, [show]);

  if (!show) {
    return null;
  }

  return <Script src={url} />;
}
