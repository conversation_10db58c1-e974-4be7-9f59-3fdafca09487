import Ad from 'themes/autumn/components/ads/Ad';
import { AdSize } from 'util/ads';

import type { Props as AdProps } from 'themes/autumn/components/ads/Ad';

interface Props {
  onRender: AdProps['onRender'];
}

export default function ScrollXAd({ onRender }: Props) {
  return (
    <Ad
      isScrollXAdSlot
      mdSizes={[]}
      onRender={onRender}
      position={99}
      // TODO Consider defaulting to `slotId` if not set
      publiftName="scrollx"
      sizes={AdSize.scrollX}
      slotId="scrollx"
      withLabel={false}
      withPlaceholder={false}
      withPlaceholderBackground={false}
    />
  );
}
