import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import ScrollXAd from './index';

describe('<ScrollXAd />', () => {
  it('renders', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        adServing: {
          data: {
            apsPublisherId: '',
            autoRefreshInterval: 0,
            bottomAnchorAdPosition: 1,
            doubleClickCat: 'a',
            doubleClickRegion: 'b',
            doubleClickSite: 'c',
            doubleClickState: 'd',
            doubleClickZone: 'e',
            enableLazyLoadAbTest: false,
            fetchMarginPercent: 100,
            fuseLibraryVersion: '2001',
            mobileScaling: 2,
            renderMarginPercent: 100,
            useBottomAnchorAd: false,
            useMantis: true,
            usePublift: false,
          },
          enabled: true,
        },
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <ScrollXAd onRender={() => {}} />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
