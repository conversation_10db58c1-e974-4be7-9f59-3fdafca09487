'use client';

import { useState } from 'react';

import InViewEvent from 'util/inViewEvent';
import { fetchTrafficList } from 'util/organization/suzuka';

import Widget from './Widget';

import type { TrafficEvent } from 'util/organization/suzuka';

export interface Props {
  limit: number;
  range: number;
  title: string;
}

export default function Traffic({ limit, range, title }: Props) {
  const [events, setEvents] = useState<TrafficEvent[]>([]);

  const onEnterEvent = () => {
    fetchTrafficList({
      limit,
      range,
    })
      .then((res) => {
        setEvents(res);
      })
      .catch(() => {
        setEvents([]);
      });
  };

  return (
    <InViewEvent onEnterEvent={onEnterEvent}>
      <Widget events={events} title={title} />
    </InViewEvent>
  );
}
