import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import Widget from './Widget';
import events from './mock';

describe('<Widget />', () => {
  it('displays events', () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        location: 'Griffith, NSW',
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Widget events={events} title="Traffic" />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
