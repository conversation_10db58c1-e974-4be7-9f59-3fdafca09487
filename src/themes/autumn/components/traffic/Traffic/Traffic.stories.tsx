import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import Component from './Widget';
import events from './mock';

import type { Meta, StoryObj } from '@storybook/nextjs-vite';

const meta: Meta<typeof Component> = {
  component: Component,
  title: 'Notice board/Traffic',
};

export default meta;

type Story = StoryObj<typeof Component>;

const store = createStore((state) => ({
  ...state,
  conf: {
    ...state.conf,
    location: 'Griffith, NSW',
  },
}));

export const Default: Story = {
  render: () => (
    <TestWrapper store={store}>
      <Component events={events} title="Traffic incidents" />
    </TestWrapper>
  ),
};
