import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { formatDistance } from 'date-fns';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { titleCase } from 'util/string';
import { useDate } from 'util/time';

import AdverseWeatherIcon from './icons/adverse-weather.svg';
import BreakdownIcon from './icons/breakdown.svg';
import ChangedConditionsIcon from './icons/changed-conditions.svg';
import CrashIcon from './icons/crash.svg';
import FireIcon from './icons/fire.svg';
import FloodIcon from './icons/flood.svg';
import HazardIcon from './icons/hazard.svg';
import PublicEventIcon from './icons/public-event.svg';
import RoadworksIcon from './icons/roadworks.svg';
import SnowIcon from './icons/snow.svg';
import TrafficSignalsIcon from './icons/traffic-signals.svg';

import type { TrafficEvent } from 'util/organization/suzuka';

export interface Props {
  events: TrafficEvent[];
  title: string;
}

export type Category =
  | 'AdverseWeather'
  | 'Breakdown'
  | 'ChangedConditions'
  | 'Crash'
  | 'Fire'
  | 'Flood'
  | 'Hazard'
  | 'PublicEvent'
  | 'Roadworks'
  | 'Snow'
  | 'TrafficSignals';

const CATEGORY_DATA: Record<
  Category,
  {
    icon: React.ComponentType<React.SVGProps<SVGElement>>;
    label: React.ReactNode;
  }
> = {
  AdverseWeather: { icon: AdverseWeatherIcon, label: 'Adverse weather' },
  Breakdown: { icon: BreakdownIcon, label: 'Breakdown' },
  ChangedConditions: {
    icon: ChangedConditionsIcon,
    label: 'Changed conditions',
  },
  Crash: { icon: CrashIcon, label: 'Crash' },
  Fire: { icon: FireIcon, label: 'Fire' },
  Flood: { icon: FloodIcon, label: 'Flood' },
  Hazard: { icon: HazardIcon, label: 'Hazard' },
  PublicEvent: { icon: PublicEventIcon, label: 'Public event' },
  Roadworks: { icon: RoadworksIcon, label: 'Roadworks' },
  Snow: { icon: SnowIcon, label: 'Snow' },
  TrafficSignals: { icon: TrafficSignalsIcon, label: 'Traffic signals' },
};

export default function Widget({ events, title }: Props) {
  const now = useDate();
  const location = useAppSelector((state) => state.conf.location);

  if (!events.length) {
    return null;
  }

  return (
    <div className="rounded-lg border border-gray-300 px-4 py-6 text-gray-900 shadow-sm">
      <div className="mb-6 text-lg font-semibold">
        {title} around {location}
      </div>
      <div className="flex flex-col gap-y-6">
        {events.map((event) => {
          const eventEventOption = event.category as Category;

          if (!CATEGORY_DATA[eventEventOption]) {
            return null;
          }

          const Icon = CATEGORY_DATA[eventEventOption].icon;

          return (
            <div
              className="flex flex-col gap-y-1 border-b-1 border-gray-200 pb-6 last:border-none"
              key={event.id}
            >
              <div className="px-4">
                <div className="flex items-center justify-start gap-x-2">
                  {CATEGORY_DATA[eventEventOption]?.label}
                </div>
                <div className="mb-2 mt-1 text-sm font-medium">
                  <Icon className="inline" />
                  {event.title}
                </div>
                {(event.suburb || event.headline) && (
                  <div className="mb-2 mt-1 text-sm font-medium">
                    {event.suburb}
                    {event.suburb && event.headline && ' • '}
                    {event.headline}
                  </div>
                )}
                <div className="flex text-sm font-medium text-gray-600">
                  <div className="flex w-1/2 items-center justify-start gap-x-2 pr-2">
                    <svg
                      fill="none"
                      height="16"
                      viewBox="0 0 17 16"
                      width="17"
                    >
                      <g clipPath="url(#clip0_10737_7999)">
                        <path
                          d="M8.5 3.5C8.5 3.36739 8.44732 3.24021 8.35355 3.14645C8.25979 3.05268 8.13261 3 8 3C7.86739 3 7.74021 3.05268 7.64645 3.14645C7.55268 3.24021 7.5 3.36739 7.5 3.5V9C7.50003 9.08813 7.52335 9.17469 7.56761 9.25091C7.61186 9.32712 7.67547 9.39029 7.752 9.434L11.252 11.434C11.3669 11.4961 11.5014 11.5108 11.627 11.4749C11.7525 11.4391 11.8591 11.3556 11.9238 11.2422C11.9886 11.1288 12.0065 10.9946 11.9736 10.8683C11.9408 10.7419 11.8598 10.6334 11.748 10.566L8.5 8.71V3.5Z"
                          fill="#4B5563"
                        />
                        <path
                          d="M8.5 16C10.6217 16 12.6566 15.1571 14.1569 13.6569C15.6571 12.1566 16.5 10.1217 16.5 8C16.5 5.87827 15.6571 3.84344 14.1569 2.34315C12.6566 0.842855 10.6217 0 8.5 0C6.37827 0 4.34344 0.842855 2.84315 2.34315C1.34285 3.84344 0.5 5.87827 0.5 8C0.5 10.1217 1.34285 12.1566 2.84315 13.6569C4.34344 15.1571 6.37827 16 8.5 16ZM15.5 8C15.5 9.85652 14.7625 11.637 13.4497 12.9497C12.137 14.2625 10.3565 15 8.5 15C6.64348 15 4.86301 14.2625 3.55025 12.9497C2.2375 11.637 1.5 9.85652 1.5 8C1.5 6.14348 2.2375 4.36301 3.55025 3.05025C4.86301 1.7375 6.64348 1 8.5 1C10.3565 1 12.137 1.7375 13.4497 3.05025C14.7625 4.36301 15.5 6.14348 15.5 8Z"
                          fill="#4B5563"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_10737_7999">
                          <rect
                            fill="white"
                            height="16"
                            transform="translate(0.5)"
                            width="16"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                    <div>
                      {titleCase(
                        formatDistance(
                          now.getTime() - event.minuteAgo * 60,
                          now,
                          {
                            addSuffix: true,
                          },
                        ).replace('about ', ''),
                      )}
                    </div>
                  </div>
                  <div className="flex w-1/2 items-center justify-start gap-x-2 pl-2">
                    <svg
                      fill="none"
                      height="14"
                      viewBox="0 0 15 14"
                      width="15"
                    >
                      <path
                        clipRule="evenodd"
                        d="M0.5 1.5C0.5 1.10218 0.658035 0.720644 0.93934 0.43934C1.22064 0.158035 1.60218 0 2 0H3C3.39782 0 3.77936 0.158035 4.06066 0.43934C4.34196 0.720644 4.5 1.10218 4.5 1.5H8.634C8.74407 1.30936 8.91396 1.16036 9.11734 1.07612C9.32072 0.991879 9.54621 0.977099 9.75884 1.03407C9.97148 1.09105 10.1594 1.21659 10.2934 1.39124C10.4274 1.56588 10.5 1.77987 10.5 2C10.5 2.22013 10.4274 2.43412 10.2934 2.60876C10.1594 2.78341 9.97148 2.90895 9.75884 2.96593C9.54621 3.0229 9.32072 3.00812 9.11734 2.92388C8.91396 2.83964 8.74407 2.69064 8.634 2.5H6.624C6.80333 2.68 6.96467 2.88167 7.108 3.105C7.746 4.097 8 5.459 8 7C8 8.993 8.257 10.092 8.713 10.7C9.069 11.176 9.608 11.421 10.5 11.484C10.5042 11.089 10.6641 10.7115 10.945 10.4337C11.2258 10.1558 11.6049 9.99998 12 10H13C13.3978 10 13.7794 10.158 14.0607 10.4393C14.342 10.7206 14.5 11.1022 14.5 11.5V12.5C14.5 12.8978 14.342 13.2794 14.0607 13.5607C13.7794 13.842 13.3978 14 13 14H12C11.6022 14 11.2206 13.842 10.9393 13.5607C10.658 13.2794 10.5 12.8978 10.5 12.5H6.366C6.25593 12.6906 6.08604 12.8396 5.88266 12.9239C5.67928 13.0081 5.45379 13.0229 5.24116 12.9659C5.02852 12.909 4.84063 12.7834 4.70662 12.6088C4.57261 12.4341 4.49997 12.2201 4.49997 12C4.49997 11.7799 4.57261 11.5659 4.70662 11.3912C4.84063 11.2166 5.02852 11.091 5.24116 11.0341C5.45379 10.9771 5.67928 10.9919 5.88266 11.0761C6.08604 11.1604 6.25593 11.3094 6.366 11.5H8.077C8.01914 11.4357 7.96409 11.369 7.912 11.3C7.243 10.407 7 9.007 7 7C7 5.54 6.754 4.403 6.267 3.645C5.877 3.04 5.315 2.645 4.5 2.533C4.49137 2.92508 4.32954 3.29818 4.04916 3.57239C3.76879 3.8466 3.39217 4.00009 3 4H2C1.60218 4 1.22064 3.84196 0.93934 3.56066C0.658035 3.27936 0.5 2.89782 0.5 2.5V1.5ZM2 1C1.86739 1 1.74021 1.05268 1.64645 1.14645C1.55268 1.24021 1.5 1.36739 1.5 1.5V2.5C1.5 2.63261 1.55268 2.75979 1.64645 2.85355C1.74021 2.94732 1.86739 3 2 3H3C3.13261 3 3.25979 2.94732 3.35355 2.85355C3.44732 2.75979 3.5 2.63261 3.5 2.5V1.5C3.5 1.36739 3.44732 1.24021 3.35355 1.14645C3.25979 1.05268 3.13261 1 3 1H2ZM12 11C11.8674 11 11.7402 11.0527 11.6464 11.1464C11.5527 11.2402 11.5 11.3674 11.5 11.5V12.5C11.5 12.6326 11.5527 12.7598 11.6464 12.8536C11.7402 12.9473 11.8674 13 12 13H13C13.1326 13 13.2598 12.9473 13.3536 12.8536C13.4473 12.7598 13.5 12.6326 13.5 12.5V11.5C13.5 11.3674 13.4473 11.2402 13.3536 11.1464C13.2598 11.0527 13.1326 11 13 11H12Z"
                        fill="#4B5563"
                        fillRule="evenodd"
                      />
                    </svg>
                    <div>{event.distance} km away</div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <Link
        className="gtm-hook-more mt-2 flex items-center gap-x-2 align-middle text-sm font-medium"
        href="https://www.livetraffic.com/"
        noStyle
        target="_blank"
      >
        <span>More On Live Traffic</span>
        <FontAwesomeIcon icon={faArrowRight} />
      </Link>
    </div>
  );
}
