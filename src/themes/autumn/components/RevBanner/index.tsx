import { useAppSelector } from 'store/hooks';
import { sendToGtm } from 'util/gtm';
import { REV_ICON } from 'util/icons';

export default function RevBanner(): React.ReactElement | null {
  const { enabled, url } = useAppSelector(
    (state) => state.conf.revNotification,
  );
  const siteName = useAppSelector((state) => state.conf.name);

  if (!enabled || !url) {
    return null;
  }

  const possessive = `${siteName}'${
    siteName[siteName.length - 1] === 's' ? '' : 's'
  }`;

  return (
    <div className="relative z-[15] bg-blue-450 p-4">
      <div className="flex justify-between">
        <div className="flex grow place-content-center items-center gap-3 text-center">
          <a
            className="flex shrink-0 cursor-pointer flex-row items-center justify-center"
            data-testid="rev-link"
            href={url}
            onClick={() => {
              sendToGtm({
                label: url,
                trigger: 'rev_banner_click',
              });
            }}
            target="_blank"
          >
            {REV_ICON}
            <div className="ml-2 mt-1 text-xs text-gray-800 underline">
              {possessive} complete view of property
            </div>
          </a>
        </div>
      </div>
    </div>
  );
}
