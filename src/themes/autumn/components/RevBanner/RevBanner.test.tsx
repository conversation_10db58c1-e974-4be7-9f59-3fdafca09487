import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import RevBanner from '.';

describe('rev banner', () => {
  it('not enabled', () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        name: 'My Site',
        revNotification: {
          enabled: false,
          url: 'http://test/it',
        },
      },
    }));

    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <RevBanner />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('enabled but empty url', () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        name: 'My Site',
        revNotification: {
          enabled: true,
          url: '',
        },
      },
    }));

    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <RevBanner />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('enabled and has url', () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        name: 'My Site',
        revNotification: {
          enabled: true,
          url: 'https://test/it',
        },
      },
    }));

    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <RevBanner />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('site name possessive case', () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        name: 'The Area News',
        revNotification: {
          enabled: true,
          url: 'https://test/it',
        },
      },
    }));

    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <RevBanner />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('test button click', async () => {
    const store = createStore((state) => ({
      ...state,
      conf: {
        ...state.conf,
        name: 'My Site',
        revNotification: {
          enabled: true,
          url: 'https://test/it',
        },
      },
    }));

    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={store}>
        <RevBanner />
      </TestWrapper>,
    );

    jest.spyOn(global, 'open').mockImplementation();
    fireEvent.click(screen.getByTestId('rev-link'));
    await waitFor(() => {
      screen.getByTestId('rev-link');
    });
    expect(container.firstChild).toMatchSnapshot();
  });
});
