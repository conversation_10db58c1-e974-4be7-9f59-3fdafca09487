/* eslint-disable import/prefer-default-export */

const phraseToIconNameMap: Record<string, string> = {
  cloudy: 'cloudy',
  fog: 'fog',
  'lightning|thunder': 'storm',
  'shower|rain|drizzle': 'shower',
  snow: 'snow',
  'sunny|sunshine': 'sunny',
  wind: 'windy',
};

export function phraseToIconName(filename: string): string | undefined {
  const entries = Object.entries(phraseToIconNameMap);
  for (let i = 0; i < entries.length; i++) {
    const [key, value] = entries[i];
    const re = new RegExp(key, 'i');
    if (filename.match(re)) {
      return value;
    }
  }
  return undefined;
}
