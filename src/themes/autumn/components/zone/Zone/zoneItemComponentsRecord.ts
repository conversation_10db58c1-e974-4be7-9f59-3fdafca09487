/* eslint-disable import/prefer-default-export */
import { ZoneItemType } from 'types/ZoneItems';

import Advertisement from '../../zoneItems/Advertisement';
import Authors from '../../zoneItems/Authors';
import Banner from '../../zoneItems/Banner';
import Carousel from '../../zoneItems/Carousel';
import CinematicFeatured from '../../zoneItems/CinematicFeatured';
import Classified from '../../zoneItems/Classified';
import ClassifiedList from '../../zoneItems/ClassifiedList';
import ClusteredStoryList from '../../zoneItems/ClusteredZoneItem';
import CodeSnippet from '../../zoneItems/CodeSnippet';
import Comments from '../../zoneItems/Comments';
import DailyMotion from '../../zoneItems/DailyMotion';
import DpeCard from '../../zoneItems/DpeCard';
import DpeList from '../../zoneItems/DpeList';
import EMagList from '../../zoneItems/EMagList';
import ExploreSimpleLinkWidget from '../../zoneItems/ExploreSimpleLinkWidget';
import ExploreTravelDealList from '../../zoneItems/ExploreTravelDealList';
import Faq from '../../zoneItems/Faq';
import FeaturedDestination from '../../zoneItems/FeaturedDestination';
import Footer from '../../zoneItems/Footer';
import Heading from '../../zoneItems/Heading';
import Iframe from '../../zoneItems/Iframe';
import Image from '../../zoneItems/Image';
import InfoWithCta from '../../zoneItems/InfoWithCta';
import MailingList from '../../zoneItems/MailingList';
import MenuList from '../../zoneItems/MenuList';
import Navigation from '../../zoneItems/Navigation';
import Newsletter from '../../zoneItems/Newsletter';
import PageCollection from '../../zoneItems/PageCollection';
import PlayHq from '../../zoneItems/PlayHq';
import RevWidget from '../../zoneItems/RevWidget';
import Socials from '../../zoneItems/Socials';
import SportsHub from '../../zoneItems/SportsHub';
import StoryList from '../../zoneItems/StoryList';
import StoryListCollection from '../../zoneItems/StoryListCollection';
import TextBlock from '../../zoneItems/TextBlock';
import TitledStoryList from '../../zoneItems/TitledStoryList';
import Traffic from '../../zoneItems/Traffic';
import UgcList from '../../zoneItems/UgcList';
import ViewJobs from '../../zoneItems/ViewJobs';
import Weather from '../../zoneItems/Weather';

import type { ZoneItemComponentsRecord } from 'types/ZoneItems';

export const zoneItemComponents: ZoneItemComponentsRecord = {
  [ZoneItemType.Advertisement]: Advertisement,
  [ZoneItemType.Authors]: Authors,
  [ZoneItemType.Banner]: Banner,
  [ZoneItemType.Carousel]: Carousel,
  [ZoneItemType.CinematicFeatured]: CinematicFeatured,
  [ZoneItemType.Classified]: Classified,
  [ZoneItemType.ClassifiedList]: ClassifiedList,
  [ZoneItemType.ClusteredStoryList]: ClusteredStoryList,
  [ZoneItemType.CodeSnippet]: CodeSnippet,
  [ZoneItemType.Comments]: Comments,
  [ZoneItemType.DailyMotion]: DailyMotion,
  [ZoneItemType.DPECard]: DpeCard,
  [ZoneItemType.DPEList]: DpeList,
  [ZoneItemType.EMagList]: EMagList,
  [ZoneItemType.ExploreTravelDealList]: ExploreTravelDealList,
  [ZoneItemType.ExploreSimpleLinkWidget]: ExploreSimpleLinkWidget,
  [ZoneItemType.FAQ]: Faq,
  [ZoneItemType.FeaturedDestination]: FeaturedDestination,
  [ZoneItemType.Footer]: Footer,
  [ZoneItemType.Heading]: Heading,
  [ZoneItemType.Iframe]: Iframe,
  [ZoneItemType.Image]: Image,
  [ZoneItemType.InfoWithCTA]: InfoWithCta,
  [ZoneItemType.MailingList]: MailingList,
  [ZoneItemType.MenuList]: MenuList,
  [ZoneItemType.Navigation]: Navigation,
  [ZoneItemType.Newsletter]: Newsletter,
  [ZoneItemType.PageCollection]: PageCollection,
  [ZoneItemType.PlayHq]: PlayHq,
  [ZoneItemType.REVWidget]: RevWidget,
  [ZoneItemType.Socials]: Socials,
  [ZoneItemType.SportsHub]: SportsHub,
  [ZoneItemType.StoryList]: StoryList,
  [ZoneItemType.StoryListCollection]: StoryListCollection,
  [ZoneItemType.TitledStoryList]: TitledStoryList,
  [ZoneItemType.TextBlock]: TextBlock,
  [ZoneItemType.Traffic]: Traffic,
  [ZoneItemType.UgcList]: UgcList,
  [ZoneItemType.ViewJobs]: ViewJobs,
  [ZoneItemType.Weather]: Weather,
};
