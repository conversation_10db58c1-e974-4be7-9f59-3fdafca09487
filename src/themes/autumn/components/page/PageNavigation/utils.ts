/* eslint-disable import/prefer-default-export */

'use client';

import { isExternalLink, urlToHref } from 'util/page';

import type { Page } from 'types/Nav';

export function anchorProps(page: Page) {
  return {
    href: urlToHref(page.url),
    rel: page.newWindow && isExternalLink(page.url) ? 'noopener' : undefined,
    target: page.newWindow ? '_blank' : undefined,
  };
}

export function normalizeUrl(url: string) {
  // Remove leading and trailing slashes
  return url.replace(/^\/+/, '').replace(/\/+$/, '');
}
