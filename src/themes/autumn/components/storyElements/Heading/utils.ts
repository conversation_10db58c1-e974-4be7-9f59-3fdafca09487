/* eslint-disable import/prefer-default-export */

import { charsToPixels, htmlToText } from 'util/device';

import { type HeightsProps } from '.';

export function headingHeights({
  deviceType,
  item: {
    element: { level, text },
  },
}: HeightsProps): number {
  const heightOverride = level === '4' ? 24 : undefined;
  const chars = htmlToText(text).length;
  return charsToPixels(chars, deviceType, 'prose', heightOverride);
}
