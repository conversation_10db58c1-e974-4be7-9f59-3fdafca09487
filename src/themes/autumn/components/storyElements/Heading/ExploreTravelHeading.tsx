import slugify from 'slugify';

import { htmlToText } from 'util/device';

import type { HeadingElement, StoryElementBaseProps } from 'types/Story';

const headingClassMap: Record<number, string> = {
  1: 'text-2xl font-bold text-gray-800',
  2: 'text-2xl/[36px] font-bold text-gray-800',
  3: 'text-lg font-bold text-gray-800',
  4: 'text-base font-extrabold text-gray-800',
  5: 'text-base font-extrabold text-gray-800',
  6: 'text-base font-extrabold text-gray-800',
};

function ExploreTravelHeading({
  element: { level, text },
}: StoryElementBaseProps<HeadingElement>): React.ReactElement | null {
  if (!level || !text) {
    return null;
  }

  const levelInt = parseInt(level, 10);

  if (levelInt < 1 || levelInt > 6) {
    return null;
  }

  // Note: Editor only allow to write h2, h3 and h4.
  type TagTypes = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  const ListTag: TagTypes = `h${level}` as TagTypes;
  const headingClasses = headingClassMap[levelInt];
  const anchorId = slugify(htmlToText(text), { lower: true });
  return (
    <div className="md:w-180">
      <ListTag
        className={headingClasses}
        dangerouslySetInnerHTML={{ __html: text }}
        id={anchorId}
      />
    </div>
  );
}

export default ExploreTravelHeading;
