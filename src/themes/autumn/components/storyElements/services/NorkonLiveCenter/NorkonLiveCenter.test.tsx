import { render, waitFor } from '@testing-library/react';

import { createStore } from 'store/store';
import { Service, ServiceType, StoryElementType } from 'types/Story';
import { TestWrapper } from 'util/jest';

import NorkonLiveCenter from '.';

import type { GenericElement } from 'types/Story';

const mockElement: GenericElement = {
  description: 'Test live blog description',
  embed: '',
  service: Service.Norkon,
  serviceId: 'test-service-id',
  serviceType: ServiceType.Blog,
  title: 'Test live blog title',
  type: StoryElementType.Generic,
};

const mockApiResponse = {
  result: [
    {
      articleBody: 'Test post 1',
      datePublished: '2025-01-01T00:00:00Z',
      headline: 'Test post 1',
    },
  ],
  success: true,
};

describe('NorkonLiveCenter', () => {
  beforeEach(() => {
    global.fetch = jest.fn();
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      json: jest.fn().mockResolvedValue(mockApiResponse),
      ok: true,
    });
  });

  it('renders correctly', () => {
    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        norkonLiveblog: {
          data: {
            apiUrl: 'https://api.test.livecenter.com/',
            assetVersion: '1.0.0',
            baseUrl: 'https://test.livecenter.com/',
            scriptUrl: 'https://test.livecenter.com/',
            tenantKey: 'test-tenant',
            websocketUrl: 'wss://test.livecenter.com/',
          },
          enabled: true,
        },
      },

      settings: {
        ...state.settings,
        host: 'example.com',
      },
      story: {
        ...state.story,
        byline: 'Test Author',
        canonicalUrl: '/test-live-blog',
        publishFrom: '2024-01-01T00:00:00Z',
        summary: 'Test summary',
        title: 'Test Live Blog',
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <NorkonLiveCenter element={mockElement} />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('injects structured data script', async () => {
    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        norkonLiveblog: {
          data: {
            apiUrl: 'https://api.test.livecenter.com/',
            assetVersion: '1.0.0',
            baseUrl: 'https://test.livecenter.com/',
            scriptUrl: 'https://test.livecenter.com/',
            tenantKey: 'tenant-key',
            websocketUrl: 'wss://test.livecenter.com/',
          },
          enabled: true,
        },
      },
      settings: {
        ...state.settings,
        host: 'example.com',
      },
      story: {
        ...state.story,
        canonicalUrl: '/test-live-blog',
        publishFrom: '2024-01-01T00:00:00Z',
        title: 'Test Live Blog',
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <NorkonLiveCenter element={mockElement} />
      </TestWrapper>,
    );

    await waitFor(() => {
      const structuredDataScript = container.querySelector(
        'script[type="application/ld+json"]',
      );
      expect(structuredDataScript).toBeInTheDocument();

      const structuredData = JSON.parse(
        structuredDataScript?.textContent || '{}',
      ) as {
        '@type': string;
        headline: string;
        liveBlogUpdate: { '@type': string; headline: string }[];
      };

      expect(structuredData['@type']).toBe('LiveBlogPosting');
      expect(structuredData.headline).toBe('Test Live Blog');
      expect(structuredData.liveBlogUpdate[0]['@type']).toBe('BlogPosting');
      expect(structuredData.liveBlogUpdate[0].headline).toBe('Test post 1');
    });
  });
});
