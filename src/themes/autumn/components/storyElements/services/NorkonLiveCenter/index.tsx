/* eslint-disable tailwindcss/no-custom-classname */

'use client';

import Script from 'next/script';
import { useInsertionEffect } from 'react';

import { useAppSelector } from 'store/hooks';
import { ServiceType } from 'types/Story';

import { useStructuredMarkup } from './hooks';

import type { GenericElement } from 'types/Story';

interface Props {
  element: GenericElement;
}

function NorkonLiveCenter({ element }: Props): React.ReactElement | null {
  const hasPianoPaywall = useAppSelector(
    (state) =>
      state.features.piano.enabled &&
      (state.piano.hasPaywall || state.piano.loadingPaywall),
  );
  const norkonFeature = useAppSelector(
    (state) => state.features.norkonLiveblog,
  );
  const { serviceId } = element;

  const structuredData = useStructuredMarkup(
    element,
    norkonFeature,
    serviceId,
  );

  useInsertionEffect(() => {
    if (norkonFeature.enabled) {
      const { assetVersion, baseUrl, tenantKey } = norkonFeature.data;

      // eslint-disable-next-line @stylistic/max-len
      const baseCssUrl = `${baseUrl}scripts/ncposts/ncposts-${assetVersion}.min.css`;
      const baseCssLink = document.createElement('link');
      baseCssLink.href = baseCssUrl;
      baseCssLink.rel = 'stylesheet';
      document.head.appendChild(baseCssLink);

      const extensionCssUrl = `${baseUrl}LiveCenter/ExtensionCss/${tenantKey}`;
      const extensionCssLink = document.createElement('link');
      extensionCssLink.href = extensionCssUrl;
      extensionCssLink.rel = 'stylesheet';
      document.head.appendChild(extensionCssLink);

      return () => {
        document.head.removeChild(baseCssLink);
        document.head.removeChild(extensionCssLink);
      };
    }

    return () => {};
  }, [norkonFeature]);

  if (
    element.serviceType !== ServiceType.Blog ||
    hasPianoPaywall ||
    !norkonFeature.enabled
  ) {
    return null;
  }

  const { assetVersion, baseUrl, scriptUrl, tenantKey, websocketUrl } =
    norkonFeature.data;

  return (
    <>
      <script
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
        type="application/ld+json"
      />
      <div className="lc-frame lc-default-theme font-opensans">
        <div className="lc-feed-container">
          <div id="master-container" />
          <div className="text-center">
            <button className="lc-load-more" id="lc-load-more" type="button">
              Load more
            </button>
          </div>
        </div>
      </div>
      <Script
        async
        id="norkon-live-center"
        src={`${scriptUrl}scripts/ncposts/ncposts-${assetVersion}.min.js`}
      />
      <Script
        async
        onLoad={() => {
          window.NcPosts.start({
            baseUrl,
            channelId: serviceId,
            container: document.getElementById('master-container'),
            extensionContainer: window.NcLiveCenterExtensions,
            showMoreElement: document.getElementById('lc-load-more'),
            tenantKey,
            wsBaseUrl: websocketUrl,
          });
        }}
        src={`${scriptUrl}LiveCenter/ExtensionJs/${tenantKey}`}
      />
    </>
  );
}

export default NorkonLiveCenter;
