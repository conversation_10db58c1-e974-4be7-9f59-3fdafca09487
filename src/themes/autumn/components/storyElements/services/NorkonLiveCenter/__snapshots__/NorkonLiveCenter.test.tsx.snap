// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`NorkonLiveCenter renders correctly 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@id":"https://example.com/test-live-blog","@type":"LiveBlogPosting","about":{"@type":"Event","description":"Test live blog description","eventAttendanceMode":"https://schema.org/OnlineEventAttendanceMode","location":{"@type":"VirtualLocation","url":"https://example.com/test-live-blog"},"name":"Test Live Blog","organizer":{"@type":"Person","name":"Test Author","url":"https://example.com/test-live-blog"},"startDate":"2024-01-01T00:00:00Z"},"coverageStartTime":"2024-01-01T00:00:00Z","description":"Test summary","headline":"Test Live Blog","liveBlogUpdate":[]}
  </script>
  <div
    class="lc-frame lc-default-theme font-opensans"
  >
    <div
      class="lc-feed-container"
    >
      <div
        id="master-container"
      />
      <div
        class="text-center"
      >
        <button
          class="lc-load-more"
          id="lc-load-more"
          type="button"
        >
          Load more
        </button>
      </div>
    </div>
  </div>
</div>
`;
