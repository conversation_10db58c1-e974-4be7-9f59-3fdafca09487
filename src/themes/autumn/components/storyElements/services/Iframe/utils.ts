/* eslint-disable import/prefer-default-export */

import type { GenericHeightsProps } from '../../common/storyElements';

export function iframeHeights({
  item: {
    element: { embed },
  },
}: GenericHeightsProps): number {
  if (!embed) {
    return 0;
  }

  const matches = embed.matchAll(
    /(?:style="[^"]*height\s*:(\d+)(?:px)?)|(?:height="\s*(\d+)(?:px)?)/gi,
  );
  if (!matches) {
    return 150;
  }

  // NOTE: Style attrib height has priority over height attribute.
  const embedHeight: number | undefined = (
    Array.from(matches) as Array<Array<string | undefined>>
  ).reduce((result: number | undefined, match: Array<string | undefined>) => {
    if (match[1]) {
      return Number(match[1]);
    }
    if (match[2] && result === undefined) {
      return Number(match[2]);
    }
    return result;
  }, undefined);

  return embedHeight !== undefined ? embedHeight : 150;
}
