import { twMerge } from 'tailwind-merge';

import { useAppSelector } from 'store/hooks';

import type { ServiceProps } from '../types';

function ExploreTravelYoutube({
  element,
  widthClassName,
}: ServiceProps): React.ReactElement | null {
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  if (!isClientSide) {
    return null;
  }

  return (
    <iframe
      allowFullScreen
      className={twMerge(
        'mx-auto aspect-video w-full border-0',
        widthClassName,
      )}
      src={`https://www.youtube.com/embed/${element.serviceId}`}
      title={`Youtube video - ${element.description}`}
    />
  );
}

export default ExploreTravelYoutube;
