import { renderHook } from '@testing-library/react';

import { useAdPublisherProvidedId } from 'components/Piano/ads';
import { createStore } from 'store/store';
import { usePageParents, usePremiumSubscription } from 'util/hooks';
import { TestWrapper } from 'util/jest';
import useMantisTargetingData from 'util/mantis';

import { useDailymotionAdConfig } from './hooks';

import type React from 'react';

jest.mock('util/hooks', () => ({
  usePageParents: jest.fn(),
  usePremiumSubscription: jest.fn(),
}));
const usePageParentsMock = usePageParents as jest.MockedFunction<
  typeof usePageParents
>;
usePageParentsMock.mockReturnValue({
  parent: undefined,
});
const usePremiumSubscriptionMock =
  usePremiumSubscription as jest.MockedFunction<typeof usePremiumSubscription>;
usePremiumSubscriptionMock.mockReturnValue({
  hasPremiumExtended: false,
  hasPremiumSubscription: false,
  hasValidatedPremium: false,
  isPremiumRequest: false,
  supportPremiumExtended: false,
  supportPremiumSubscription: false,
});

jest.mock('util/mantis', () => ({
  __esModule: true,
  default: jest.fn(),
}));
const useMantisTargetingDataMock =
  useMantisTargetingData as jest.MockedFunction<typeof useMantisTargetingData>;
useMantisTargetingDataMock.mockReturnValue({
  isReady: false,
  targetingData: undefined,
});

jest.mock('components/Piano/ads', () => ({
  useAdPublisherProvidedId: jest.fn(),
}));
const useAdPublisherProvidedIdMock =
  useAdPublisherProvidedId as jest.MockedFunction<
    typeof useAdPublisherProvidedId
  >;
useAdPublisherProvidedIdMock.mockReturnValue(null);

describe('useDailymotionAdConfig', () => {
  it('should render config with minimum setup', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=viewType&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should return undefined when adServing is disabled', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              enabled: false,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeUndefined();
  });

  it('should return undefined when doubleClickSite is empty', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: '',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeUndefined();
  });

  it('should return undefined when googleAdManagerNetworkIdentifier is empty', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: '',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeUndefined();
  });

  it('should remap a `section` viewType value', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'section',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=index&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should remap a `story` viewType value', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'story',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=article&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should use the provided non-mapped viewType value', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'NON_MAPPED_VALUE',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=NON_MAPPED_VALUE&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should default to `index` if viewType is empty', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: '',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=index&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should add cat if page name is defined', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          page: {
            name: 'pageName',
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/pagename',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=pagename',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should add cat if adServing doubleClickCat is defined', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickCat: 'doubleClickCat',
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/doubleclickcat',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=doubleclickcat',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should add cat if page doubleClickCat is defined', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          page: {
            doubleClickCat: 'pageDoubleClickCat',
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/pagedoubleclickcat',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=pagedoubleclickcat',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should prefer page doubleClickCat over pageName for cat', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          page: {
            doubleClickCat: 'pageDoubleClickCat',
            name: 'pageName',
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/pagedoubleclickcat',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=pagedoubleclickcat',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should prefer adServing doubleClickCat over pageName for cat', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickCat: 'doubleClickCat',
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          page: {
            name: 'pageName',
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/doubleclickcat',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=doubleclickcat',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should prefer page doubleClickCat over adServing doubleClickCat for cat', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickCat: 'doubleClickCat',
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          page: {
            doubleClickCat: 'pageDoubleClickCat',
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/pagedoubleclickcat',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=pagedoubleclickcat',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should prefer page doubleClickCat over adServing doubleClickCat and page name for cat', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickCat: 'doubleClickCat',
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          page: {
            doubleClickCat: 'pageDoubleClickCat',
            name: 'pageName',
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/pagedoubleclickcat',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=pagedoubleclickcat',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should add cat if parent page (with name) is provided', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    usePageParentsMock.mockReturnValueOnce({
      parent: {
        altMenuName: 'parentAltMenuName',
        doubleClickCat: '',
        id: 0xc0de,
        menuName: 'parentMenuName',
        name: 'parentName',
        showHeading: false,
        showSiblingsOnChildPages: false,
        url: '/parentUrl',
      },
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/parentname',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=parentname',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should add cat if parent page (with name and doubleClickCat) is provided', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    usePageParentsMock.mockReturnValueOnce({
      parent: {
        altMenuName: 'parentAltMenuName',
        doubleClickCat: 'parentDoubleClickCat',
        id: 0xc0de,
        menuName: 'parentMenuName',
        name: 'parentName',
        showHeading: false,
        showSiblingsOnChildPages: false,
        url: '/parentUrl',
      },
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/parentdoubleclickcat',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=parentdoubleclickcat',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should add cat1 if parent page is provided and page doubleClickCat is provided', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          page: {
            doubleClickCat: 'pageDoubleClickCat',
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    usePageParentsMock.mockReturnValueOnce({
      parent: {
        altMenuName: 'parentAltMenuName',
        doubleClickCat: 'parentDoubleClickCat',
        id: 0xc0de,
        menuName: 'parentMenuName',
        name: 'parentName',
        showHeading: false,
        showSiblingsOnChildPages: false,
        url: '/parentUrl',
      },
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/parentdoubleclickcat/pagedoubleclickcat',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=parentdoubleclickcat&cat1=pagedoubleclickcat',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should add cat1 if parent page is provided and page name is provided', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          page: {
            name: 'pageName',
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    usePageParentsMock.mockReturnValueOnce({
      parent: {
        altMenuName: 'parentAltMenuName',
        doubleClickCat: 'parentDoubleClickCat',
        id: 0xc0de,
        menuName: 'parentMenuName',
        name: 'parentName',
        showHeading: false,
        showSiblingsOnChildPages: false,
        url: '/parentUrl',
      },
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/parentdoubleclickcat/pagename',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=parentdoubleclickcat&cat1=pagename',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should prefer page doubleClickCat over page name for cat1', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          page: {
            doubleClickCat: 'pageDoubleClickCat',
            name: 'pageName',
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    usePageParentsMock.mockReturnValueOnce({
      parent: {
        altMenuName: 'parentAltMenuName',
        doubleClickCat: 'parentDoubleClickCat',
        id: 0xc0de,
        menuName: 'parentMenuName',
        name: 'parentName',
        showHeading: false,
        showSiblingsOnChildPages: false,
        url: '/parentUrl',
      },
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe(
      '/networkIdentifier/doubleClickSite/parentdoubleclickcat/pagedoubleclickcat',
    );
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&cat=parentdoubleclickcat&cat1=pagedoubleclickcat',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should include mantis targetting data if useMantis is true and is a story', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
                useMantis: true,
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    useMantisTargetingDataMock.mockReturnValueOnce({
      isReady: true,
      targetingData: {
        iab_context: 'iabContext',
        mantis: 'mantis',
        mantis_context: 'mantisContext',
      },
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe(
      'ctype=viewType&pageid=storyId&iab_context=iabContext&mantis=mantis&mantis_context=mantisContext',
    );
    expect(result?.ppid).toBeUndefined();
  });

  it('should not include mantis targetting data if useMantis is false', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
                useMantis: false,
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    useMantisTargetingDataMock.mockReturnValueOnce({
      isReady: true,
      targetingData: {
        iab_context: 'iabContext',
        mantis: 'mantis',
        mantis_context: 'mantisContext',
      },
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=viewType&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should not include mantis targetting data if not a story', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
                useMantis: true,
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: '',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    useMantisTargetingDataMock.mockReturnValueOnce({
      isReady: true,
      targetingData: {
        iab_context: 'iabContext',
        mantis: 'mantis',
        mantis_context: 'mantisContext',
      },
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=viewType&pageid=');
    expect(result?.ppid).toBeUndefined();
  });

  it("should render dynamiciu as 'adfree' with supportPremiumExtended = true and hasPremiumExtended = true", () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    usePremiumSubscriptionMock.mockReturnValueOnce({
      hasPremiumExtended: true,
      hasPremiumSubscription: true,
      hasValidatedPremium: true,
      isPremiumRequest: true,
      supportPremiumExtended: true,
      supportPremiumSubscription: true,
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('adfree');
    expect(result?.keyvalues).toBe('ctype=viewType&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should render standard dynamiciu with supportPremiumExtended = false', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    usePremiumSubscriptionMock.mockReturnValueOnce({
      hasPremiumExtended: true,
      hasPremiumSubscription: true,
      hasValidatedPremium: true,
      isPremiumRequest: true,
      supportPremiumExtended: false,
      supportPremiumSubscription: true,
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=viewType&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should render standard dynamiciu with hasPremiumExtended = false', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    usePremiumSubscriptionMock.mockReturnValueOnce({
      hasPremiumExtended: false,
      hasPremiumSubscription: true,
      hasValidatedPremium: true,
      isPremiumRequest: true,
      supportPremiumExtended: true,
      supportPremiumSubscription: true,
    });

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=viewType&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should render with ppid', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    useAdPublisherProvidedIdMock.mockReturnValueOnce('testPpid');

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=viewType&pageid=storyId');
    expect(result?.ppid).toBe('testPpid');
  });

  it('should render no ppid if ppid is empty', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    useAdPublisherProvidedIdMock.mockReturnValueOnce('');

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=viewType&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });

  it('should render no ppid if ppid is null', () => {
    const wrapper = ({ children }: React.PropsWithChildren) => (
      <TestWrapper
        store={createStore(() => ({
          conf: {
            googleAdManagerNetworkIdentifier: 'networkIdentifier',
          },
          features: {
            adServing: {
              data: {
                doubleClickSite: 'doubleClickSite',
              },
              enabled: true,
            },
          },
          settings: {
            viewType: 'viewType',
          },
          story: {
            id: 'storyId',
          },
        }))}
      >
        {children}
      </TestWrapper>
    );

    useAdPublisherProvidedIdMock.mockReturnValueOnce(null);

    const hook = renderHook(() => useDailymotionAdConfig(), { wrapper });
    const result = hook.result.current;

    expect(result).toBeDefined();
    expect(result?.dynamiciu).toBe('/networkIdentifier/doubleClickSite');
    expect(result?.keyvalues).toBe('ctype=viewType&pageid=storyId');
    expect(result?.ppid).toBeUndefined();
  });
});
