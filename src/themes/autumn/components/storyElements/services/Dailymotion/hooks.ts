/* eslint-disable import/prefer-default-export */
import { useMemo } from 'react';

import { useAdPublisherProvidedId } from 'components/Piano/ads';
import { useAppSelector } from 'store/hooks';
import { generateAdCat } from 'util/ads';
import { VIEW_TYPE_CTYPE_MAP } from 'util/constants';
import { usePageParents, usePremiumSubscription } from 'util/hooks';
import useMantisTargetingData from 'util/mantis';

type CustomConfig = Exclude<
  DMPlayerOptions['params']['customConfig'],
  undefined
>;
type DailymotionAdConfig =
  | undefined
  | Pick<CustomConfig, 'dynamiciu' | 'keyvalues' | 'ppid'>;

export function useDailymotionAdConfig(): DailymotionAdConfig {
  const adServing = useAppSelector((state) => state.features.adServing);
  const pageDoubleClickCat = useAppSelector(
    (state) => state.page.doubleClickCat,
  );
  const networkIdentifier = useAppSelector(
    (state) => state.conf.googleAdManagerNetworkIdentifier,
  );
  const pageName = useAppSelector((state) => state.page.name);
  const storyId = useAppSelector((state) => state.story.id);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const { parent: parentPage } = usePageParents();
  const { targetingData: mantisTargetingData } = useMantisTargetingData();

  const { hasPremiumExtended, supportPremiumExtended } =
    usePremiumSubscription();

  const ppid = useAdPublisherProvidedId();

  const adDoubleClickSite =
    adServing.enabled && adServing.data.doubleClickSite;
  const adDoubleClickCat = adServing.enabled
    ? adServing.data.doubleClickCat
    : '';
  const isStory = !!storyId;
  const useMantis = adServing.enabled && adServing.data.useMantis && isStory;

  return useMemo(() => {
    if (!networkIdentifier || !adDoubleClickSite) {
      return undefined;
    }

    const ctype = VIEW_TYPE_CTYPE_MAP[viewType] || viewType || 'index';

    const adUnitParts = [networkIdentifier, adDoubleClickSite];
    const adKeyParts = [`ctype=${ctype}`, `pageid=${storyId}`];
    const cat = parentPage?.name
      ? generateAdCat(parentPage.doubleClickCat, parentPage.name)
      : generateAdCat(pageDoubleClickCat || adDoubleClickCat, pageName);
    const cat1 = parentPage?.name
      ? generateAdCat(pageDoubleClickCat, pageName)
      : '';

    if (cat) {
      adUnitParts.push(cat);
      adKeyParts.push(`cat=${cat}`);
    }

    if (cat1) {
      adUnitParts.push(cat1);
      adKeyParts.push(`cat1=${cat1}`);
    }

    const mantisKeys =
      useMantis && mantisTargetingData
        ? Object.entries(mantisTargetingData).map(
            ([key, value]) => `${key}=${value}`,
          )
        : [];

    const adKey = [...adKeyParts, ...mantisKeys].join('&');
    const adUnitString = `/${adUnitParts.join('/')}`;
    const adUnit =
      supportPremiumExtended && hasPremiumExtended ? 'adfree' : adUnitString;

    const ppidConfig = ppid ? { ppid } : {};

    return {
      ...ppidConfig,
      dynamiciu: adUnit,
      keyvalues: adKey,
    };
  }, [
    pageDoubleClickCat,
    mantisTargetingData,
    networkIdentifier,
    pageName,
    parentPage?.doubleClickCat,
    parentPage?.name,
    storyId,
    supportPremiumExtended,
    adDoubleClickCat,
    adDoubleClickSite,
    useMantis,
    hasPremiumExtended,
    viewType,
    ppid,
  ]);
}
