'use client';

import { ServiceType } from 'types/Story';
import { VIDEO_WIDTHS, charsToPixels, htmlToText } from 'util/device';

import type { GenericHeightsProps } from '../../common/storyElements';

export function dailyMotionHeights({
  deviceType,
  item: {
    element: { description, serviceId, serviceType },
  },
}: GenericHeightsProps): number {
  const store = window.getStore();
  const state = store.getState();
  const { dailymotionPlayerId: playerId } = state.conf;

  if (serviceType !== ServiceType.Video || !playerId || !serviceId.trim()) {
    return 0;
  }

  // Add 2 for chars worth for icon.
  const chars = description ? htmlToText(description).length + 2 : 0;

  // When description add top padding
  const descHeight = description
    ? 12 + charsToPixels(chars, deviceType, 'caption')
    : 0;
  const videoHeight = VIDEO_WIDTHS[deviceType];
  return videoHeight + descHeight;
}

// Copied from DailyMotionHeights
export function ExploreTravelDailymotionHeights({
  deviceType,
  item: {
    element: { description, serviceId, serviceType },
  },
}: GenericHeightsProps): number {
  const store = window.getStore();
  const state = store.getState();
  const { dailymotionPlayerId: playerId } = state.conf;

  if (serviceType !== ServiceType.Video || !playerId || !serviceId.trim()) {
    return 0;
  }

  // Add 2 for chars worth for icon.
  const chars = description ? htmlToText(description).length + 2 : 0;

  // When description add top padding
  const descHeight = description
    ? 12 + charsToPixels(chars, deviceType, 'caption')
    : 0;
  const videoHeight = VIDEO_WIDTHS[deviceType];
  return videoHeight + descHeight;
}
