import { useAppSelector } from 'store/hooks';
import { StoryViewType } from 'types/ZoneItems';

import Dailymotion from './index';

import type { ServiceProps } from '../types';

function ExploreTravelDailymotion({
  element,
  widthClassName,
}: ServiceProps): React.ReactElement | null {
  const explorePlayerId = useAppSelector(
    (state) => state.conf.dailymotionPlayerIdForExploreTravelArticles,
  );
  // This story element component is reused by story-commercial
  const isStoryCommercial = useAppSelector(
    (state) =>
      (state.settings.viewType as StoryViewType) ===
      StoryViewType.STORY_COMMERCIAL,
  );

  return (
    <Dailymotion
      customPlayerId={isStoryCommercial ? undefined : explorePlayerId}
      element={element}
      ignorePaywall={isStoryCommercial}
      widthClassName={widthClassName}
    />
  );
}

export default ExploreTravelDailymotion;
