/* eslint-disable import/prefer-default-export */

'use client';

import { ServiceType } from 'types/Story';

import type { GenericHeightsProps } from '../../common/storyElements';

export function viafouraLiveBlogHeights({
  item: {
    element: { serviceType },
  },
}: GenericHeightsProps): number {
  const store = window.getStore();
  const state = store.getState();

  if (
    serviceType !== ServiceType.Blog ||
    !state.features.viafoura.enabled ||
    !state.features.viafoura.data.sectionUuid
  ) {
    return 0;
  }

  // We can't really estimate height, so just return large heights to force
  // ad immediately after.
  return 1000;
}
