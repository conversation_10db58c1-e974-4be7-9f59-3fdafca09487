/* eslint-disable import/prefer-default-export */
import { charsToPixels, htmlToText } from 'util/device';

import type { GenericHeightsProps } from '../../common/storyElements';

export function twentyFourLiveBlogHeights({
  deviceType,
  item: {
    element: { description },
  },
}: GenericHeightsProps): number {
  const chars = htmlToText(description).length;
  return 960 + (chars ? charsToPixels(chars, deviceType, 'prose') : 0);
}
