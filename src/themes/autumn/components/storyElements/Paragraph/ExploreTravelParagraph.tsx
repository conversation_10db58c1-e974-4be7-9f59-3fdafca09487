import type { ParagraphElement, StoryElementBaseProps } from 'types/Story';

function ExploreTravelParagraph({
  element: { text },
}: StoryElementBaseProps<ParagraphElement>): React.ReactElement {
  return (
    <div className="md:w-180">
      <p
        className="text-lg font-normal leading-9 text-gray-800 md:text-xl md:leading-8 [&_a]:text-gray-800 [&_a]:underline [&_a]:decoration-gray-500 hover:[&_a]:decoration-gray-800"
        dangerouslySetInnerHTML={{ __html: text }}
      />
    </div>
  );
}

export default ExploreTravelParagraph;
