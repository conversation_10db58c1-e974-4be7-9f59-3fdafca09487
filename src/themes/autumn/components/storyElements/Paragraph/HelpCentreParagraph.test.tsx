import { render, screen } from '@testing-library/react';

import { StoryElementType } from 'types/Story';

import HelpCentreParagraph from './HelpCentreParagraph';

import type { ParagraphElement } from 'types/Story';

describe('HelpCentreParagraph story element', () => {
  it('matches snapshot', () => {
    const element: ParagraphElement = {
      text: '<p>This is a test paragraph with <strong>bold text</strong> and a <a href="https://example.com">link</a>.</p>',
      type: StoryElementType.Paragraph,
    };

    const { container } = render(
      <HelpCentreParagraph element={element} index={0} />,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders paragraph text correctly', () => {
    const element: ParagraphElement = {
      text: 'This is a simple test paragraph.',
      type: StoryElementType.Paragraph,
    };

    render(<HelpCentreParagraph element={element} index={0} />);

    expect(
      screen.getByText('This is a simple test paragraph.'),
    ).toBeInTheDocument();
  });

  it('renders HTML content correctly', () => {
    const element: ParagraphElement = {
      text: 'This is a paragraph with <strong>bold text</strong> and a <a href="https://example.com">link</a>.',
      type: StoryElementType.Paragraph,
    };

    const { container } = render(
      <HelpCentreParagraph element={element} index={0} />,
    );

    // Check that HTML is rendered as elements, not text
    expect(container.querySelector('strong')).toBeInTheDocument();
    expect(container.querySelector('a')).toBeInTheDocument();
    expect(container.querySelector('a')).toHaveAttribute(
      'href',
      'https://example.com',
    );
  });

  it('applies correct styling classes', () => {
    const element: ParagraphElement = {
      text: 'Test paragraph with <a href="#">link</a>',
      type: StoryElementType.Paragraph,
    };

    const { container } = render(
      <HelpCentreParagraph element={element} index={0} />,
    );

    const paragraph = container.querySelector('p');
    expect(paragraph).toHaveClass('text-base', 'leading-6', 'text-gray-800');
  });

  it('applies correct link styling classes', () => {
    const element: ParagraphElement = {
      text: 'Text with <a href="https://example.com">a test link</a>.',
      type: StoryElementType.Paragraph,
    };

    const { container } = render(
      <HelpCentreParagraph element={element} index={0} />,
    );

    const paragraph = container.querySelector('p');
    expect(paragraph).toHaveClass(
      'prose-a:text-blue-600',
      'prose-a:underline',
      'prose-a:decoration-blue-400',
      'prose-a:visited:text-gray-500',
      'prose-a:visited:decoration-gray-500',
      'prose-a:hover:decoration-blue-600',
    );
  });
});
