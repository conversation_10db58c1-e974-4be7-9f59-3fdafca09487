import { charsToPixels, htmlToText } from 'util/device';

import { type HeightsProps } from '.';

export function paragraphHeights({
  deviceType,
  item: {
    element: { text },
  },
}: HeightsProps): number {
  const chars = htmlToText(text).length;
  return charsToPixels(chars, deviceType, 'prose');
}

export function paragraphHeightsExploreTravel({
  deviceType,
  item: {
    element: { text },
  },
}: HeightsProps): number {
  const chars = htmlToText(text).length;
  return charsToPixels(chars, deviceType, 'prose');
}
