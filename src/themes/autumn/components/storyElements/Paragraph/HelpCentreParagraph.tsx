import type { ParagraphElement, StoryElementBaseProps } from 'types/Story';

function HelpCentreParagraph({
  element: { text },
}: StoryElementBaseProps<ParagraphElement>): React.ReactElement {
  return (
    <p
      className="prose text-base leading-6 text-gray-800 prose-a:text-blue-600 prose-a:underline prose-a:decoration-blue-400 prose-a:visited:text-gray-500 prose-a:visited:decoration-gray-500 prose-a:hover:decoration-blue-600"
      dangerouslySetInnerHTML={{ __html: text }}
    />
  );
}

export default HelpCentreParagraph;
