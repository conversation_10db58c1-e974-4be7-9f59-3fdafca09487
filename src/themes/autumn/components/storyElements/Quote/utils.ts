import { charsToPixels, htmlToText } from 'util/device';

import { type HeightsProps } from '.';

export function quoteHeights({
  deviceType,
  item: {
    element: { author, text },
  },
}: HeightsProps): number {
  if (!text) {
    return 0;
  }
  const chars = htmlToText(text).length;
  const authorChars = htmlToText(author).length;
  return (
    charsToPixels(chars, deviceType, 'quote') +
    (authorChars
      ? // plus top padding
        charsToPixels(authorChars, deviceType, 'quoteAuthor') + 16
      : 0)
  );
}

export function quoteHeightsExploreTravel({
  deviceType,
  item: {
    element: { author, text },
  },
}: HeightsProps): number {
  if (!text) {
    return 0;
  }
  const chars = htmlToText(text).length;
  const authorChars = htmlToText(author).length;
  return (
    charsToPixels(chars, deviceType, 'quote') +
    (authorChars
      ? // plus top padding
        charsToPixels(authorChars, deviceType, 'quoteAuthor') + 16
      : 0)
  );
}
