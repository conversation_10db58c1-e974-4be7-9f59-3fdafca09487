import type { QuoteElement, StoryElementBaseProps } from 'types/Story';

function ExploreTravelQuote({
  element: { author, text },
}: StoryElementBaseProps<QuoteElement>): React.ReactElement | null {
  if (!text) {
    return null;
  }
  return (
    <blockquote className="quote-element mx-4 md:w-180 lg:mx-8">
      <p
        className="border-l-4 border-gray-200 pl-4 text-lg/[28px] font-medium italic text-gray-900 before:content-[open-quote] after:content-[close-quote]"
        dangerouslySetInnerHTML={{ __html: text }}
      />
      {author && (
        <cite className="block pl-5 pt-4 font-inter not-italic text-gray-500">
          - {author}
        </cite>
      )}
    </blockquote>
  );
}

export default ExploreTravelQuote;
