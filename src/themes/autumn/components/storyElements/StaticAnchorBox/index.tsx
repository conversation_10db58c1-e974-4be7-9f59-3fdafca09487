import React from 'react';

import { scrollToAnchor } from 'themes/autumn/templates/zoneItems/navigation/common/utils';
import { htmlToText } from 'util/device';

import type { AnchorBoxElement, StoryElementBaseProps } from 'types/Story';

export default function StaticAnchorBox({
  element: { elements },
}: StoryElementBaseProps<AnchorBoxElement>): React.ReactElement {
  const renderAnchorLinks = () =>
    elements.map((element) => {
      const text = htmlToText(element.text);
      return (
        <div className="py-2.5" key={text}>
          <button
            aria-label={text}
            className="line-clamp-2 w-full text-left font-inter text-sm font-semibold text-gray-800 focus:outline-none"
            dangerouslySetInnerHTML={{
              __html: text,
            }}
            onClick={() => scrollToAnchor(text)}
            type="button"
          />
        </div>
      );
    });

  return (
    <div className="xl:relative xl:w-180">
      <div className="hidden bg-white xl:absolute xl:left-[-250px] xl:block xl:w-[200px] xl:overflow-y-auto">
        <div className="pb-4 font-inter text-base font-bold">
          In this article
        </div>
        <div className="grid grid-cols-1 divide-y divide-gray-200 border-l border-gray-800 pl-3">
          {renderAnchorLinks()}
        </div>
      </div>
    </div>
  );
}
