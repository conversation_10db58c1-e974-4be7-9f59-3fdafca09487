import { StoryElementType } from 'types/Story';

import Gallery from './Gallery';
import { galleryHeights } from './Gallery/utils';
import Generic from './Generic';
import ExploreTravelGeneric from './Generic/ExploreTravelGeneric';
import { genericHeights } from './Generic/utils';
import Heading from './Heading';
import ExploreTravelHeading from './Heading/ExploreTravelHeading';
import { headingHeights } from './Heading/utils';
import Image from './Image';
import ExploreTravelImage from './Image/ExploreTravelImage';
import { imageHeights } from './Image/utils';
import List from './List';
import ExploreTravelList from './List/ExploreTravelList';
import HelpCentreList from './List/HelpCentreList';
import { listHeights } from './List/utils';
import Listbox from './Listbox';
import HelpCentreListbox from './Listbox/HelpCentreListbox';
import Paragraph from './Paragraph';
import ExploreTravelParagraph from './Paragraph/ExploreTravelParagraph';
import HelpCentreParagraph from './Paragraph/HelpCentreParagraph';
import {
  paragraphHeights,
  paragraphHeightsExploreTravel,
} from './Paragraph/utils';
import Quote from './Quote';
import ExploreTravelQuote from './Quote/ExploreTravelQuote';
import { quoteHeights, quoteHeightsExploreTravel } from './Quote/utils';
import StaticAnchorBox from './StaticAnchorBox';

import type { EstimateHeightFn } from './common/storyElements';

export type StoryComponentMap = Record<
  string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [React.ComponentType<any>, EstimateHeightFn?]
>;

export const defaultStoryComponentMap: StoryComponentMap = {
  [StoryElementType.Heading]: [Heading, headingHeights as EstimateHeightFn],
  [StoryElementType.Image]: [Image, imageHeights as EstimateHeightFn],
  [StoryElementType.List]: [List, listHeights as EstimateHeightFn],
  [StoryElementType.Listbox]: [Listbox],
  [StoryElementType.Paragraph]: [
    Paragraph,
    paragraphHeights as EstimateHeightFn,
  ],
  [StoryElementType.Quote]: [Quote, quoteHeights as EstimateHeightFn],
  [StoryElementType.Gallery]: [Gallery, galleryHeights as EstimateHeightFn],
  [StoryElementType.Generic]: [Generic, genericHeights as EstimateHeightFn],
};

export const exploreTravelStoryComponentMap: StoryComponentMap = {
  ...defaultStoryComponentMap,
  [StoryElementType.Image]: [
    ExploreTravelImage,
    imageHeights as EstimateHeightFn,
  ],
  [StoryElementType.Heading]: [
    ExploreTravelHeading,
    headingHeights as EstimateHeightFn,
  ],
  [StoryElementType.Paragraph]: [
    ExploreTravelParagraph,
    paragraphHeightsExploreTravel as EstimateHeightFn,
  ],
  [StoryElementType.Quote]: [
    ExploreTravelQuote,
    quoteHeightsExploreTravel as EstimateHeightFn,
  ],
  [StoryElementType.AnchorBox]: [StaticAnchorBox],
  [StoryElementType.Generic]: [
    ExploreTravelGeneric,
    genericHeights as EstimateHeightFn,
  ],
  [StoryElementType.List]: [
    ExploreTravelList,
    genericHeights as EstimateHeightFn,
  ],
};

export const helpCentreStoryComponentMap: StoryComponentMap = {
  ...defaultStoryComponentMap,
  [StoryElementType.Listbox]: [HelpCentreListbox],
  [StoryElementType.List]: [HelpCentreList, listHeights as EstimateHeightFn],
  [StoryElementType.Paragraph]: [
    HelpCentreParagraph,
    paragraphHeights as EstimateHeightFn,
  ],
};
