/* eslint-disable import/prefer-default-export */

import { hasValidURI } from 'util/image';

import { calcImageHeights } from '../Image/utils';

import { type HeightsProps } from '.';

export function galleryHeights({
  deviceType,
  item: {
    element: { description, elements },
  },
}: HeightsProps): number {
  if (!elements.length) {
    return 0;
  }
  const element = elements[0];
  if (!hasValidURI(element)) {
    return 0;
  }

  return calcImageHeights(deviceType, element, description);
}
