import { faCamera } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import React from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import AdImage from 'themes/autumn/templates/Classifieds/AdImage';
import { ImageResizeMode, getImageAspectClass } from 'util/image';

import StoryImage from '../common/StoryImage';

import type { ImageElement } from 'types/Story';

const MAX_THUMBNAILS_TO_SHOW = 4;

interface Props {
  allowGallery: boolean;
  description?: string;
  elements: ImageElement[];
  index: number;
  openGallery: (slideNum: number) => void;
}

function WithThumbsPreviewGallery({
  allowGallery,
  description,
  elements,
  index,
  openGallery,
}: Props) {
  const previewElement = elements[0];
  const imagesSideBar = elements.map((element) => element.uri).slice(1, 5);

  const { cropConfig } = previewElement;
  const aspectClass = getImageAspectClass(cropConfig);

  const scrollToPaywall = (e: React.MouseEvent<HTMLButtonElement>) => {
    const targetElement = document.getElementById('paywall');

    if (!targetElement) {
      return;
    }

    e.preventDefault();
    window.scrollTo({
      behavior: 'smooth',
      top: targetElement.offsetTop - 72,
    });
  };

  return (
    <div className="mb-6">
      <div className="relative">
        <div className="flex w-full flex-col gap-x-2 md:flex-row">
          <button
            className={clsx(
              'relative w-full focus-visible:outline-none',
              aspectClass,
              {
                'cursor-default': !allowGallery,
                'cursor-pointer': allowGallery,
              },
            )}
            onClick={allowGallery ? () => openGallery(0) : scrollToPaywall}
            type="button"
          >
            <div className="absolute inset-0 bg-black opacity-0 transition-opacity duration-300 hover:opacity-20" />
            <StoryImage
              alt={description}
              fitMode={ImageResizeMode.MAX}
              fixedSize="large"
              highPriority={index === 0}
              image={previewElement}
              // eslint-disable-next-line @stylistic/max-len
              imageClassName="max-h-[396px] min-h-[200px] md:h-[396px] object-cover bg-gray-550"
              lazyLoad={index !== 0}
              sourceFixedSize="large"
            />
          </button>
          <div className="mx-2 mt-2 flex flex-row gap-2 md:mx-0 md:mt-0 md:flex-col md:gap-x-0">
            {imagesSideBar.map((image, i) => (
              // eslint-disable-next-line react/no-array-index-key
              <React.Fragment key={`${image}-${i}`}>
                <button
                  className="relative block"
                  onClick={
                    allowGallery
                      ? () =>
                          openGallery(
                            i === imagesSideBar.length - 1 ? 0 : i + 1,
                          )
                      : scrollToPaywall
                  }
                  type="button"
                >
                  <div className="group relative">
                    <div
                      className={clsx(
                        'absolute inset-0 bg-black transition-opacity duration-300',
                        index === imagesSideBar.length - 1 &&
                          elements.length > MAX_THUMBNAILS_TO_SHOW
                          ? 'opacity-20 group-hover:opacity-0'
                          : 'opacity-0 group-hover:opacity-20',
                      )}
                    />
                    <AdImage
                      alt={previewElement.title}
                      className="mx-auto h-[92px] w-120 rounded-md bg-gray-650 object-cover object-right"
                      height={258}
                      url={image}
                      width={330}
                    />
                    {i === imagesSideBar.length - 1 &&
                      elements.length > MAX_THUMBNAILS_TO_SHOW && (
                        <span className="absolute inset-0 flex items-center justify-center font-inter text-2xl font-medium text-white">
                          +{elements.length - MAX_THUMBNAILS_TO_SHOW}
                        </span>
                      )}
                  </div>
                </button>
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>

      {description && (
        <div className="px-4 pt-3 text-left font-inter text-sm font-normal leading-5 text-gray-500 md:px-0">
          <FontAwesomeIcon className="mr-2" icon={faCamera} />
          {description}
        </div>
      )}
    </div>
  );
}

export default WithThumbsPreviewGallery;
