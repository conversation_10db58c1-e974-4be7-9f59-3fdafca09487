// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`HelpCentreList story element matches snapshot for ordered list 1`] = `
<ol
  class="list-outside list-decimal mb-6 pl-2 text-base text-gray-800 marker:text-gray-400"
>
  <li
    class="mb-4 pl-1"
  >
    <span
      class="text-base text-gray-800"
    >
      First step
    </span>
  </li>
  <li
    class="mb-4 pl-1"
  >
    <span
      class="text-base text-gray-800"
    >
      Second step
    </span>
  </li>
  <li
    class="mb-4 pl-1"
  >
    <span
      class="text-base text-gray-800"
    >
      Third step
    </span>
  </li>
</ol>
`;

exports[`HelpCentreList story element matches snapshot for unordered list 1`] = `
<ul
  class="list-outside list-disc mb-6 pl-2 text-base text-gray-800 marker:text-gray-400"
>
  <li
    class="mb-4 pl-1"
  >
    <span
      class="text-base text-gray-800"
    >
      First item
    </span>
  </li>
  <li
    class="mb-4 pl-1"
  >
    <span
      class="text-base text-gray-800"
    >
      Second item
    </span>
  </li>
  <li
    class="mb-4 pl-1"
  >
    <span
      class="text-base text-gray-800"
    >
      Third item
    </span>
  </li>
</ul>
`;
