import clsx from 'clsx';

import { ListElementType, StoryElementType } from 'types/Story';

import Generic from '../Generic';

import type { ReactNode } from 'react';
import type {
  ListElement,
  StoryElement,
  StoryElementBaseProps,
} from 'types/Story';

function getElementContent(item: StoryElement, idx: number) {
  switch (item.type) {
    case StoryElementType.Heading:
      return item.text;
    case StoryElementType.Generic:
      return <Generic element={item} index={idx} />;
    case StoryElementType.Paragraph:
      return item.text;
    case StoryElementType.Quote:
      return (
        <blockquote className="text-base text-gray-800">
          <span className="italic before:content-[open-quote] after:content-[close-quote]">
            {item.text}
          </span>
          {item.author && (
            <cite className="pl-3 not-italic">- {item.author}</cite>
          )}
        </blockquote>
      );
    default:
      return null;
  }
}

function HelpCentreList({
  element: { items, listType },
}: StoryElementBaseProps<ListElement>): React.ReactElement | null {
  if (!items || !items.length) {
    return null;
  }
  const ordered = listType === ListElementType.Ordered;
  const ListTag = ordered ? 'ol' : 'ul';
  const listStyle = ordered ? 'list-decimal' : 'list-disc';
  return (
    <ListTag
      className={clsx(
        'list-outside',
        listStyle,
        'mb-6 pl-2 text-base text-gray-800 marker:text-gray-400',
      )}
    >
      {items.map((item, idx) => {
        const content: ReactNode =
          typeof item === 'string' ? item : getElementContent(item, idx);
        const hasLink = typeof content === 'string' && /<a\b/i.test(content);
        if (!content) {
          return null;
        }

        return (
          // eslint-disable-next-line react/no-array-index-key
          <li className="mb-4 pl-1" key={idx}>
            {typeof content === 'string' ? (
              <span
                className={clsx('text-base text-gray-800', {
                  prose: hasLink,
                })}
                dangerouslySetInnerHTML={{ __html: content }}
              />
            ) : (
              content
            )}
          </li>
        );
      })}
    </ListTag>
  );
}

export default HelpCentreList;
