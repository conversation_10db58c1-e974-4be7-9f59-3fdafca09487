/* eslint-disable import/prefer-default-export */

import { charsToPixels, htmlToText } from 'util/device';

import { type HeightsProps } from '.';

export function listHeights({
  deviceType,
  item: {
    element: { items },
  },
}: HeightsProps): number {
  const chars = items.reduce(
    (obj: number, item) =>
      obj + (typeof item === 'string' ? htmlToText(item).length : 0),
    0,
  );
  const listItemMargins = items.length * 24;
  return charsToPixels(chars, deviceType, 'prose') + listItemMargins;
}
