import { render, screen } from '@testing-library/react';

import { ListElementType, StoryElementType } from 'types/Story';

import HelpCentreList from './HelpCentreList';

import type { ListElement } from 'types/Story';

describe('HelpCentreList story element', () => {
  it('matches snapshot for unordered list', () => {
    const element: ListElement = {
      items: ['First item', 'Second item', 'Third item'],
      listType: ListElementType.Unordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('matches snapshot for ordered list', () => {
    const element: ListElement = {
      items: ['First step', 'Second step', 'Third step'],
      listType: ListElementType.Ordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders unordered list items correctly', () => {
    const element: ListElement = {
      items: ['Item one', 'Item two', 'Item three'],
      listType: ListElementType.Unordered,
      type: StoryElementType.List,
    };

    render(<HelpCentreList element={element} index={0} />);

    expect(screen.getByText('Item one')).toBeInTheDocument();
    expect(screen.getByText('Item two')).toBeInTheDocument();
    expect(screen.getByText('Item three')).toBeInTheDocument();
  });

  it('renders ordered list items correctly', () => {
    const element: ListElement = {
      items: ['First step', 'Second step', 'Third step'],
      listType: ListElementType.Ordered,
      type: StoryElementType.List,
    };

    render(<HelpCentreList element={element} index={0} />);

    expect(screen.getByText('First step')).toBeInTheDocument();
    expect(screen.getByText('Second step')).toBeInTheDocument();
    expect(screen.getByText('Third step')).toBeInTheDocument();
  });

  it('renders HTML content in list items', () => {
    const element: ListElement = {
      items: [
        'Item with <strong>bold text</strong>',
        'Item with <a href="https://example.com">link</a>',
      ],
      listType: ListElementType.Unordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );

    expect(container.querySelector('strong')).toBeInTheDocument();
    expect(container.querySelector('a')).toBeInTheDocument();
    expect(container.querySelector('a')).toHaveAttribute(
      'href',
      'https://example.com',
    );
  });

  it('applies correct styling classes for unordered list', () => {
    const element: ListElement = {
      items: ['Test item'],
      listType: ListElementType.Unordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );

    const list = container.querySelector('ul');
    expect(list).toHaveClass(
      'list-outside',
      'list-disc',
      'mb-6',
      'pl-2',
      'text-base',
      'text-gray-800',
      'marker:text-gray-400',
    );
  });

  it('applies correct styling classes for ordered list', () => {
    const element: ListElement = {
      items: ['Test item'],
      listType: ListElementType.Ordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );

    const list = container.querySelector('ol');
    expect(list).toHaveClass(
      'list-outside',
      'list-decimal',
      'mb-6',
      'pl-2',
      'text-base',
      'text-gray-800',
      'marker:text-gray-400',
    );
  });

  it('applies correct styling classes to list items', () => {
    const element: ListElement = {
      items: ['Test item'],
      listType: ListElementType.Unordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );

    const listItem = container.querySelector('li');
    expect(listItem).toHaveClass('mb-4', 'pl-1');

    const span = container.querySelector('span');
    expect(span).toHaveClass('text-base', 'text-gray-800');
  });

  it('applies prose to items with links', () => {
    const element: ListElement = {
      items: [
        'Item with <a href="https://example.com">opening tag link</a>',
        'Item with <A HREF="#top">uppercase link</A>',
        'Item with <a class="test" href="#">link with attributes</a>',
        'Item without links',
      ],
      listType: ListElementType.Unordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );

    const spans = container.querySelectorAll('span');

    expect(spans[0]).toHaveClass('prose');
    expect(spans[1]).toHaveClass('prose');
    expect(spans[2]).toHaveClass('prose');
    expect(spans[3]).not.toHaveClass('prose');
  });

  it('applies prose to paragraph elements with links in their text', () => {
    const element: ListElement = {
      items: [
        {
          text: 'This paragraph has a <a href="https://example.com">link</a> in it',
          type: StoryElementType.Paragraph,
        },
        {
          text: 'This paragraph has no links',
          type: StoryElementType.Paragraph,
        },
        {
          level: '3',
          text: 'Heading with <a href="#">link</a>',
          type: StoryElementType.Heading,
        },
      ],
      listType: ListElementType.Unordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );

    const spans = container.querySelectorAll('span');

    expect(spans[0]).toHaveClass('prose');
    expect(spans[1]).not.toHaveClass('prose');
    expect(spans[2]).toHaveClass('prose');
  });

  it('does not apply prose class to non-string content elements', () => {
    const element: ListElement = {
      items: [
        {
          author: 'Author Name',
          text: 'This is a quote without links',
          type: StoryElementType.Quote,
        },
      ],
      listType: ListElementType.Unordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );

    const blockquote = container.querySelector('blockquote');
    expect(blockquote).toBeInTheDocument();

    const proseSpan = container.querySelector('span.prose');
    expect(proseSpan).not.toBeInTheDocument();
  });

  it('returns null when items array is empty', () => {
    const element: ListElement = {
      items: [],
      listType: ListElementType.Unordered,
      type: StoryElementType.List,
    };

    const { container } = render(
      <HelpCentreList element={element} index={0} />,
    );

    expect(container.firstChild).toBeNull();
  });
});
