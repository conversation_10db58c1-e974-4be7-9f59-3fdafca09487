import List from '.';

import type { ListElement, StoryElementBaseProps } from 'types/Story';

function ExploreTravelList({
  element,
  index,
}: StoryElementBaseProps<ListElement>): React.ReactElement | null {
  return (
    <div className="font-normal prose-a:text-gray-800 prose-a:decoration-gray-500 prose-a:hover:decoration-gray-800">
      <List element={element} index={index} />
    </div>
  );
}

export default ExploreTravelList;
