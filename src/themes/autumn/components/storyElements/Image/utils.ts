import { StoryChannel } from 'types/Story';
import {
  DeviceType,
  IMG_WIDTHS,
  charsToPixels,
  htmlToText,
} from 'util/device';
import { ImageResizeMode, getTransformParams, hasValidURI } from 'util/image';

import { type HeightsProps } from '.';

import type { ImageElement } from 'types/Story';

export function calcImageHeights(
  deviceType: DeviceType,
  element: ImageElement,
  description?: string,
): number {
  const { height, width } = element;

  if (!hasValidURI(element)) {
    return 0;
  }

  const { h: heightRest, w: widthRest } = getTransformParams({
    fit: ImageResizeMode.MAX,
    height,
    image: element,
    size: 'large',
    useFocalPoint: false,
    width,
  });
  const { h: heightMobile, w: widthMobile } = getTransformParams({
    fit: ImageResizeMode.MAX,
    height,
    image: element,
    size: 'medium',
    useFocalPoint: false,
    width,
  });

  // Add 2 for chars worth for icon.
  const chars = description ? htmlToText(description).length + 2 : 0;

  const scale =
    IMG_WIDTHS[deviceType] /
    (deviceType === DeviceType.MOBILE ? widthMobile : widthRest);
  const newHeight = Math.round(
    (deviceType === DeviceType.MOBILE ? heightMobile : heightRest) * scale,
  );

  // Add 12px top padding
  const captionHeight = chars
    ? charsToPixels(chars, deviceType, 'caption') + 12
    : 0;
  return newHeight + captionHeight;
}

export function imageHeights({
  deviceType,
  item: { element },
}: HeightsProps): number {
  const { visible } = element;
  if (visible && !visible.includes(StoryChannel.Web)) {
    return 0;
  }
  const store = window.getStore();
  const { story } = store.getState();
  const description = element.description || story.title;

  return calcImageHeights(deviceType, element, description);
}
