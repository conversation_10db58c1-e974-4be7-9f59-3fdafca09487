import clsx from 'clsx';

import { useAppSelector } from 'store/hooks';
import { StoryChannel } from 'types/Story';
import { ImageResizeMode, getImageAspectClass, hasValidURI } from 'util/image';

import StoryImage from '../common/StoryImage';
import StoryImageLink from '../common/StoryImageLink';

import type { ImageElement, StoryElementBaseProps } from 'types/Story';
import type { DeviceType } from 'util/device';

interface Props extends StoryElementBaseProps {
  className?: string;
  element: ImageElement;
}

export interface HeightsProps {
  deviceType: DeviceType;
  item: StoryElementBaseProps<ImageElement>;
}

function ExploreTravelImage({
  className,
  element,
  index,
}: Props): React.ReactElement | null {
  const story = useAppSelector((state) => state.story);
  const { cropConfig, visible } = element;

  if (
    (visible && !visible.includes(StoryChannel.Web)) ||
    !hasValidURI(element)
  ) {
    return null;
  }

  const description = element.description || story.title;
  const aspectClass = getImageAspectClass(cropConfig);

  return (
    <div className="mx-auto mb-6 md:my-6">
      <div className={aspectClass}>
        {element.link && (
          <StoryImageLink
            alt={description}
            fitMode={ImageResizeMode.MAX}
            fixedSize="large"
            image={element}
            imageClassName="md:min-w-[980px]"
            link={element.link}
            resize
            sourceFixedSize="exploreTravel"
          />
        )}
        {!element.link && (
          <StoryImage
            alt={description}
            fitMode={ImageResizeMode.MAX}
            fixedSize="large"
            highPriority={index === 0}
            image={element}
            imageClassName="md:min-w-[980px]"
            lazyLoad={index !== 0}
            resize
            sourceFixedSize="exploreTravel"
          />
        )}
      </div>
      {description && (
        <div
          className={clsx(
            'px-4 pt-3 font-inter text-sm font-normal leading-5 text-gray-500 md:px-0',
            className,
          )}
        >
          {description}
        </div>
      )}
    </div>
  );
}

export default ExploreTravelImage;
