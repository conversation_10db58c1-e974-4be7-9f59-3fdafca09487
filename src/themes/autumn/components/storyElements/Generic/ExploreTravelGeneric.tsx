import { useAppSelector } from 'store/hooks';
import { Service, ServiceType } from 'types/Story';

import BarTvSports from '../services/BarTvSports';
import { getBarTvVideoUrl } from '../services/BarTvSports/utils';
import ExploreTravelDailymotion from '../services/Dailymotion/ExploreTravelDailymotion';
import { dailyMotionHeights } from '../services/Dailymotion/utils';
import Facebook from '../services/Facebook';
import { getFacebookVideoUrl } from '../services/Facebook/utils';
import FeaturedComments from '../services/FeaturedComments';
import Iframe from '../services/Iframe';
import OpenUniversitiesAustralia from '../services/OpenUniversitiesAustralia';
import ExploreTravelThreads from '../services/Threads/ExploreTravelThreads';
import { threadsHeights } from '../services/Threads/utils';
import ExploreTravelYoutube from '../services/YouTube/ExploreTravelYoutube';
import { exploreTravelYouTubeHeights } from '../services/YouTube/utils';

import { defaultServiceComponentsMap } from './utils';

import type { EstimateHeightFn } from '../common/storyElements';
import type { ServiceProps } from '../services/types';
import type { GenericElement, StoryElementBaseProps } from 'types/Story';

const serviceComponentsMap: Record<
  string,
  [React.ComponentType<ServiceProps>, EstimateHeightFn?]
> = {
  ...defaultServiceComponentsMap,
  [Service.Dailymotion]: [
    ExploreTravelDailymotion,
    dailyMotionHeights as EstimateHeightFn,
  ],
  [Service.YouTube]: [
    ExploreTravelYoutube,
    exploreTravelYouTubeHeights as EstimateHeightFn,
  ],
  [Service.Threads]: [
    ExploreTravelThreads,
    threadsHeights as EstimateHeightFn,
  ],
};

interface Props extends StoryElementBaseProps {
  element: GenericElement;
}

function ExploreTravelGeneric({ element }: Props): React.ReactElement | null {
  const story = useAppSelector((state) => state.story);
  const { service, serviceId } = element;
  if (serviceId) {
    const ServiceComponent: React.ComponentType<ServiceProps> =
      serviceComponentsMap[service]?.[0];
    if (ServiceComponent) {
      return <ServiceComponent element={element} widthClassName="md:w-180" />;
    }
    return null;
  }
  if (service === Service.Iframe) {
    if (
      story.tags.find((it) => it.includes('open-universities-australia')) &&
      element.embed.includes('<img data-link=')
    ) {
      return (
        <OpenUniversitiesAustralia
          element={element}
          widthClassName="md:w-180"
        />
      );
    }
    if (getFacebookVideoUrl(element)) {
      return <Facebook element={element} widthClassName="md:w-180" />;
    }
    if (getBarTvVideoUrl(element)) {
      return <BarTvSports element={element} widthClassName="md:w-180" />;
    }
  }
  if (element.serviceType === ServiceType.FeaturedComments) {
    return <FeaturedComments element={element} widthClassName="md:w-180" />;
  }
  return <Iframe element={element} widthClassName="md:w-180" />;
}

export default ExploreTravelGeneric;
