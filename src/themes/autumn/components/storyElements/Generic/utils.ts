import { Service, ServiceType } from 'types/Story';

import {
  barTvSportsHeights,
  getBarTvVideoUrl,
} from '../services/BarTvSports/utils';
import BrightCove from '../services/BrightCove';
import { brightCoveHeights } from '../services/BrightCove/utils';
import CoverItLive from '../services/CoverItLive';
import Dailymotion from '../services/Dailymotion';
import { dailyMotionHeights } from '../services/Dailymotion/utils';
import Facebook from '../services/Facebook';
import {
  facebookHeights,
  getFacebookVideoUrl,
} from '../services/Facebook/utils';
import GoogleCharts from '../services/GoogleCharts';
import { iframeHeights } from '../services/Iframe/utils';
import Instagram from '../services/Instagram';
import { instagramHeights } from '../services/Instagram/utils';
import Norkon from '../services/NorkonLiveCenter';
import Ooyala from '../services/Ooyala';
import PollDaddy from '../services/PollDaddy';
import { pollDaddyHeights } from '../services/PollDaddy/utils';
import RebelMouse from '../services/RebelMouse';
import Storify from '../services/Storify';
import SurveyMonkey from '../services/SurveyMonkey';
import Threads from '../services/Threads';
import { threadsHeights } from '../services/Threads/utils';
import Tiktok from '../services/Tiktok';
import { tiktokHeights } from '../services/Tiktok/utils';
import TwentyFourLiveBlog from '../services/TwentyFourLiveBlog';
import { twentyFourLiveBlogHeights } from '../services/TwentyFourLiveBlog/utils';
import TwitterTimeline from '../services/TwitterTimeline';
import { twitterTimelineHeights } from '../services/TwitterTimeline/utils';
import TwitterTweet from '../services/TwitterTweet';
import { twitterTweetHeights } from '../services/TwitterTweet/utils';
import ViafouraLiveBlog from '../services/ViafouraLiveBlog';
import { viafouraLiveBlogHeights } from '../services/ViafouraLiveBlog/utils';
import YouTube from '../services/YouTube';
import { youTubeHeights } from '../services/YouTube/utils';

import type {
  EstimateHeightFn,
  GenericHeightsProps,
} from '../common/storyElements';
import type { ServiceProps } from '../services/types';

export const defaultServiceComponentsMap: Record<
  string,
  [React.ComponentType<ServiceProps>, EstimateHeightFn?]
> = {
  [Service.Brightcove]: [BrightCove, brightCoveHeights as EstimateHeightFn],
  [Service.CoverItLive]: [CoverItLive],
  [Service.Dailymotion]: [Dailymotion, dailyMotionHeights as EstimateHeightFn],
  [Service.Facebook]: [Facebook, facebookHeights as EstimateHeightFn],
  [Service.GoogleCharts]: [GoogleCharts],
  [Service.Instagram]: [Instagram, instagramHeights as EstimateHeightFn],
  [Service.Ooyala]: [Ooyala],
  [Service.Norkon]: [Norkon],
  [Service.PollDaddy]: [PollDaddy, pollDaddyHeights as EstimateHeightFn],
  [Service.RebelMouse]: [RebelMouse],
  [Service.Storify]: [Storify],
  [Service.SurveyMonkey]: [SurveyMonkey],
  [Service.Threads]: [Threads, threadsHeights as EstimateHeightFn],
  [Service.Tiktok]: [Tiktok, tiktokHeights as EstimateHeightFn],
  [Service.TwentyFourLiveBlog]: [
    TwentyFourLiveBlog,
    twentyFourLiveBlogHeights as EstimateHeightFn,
  ],
  [Service.TwitterTweet]: [
    TwitterTweet,
    twitterTweetHeights as EstimateHeightFn,
  ],
  [Service.TwitterTimeline]: [
    TwitterTimeline,
    twitterTimelineHeights as EstimateHeightFn,
  ],
  [Service.ViafouraLiveBlog]: [
    ViafouraLiveBlog,
    viafouraLiveBlogHeights as EstimateHeightFn,
  ],
  [Service.YouTube]: [YouTube, youTubeHeights as EstimateHeightFn],
};

export function genericHeights({
  deviceType,
  item: { element, index },
}: GenericHeightsProps): number {
  const { service, serviceId } = element;
  const store = window.getStore();
  const { story } = store.getState();

  if (serviceId) {
    const estimateHeightFn = defaultServiceComponentsMap[service]?.[1];
    if (estimateHeightFn) {
      return estimateHeightFn({ deviceType, item: { element, index } });
    }
    return 0;
  }
  if (service === Service.Iframe) {
    if (
      story.tags.find((it) => it.includes('open-universities-australia')) &&
      element.embed.includes('<img data-link=')
    ) {
      // skipping: old and no usage.
      return 0;
    }
    if (getFacebookVideoUrl(element)) {
      return facebookHeights({ deviceType, item: { element, index } });
    }
    if (getBarTvVideoUrl(element)) {
      return barTvSportsHeights({
        deviceType,
        item: { element, index },
      });
    }
  }
  if (element.serviceType === ServiceType.FeaturedComments) {
    // skipping: This was only intended for Echidna (not supporting ads)
    return 0;
  }
  return iframeHeights({ deviceType, item: { element, index } });
}
