import { render, screen } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { ListboxSubType } from 'types/Story';
import { TestWrapper } from 'util/jest';

import Listbox from './index';

import type { ListboxWebboxElement } from 'types/Story';

describe('Listbox story element', () => {
  const store = createStore((state) => ({
    ...state,
    accessToken: 'test_token',
    conf: {
      ...state.conf,
      mode: RenderMode.NORMAL,
    },
  }));

  describe('ListboxHTMLNotes', () => {
    it('matches snapshot', () => {
      const element = {
        content: '<ul><li>Test content</li></ul>',
        headline: 'Test Headline',
        subType: ListboxSubType.Webbox,
      } as ListboxWebboxElement;

      const { container } = render(
        <TestWrapper store={store}>
          <Listbox element={element} index={0} />
        </TestWrapper>,
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders headline and content correctly', () => {
      const element = {
        content: '<ul><li>Test content</li></ul>',
        headline: 'Test Headline',
        subType: ListboxSubType.Webbox,
      } as ListboxWebboxElement;

      render(
        <TestWrapper store={store}>
          <Listbox element={element} index={0} />
        </TestWrapper>,
      );

      expect(screen.getByText('Test Headline')).toBeInTheDocument();
      expect(screen.getByText('Test content')).toBeInTheDocument();
    });

    it('applies correct styling classes', () => {
      const element = {
        content: '<p>Test content</p>',
        headline: 'Test Headline',
        subType: ListboxSubType.Webbox,
      } as ListboxWebboxElement;

      const { container } = render(
        <TestWrapper store={store}>
          <Listbox element={element} index={0} />
        </TestWrapper>,
      );

      const mainDiv = container.querySelector(
        '[data-testid="listbox-webbox"]',
      );
      expect(mainDiv).toHaveClass('rounded-sm', 'border', 'border-gray-200');

      const headline = screen.getByText('Test Headline');
      expect(headline).toHaveClass(
        'font-inter',
        'text-2xl',
        'font-semibold',
        'leading-9',
        'text-gray-800',
      );

      const content = mainDiv!.children[1];
      expect(content).toHaveClass(
        'prose',
        'text-base',
        'leading-6',
        'text-gray-800',
        'prose-ul:mt-2',
        'prose-li:mt-2',
        'prose-li:marker:text-gray-800',
        'prose-strong:text-gray-800',
        'font-normal',
        'prose-strong:font-bold',
        'prose-a:text-gray-800',
        'prose-a:underline',
        'prose-a:decoration-gray-800',
        'prose-a:visited:text-gray-600',
        'prose-a:hover:decoration-gray-800',
        'prose-a:font-normal',
        'font-inter',
      );
    });
  });

  it('returns null for unsupported subType', () => {
    const element = {
      content: 'Content',
      headline: 'Test',
      subType: 'unknown',
      type: 'Listbox',
    } as unknown as ListboxWebboxElement;

    const { container } = render(
      <TestWrapper store={store}>
        <Listbox element={element} index={0} />
      </TestWrapper>,
    );
    expect(
      container.querySelector('[data-testid="listbox-webbox"]'),
    ).toBeNull();
  });
});
