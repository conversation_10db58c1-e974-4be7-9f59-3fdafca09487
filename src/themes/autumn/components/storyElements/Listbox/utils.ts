export interface AnchorInfo {
  buttonText: string;
  description: string;
  url: string;
}

export default function extractAnchorInfo(
  htmlContent: string,
): AnchorInfo | null {
  if (typeof document === 'undefined') {
    return null;
  }

  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;
  const anchorTag = tempDiv.querySelector('a');
  if (!anchorTag) {
    tempDiv.remove();
    return null;
  }

  const url = anchorTag.getAttribute('href') || '';
  const buttonText = anchorTag.textContent || '';
  anchorTag.remove();
  const description = tempDiv.innerHTML || '';

  tempDiv.remove();

  return {
    buttonText: buttonText.trim(),
    description: description.trim(),
    url,
  };
}
