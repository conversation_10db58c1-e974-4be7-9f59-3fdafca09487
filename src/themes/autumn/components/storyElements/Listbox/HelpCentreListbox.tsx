import clsx from 'clsx';
import { useEffect, useState } from 'react';

import InfoWithCTA from 'themes/autumn/templates/zoneItems/infowithcta/Default';
import { ListboxSubType, StoryElementType } from 'types/Story';
import { ZoneItemType, ZoneName } from 'types/ZoneItems';
import { useFetchStyleByTheme } from 'util/hooks';

import extractAnchorInfo from './utils';

import type { AnchorInfo } from './utils';
import type {
  ListboxElement,
  ListboxWebboxElement,
  StoryElementBaseProps,
} from 'types/Story';

function HelpCentreListboxWebbox({
  element: { content, headline },
  index,
}: StoryElementBaseProps<ListboxWebboxElement>): React.ReactElement | null {
  const fontStyle = useFetchStyleByTheme(
    StoryElementType.Listbox,
    'fontStyle',
  );
  const headingFontStyle = useFetchStyleByTheme(
    StoryElementType.Listbox,
    'headingFontStyle',
  );

  const [anchorInfo, setAnchorInfo] = useState<AnchorInfo | null>(null);

  useEffect(() => {
    const info = extractAnchorInfo(content);
    setAnchorInfo(info);
  }, [content]);

  if (anchorInfo) {
    return (
      <InfoWithCTA
        className="!bg-stone-100"
        elementId={index}
        index={index}
        order={index}
        zoneItemData={{
          buttonText: anchorInfo.buttonText,
          description: anchorInfo.description,
          template: 'Default',
          title: headline,
          url: anchorInfo.url,
        }}
        zoneItemId={0}
        zoneItemType={ZoneItemType.InfoWithCTA}
        zoneName={ZoneName.MAIN}
      />
    );
  }

  return (
    <div
      className="rounded-sm border border-gray-200 px-7 py-5 md:w-180 md:max-w-full"
      data-testid="listbox-webbox"
    >
      <h3
        className={clsx(
          'mb-6 text-2xl font-semibold leading-9 text-gray-800',
          headingFontStyle,
        )}
      >
        {headline}
      </h3>
      <div
        className={clsx(
          'prose',
          'text-base',
          'leading-6',
          'text-gray-800',
          'prose-ul:mt-2',
          'prose-li:mt-2',
          'prose-li:marker:text-gray-800',
          'prose-strong:text-gray-800',
          'font-normal',
          'prose-strong:font-bold',
          'prose-a:text-gray-800',
          'prose-a:underline',
          'prose-a:decoration-gray-800',
          'prose-a:visited:text-gray-600',
          'prose-a:hover:decoration-gray-800',
          'prose-a:font-normal',
          fontStyle,
        )}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </div>
  );
}

export default function HelpCentreListbox({
  element,
  index,
}: StoryElementBaseProps<ListboxElement>): React.ReactElement | null {
  switch (element.subType) {
    case ListboxSubType.Webbox:
      return <HelpCentreListboxWebbox element={element} index={index} />;
    default:
      return null;
  }
}
