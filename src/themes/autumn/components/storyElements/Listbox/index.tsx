import clsx from 'clsx';

import { ListboxSubType, StoryElementType } from 'types/Story';
import { useFetchStyleByTheme } from 'util/hooks';

import type {
  ListboxElement,
  ListboxWebboxElement,
  StoryElementBaseProps,
} from 'types/Story';

function ListboxWebbox({
  element: { content, headline },
}: StoryElementBaseProps<ListboxWebboxElement>): React.ReactElement | null {
  const fontStyle = useFetchStyleByTheme(
    StoryElementType.Listbox,
    'fontStyle',
  );
  const headingFontStyle = useFetchStyleByTheme(
    StoryElementType.Listbox,
    'headingFontStyle',
  );
  return (
    <div
      className="rounded-sm border border-gray-200 px-7 py-5 md:w-180 md:max-w-full"
      data-testid="listbox-webbox"
    >
      <h3
        className={clsx(
          'mb-6 text-2xl font-semibold leading-9 text-gray-800',
          headingFontStyle,
        )}
      >
        {headline}
      </h3>
      <div
        className={clsx(
          'prose',
          'text-base',
          'leading-6',
          'text-gray-800',
          'prose-ul:mt-2',
          'prose-li:mt-2',
          'prose-li:marker:text-gray-800',
          'prose-strong:text-gray-800',
          'font-normal',
          'prose-strong:font-bold',
          'prose-a:text-gray-800',
          'prose-a:underline',
          'prose-a:decoration-gray-800',
          'prose-a:visited:text-gray-600',
          'prose-a:hover:decoration-gray-800',
          'prose-a:font-normal',
          fontStyle,
        )}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </div>
  );
}

export default function Listbox({
  element,
  index,
}: StoryElementBaseProps<ListboxElement>): React.ReactElement | null {
  switch (element.subType) {
    case ListboxSubType.Webbox:
      return <ListboxWebbox element={element} index={index} />;
    default:
      return null;
  }
}
