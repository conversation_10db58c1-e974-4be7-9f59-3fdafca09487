'use client';

import { Transition, TransitionChild } from '@headlessui/react';
import clsx from 'clsx';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { onPianoReady } from 'components/Piano/ready';
import { useAppSelector } from 'store/hooks';
import {
  AuthField,
  authErrorToast,
  authSuccessToast,
  createPassword,
  handleErrors,
  redirectToLogin,
} from 'util/auth';
import { useHashObject, useOnce } from 'util/hooks';

import Button from '../../generic/Button';

import type { FormEventHandler } from 'react';
import type { PhoenixApiError } from 'types/phoenix-types/responses';

interface PasswordInputProps {
  className?: string;
  clearErrors: () => void;
  errors: PhoenixApiError[];
  inputName: string;
  name: string;
  password: string;
  setPassword: (password: string) => void;
  setShowPassword: React.Dispatch<React.SetStateAction<boolean>>;
  showPassword: boolean;
}

// eslint-disable-next-line react/display-name
const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  (
    {
      className,
      clearErrors,
      errors,
      inputName,
      name,
      password,
      setPassword,
      setShowPassword,
      showPassword,
    },
    ref,
  ) => (
    <div className={className}>
      <div className="mt-4.5 text-sm font-semibold">{name}</div>
      <div className="relative mt-2">
        <input
          autoComplete="new-password"
          className={clsx(
            'w-full rounded border py-3 pl-5 pr-16 text-sm leading-4',
            errors.length > 0 ? 'border-red-500' : 'border-gray-900',
          )}
          name={inputName}
          onChange={(e) => setPassword(e.target.value)}
          onFocus={clearErrors}
          ref={ref}
          type={showPassword ? 'text' : 'password'}
          value={password}
        />
        {errors.length === 0 && (
          <button
            className="absolute right-4 top-1/2 -translate-y-1/2 text-sm font-medium"
            onClick={() => setShowPassword((curr) => !curr)}
            type="button"
          >
            {showPassword ? 'Hide' : 'Show'}
          </button>
        )}
        {errors.length > 0 && (
          <div className="absolute right-4 top-1/2 -translate-y-1/2">
            <svg fill="none" height="24" width="24">
              <path
                clipRule="evenodd"
                d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2ZM13 13V7h-2v6h2Zm0 4v-2h-2v2h2Zm-9-5c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8-8 3.58-8 8Z"
                fill="#DC2626"
                fillRule="evenodd"
              />
            </svg>
          </div>
        )}
      </div>
      {errors.length > 0 && (
        <div className="mt-2 flex flex-col gap-y-2 text-sm font-medium text-red-600">
          {errors.map((e) => (
            <div key={e.message}>
              {e.message.slice(0, 1).toUpperCase()}
              {e.message.slice(1)}
            </div>
          ))}
        </div>
      )}
    </div>
  ),
);

export default function CreatePasswordModal() {
  const name = useAppSelector((state) => state.conf.name);
  const [errors, setErrors] = useState<PhoenixApiError[]>([]);
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [confirmation, setConfirmation] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const hash = useHashObject();
  const passwordRef = useRef<HTMLInputElement>(null);
  const user = useAppSelector((state) => state.piano.user);
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);

  const passwordErrors = useMemo(
    () => errors.filter((e) => e.field === AuthField.PASSWORD),
    [errors],
  );

  const confirmationErrors = useMemo(
    () => errors.filter((e) => e.field === AuthField.CONFIRM_PASSWORD),
    [errors],
  );

  const close = useCallback(() => {
    setOpen(false);
  }, []);

  const clearPasswordErrors = useCallback(() => {
    setErrors((currErrors) =>
      currErrors.filter((e) => e.field !== AuthField.PASSWORD),
    );
  }, [setErrors]);

  const clearConfirmationErrors = useCallback(() => {
    setErrors((currErrors) =>
      currErrors.filter((e) => e.field !== AuthField.CONFIRM_PASSWORD),
    );
  }, [setErrors]);

  const submit = useCallback<FormEventHandler<HTMLFormElement>>(
    (e) => {
      e.preventDefault();

      if (password === '') {
        setErrors([
          {
            field: AuthField.PASSWORD,
            message: 'Please enter a password.',
          },
        ]);
        return;
      }

      if (password !== confirmation) {
        setErrors([
          {
            field: AuthField.CONFIRM_PASSWORD,
            message: "Passwords don't match, check again.",
          },
        ]);
        return;
      }

      setIsLoading(true);

      onPianoReady(async (tp) => {
        const accessToken = tp.pianoId.getToken();
        if (!accessToken) {
          authErrorToast(
            'An unknown error occured. Please try reloading the page.',
          );
          setIsLoading(false);
          return;
        }

        try {
          const createPasswordRes = await createPassword({
            accessToken,
            password,
          });

          if (createPasswordRes.success) {
            const url = new URL(window.location.href);
            url.hash = '';
            url.searchParams.set('msg', 'password');
            window.location.href = url.toString();
            return;
          }

          handleErrors({
            errors: createPasswordRes.errors,
            setErrors,
          });
        } catch {
          authErrorToast('Unable to connect to authentication server');
        }
        setIsLoading(false);
      });
    },
    [password, confirmation],
  );

  useOnce(() => {
    if (!pianoInitialized) {
      return false;
    }

    const canSetPassword =
      user?.passwordType === 'passwordless' ||
      user?.passwordType === 'passwordExpired';

    if ('createpassword' in hash) {
      if (!user) {
        redirectToLogin();
      } else if (canSetPassword) {
        setOpen(true);
      } else {
        authSuccessToast("You've already got a password, you're all set");
        const url = new URL(window.location.href);
        url.hash = '';
        window.history.pushState('', document.title, url.toString());
      }
      return true;
    }
    return false;
  }, [hash, user, pianoInitialized]);

  useEffect(() => {
    passwordRef.current?.focus();
  }, []);

  return (
    <Transition
      appear
      as="div"
      className="fixed inset-0 z-above-piano"
      show={open}
      unmount
    >
      <TransitionChild
        as="div"
        enter="transition-opacity ease-linear duration-150"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        onClick={close}
      >
        <div className="absolute inset-0 bg-black/50" />
      </TransitionChild>
      <div className="absolute inset-x-0 top-24 flex items-center justify-center">
        <div className="relative w-80 rounded bg-white px-5 pb-10 pt-16">
          <button
            aria-label="Close"
            className="absolute right-3.5 top-3.5 hover:opacity-50"
            onClick={close}
            type="button"
          >
            <svg fill="none" height="24" width="24">
              <path
                d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41Z"
                fill="#1D1D1D"
                fill-opacity=".54"
              />
            </svg>
          </button>

          <div className="text-center font-inter font-semibold leading-6">
            Setup a password to access
            <br />
            {name} website and app.
          </div>

          <form onSubmit={submit}>
            <PasswordInput
              clearErrors={clearPasswordErrors}
              errors={passwordErrors}
              inputName="new-password"
              name="Password"
              password={password}
              ref={passwordRef}
              setPassword={setPassword}
              setShowPassword={setShowPassword}
              showPassword={showPassword}
            />

            <PasswordInput
              clearErrors={clearConfirmationErrors}
              errors={confirmationErrors}
              inputName="confirm-new-password"
              name="Retype password"
              password={confirmation}
              setPassword={setConfirmation}
              setShowPassword={setShowConfirmation}
              showPassword={showConfirmation}
            />

            <Button
              bgColor="bg-red-600 disabled:bg-gray-300"
              buttonClassName="lg:w-full py-px"
              className="mt-4"
              disabled={isLoading}
              fontSize="text-sm"
              height="h-10.5 md:h-10.5 lg:h-10.5"
              hoverColor="hover:bg-red-700 disabled:hover:bg-gray-300"
              mobileFullWidth
              text="Continue"
              textColor="text-white"
              type="submit"
            />
          </form>
        </div>
      </div>
    </Transition>
  );
}
