import React from 'react';

interface SocialButtonProps {
  icon: React.JSX.Element;
  onClick: () => void;
  text: string;
}

export default function SocialButton({
  icon,
  onClick,
  text,
}: SocialButtonProps) {
  return (
    <button
      className="relative rounded border border-gray-700 px-3 py-2.5 text-center font-inter text-sm leading-5 text-gray-800 shadow-sm hover:border-gray-300"
      onClick={onClick}
      type="button"
    >
      <div className="absolute left-6 top-1/2 -translate-y-1/2">{icon}</div>
      {text}
    </button>
  );
}
