import dynamic from 'next/dynamic';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import { ThemeVariant } from 'store/slices/conf';

const themeVariantFooterMap: Record<ThemeVariant, React.ComponentType> = {
  [ThemeVariant.AGS]: dynamic(
    () => import('themes/autumn/templates/zoneItems/footer/Default'),
    { ssr: true },
  ),
  [ThemeVariant.DEFAULT]: dynamic(
    () => import('themes/autumn/templates/zoneItems/footer/Default'),
    { ssr: true },
  ),
  [ThemeVariant.ECHIDNA]: dynamic(
    () => import('themes/autumn/templates/zoneItems/footer/Echidna'),
    { ssr: true },
  ),
  [ThemeVariant.EXPLORE]: dynamic(
    () => import('themes/autumn/templates/zoneItems/footer/Explore'),
    { ssr: true },
  ),
  [ThemeVariant.MOP]: dynamic(
    () => import('themes/autumn/templates/zoneItems/footer/Default'),
    { ssr: true },
  ),
};

export default function Footer(): React.ReactElement {
  const themeVariant = useAppSelector((state) => state.conf.themeVariant);
  const Component = themeVariantFooterMap[themeVariant];
  return <Component />;
}
