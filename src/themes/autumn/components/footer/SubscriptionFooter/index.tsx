import clsx from 'clsx';

import HelpLink from 'components/HelpButton/HelpLink';
import Link from 'themes/autumn/components/generic/Link';
import { sendToGtm } from 'util/gtm';

interface SubscriptionFooterProps {
  hidePaymentMethod?: boolean;
  showLinksOnDesktop?: boolean;
  withBorders?: boolean;
}

function PaymentMethods(): React.ReactElement {
  return (
    <div className="border-t-1 border-gray-200 px-4">
      <div className="mx-auto flex max-w-[960px] flex-col-reverse items-center justify-start pb-7 pt-9 md:flex-row md:justify-between md:py-8">
        <div className="mt-8 text-center font-inter text-xxs text-gray-500 md:mt-0 md:text-left md:text-xs">
          You can help support local journalism via these payment methods.
        </div>
        <div className="flex flex-row gap-2.5">
          {/* Visa */}
          <svg fill="none" height="21" width="33">
            <path
              clipRule="evenodd"
              d="M0 2.196C0 .983.983 0 2.196 0h28.008C31.417 0 32.4.983 32.4 2.196v15.858a2.196 2.196 0 01-2.196 2.196H2.196A2.196 2.196 0 010 18.054V2.196z"
              fill="#EFEFF4"
              fillRule="evenodd"
            />
            <path
              clipRule="evenodd"
              d="M16.3 8.268c-.015 1.114 1.075 1.735 1.895 2.105.844.379 1.127.622 1.124.961-.006.52-.673.748-1.296.757-1.089.016-1.721-.271-2.225-.488l-.392 1.694c.505.215 1.44.402 2.409.41 2.274 0 3.763-1.037 3.77-2.645.01-2.04-3.055-2.154-3.035-3.066.008-.277.293-.572.92-.647.31-.038 1.165-.067 2.135.346l.38-1.64a6.229 6.229 0 00-2.026-.343c-2.141 0-3.647 1.05-3.66 2.556zm9.344-2.415c-.415 0-.765.224-.921.567l-3.25 7.167h2.273l.453-1.155h2.777l.263 1.155h2.003l-1.748-7.734h-1.85zm.318 2.09l.656 2.903h-1.796l1.14-2.904zm-12.418-2.09l-1.792 7.734h2.166l1.791-7.734h-2.165zm-3.205 0l-2.254 5.264-.912-4.476c-.107-.5-.53-.788-.999-.788H2.488l-.051.224c.756.152 1.616.397 2.137.658.319.16.41.3.514.68l1.728 6.172h2.289l3.51-7.734h-2.276z"
              fill="#1A1F71"
              fillRule="evenodd"
            />
          </svg>
          {/* Mastercard */}
          <svg fill="none" height="21" width="33">
            <path
              clipRule="evenodd"
              d="M.2 2.196C.2.983 1.183 0 2.396 0h28.008C31.617 0 32.6.983 32.6 2.196v15.858a2.196 2.196 0 01-2.196 2.196H2.396A2.196 2.196 0 01.2 18.054V2.196z"
              fill="#EFEFF4"
              fillRule="evenodd"
            />
            <path
              d="M10.144 18.065v-1.068a.618.618 0 00-.187-.49.651.651 0 00-.5-.187.69.69 0 00-.613.303.634.634 0 00-.246-.229.655.655 0 00-.33-.073.589.589 0 00-.512.252v-.21h-.38v1.702h.384v-.936a.39.39 0 01.104-.324.409.409 0 01.323-.128c.252 0 .38.16.38.448v.947h.383v-.943a.391.391 0 01.105-.324.411.411 0 01.322-.128c.26 0 .383.16.383.448v.947l.384-.007zm5.67-1.702h-.624v-.516h-.383v.516h-.347v.338h.354v.784c0 .395.157.63.606.63a.908.908 0 00.475-.132l-.11-.317a.714.714 0 01-.335.096c-.183 0-.252-.113-.252-.284V16.7h.62l-.003-.338zm3.238-.043a.525.525 0 00-.46.25v-.207h-.375v1.702h.38v-.954c0-.281.123-.438.364-.438a.634.634 0 01.238.043l.116-.356a.833.833 0 00-.27-.047l.007.008zm-4.895.179a1.331 1.331 0 00-.712-.178c-.441 0-.73.206-.73.544 0 .278.212.449.603.502l.182.025c.208.029.307.082.307.178 0 .132-.139.207-.398.207a.945.945 0 01-.58-.178l-.183.288c.221.15.486.228.756.22.503 0 .795-.23.795-.555 0-.324-.23-.455-.61-.509l-.182-.025c-.164-.021-.295-.053-.295-.167 0-.114.124-.2.332-.2.192.003.38.053.547.147l.168-.3zm10.168-.178a.526.526 0 00-.46.249v-.207h-.375v1.702h.38v-.954c0-.281.123-.438.364-.438a.633.633 0 01.237.043l.117-.356a.833.833 0 00-.27-.047l.007.008zm-4.891.89a.84.84 0 00.264.645.884.884 0 00.667.245.928.928 0 00.628-.203l-.183-.3a.78.78 0 01-.456.154.566.566 0 01-.367-.174.54.54 0 010-.741.566.566 0 01.367-.175.78.78 0 01.456.153l.183-.299a.927.927 0 00-.628-.203.903.903 0 00-.667.245.858.858 0 00-.264.645v.008zm3.555 0v-.848h-.38v.207a.66.66 0 00-.547-.25.925.925 0 00-.645.261.88.88 0 00-.267.63c0 .236.096.462.267.629.171.167.403.26.645.26a.676.676 0 00.548-.248v.206h.38v-.847zm-1.412 0a.507.507 0 01.103-.277.54.54 0 01.806-.047.51.51 0 01-.09.782.538.538 0 01-.79-.251.5.5 0 01-.03-.207zm-4.582-.89a.924.924 0 00-.641.27.879.879 0 00-.258.632.88.88 0 00.276.626.925.925 0 00.649.252c.263.013.52-.07.725-.232l-.183-.274a.844.844 0 01-.507.178.492.492 0 01-.348-.105.469.469 0 01-.174-.311h1.296v-.143c0-.534-.34-.89-.83-.89l-.005-.004zm0 .33a.442.442 0 01.309.118.422.422 0 01.133.296h-.912a.434.434 0 01.145-.299.456.456 0 01.318-.114h.008zm9.511.563V15.68h-.364v.89a.661.661 0 00-.548-.25.925.925 0 00-.645.261.88.88 0 00-.268.63.88.88 0 00.268.629c.17.167.403.26.645.26a.676.676 0 00.547-.248v.206h.366v-.844zm.634.604a.186.186 0 01.126.048.161.161 0 010 .239.183.183 0 01-.057.035.175.175 0 01-.07.014.185.185 0 01-.163-.103.164.164 0 01.095-.22.187.187 0 01.074-.013h-.005zm0 .3a.132.132 0 00.095-.039.13.13 0 000-.178.135.135 0 00-.192 0 .13.13 0 000 .178.137.137 0 00.044.029.138.138 0 00.058.01h-.005zm.01-.211c.018-.001.035.004.048.014a.044.044 0 01.017.037.041.041 0 01-.013.032.064.064 0 01-.038.016l.052.06h-.042l-.049-.06h-.016v.06h-.035v-.157l.077-.002zm-.04.03v.043h.04a.04.04 0 00.023 0 .018.018 0 000-.016.017.017 0 000-.016.039.039 0 00-.022 0l-.04-.011zm-2.007-.723a.506.506 0 01.103-.277.54.54 0 01.806-.047.51.51 0 01.103.556.517.517 0 01-.194.227.537.537 0 01-.788-.252.498.498 0 01-.03-.207zm-12.818 0v-.85h-.38v.206a.661.661 0 00-.548-.25.925.925 0 00-.645.261.88.88 0 00-.267.63.88.88 0 00.267.629c.171.167.403.26.645.26a.676.676 0 00.548-.248v.206h.38v-.844zm-1.413 0a.507.507 0 01.103-.277.54.54 0 01.806-.047.51.51 0 01.103.556.517.517 0 01-.194.227.537.537 0 01-.289.086.536.536 0 01-.389-.158.508.508 0 01-.144-.387h.004z"
              fill="#231F20"
            />
            <path d="M19.78 3.396h-5.749v10.077h5.75V3.396z" fill="#FF5F00" />
            <path
              d="M14.396 8.435a6.269 6.269 0 01.66-2.802 6.43 6.43 0 011.85-2.237 6.663 6.663 0 00-3.36-1.334 6.716 6.716 0 00-3.572.608 6.517 6.517 0 00-2.698 2.364 6.292 6.292 0 00-1.001 3.4c0 1.203.347 2.381 1 3.4a6.517 6.517 0 002.699 2.364 6.716 6.716 0 003.572.608 6.662 6.662 0 003.36-1.333 6.43 6.43 0 01-1.85-2.237 6.268 6.268 0 01-.66-2.8z"
              fill="#EB001B"
            />
            <path
              d="M27.538 8.435c0 1.203-.347 2.381-1.001 3.4a6.517 6.517 0 01-2.699 2.364 6.717 6.717 0 01-3.573.608 6.663 6.663 0 01-3.359-1.334 6.441 6.441 0 001.85-2.238 6.278 6.278 0 000-5.602 6.442 6.442 0 00-1.85-2.238 6.663 6.663 0 013.36-1.333 6.716 6.716 0 013.572.607 6.517 6.517 0 012.699 2.364 6.292 6.292 0 011 3.4v.002zM26.91 12.405V12.2h.085v-.043h-.217v.043h.093v.206h.038zm.42 0v-.25h-.065l-.076.179-.077-.178h-.059v.25h.048v-.188l.071.162h.05l.07-.162v.189l.039-.002z"
              fill="#F79E1B"
            />
          </svg>
          {/* Paypal */}
          <svg fill="none" height="21" width="33">
            <path
              clipRule="evenodd"
              d="M.4 2.196C.4.983 1.383 0 2.596 0h28.008C31.817 0 32.8.983 32.8 2.196v15.858a2.196 2.196 0 01-2.196 2.196H2.596A2.196 2.196 0 01.4 18.054V2.196z"
              fill="#EFEFF4"
              fillRule="evenodd"
            />
            <path
              d="M20.96 7.087h-1.998c-.118 0-.236.118-.294.236l-.824 5.174c0 .***************.177h1.058c.118 0 .177-.06.177-.177l.235-1.47c0-.117.117-.235.294-.235h.646c1.353 0 2.117-.647 2.294-1.94.117-.53 0-1-.235-1.294-.353-.294-.882-.47-1.53-.47zm.236 1.94c-.118.707-.647.707-1.176.707h-.353l.235-1.353c0-.059.06-.118.177-.118h.117c.353 0 .706 0 .882.***************.235.118.529z"
              fill="#139AD6"
            />
            <path
              d="M6.554 7.088h-2c-.117 0-.234.117-.293.235l-.824 5.174c0 .**************.177h.94c.118 0 .236-.118.295-.236l.235-1.41c0-.118.118-.236.294-.236h.647c1.352 0 2.117-.647 2.293-1.94.118-.53 0-1-.235-1.294-.353-.294-.823-.47-1.529-.47zm.235 1.94c-.117.706-.647.706-1.176.706H5.32l.235-1.353c0-.059.06-.117.177-.117h.117c.353 0 .706 0 .882.235.06.059.118.235.06.529zM12.61 8.97h-.94c-.059 0-.177.058-.177.117l-.058.294-.06-.118c-.234-.294-.646-.411-1.116-.411-1.059 0-2 .823-2.176 1.94a1.753 1.753 0 00.353 1.47c.294.353.705.47 1.235.47.882 0 1.352-.529 1.352-.529l-.059.294c0 .**************.177h.882c.117 0 .235-.118.294-.236l.529-3.292c-.059-.06-.176-.177-.235-.177zm-1.352 1.88c-.117.53-.529.942-1.117.942-.294 0-.53-.118-.647-.236-.117-.176-.176-.411-.176-.705.059-.53.529-.941 1.058-.941.294 0 .47.118.647.235.177.177.235.47.235.706z"
              fill="#263B80"
            />
            <path
              d="M26.958 8.97h-.94c-.06 0-.177.058-.177.117l-.059.294-.058-.118c-.236-.294-.647-.411-1.118-.411-1.058 0-1.999.823-2.175 1.94a1.753 1.753 0 00.352 1.47c.294.353.706.47 1.235.47.882 0 1.353-.529 1.353-.529l-.059.294c0 .118.059.177.176.177h.882c.118 0 .236-.118.294-.236l.53-3.292c-.06-.06-.118-.177-.236-.177zm-1.352 1.88c-.118.53-.53.942-1.117.942-.294 0-.53-.118-.647-.236-.118-.176-.176-.411-.176-.705.058-.53.529-.941 1.058-.941.294 0 .47.118.647.235.235.177.294.47.235.706z"
              fill="#139AD6"
            />
            <path
              d="M17.727 8.969h-1c-.118 0-.177.059-.235.117l-1.294 2-.588-1.882c-.059-.118-.118-.176-.294-.176h-.94c-.118 0-.177.117-.177.235l1.058 3.116-1 1.412c-.058.117 0 .294.118.294h.941c.118 0 .176-.06.235-.118l3.234-4.645c.177-.177.06-.353-.058-.353z"
              fill="#263B80"
            />
            <path
              d="M28.076 7.264l-.823 5.292c0 .***************.177h.823c.118 0 .236-.118.294-.236l.824-5.174c0-.118-.06-.177-.177-.177h-.94c-.06-.059-.118 0-.177.118z"
              fill="#139AD6"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}

export default function SubscriptionFooter({
  hidePaymentMethod = false,
  showLinksOnDesktop = false,
  withBorders = false,
}: SubscriptionFooterProps): React.ReactElement {
  return (
    <>
      <div
        className={clsx('mb-6 flex justify-center gap-x-5 text-sm', {
          'border-t border-gray-200 pt-6': withBorders,
          'md:hidden': !showLinksOnDesktop,
        })}
      >
        <HelpLink
          className="no-underline"
          hideVisited
          onClick={() => {
            sendToGtm({
              action: 'subscribe_page_stage',
              label: 'footer_help_link_click',
            });
          }}
        >
          Help
        </HelpLink>
        {withBorders && <div className="text-blue-600">|</div>}
        <Link
          className="no-underline"
          hideVisited
          href="/contact/"
          onClick={() => {
            sendToGtm({
              action: 'subscribe_page_stage',
              label: 'footer_contact_link_click',
            });
          }}
        >
          Contact
        </Link>
      </div>

      {!hidePaymentMethod && <PaymentMethods />}
    </>
  );
}
