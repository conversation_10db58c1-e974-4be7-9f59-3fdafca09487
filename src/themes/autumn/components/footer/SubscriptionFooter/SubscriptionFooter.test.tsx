import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import SubscriptionFooter from '.';

describe('subscription footer', () => {
  it('renders', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={createStore()}>
        <SubscriptionFooter />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with borders', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={createStore()}>
        <SubscriptionFooter hidePaymentMethod showLinksOnDesktop withBorders />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with links on desktops', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={createStore()}>
        <SubscriptionFooter showLinksOnDesktop />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with links and no payment method', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={createStore()}>
        <SubscriptionFooter hidePaymentMethod showLinksOnDesktop />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
