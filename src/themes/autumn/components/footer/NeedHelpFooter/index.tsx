import HelpLink from 'components/HelpButton/HelpLink';

interface NeedHelpFooterProps {
  onHelpLinkClick?: () => void;
}

export default function NeedHelpFooter({
  onHelpLinkClick,
}: NeedHelpFooterProps) {
  return (
    <div className="mt-24 flex h-20 w-full items-center justify-center border-t border-gray-300">
      <HelpLink
        className="inline-block text-center text-sm font-medium"
        hideVisited
        onClick={onHelpLinkClick}
      >
        Need help?
      </HelpLink>
    </div>
  );
}
