'use client';

import { atcb_action } from 'add-to-calendar-button';
import { useRef } from 'react';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  date: Date;
  location?: string;
  name: string;
}

export default function AddToCalendarButton({
  children,
  date,
  location,
  name,
  ...props
}: Props) {
  const ref = useRef<HTMLButtonElement>(null);

  const dateString = `${date.getFullYear()}-${(date.getMonth() + 1)
    .toString()
    .padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  const timeString = `${date.getHours().toString().padStart(2, '0')}:${date
    .getMinutes()
    .toString()
    .padStart(2, '0')}`;

  return (
    <button
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
      onClick={() => {
        atcb_action(
          {
            endDate: dateString,
            endTime: timeString,
            location,
            name,
            options: [
              'Apple',
              'Google',
              'iCal',
              'Microsoft365',
              'Outlook.com',
              'MicrosoftTeams',
              'Yahoo',
            ],
            startDate: dateString,
            startTime: timeString,
            timeZone:
              typeof Intl === 'undefined'
                ? 'Australia/Sydney'
                : // eslint-disable-next-line compat/compat
                  Intl.DateTimeFormat().resolvedOptions().timeZone,
          },
          ref.current ?? undefined,
        ).catch(console.error);
      }}
      ref={ref}
      type="button"
    >
      {children}
    </button>
  );
}
