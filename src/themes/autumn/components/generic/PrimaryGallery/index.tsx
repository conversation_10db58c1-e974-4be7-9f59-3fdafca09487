import clsx from 'clsx';
import React, { useCallback, useState } from 'react';

import AdImage from 'themes/autumn/templates/Classifieds/AdImage';

import Gallery from '../Gallery';

import type { GalleryItem } from 'types/Gallery';

interface Props {
  className?: string;
  elements: GalleryItem[];
  images: string[];
  isPreview?: boolean;
  title?: string;
  url?: string;
}

export default function PrimaryGallery({
  className,
  elements,
  images,
  isPreview,
  title = '',
  url,
}: Props) {
  const [open, setOpen] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  const openGallery = useCallback(
    (index: number) => {
      setCurrentSlide(index);
      setOpen(true);
    },
    [setCurrentSlide, setOpen],
  );

  const close = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  return (
    <div className={clsx('flex w-full flex-col gap-x-2', className)}>
      {open && (
        <Gallery
          close={close}
          currentSlide={currentSlide}
          elements={elements}
          isPreview={isPreview}
          shareUrl={url}
        />
      )}
      <button
        className="relative flex-1 focus-visible:outline-none"
        onClick={() => openGallery(0)}
        type="button"
      >
        <div className="absolute inset-0 bg-black opacity-0 transition-opacity duration-300 hover:opacity-20" />
        {isPreview ? (
          <img
            alt="preview"
            className="mx-auto max-h-[240px] w-full rounded-md bg-gray-550 object-cover md:max-h-[362px]"
            src={images[0]}
          />
        ) : (
          <AdImage
            alt={title}
            className="mx-auto max-h-[460px] w-full rounded-md bg-gray-550 object-contain"
            height={768}
            url={images[0]}
            width={1024}
          />
        )}
        <div className="absolute bottom-4 right-6 flex select-none flex-row items-center justify-center rounded bg-gray-100 px-4 py-2 font-inter text-xs font-medium text-gray-800">
          <svg
            className="fill-current text-gray-500"
            fill="none"
            height="14"
            viewBox="0 0 16 14"
            width="16"
          >
            <path
              clipRule="evenodd"
              d="M2 0C0.895431 0 0 0.89543 0 2V12C0 13.1046 0.895431 14 2 14H14C15.1046 14 16 13.1046 16 12V2C16 0.895431 15.1046 0 14 0H2ZM14 12H2L6 4L9 10L11 6L14 12Z"
              fillRule="evenodd"
            />
          </svg>

          <span className="ml-2.5 text-xs">
            {elements.length === 1 ? 'View Photo' : 'View Gallery'}
          </span>
        </div>
      </button>
    </div>
  );
}
