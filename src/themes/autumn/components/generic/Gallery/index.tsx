'use client';

/*
  Gallery implementation based on `SimpleCarousel` example from
  https://github.com/FormidableLabs/react-swipeable/tree/main/examples
*/
import clsx from 'clsx';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSwipeable } from 'react-swipeable';

import { useAppSelector } from 'store/hooks';
import AdProvider from 'themes/autumn/components/ads/AdProvider';
import { GalleryItemType, GalleryLayout } from 'types/Gallery';
import { StoryElementType } from 'types/Story';
import { StoryViewType } from 'types/ZoneItems';
import { AdSize } from 'util/ads';
import { useHashObject, useLazyLoadComponentState } from 'util/hooks';
import { hasValidURI, storyImageUrl } from 'util/image';
import { generateHashString } from 'util/page';

import Ad from '../../ads/Ad';
import StorySocials from '../../stories/StorySocials';
import Link from '../Link';
import LoadingSpinner from '../LoadingSpinner';

import type { GalleryItem } from 'types/Gallery';

enum Direction {
  NEXT = 'next',
  PREVIOUS = 'previous',
}

interface GalleryItemProps {
  className: string;
  containerWidth: number;
  displaySpinner: boolean;
  image: GalleryItem;
  order: number;
  position: string;
  preloadImage?: boolean;
  visible: boolean;
}

function GalleryItemEntry({
  className,
  containerWidth,
  displaySpinner = true,
  image: item,
  order,
  position,
  preloadImage = false,
  visible,
}: GalleryItemProps): React.ReactElement | null {
  const { transformUrl } = useAppSelector((state) => state.settings);

  if (!item) {
    return null;
  }

  let content: React.ReactElement | null;

  if (item.type === GalleryItemType.VIDEO_YOUTUBE) {
    content = (
      <div className="relative z-10 mx-auto flex size-full items-center justify-center lg:items-start">
        <div className="relative w-full pb-[56.25%]">
          <iframe
            allow="autoplay"
            className="absolute left-0 top-0 size-full border-0"
            id={item.id}
            src={`https://www.youtube.com/embed/${item.id}?autoplay=${
              visible ? 1 : 0
            }`}
            title={item.description}
          />
        </div>
      </div>
    );
  } else {
    let transformedUrl: string | undefined;
    // eslint-disable-next-line default-case
    switch (item.type) {
      case GalleryItemType.PHOTO:
        transformedUrl = encodeURI(item.url);
        break;
      case StoryElementType.Image:
        transformedUrl = hasValidURI(item)
          ? storyImageUrl({
              height: item.height,
              image: item,
              transformUrl,
              useFocalPoint: false,
              width: item.width,
            })
          : undefined;
        break;
    }

    content = (
      <div
        className="relative z-10 mx-auto size-full bg-contain bg-center bg-no-repeat lg:bg-top"
        key={transformedUrl}
        style={{
          backgroundImage:
            preloadImage && transformedUrl
              ? `url(${transformedUrl})`
              : undefined,
        }}
      />
    );
  }

  return (
    <div
      className={`shrink-0 basis-full transform-gpu bg-neutral-900 ${className}`}
      style={{ order, transform: position }}
    >
      {containerWidth > 0 && displaySpinner && (
        <div
          className={clsx(
            'absolute left-1/2 top-1/2 z-0',
            '-ml-8 -mt-8 size-16',
            'lg:top-1/4 lg:-ml-16 lg:-mt-16 lg:size-32',
          )}
        >
          <LoadingSpinner />
        </div>
      )}
      {content}
    </div>
  );
}

function refreshGalleryAds() {
  AdProvider.refresh('gallery-top', 'gallery-mrec');
}

function getOrder(index: number, pos: number, numItems: number) {
  // Offset by 1 so that the "first" item in the list
  // is the last item, as item 0 appears to the left
  // of the shown item
  const offset = (index + 1) % numItems;
  let relative = offset - pos;
  if (relative < 0) {
    relative += numItems;
  }
  return relative;
}

interface OpenGalleryProps {
  close: () => void;
  currentSlide?: number;
  elements: GalleryItem[];
  isPreview?: boolean;
  shareUrl?: string;
}

function Gallery({
  close,
  currentSlide = 0,
  elements,
  isPreview = false,
  shareUrl,
}: OpenGalleryProps): React.ReactElement | null {
  const photoGalleryFeature = useAppSelector(
    (state) => state.features.photoGallery,
  );
  const isWithThumbLayout =
    photoGalleryFeature.enabled &&
    photoGalleryFeature.data.layout === GalleryLayout.V2;
  const { staticUrl } = useAppSelector((state) => state.settings);
  const storyUrl = useAppSelector((state) => state.story.url);
  const isStory = useAppSelector(
    (state) => state.settings.viewType === (StoryViewType.STORY as string),
  );
  const hash = useHashObject();

  const communityShareContentEnabled = useAppSelector(
    (state) => state.features.communityShareContent.enabled,
  );

  const url = shareUrl || storyUrl;

  const [containerWidth, setContainerWidth] = useState(0);
  const [currentIdx, setCurrentIdx] = useState(currentSlide);
  const [sliding, setSliding] = useState(false);
  const [direction, setDirection] = useState<Direction>(Direction.NEXT);

  const currentImage = elements[currentIdx];
  const [initialized, setInitialized] = useState(false);
  const galleryTopRef = useRef<HTMLDivElement>(null);
  const { showComponentPlaceholder } = useLazyLoadComponentState();

  useEffect(() => {
    if (!initialized) {
      // force to load the ads when the gallery is opened
      setTimeout(() => {
        if (galleryTopRef.current && galleryTopRef.current.clientHeight < 10) {
          refreshGalleryAds();
        }
      }, 1000);
      setInitialized(true);
    }
  }, [initialized]);

  const slide = useCallback(
    (slideDirection: Direction) => {
      if (slideDirection === Direction.NEXT) {
        setCurrentIdx((current) => (current + 1) % elements.length);
      } else {
        setCurrentIdx(
          (current) => (current - 1 + elements.length) % elements.length,
        );
      }

      refreshGalleryAds();
      setSliding(true);
      setDirection(slideDirection);
      setTimeout(() => {
        setSliding(false);
      }, 50);
    },
    [setCurrentIdx, elements.length],
  );

  useEffect(() => {
    if (!isWithThumbLayout && isStory) {
      window.history.replaceState(
        null,
        '',
        currentIdx === 0
          ? window.location.hash.replace(/(&?slide=\d+&?)/, '')
          : generateHashString({
              slide: currentIdx.toString(),
            }),
      );
    }
  }, [currentIdx, hash, isStory, isWithThumbLayout]);

  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => slide(Direction.NEXT),
    onSwipedRight: () => slide(Direction.PREVIOUS),
  });

  useEffect(() => {
    function onKeyDown(e: KeyboardEvent) {
      switch (e.key) {
        case 'ArrowLeft':
          slide(Direction.PREVIOUS);
          break;
        case 'ArrowRight':
          slide(Direction.NEXT);
          break;
        case 'Escape':
          close();
          break;
        default:
          break;
      }
    }
    document.addEventListener('keydown', onKeyDown);
    return () => {
      document.removeEventListener('keydown', onKeyDown);
    };
  }, [close, slide]);

  const calcTranslate = () => {
    if (!sliding) return 'translateX(calc(-100%))';
    if (direction === Direction.PREVIOUS)
      return 'translateX(calc(2 * (-100%)))';
    return 'translateX(0%)';
  };

  const hasDescription =
    currentImage.type !== GalleryItemType.AD &&
    currentImage.type !== GalleryItemType.CTA;

  return (
    <div className="fixed left-0 top-0 z-50 flex size-full flex-col overflow-y-auto bg-neutral-900">
      <div className="mx-auto flex w-full grow flex-col lg:max-w-lg lg:pb-2">
        {/* Close Button */}
        <button
          className="absolute right-3 top-3"
          onClick={close}
          type="button"
        >
          <img
            alt="close"
            className="size-8"
            draggable="false"
            src={`${staticUrl}/images/photo-gallery/close.svg`}
          />
        </button>
        {/* Top Ad */}
        {!isPreview && showComponentPlaceholder && (
          <div className="mx-auto mt-14 w-full landscape:mt-2.5">
            <Ad
              forwardedRef={galleryTopRef}
              mdSizes={AdSize.leaderboard}
              position={1}
              publiftName="gallery-1"
              sizes={[]}
              slotId="gallery-top"
              targetingArguments={{
                adslot: 'gallery',
              }}
              withLabel={false}
              withPlaceholderBackground={false}
            />
          </div>
        )}

        {/* Gallery */}
        <div
          className={clsx(
            'flex size-full flex-col lg:flex-row lg:overflow-y-hidden',
            { 'mt-14': isPreview },
          )}
        >
          <div className="flex size-full flex-col overflow-hidden lg:ml-6">
            {/* Gallery Images */}
            <div
              className="my-4 size-full lg:mb-0"
              ref={(ref) => {
                setContainerWidth(ref?.clientWidth ?? 0);
              }}
            >
              <div
                className="flex size-full"
                // eslint-disable-next-line react/jsx-props-no-spreading
                {...swipeHandlers}
              >
                {elements.map((image, index) => (
                  <GalleryItemEntry
                    className={clsx({ 'transition-all': !sliding })}
                    containerWidth={containerWidth}
                    displaySpinner={!isPreview}
                    image={image}
                    // eslint-disable-next-line react/no-array-index-key
                    key={index}
                    order={getOrder(index, currentIdx, elements.length)}
                    position={elements.length === 1 ? 'none' : calcTranslate()}
                    preloadImage={
                      // Preload last when current is the first one
                      (currentIdx === 0 && index === elements.length - 1) ||
                      // Preload First when current image is last one
                      (currentIdx === elements.length - 1 && index === 0) ||
                      // Preload 1 next and 1 prev image
                      Math.abs(index - currentIdx) <= 1
                    }
                    visible={currentIdx === index}
                  />
                ))}
              </div>
            </div>
            {/* Gallery Controls */}
            {elements.length > 1 && (
              <div className="fixed bottom-0 left-0 z-20 flex h-16 w-full items-center justify-center bg-neutral-900 px-4 py-2 opacity-80 lg:relative">
                <div className="flex w-full flex-row items-center justify-between text-white">
                  <button
                    aria-label="Previous"
                    className="flex size-16 items-center justify-center"
                    onClick={() => slide(Direction.PREVIOUS)}
                    type="button"
                  >
                    <svg
                      className="fill-current"
                      height="48"
                      viewBox="0 0 48 48"
                      width="48"
                    >
                      <path d="M28.59 36L31.41 33.18L22.25 24L31.41 14.82L28.59 12L16.59 24L28.59 36Z" />
                    </svg>
                  </button>
                  <div className="font-inter text-lg">
                    {currentIdx + 1} of {elements.length}
                  </div>
                  <button
                    aria-label="Next"
                    className="flex size-16 items-center justify-center"
                    onClick={() => slide(Direction.NEXT)}
                    type="button"
                  >
                    <svg
                      className="fill-current"
                      height="48"
                      viewBox="0 0 48 48"
                      width="48"
                    >
                      <path d="M19.41 12L16.59 14.82L25.75 24L16.59 33.18L19.41 36L31.41 24L19.41 12Z" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Right AD, Social and description */}
          <div className="mt-8 px-3 pb-20 text-center lg:mx-6 lg:w-full lg:max-w-mrec lg:shrink-0 lg:flex-col lg:px-0 lg:pb-1 lg:text-left landscape:lg:flex">
            <div className="lg:overflow-y-auto">
              <StorySocials
                className="mb-3 inline-flex flex-row lg:-ml-1"
                url={
                  isWithThumbLayout ? url : `${url}${generateHashString(hash)}`
                }
              />
              <div className="mx-auto max-h-16 max-w-sm overflow-y-auto px-4 font-inter text-base text-white lg:max-h-40 lg:w-full lg:max-w-mrec lg:px-0">
                {hasDescription && currentImage.description}
                <div className="sticky bottom-0 h-4 w-full bg-gradient-to-t from-neutral-900" />
              </div>
            </div>
            {!isPreview && communityShareContentEnabled && (
              <Link
                className="text-sm text-white"
                href="/notice-board/share-photos/"
                noStyle
              >
                Want to be featured?{' '}
                <span className="font-semibold underline">
                  Send us your photos.
                </span>
              </Link>
            )}

            <div className="hidden lg:block">
              <div className="my-7 border-b border-gray-300" />
              {!isPreview && showComponentPlaceholder && (
                <div className="w-full lg:shrink-0">
                  <Ad
                    mdSizes={AdSize.mrec}
                    position={1}
                    publiftName="gallery-2"
                    sizes={[]}
                    slotId="gallery-mrec"
                    targetingArguments={{
                      adslot: 'gallery',
                    }}
                    withLabel={false}
                    withPlaceholderBackground={false}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Gallery;
