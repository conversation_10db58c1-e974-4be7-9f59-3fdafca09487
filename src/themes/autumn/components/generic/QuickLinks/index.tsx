import { useCallback } from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { sendGtmInteractionEvent } from 'themes/autumn/templates/Classifieds/utils';
import {
  ImageResizeMode,
  TransformOutputFormat,
  storyImageUrl,
} from 'util/image';

interface QuickLinkItemProps {
  href: string;
  iconUrl: string;
  label: string;
}

interface QuickLinksProps {
  items: QuickLinkItemProps[];
  title: string;
}

function QuickLinkItem({ href, iconUrl, label }: QuickLinkItemProps) {
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);

  const handleLinkClick = useCallback(
    () =>
      sendGtmInteractionEvent(
        {
          action: 'click',
          label,
          section: 'noticeboard',
        },
        'quicklink_clicks',
      ),
    [label],
  );

  return (
    <Link
      className="flex h-12 items-center rounded-full border border-gray-300 p-1.5 pr-3 shadow-sm hover:shadow-md focus:shadow-md"
      href={href}
      noStyle
      onClick={handleLinkClick}
    >
      <div className="flex size-10 items-center justify-center overflow-hidden rounded-full bg-gray-200">
        <img
          alt={label}
          className="size-full object-cover"
          src={storyImageUrl({
            fit: ImageResizeMode.MAX,
            height: 160,
            image: { uri: iconUrl },
            outputFormat: TransformOutputFormat.WEBP,
            transformUrl,
            width: 160,
          })}
        />
      </div>
      <span className="ml-2 text-sm font-medium text-gray-900">{label}</span>
    </Link>
  );
}

export default function QuickLinks({ items, title }: QuickLinksProps) {
  return (
    <div className="mb-10 md:mb-5">
      {!!title && (
        <h2 className="mb-4 text-xl font-semibold text-gray-900">{title}</h2>
      )}
      <div className="flex flex-wrap gap-4">
        {items.map((item) => (
          <QuickLinkItem
            href={item.href}
            iconUrl={item.iconUrl}
            key={item.label}
            label={item.label}
          />
        ))}
      </div>
    </div>
  );
}
