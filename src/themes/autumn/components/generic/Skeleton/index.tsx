import clsx from 'clsx';

import type { PropsWithChildren } from 'react';

interface SkeletonProps extends PropsWithChildren {
  className?: string;
  showContent: boolean;
  useSpan?: boolean;
}

export default function Skeleton({
  children,
  className,
  showContent: show,
  useSpan = false,
}: SkeletonProps) {
  if (useSpan) {
    return (
      <span className={clsx('relative block', className)}>
        {show ? (
          children
        ) : (
          <>
            <span className="absolute inset-0 block animate-pulse bg-gray-300" />
            <span className="pointer-events-none block opacity-0">
              {children}
            </span>
          </>
        )}
      </span>
    );
  }

  return (
    <div className={clsx('relative', className)}>
      {show ? (
        children
      ) : (
        <>
          <div className="absolute inset-0 animate-pulse bg-gray-300" />
          <div className="pointer-events-none opacity-0">{children}</div>
        </>
      )}
    </div>
  );
}
