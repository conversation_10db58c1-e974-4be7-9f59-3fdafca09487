import clsx from 'clsx';

import Link from 'themes/autumn/components/generic/Link';

const SVG_PLAY_ICON = (
  <svg fill="none" height="65" viewBox="0 0 65 65" width="65">
    <path
      clipRule="evenodd"
      d="M32.8458 64.5418C50.5189 64.5418 64.8457 50.2149 64.8457 32.5419C64.8457 14.8687 50.5189 0.54184 32.8458 0.54184C15.1726 0.54184 0.845734 14.8687 0.845734 32.5419C0.845734 50.2149 15.1726 64.5418 32.8458 64.5418Z"
      fill="white"
      fillOpacity="0.7"
      fillRule="evenodd"
    />
    <path
      clipRule="evenodd"
      d="M26.4459 19.5684L44.0891 32.5414L26.4459 45.5143V19.5684Z"
      fill="black"
      fillOpacity="0.5"
      fillRule="evenodd"
    />
  </svg>
);
const SVG_CAMERA_ICON = (
  <svg fill="none" height="61" viewBox="0 0 83 82" width="65">
    <path
      clipRule="evenodd"
      d="M30.9769 7.70831L24.8769 14.375H14.3102C10.6436 14.375 7.64355 17.375 7.64355 21.0416V61.0416C7.64355 64.7083 10.6436 67.7083 14.3102 67.7083H67.6436C71.3102 67.7083 74.3102 64.7083 74.3102 61.0416V21.0416C74.3102 17.375 71.3102 14.375 67.6436 14.375H57.0769L50.9769 7.70831H30.9769ZM40.9769 51.0416C46.4997 51.0416 50.9769 46.5645 50.9769 41.0416C50.9769 35.5188 46.4997 31.0416 40.9769 31.0416C35.454 31.0416 30.9769 35.5188 30.9769 41.0416C30.9769 46.5645 35.454 51.0416 40.9769 51.0416ZM24.3102 41.0416C24.3102 50.2416 31.7769 57.7083 40.9769 57.7083C50.1769 57.7083 57.6436 50.2416 57.6436 41.0416C57.6436 31.8416 50.1769 24.375 40.9769 24.375C31.7769 24.375 24.3102 31.8416 24.3102 41.0416Z"
      fill="white"
      fillRule="evenodd"
    />
    <line
      stroke="white"
      strokeWidth="7"
      x1="7.45192"
      x2="79.8674"
      y1="2.86313"
      y2="75.2786"
    />
  </svg>
);
const SVG_LOCATION_ICON = (
  <svg fill="none" height="21" viewBox="0 0 21 21" width="21">
    <path
      clipRule="evenodd"
      d="M10.8555 2.26535C7.49199 2.26535 4.77161 4.98573 4.77161 8.34927C4.77161 12.9122 10.8555 19.648 10.8555 19.648C10.8555 19.648 16.9394 12.9122 16.9394 8.34927C16.9394 4.98573 14.2191 2.26535 10.8555 2.26535ZM6.5107 8.34998C6.5107 5.95118 8.45755 4.00432 10.8564 4.00432C13.2552 4.00432 15.202 5.95118 15.202 8.34998C15.202 10.8531 12.6989 14.599 10.8564 16.937C9.04856 14.6164 6.5107 10.827 6.5107 8.34998ZM8.68315 8.3499C8.68315 7.14988 9.65595 6.17707 10.856 6.17707C11.6323 6.17707 12.3496 6.59121 12.7377 7.26349C13.1258 7.93576 13.1258 8.76404 12.7377 9.43631C12.3496 10.1086 11.6323 10.5227 10.856 10.5227C9.65595 10.5227 8.68315 9.54992 8.68315 8.3499Z"
      fill="#059669"
      fillRule="evenodd"
    />
  </svg>
);

export interface CardProps {
  bottomSection?: React.ReactElement;
  className?: string;
  date?: string;
  descriptionSection?: React.ReactElement;
  imageLabelOverlay?: React.ReactElement;
  isVideo?: boolean;
  location: string;
  target?: string;
  thumbnail?: string;
  title: string;
  titleClassName?: string;
  url: string;
}

export default function Card({
  bottomSection,
  className = '',
  date,
  descriptionSection,
  imageLabelOverlay,
  isVideo = false,
  location,
  target,
  thumbnail,
  title,
  titleClassName = 'text-base',
  url,
}: CardProps): React.ReactElement {
  return (
    <Link
      className={clsx(
        'flex size-full flex-col overflow-hidden rounded-lg bg-white shadow-md',
        className,
      )}
      href={url}
      noStyle
      target={target}
    >
      <div
        className={clsx(
          'relative h-44 w-full rounded-t-lg bg-gray-200 bg-cover bg-center',
          {
            'flex items-center justify-center': !thumbnail || isVideo,
          },
        )}
        style={{
          backgroundImage: thumbnail
            ? `url("${encodeURI(thumbnail)}")`
            : undefined,
        }}
      >
        <div className="absolute inset-0 rounded-t-lg bg-black/10" />
        {!thumbnail && SVG_CAMERA_ICON}
        {isVideo && SVG_PLAY_ICON}
        {imageLabelOverlay}
      </div>
      <div className="flex-[2] px-3.5 pb-5">
        {date && (
          <div className="mt-5 font-inter text-sm font-medium text-green-600">
            {date}
          </div>
        )}
        <div
          className={clsx(
            'mt-3 font-inter font-semibold text-gray-900',
            titleClassName,
          )}
        >
          {title}
        </div>
        <div className="mt-3 flex flex-row justify-start space-x-2">
          {SVG_LOCATION_ICON}
          <div className="w-full font-inter text-sm text-gray-500">
            {location}
          </div>
        </div>
        {descriptionSection && (
          <div className="mt-3 line-clamp-4 text-sm text-gray-500">
            {descriptionSection}
          </div>
        )}
      </div>
      {bottomSection && (
        <div className="flex flex-row items-center justify-between border-t border-gray-300 px-3.5 py-2">
          {bottomSection}
        </div>
      )}
    </Link>
  );
}
