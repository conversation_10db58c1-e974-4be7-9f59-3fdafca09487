import clsx from 'clsx';
import { forwardRef } from 'react';

import { isExternalLink } from 'util/page';

import type { ForwardedRef } from 'react';

interface BaseProps {
  bgColor: string;
  buttonClassName?: string;
  desktopFullWidth?: boolean;
  fontSize: string;
  height?: string;
  hoverColor: string;
  mobileFontSize?: boolean;
  mobileFullWidth?: boolean;
  paddingX?: string;
  text: string;
  textColor: string;
}

interface ButtonProps
  extends BaseProps,
    React.ButtonHTMLAttributes<HTMLButtonElement> {
  type: 'button' | 'submit' | 'reset';
}

interface AnchorProps
  extends BaseProps,
    React.AnchorHTMLAttributes<HTMLAnchorElement> {
  href: string;
}

type Props = ButtonProps | AnchorProps;

function Button(
  {
    bgColor,
    buttonClassName,
    className,
    desktopFullWidth,
    fontSize,
    height = 'md:h-10 lg:h-10',
    hoverColor,
    mobileFontSize,
    mobileFullWidth,
    paddingX = 'px-4',
    text,
    textColor,
    ...props
  }: Props,
  ref: ForwardedRef<HTMLDivElement>,
): React.ReactElement {
  const useRel =
    'href' in props &&
    props.href &&
    isExternalLink(props.href) &&
    props.target !== undefined &&
    props.target !== '_self';
  return (
    <div
      className={clsx(
        'flex md:flex-row md:justify-end lg:flex-row lg:justify-end',
        { 'md:w-full': desktopFullWidth, 'w-full': mobileFullWidth },
        className,
      )}
      ref={ref}
    >
      {'href' in props ? (
        <a
          className={clsx(
            'flex items-center justify-center',
            'rounded-md',
            bgColor,
            height,
            hoverColor,
            buttonClassName,
            {
              'md:w-auto': !desktopFullWidth,
              'md:w-full': desktopFullWidth,
              'w-full': mobileFullWidth,
            },
          )}
          rel={useRel ? 'noopener' : undefined}
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...props}
        >
          <span
            className={clsx(
              'font-medium leading-6 md:py-2 lg:py-2',
              fontSize,
              paddingX,
              textColor,
              {
                'py-1': !mobileFullWidth,
                'py-2': mobileFullWidth,
                'text-xs': mobileFontSize,
              },
            )}
          >
            {text}
          </span>
        </a>
      ) : (
        // eslint-disable-next-line react/button-has-type
        <button
          className={clsx(
            'flex items-center justify-center',
            'rounded-md',
            bgColor,
            height,
            hoverColor,
            buttonClassName,
            {
              'md:w-auto': !desktopFullWidth,
              'md:w-full': desktopFullWidth,
              'w-full': mobileFullWidth,
            },
          )}
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...props}
        >
          <span
            className={clsx(
              'py-2 font-medium leading-6',
              fontSize,
              paddingX,
              textColor,
              { 'text-xs': mobileFontSize },
            )}
          >
            {text}
          </span>
        </button>
      )}
    </div>
  );
}

export default forwardRef(Button);
