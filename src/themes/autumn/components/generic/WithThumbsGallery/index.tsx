import clsx from 'clsx';
import React, { useCallback, useState } from 'react';

import AdImage from 'themes/autumn/templates/Classifieds/AdImage';

import Gallery from '../Gallery';

import type { GalleryItem } from 'types/Gallery';

interface Props {
  className?: string;
  elements: GalleryItem[];
  images: string[];
  isPreview?: boolean;
  primaryImageClass?: string;
  title?: string;
  url?: string;
}

const MAX_THUMBNAILS_TO_SHOW = 4;

export default function WithThumbsGallery({
  className,
  elements,
  images,
  isPreview,
  primaryImageClass = 'h-[85px] xl:h-[92px]',
  title = '',
  url,
}: Props) {
  const [open, setOpen] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  const openGallery = useCallback(
    (index: number) => {
      setCurrentSlide(index);
      setOpen(true);
    },
    [setCurrentSlide, setOpen],
  );

  const close = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  const imagesSideBar = images.slice(1, 5);

  return (
    <div
      className={clsx('flex w-full flex-col gap-x-2 md:flex-row', className)}
    >
      {open && (
        <Gallery
          close={close}
          currentSlide={currentSlide}
          elements={elements}
          isPreview={isPreview}
          shareUrl={url}
        />
      )}
      <button
        className="relative flex-1 focus-visible:outline-none"
        onClick={() => openGallery(0)}
        type="button"
      >
        <div className="absolute inset-0 bg-black opacity-0 transition-opacity duration-300 hover:opacity-20" />
        {isPreview ? (
          <img
            alt="preview"
            className={clsx(
              'mx-auto max-h-[240px] w-full rounded-md bg-gray-550 object-cover',
              elements.length === 1 ? 'md:max-h-[440px]' : 'md:max-h-[362px]',
            )}
            src={images[0]}
          />
        ) : (
          <AdImage
            alt={title}
            className={clsx(
              'mx-auto max-h-[396px] min-h-[200px] w-full rounded-md bg-gray-550 object-contain',
              elements.length === 1 ? 'md:max-h-[460px]' : 'md:h-[396px]',
            )}
            height={413}
            url={images[0]}
            width={800}
          />
        )}
      </button>
      <div className="mx-2 mt-2 flex flex-row gap-2 md:mx-0 md:mt-0 md:flex-col md:gap-x-0">
        {imagesSideBar.map((image, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <React.Fragment key={`${image}-${index}`}>
            <button
              className="relative block"
              onClick={() =>
                openGallery(index === imagesSideBar.length - 1 ? 0 : index + 1)
              }
              type="button"
            >
              <div className="group relative">
                <div
                  className={clsx(
                    'absolute inset-0 bg-black transition-opacity duration-300',
                    index === imagesSideBar.length - 1 &&
                      elements.length > MAX_THUMBNAILS_TO_SHOW
                      ? 'opacity-20 group-hover:opacity-0'
                      : 'opacity-0 group-hover:opacity-20',
                  )}
                />
                {isPreview ? (
                  <img
                    alt="preview"
                    className="mx-auto h-[86px] w-[110px] rounded-md bg-gray-650 object-cover"
                    src={image}
                  />
                ) : (
                  <AdImage
                    alt={title}
                    className={clsx(
                      'mx-auto aspect-[11/9] rounded-md bg-gray-650 object-cover',
                      primaryImageClass,
                    )}
                    height={92}
                    url={image}
                    width={120}
                  />
                )}
                {index === imagesSideBar.length - 1 &&
                  elements.length > MAX_THUMBNAILS_TO_SHOW && (
                    <span className="absolute inset-0 flex items-center justify-center text-2xl font-medium text-white">
                      +{elements.length - MAX_THUMBNAILS_TO_SHOW}
                    </span>
                  )}
              </div>
            </button>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
