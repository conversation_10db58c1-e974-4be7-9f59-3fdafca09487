import clsx from 'clsx';

interface Props {
  children: string;
  className: string;
  textBasedColors?: boolean;
}

const TEXT_BASED_COLORS: Record<string, string> = {
  new: 'bg-green-600 text-white',
};

export default function Badge({
  children,
  className,
  textBasedColors = false,
}: Props): React.ReactElement {
  const color =
    (textBasedColors && TEXT_BASED_COLORS[children.toLocaleLowerCase()]) || '';

  return (
    <div
      className={clsx(
        'rounded-lg px-2 py-0 text-xxs font-normal leading-4',
        color,
        className,
      )}
    >
      {children}
    </div>
  );
}
