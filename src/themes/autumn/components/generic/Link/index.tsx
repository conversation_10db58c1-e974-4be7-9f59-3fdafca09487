'use client';

import clsx from 'clsx';
import { useEffect, useState } from 'react';

import { isExternalLink } from 'util/page';

import type { AnchorHTMLAttributes, DetailedHTMLProps } from 'react';
import type { LinkStyle } from 'types/theme';

export interface LinkProps
  extends Omit<
    DetailedHTMLProps<
      AnchorHTMLAttributes<HTMLAnchorElement>,
      HTMLAnchorElement
    >,
    // Set automatically by the component
    'rel'
  > {
  channel?: string;
  hideVisited?: boolean;
  isSponsored?: boolean;
  linkStyle?: LinkStyle;
  noStyle?: boolean;
  trusted?: boolean;
}

const SEO_TRUSTED_TLDS = [
  '.gov',
  '.gov.au',
  '.edu',
  '.edu.au',
  '.org.au',
  '.csiro.au',
  '.asn.au',
];
const SEO_TRUSTED_DOMAINS = ['www.nytimes.com', 'www.wsj.com.au'];

const linkStyleClasses: Record<
  LinkStyle,
  Partial<Record<'base' | 'hideVisited' | 'hover' | 'visited', string>>
> = {
  default: {
    base: 'text-blue-600 underline decoration-blue-400',
    hideVisited: 'visited:text-blue-600',
    hover: 'hover:text-blue-600 hover:decoration-blue-600',
    visited: 'visited:text-gray-500 visited:decoration-gray-500',
  },
  index: {
    hover: 'hover:opacity-70',
  },
};

export default function Link({
  channel,
  children,
  className,
  hideVisited,
  href,
  isSponsored,
  linkStyle = 'default',
  noStyle,
  target,
  trusted = false,
  ...props
}: LinkProps): React.ReactElement {
  const isExternal = href && isExternalLink(href);
  const relParts = [];

  if (isExternal && target !== undefined && target !== '_self' && !trusted) {
    relParts.push('noopener');
  }

  // Paid external links should have `rel="sponsored"` except for certain
  // hostnames and TLDs that are considered high authority, e.g. government
  if (isExternal && isSponsored) {
    let hostname: string | undefined;
    try {
      hostname = new URL(href).hostname;
    } catch {
      hostname = undefined;
      // Ignore invalid hostname
    }

    if (
      hostname &&
      !SEO_TRUSTED_DOMAINS.includes(hostname) &&
      !SEO_TRUSTED_TLDS.some((tld) => hostname.endsWith(tld))
    )
      relParts.push('sponsored');
  }

  const rel = relParts.length ? relParts.join(' ') : undefined;

  const [hrefValue, setHrefValue] = useState(href);

  useEffect(() => {
    if (channel && href) {
      let url: URL | null = null;
      try {
        url = isExternal ? new URL(href) : new URL(href, 'https://dummy');
      } catch {
        console.error(`Invalid URL: ${href}`);
      }

      if (url !== null) {
        url.searchParams.set('channel', channel);
        url.searchParams.set('referrer', window.location.href);

        const finalHref = isExternal
          ? url.toString()
          : [url.pathname, url.search, url.hash].join('');

        setHrefValue(finalHref);
        return;
      }
    }
    setHrefValue(href);
  }, [channel, href, isExternal]);

  /* eslint-disable react/jsx-props-no-spreading */
  return (
    <a
      {...props}
      className={
        clsx(
          className,
          !noStyle && linkStyleClasses[linkStyle]?.base,
          !noStyle && linkStyleClasses[linkStyle]?.hover,
          !noStyle && !hideVisited && linkStyleClasses[linkStyle]?.visited,
          !noStyle && hideVisited && linkStyleClasses[linkStyle]?.hideVisited,
        ) || undefined
      }
      href={hrefValue}
      rel={rel}
      target={target}
    >
      {children ?? null}
    </a>
  );
  /* eslint-enable react/jsx-props-no-spreading */
}
