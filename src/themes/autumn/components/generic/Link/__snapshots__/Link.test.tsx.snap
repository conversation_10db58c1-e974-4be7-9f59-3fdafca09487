// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`<Link /> renders 1`] = `
<a
  class="text-blue-600 underline decoration-blue-400 hover:text-blue-600 hover:decoration-blue-600 visited:text-gray-500 visited:decoration-gray-500"
  href="/test"
/>
`;

exports[`<Link /> renders external links with noopener 1`] = `
<a
  class="text-blue-600 underline decoration-blue-400 hover:text-blue-600 hover:decoration-blue-600 visited:text-gray-500 visited:decoration-gray-500"
  href="https://google.com"
  rel="noopener"
  target="_blank"
/>
`;

exports[`<Link /> renders with className 1`] = `
<a
  class="ml-1 pb-2 text-blue-600 underline decoration-blue-400 hover:text-blue-600 hover:decoration-blue-600 visited:text-gray-500 visited:decoration-gray-500"
  href="/test"
/>
`;

exports[`<Link /> renders with hideVisited 1`] = `
<a
  class="text-blue-600 underline decoration-blue-400 hover:text-blue-600 hover:decoration-blue-600 visited:text-blue-600"
  href="/test"
/>
`;

exports[`<Link /> renders with noStyle & className & hideVisited 1`] = `
<a
  class="ml-1 pb-2"
  href="/test"
/>
`;

exports[`<Link /> renders with noStyle & className 1`] = `
<a
  class="ml-1 pb-2"
  href="/test"
/>
`;

exports[`<Link /> renders with noStyle 1`] = `
<a
  href="/test"
/>
`;
