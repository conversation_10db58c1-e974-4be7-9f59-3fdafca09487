import { render } from '@testing-library/react';

import Link from '.';

describe('<Link />', () => {
  it('renders', () => {
    expect.assertions(1);

    const { container } = render(<Link href="/test" />);

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders external links with noopener', () => {
    expect.assertions(2);

    const { container } = render(
      <Link href="https://google.com" target="_blank" />,
    );

    expect(container.firstChild).toMatchSnapshot();
    expect(container.firstElementChild?.getAttribute('rel')).toBe('noopener');
  });

  it('renders with noStyle', () => {
    expect.assertions(1);

    const { container } = render(<Link href="/test" noStyle />);

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with className', () => {
    expect.assertions(1);

    const { container } = render(<Link className="ml-1 pb-2" href="/test" />);

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with noStyle & className', () => {
    expect.assertions(1);

    const { container } = render(
      <Link className="ml-1 pb-2" href="/test" noStyle />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with hideVisited', () => {
    expect.assertions(1);

    const { container } = render(<Link hideVisited href="/test" />);

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with noStyle & className & hideVisited', () => {
    expect.assertions(1);

    const { container } = render(
      <Link className="ml-1 pb-2" hideVisited href="/test" noStyle />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('re-renders with updated href value', () => {
    const { container, rerender } = render(<Link href="/test/" />);
    expect(container.firstChild).toHaveAttribute('href', '/test/');

    rerender(<Link href="/test-b/" />);
    expect(container.firstChild).toHaveAttribute('href', '/test-b/');
  });

  it('renders with empty href value', () => {
    // eslint-disable-next-line jsx-a11y/anchor-is-valid
    const { container } = render(<Link href="" />);
    expect(container.firstChild).toHaveAttribute('href', '');
  });

  it('renders sponsored links', () => {
    const { container } = render(
      <Link href="https://test.com/" isSponsored noStyle />,
    );
    expect(container.firstChild).toHaveAttribute('rel', 'sponsored');
  });

  it('renders trusted sponsored TLDs', () => {
    const { container } = render(
      <Link href="https://test.gov/" isSponsored noStyle target="_blank" />,
    );
    expect(container.firstChild).toHaveAttribute('rel', 'noopener');
  });

  it('renders trusted sponsored hostnames', () => {
    const { container } = render(
      <Link href="https://www.nytimes.com/" isSponsored noStyle />,
    );
    expect(container.firstChild).not.toHaveAttribute('rel');
  });
});
