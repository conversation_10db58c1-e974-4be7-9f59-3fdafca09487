import React from 'react';
import { twMerge } from 'tailwind-merge';

interface ATagProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  tag?: 'a';
}

interface ButtonTagProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  tag: 'button';
}
type Props = ATagProps | ButtonTagProps;

export default function DropDownItem({
  children,
  className,
  tag = 'a',
  ...props
}: Props): React.ReactElement {
  const Tag = tag;

  return (
    <Tag
      className={twMerge(
        'flex items-center px-4 py-2 text-sm text-gray-700 hover:text-gray-900',
        'hover:bg-gray-100',
        tag === 'button' && 'w-full text-left',
        className,
      )}
      role="menuitem"
      tabIndex={-1}
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...(props as React.HTMLAttributes<HTMLElement>)}
    >
      {children}
    </Tag>
  );
}
