import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import ShareButton from '.';

describe('share button', () => {
  it('renders', () => {
    expect.assertions(1);

    const store = createStore();

    const { container } = render(
      <TestWrapper store={store}>
        <ShareButton>
          <div className="mr-2.5 flex flex-row items-center rounded bg-white px-2.5 py-2 font-inter text-sm font-medium text-gray-900 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none">
            <svg
              className="mr-2.5 fill-current"
              fill="none"
              height="14"
              viewBox="0 0 14 14"
              width="14"
            >
              <path d="M11 5.39998C12.3255 5.39998 13.4 4.32546 13.4 2.99998C13.4 1.67449 12.3255 0.599976 11 0.599976C9.67449 0.599976 8.59997 1.67449 8.59997 2.99998C8.59997 3.10036 8.60613 3.19931 8.6181 3.29646L4.66605 5.27249C4.2344 4.85609 3.6471 4.59998 2.99997 4.59998C1.67449 4.59998 0.599976 5.67449 0.599976 6.99998C0.599976 8.32546 1.67449 9.39997 2.99997 9.39997C3.64711 9.39997 4.23442 9.14385 4.66608 8.72743L8.61811 10.7035C8.60614 10.8006 8.59997 10.8996 8.59997 11C8.59997 12.3255 9.67449 13.4 11 13.4C12.3255 13.4 13.4 12.3255 13.4 11C13.4 9.67449 12.3255 8.59998 11 8.59998C10.3528 8.59998 9.76554 8.85609 9.33389 9.27249L5.38184 7.29646C5.39381 7.19931 5.39997 7.10036 5.39997 6.99998C5.39997 6.89957 5.39381 6.80061 5.38183 6.70345L9.33387 4.72743C9.76552 5.14385 10.3528 5.39998 11 5.39998Z" />
            </svg>
            Share
          </div>
        </ShareButton>
      </TestWrapper>,
    );

    expect(container).toMatchSnapshot();
  });
});
