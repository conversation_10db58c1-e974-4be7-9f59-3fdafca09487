// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`share button renders 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="relative"
    >
      <button
        aria-expanded="false"
        aria-haspopup="true"
        class="font-semibold"
        type="button"
      >
        <div
          class="mr-2.5 flex flex-row items-center rounded bg-white px-2.5 py-2 font-inter text-sm font-medium text-gray-900 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        >
          <svg
            class="mr-2.5 fill-current"
            fill="none"
            height="14"
            viewBox="0 0 14 14"
            width="14"
          >
            <path
              d="M11 5.39998C12.3255 5.39998 13.4 4.32546 13.4 2.99998C13.4 1.67449 12.3255 0.599976 11 0.599976C9.67449 0.599976 8.59997 1.67449 8.59997 2.99998C8.59997 3.10036 8.60613 3.19931 8.6181 3.29646L4.66605 5.27249C4.2344 4.85609 3.6471 4.59998 2.99997 4.59998C1.67449 4.59998 0.599976 5.67449 0.599976 6.99998C0.599976 8.32546 1.67449 9.39997 2.99997 9.39997C3.64711 9.39997 4.23442 9.14385 4.66608 8.72743L8.61811 10.7035C8.60614 10.8006 8.59997 10.8996 8.59997 11C8.59997 12.3255 9.67449 13.4 11 13.4C12.3255 13.4 13.4 12.3255 13.4 11C13.4 9.67449 12.3255 8.59998 11 8.59998C10.3528 8.59998 9.76554 8.85609 9.33389 9.27249L5.38184 7.29646C5.39381 7.19931 5.39997 7.10036 5.39997 6.99998C5.39997 6.89957 5.39381 6.80061 5.38183 6.70345L9.33387 4.72743C9.76552 5.14385 10.3528 5.39998 11 5.39998Z"
            />
          </svg>
          Share
        </div>
      </button>
      <div
        class="pointer-events-none fixed inset-0 z-20"
      >
        <div
          aria-labelledby="menu-button"
          aria-orientation="vertical"
          class="pointer-events-auto absolute mt-2 w-11/12 rounded-md md:w-56 bg-white shadow-lg ring-1 ring-black/5 z-20 text-left transition focus:outline-none duration-75 ease-in invisible scale-95 opacity-0"
          role="menu"
          style="top: 0px; right: 1004px;"
          tabindex="-1"
        >
          <div
            class="whitespace-pre-wrap py-1"
            role="none"
          >
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-facebook appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-facebook"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-blue-500"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"facebook"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Facebook
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-twitter appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://twitter.com/share?url=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-twitter"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-black"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"x-twitter"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Twitter
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-whatsapp appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://wa.me/?text=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-whatsapp"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-green-400"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"whatsapp"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Whatsapp
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
