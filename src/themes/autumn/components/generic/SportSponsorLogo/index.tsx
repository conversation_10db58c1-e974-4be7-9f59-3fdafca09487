import clsx from 'clsx';
import { twMerge } from 'tailwind-merge';

import { onGtmReady } from 'components/GoogleTagManager/ready';
import Link from 'themes/autumn/components/generic/Link';
import { sendToGtm } from 'util/gtm';
import { useOnce } from 'util/hooks';
import useSportSponsorData from 'util/sportsHubSponsor';

interface Props {
  className?: string;
  imgClassName?: string;
  sendImpression?: boolean;
  showSponsoredBy?: boolean;
}

export default function SportSponsorLogo({
  className,
  imgClassName,
  sendImpression = true,
  showSponsoredBy = true,
}: Props): React.ReactElement | null {
  const sponsorData = useSportSponsorData();

  useOnce(() => {
    if (sponsorData && sendImpression) {
      onGtmReady(() => {
        sendToGtm({
          action: 'impression',
          label: sponsorData.name.toLowerCase(),
          section: 'logo',
          trigger: 'widget_sponsorship',
        });
      });
      return true;
    }
    return false;
  }, [sendImpression, sponsorData]);

  if (!sponsorData) {
    return null;
  }

  return (
    <Link
      href={sponsorData.url}
      noStyle
      onClick={() => {
        sendToGtm({
          action: 'click',
          label: sponsorData.name.toLowerCase(),
          section: 'logo',
          trigger: 'widget_sponsorship',
        });
      }}
      target={sponsorData.url.startsWith('http') ? '_blank' : '_self'}
    >
      <div className={clsx('flex items-center', className)}>
        {showSponsoredBy && (
          <span
            className={clsx(
              'mr-3 font-inter text-xs font-normal',
              !sponsorData.textColor && 'text-gray-300',
            )}
            style={{ color: sponsorData.textColor }}
          >
            {sponsorData.text}
          </span>
        )}
        <img
          alt={`${sponsorData.name} Logo`}
          className={twMerge('inline-block max-h-9', imgClassName)}
          src={sponsorData.logo}
        />
      </div>
    </Link>
  );
}
