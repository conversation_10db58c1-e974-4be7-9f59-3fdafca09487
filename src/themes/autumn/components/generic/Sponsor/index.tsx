import { useAppSelector } from 'store/hooks';
import { setGtmDataLayer } from 'util/gtm';

import Link from '../Link';

import type { SportPage } from 'types/SportsHub';
import type { GameType } from 'types/playhq';

interface SponsorProps {
  gameType: GameType | SportPage;
  section: string;
}

export default function Sponsor({ gameType, section }: SponsorProps) {
  const sportsHubSponsor = useAppSelector(
    (state) => state.features.sportsHubSponsor,
  );

  if (
    !sportsHubSponsor.enabled ||
    !sportsHubSponsor.data.sponsorData[gameType]
  ) {
    return null;
  }

  const sponsorData = sportsHubSponsor.data.sponsorData[gameType];

  return (
    <Link
      className="flex items-center justify-center gap-x-1 space-x-1 rounded-b-md border-t-1 py-1 no-underline outline-none ring-0"
      href={sponsorData.url}
      noStyle
      onClick={() => {
        setGtmDataLayer({
          data: {
            action: 'click',
            section,
            type: sponsorData.name.toLowerCase(),
          },
          event: 'widget_sponsorship',
        });
      }}
      style={{
        backgroundColor: sponsorData.backgroundColor,
        color: sponsorData.textColor,
      }}
      target="_blank"
      trusted
    >
      <p className="text-sm">{sponsorData.text}</p>
      {sponsorData.logo && (
        <img
          alt={sponsorData.name}
          className="h-auto w-28"
          src={sponsorData.logo}
        />
      )}
    </Link>
  );
}
