import { render } from '@testing-library/react';

import Container from '.';

describe('container', () => {
  it('renders with gutter', () => {
    expect.assertions(1);

    const { container } = render(<Container>Test Content</Container>);

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders without gutter', () => {
    expect.assertions(1);

    const { container } = render(<Container noGutter>Test Content</Container>);

    expect(container.firstChild).toMatchSnapshot();
  });
});
