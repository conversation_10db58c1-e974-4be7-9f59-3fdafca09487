import clsx from 'clsx';
import React from 'react';

interface Props {
  children: React.ReactNode;
  className?: string;
  dataTestid?: string;
  maxWidthMd?: string;
  maxWidthSm?: string;
  noGutter?: boolean;
}

export default function Container({
  children,
  className,
  dataTestid,
  maxWidthMd = 'md:max-w-lg',
  maxWidthSm = 'max-w-sm',
  noGutter = false,
}: Props): React.ReactElement {
  return (
    <div
      className={clsx(
        'mx-auto w-full',
        maxWidthSm,
        maxWidthMd,
        {
          'px-4 md:px-6 xl:px-0': !noGutter,
        },
        className,
      )}
      data-testid={dataTestid}
    >
      {children}
    </div>
  );
}
