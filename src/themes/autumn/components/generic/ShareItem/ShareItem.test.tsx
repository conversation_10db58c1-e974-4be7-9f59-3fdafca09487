import { faFacebook } from '@fortawesome/free-brands-svg-icons';
import { render } from '@testing-library/react';

import ShareItem from '.';

describe('<ShareItem />', () => {
  it('renders white icon in blue circle', () => {
    expect.assertions(1);

    const { container } = render(
      <ShareItem
        bgColor="text-blue-500"
        color="text-white"
        href="https://example.com"
        icon={faFacebook}
        label=""
        tag="a"
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
