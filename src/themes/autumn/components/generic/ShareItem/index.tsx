import { faCircle } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import DropDownItem from 'themes/autumn/components/generic/DropDownItem';

import type { IconDefinition } from '@fortawesome/free-solid-svg-icons';

interface BaseProps {
  bgColor: string;
  className?: string;
  color: string;
  icon: IconDefinition;
  inDropDown?: boolean;
  label: string;
  size?: string;
  withBackground?: boolean;
}

interface AnchorProps extends BaseProps {
  href: string;
  tag?: 'a';
}

interface ButtonProps extends BaseProps {
  onClick: () => void;
  tag: 'button';
}

type Props = AnchorProps | ButtonProps;

export default function ShareItem({
  bgColor,
  color,
  icon,
  inDropDown,
  label,
  size = '',
  tag: Tag = 'a',
  withBackground = true,
  ...props
}: Props): React.ReactElement {
  let itemProps = {};

  if (Tag === 'a') {
    itemProps = {
      rel: 'noopener',
      target: '_blank',
    };
  }

  if (Tag === 'button') {
    itemProps = {
      type: 'button',
    };
  }

  const inner = (
    <>
      <span
        className={`fa-stack fa-fw transition-opacity hover:opacity-70 ${size}`}
        data-testid={`share-item-${label.toLowerCase()}`}
      >
        {withBackground && (
          <FontAwesomeIcon
            className={clsx('fa-stack-2x', bgColor)}
            icon={faCircle}
          />
        )}
        <FontAwesomeIcon
          className={`fa-stack-1x ${color}`}
          icon={icon}
          inverse
        />
      </span>
      <span
        className={clsx({
          'pl-2': inDropDown,
          'sr-only': !inDropDown,
        })}
      >
        {label}
      </span>
    </>
  );

  if (!inDropDown) {
    // eslint-disable-next-line react/jsx-props-no-spreading
    return <Tag {...props}>{inner}</Tag>;
  }

  return (
    // eslint-disable-next-line react/jsx-props-no-spreading
    <DropDownItem tag={Tag} {...itemProps} {...props}>
      {inner}
    </DropDownItem>
  );
}
