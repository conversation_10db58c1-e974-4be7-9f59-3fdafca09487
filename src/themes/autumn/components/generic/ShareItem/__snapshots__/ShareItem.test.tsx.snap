// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ShareItem /> renders white icon in blue circle 1`] = `
<a
  href="https://example.com"
>
  <span
    class="fa-stack fa-fw transition-opacity hover:opacity-70 "
    data-testid="share-item-"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-blue-500"}
    </i>
    <i>
      {"icon":{"prefix":"fab","iconName":"facebook"},"className":"fa-stack-1x text-white","inverse":true}
    </i>
  </span>
  <span
    class="sr-only"
  />
</a>
`;
