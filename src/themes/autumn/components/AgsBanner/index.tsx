import { useAppSelector } from 'store/hooks';
import { StoryViewType } from 'types/ZoneItems';
import { sendToGtm } from 'util/gtm';
import { VODCAST_ICON } from 'util/icons';

import Link from '../generic/Link';

export default function AgsBanner({
  wrapperClass,
}: {
  wrapperClass?: string;
}): React.ReactElement | null {
  const displayAgsBanner = useAppSelector(
    (state) => state.conf.displayAgsBanner,
  );
  const viewType = useAppSelector(
    (state) => state.settings.viewType,
  ) as StoryViewType;

  if (viewType !== StoryViewType.STORY || !displayAgsBanner) {
    return null;
  }

  return (
    <div className={wrapperClass}>
      <div className="relative w-full bg-[rgba(60,184,112,0.3)] p-3">
        <div className="flex justify-between">
          <div className="flex grow place-content-center items-center gap-3 text-center">
            <Link
              className="flex cursor-pointer flex-row items-center justify-center gap-3"
              data-testid="agricast-link"
              href="/video/agricast"
              onClick={() => {
                sendToGtm({
                  label: '/video/agricast',
                  trigger: 'agricast_banner_click',
                });
              }}
              target="_self"
            >
              <div className="flex h-8 items-center justify-center">
                {VODCAST_ICON}
              </div>
              <div className="text-xs text-black underline">
                Stay informed with our new vodcast
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
