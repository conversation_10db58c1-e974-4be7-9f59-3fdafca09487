.dm-shorts {
  :global {
    .dm-story .dm-story-card-component .dm-story-card-title {
      overflow: hidden;
      font-family: var(--font-inter);
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 150%;
      z-index: 1;
      bottom: 42px;
    }
    .dm-story-card-created {
      bottom: 20px !important;
    }
    .dm-story-card-component .dm-story-card-created {
      z-index: 1;
    }
    .dm-story .dm-story-card-play {
      z-index: 1;
      background: #fff !important;
      opacity: 0.7;
      width: 48px !important;
    }
    .dm-story .dm-story-card-play path {
      transform: scale(0.7) translate(6px, 5px);
      -ms-transform: scale(0.7) translate(6px, 5px);
      -webkit-transform: scale(0.7) translate(6px, 5px);
      fill: black !important;
      opacity: 0.7;
    }
    .dm-story .splide__arrow--next {
      right: -20px;
    }
    .splide__arrow--prev {
      left: -20px;
    }
    .splide__arrow:disabled {
      opacity: 0;
    }
    .splide__arrow {
      background-color: #fff;
      opacity: 1;
      border: 1px solid #cccccc;
    }
    .splide__arrow svg {
      fill: #111827;
      height: 16px;
      width: 16px;
    }
    .splide__arrow {
      height: 40px;
      width: 40px;
    }
    .dm-story .dm-story-card-component {
      margin-right: 12px;
      border-radius: 8px;
    }

    .dm-story .dm-preview-holder {
      background: linear-gradient(
        0deg,
        rgba(0, 0, 0, 0.75) 0%,
        rgba(0, 0, 0, 0) 100%
      );
    }

    .dm-story .dm-preview-holder.darker-filter {
      background: none;
    }

    .full-screen {
      li {
        --dm-item-height: 470px;
      }
    }

    .zone-item {
      li {
        --dm-item-height: 424px;
      }
    }

    .dm-ad-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 100;
    }

    .dm-ad-wrapper > div {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    @media only screen and (max-width: 444px) {
      .splide__arrow--next,
      .splide__arrow--prev {
        display: none;
      }
      .dm-ad-wrapper {
        align-items: baseline;
      }
      .dm-ad-wrapper > div {
        align-items: baseline;
        margin-top: 20px;
      }
    }
  }
}
