'use client';

import clsx from 'clsx';
import Script from 'next/script';
import { useEffect, useRef, useState } from 'react';

import AdProvider from 'themes/autumn/components/ads/AdProvider';
import { AdSize, Viewport } from 'util/ads';
import { encodeDmCustomConfig } from 'util/string';

import styles from './styles.module.css';

export interface CtaCard {
  cta_card: {
    link: string;
    text: string;
  };
}

export interface VideoShortsProps {
  adFrequency: number;
  adKey?: string;
  className?: string;
  ctaRecord?: Record<string, CtaCard>;
  numberOfVideo: number;
  playerId: string;
  playlistId: string;
}

interface DmStoryProps {
  customConfig?: string;
  numOfVideos: number;
  playerId: string;
  playlistId: string;
}

function generateAd(slotId: string) {
  const store = window.getStore();
  const { adServing } = store.getState().features;

  if (!adServing.enabled) {
    return;
  }

  AdProvider.createAd({
    publiftName: 'shorts-1',
    sizes: {
      [Viewport.SM]: [AdSize.halfPage, AdSize.mrec],
    },
    slotId,
    targetingArguments: {
      videoshorts: 'true',
    },
  });
}

function VideoShorts({
  adFrequency,
  adKey,
  className = 'wrapper',
  ctaRecord,
  numberOfVideo,
  playerId,
  playlistId,
}: VideoShortsProps): React.ReactElement | null {
  const [loading, setLoading] = useState(true);
  const ref = useRef<HTMLDivElement>(null);
  const limit = numberOfVideo || 20;

  useEffect(() => {
    const enterListener = () => {
      window.document
        .querySelectorAll('.dm-preview-holder')
        .forEach((holder) => {
          holder.classList.add('darker-filter');
        });
    };
    document.addEventListener('dm-story-enter-fullscreen', enterListener);

    const exitListener = () => {
      window.document
        .querySelectorAll('.dm-preview-holder')
        .forEach((holder) => {
          holder.classList.remove('darker-filter');
        });
    };
    document.addEventListener('dm-story-exit-fullscreen', exitListener);

    window.DailymotionStory = window.DailymotionStory || {};
    window.DailymotionStory.interstitialAds = {
      // set condition for slide index
      condition: (index) => adFrequency > 0 && index % adFrequency === 0,
      /**
       * @description if condition satisfied, story will create
       * adContainer then call this function
       *
       * @param {HTMLElement} adContainer a htmlElement object for ads
       */
      onCondition: (adContainer: HTMLElement) => {
        if (adFrequency > 0) {
          const slotId = 'video-shorts';
          // eslint-disable-next-line no-param-reassign
          adContainer.innerHTML = `<div id="${slotId}"></div>`;
          generateAd(slotId);
        }
      },
    };

    return () => {
      document.removeEventListener('dm-story-enter-fullscreen', enterListener);
      document.removeEventListener('dm-story-exit-fullscreen', exitListener);
    };
  }, [adFrequency]);

  useEffect(() => {
    if (!ref.current) {
      return undefined;
    }

    const observer = new MutationObserver(() => {
      const active = ref.current?.querySelector('.is-active');
      if (active) {
        setLoading(false);
        observer.disconnect();
      }
    });

    observer.observe(ref.current, {
      attributes: true,
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  if (playerId.trim() === '' || playlistId.trim() === '') {
    return null;
  }

  const props: DmStoryProps = {
    numOfVideos: limit,
    playerId,
    playlistId,
  };

  if (adKey) {
    const customConfig: Record<string, string> = {
      customParams: adKey,
    };
    props.customConfig = encodeDmCustomConfig(customConfig);
  }

  return (
    <div
      className={clsx('dm-shorts', {
        'my-3 min-h-[430px] rounded-lg bg-gray-100 md:min-h-[450px]': loading,
      })}
      ref={ref}
    >
      {ctaRecord && (
        <Script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(ctaRecord),
          }}
          id="dm_story_text"
          type="application/ld+json"
        />
      )}
      <div className={clsx('pb-5.5 pt-3', styles['dm-shorts'])}>
        <div
          className={clsx('dm-story', className)}
          /* eslint-disable-next-line react/jsx-props-no-spreading */
          {...props}
        />
        <Script
          async
          src="https://srvr.dmvs-apac.com/dm-story/dm-story.min.js"
        />
      </div>
    </div>
  );
}

export default VideoShorts;
