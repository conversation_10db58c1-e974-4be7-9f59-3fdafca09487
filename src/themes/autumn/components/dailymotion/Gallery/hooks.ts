/* eslint-disable import/prefer-default-export */

'use client';

import { useCallback, useEffect, useState } from 'react';

import type { ApiResponse, Video } from './types';

export function useDailymotionPlaylist(
  playlistId: string | undefined,
  numberOfVideo: number,
) {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const fields = 'id,title,thumbnail_480_url,duration';

  const fetchVideosFromPlaylist = useCallback(
    async (page: number = 1, append: boolean = false): Promise<boolean> => {
      setLoading(true);
      try {
        const response = await fetch(
          `https://api.dailymotion.com/playlist/${playlistId}/videos?` +
            `fields=${fields}&limit=${numberOfVideo}&page=${page}`,
        );

        if (!response.ok) {
          throw new Error('Failed to fetch playlist videos');
        }

        const data = (await response.json()) as ApiResponse;

        if (append) {
          setVideos((prev) => [...prev, ...(data.list || [])]);
        } else {
          setVideos(data.list || []);
        }

        setHasMore(data.has_more || false);
        return true;
      } catch (error) {
        console.error('Error fetching playlist videos:', error);
        if (!append) {
          setVideos([]);
        }
        return false;
      } finally {
        setLoading(false);
      }
    },
    [playlistId, numberOfVideo],
  );

  const loadMore = useCallback(async () => {
    if (playlistId) {
      const nextPage = currentPage + 1;
      const success = await fetchVideosFromPlaylist(nextPage, true);
      if (success) {
        setCurrentPage(nextPage);
      }
    }
  }, [currentPage, playlistId, fetchVideosFromPlaylist]);

  // Initialize - fetch from playlist
  useEffect(() => {
    if (playlistId) {
      fetchVideosFromPlaylist(1, false).catch(() => {});
    }
  }, [fetchVideosFromPlaylist, playlistId]);

  return {
    hasMore,
    loadMore,
    loading,
    videos,
  };
}
