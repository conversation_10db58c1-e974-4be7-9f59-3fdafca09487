import { fireEvent, render, screen, waitFor } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import GalleryPresentation from './GalleryPresentation';

import type { Video } from './types';

// Mock Next.js Script component - avoid using React hooks in mock
jest.mock('next/script', () => ({
  __esModule: true,
  default: ({ onLoad }: { onLoad: () => void }) => {
    // Call onLoad immediately to simulate script loading
    setTimeout(() => {
      onLoad();
    }, 0);
    return null;
  },
}));

// Mock Dailymotion player
const mockPlayer = {
  getRootNode: jest.fn(),
  getSettings: jest.fn(),
  getState: jest.fn(),
  loadContent: jest.fn(),
  off: jest.fn(),
  on: jest.fn(),
  pause: jest.fn(),
  play: jest.fn(),
  seek: jest.fn(),
  setAspectRatio: jest.fn(),
  setFullScreen: jest.fn(),
  setLoop: jest.fn(),
  setMute: jest.fn(),
  setQuality: jest.fn(),
  setScaleMode: jest.fn(),
  setSubtitles: jest.fn(),
  setVolume: jest.fn(),
};

// Mock complete DailymotionAPI interface
const mockDailymotionAPI: DailymotionAPI = {
  createPlayer: jest.fn().mockResolvedValue(mockPlayer),
  events: {},
  getAllPlayers: jest.fn(),
  getPlayer: jest.fn(),
  pipClose: jest.fn(),
  pipCollapse: jest.fn(),
  pipExpand: jest.fn(),
  pipResume: jest.fn(),
};

// Extend global Window interface for testing
declare global {
  interface Window {
    dailymotion: DailymotionAPI;
  }
}

const mockVideos: Video[] = [
  {
    created_time: 1640995200,
    duration: 120,
    id: 'x1234567',
    thumbnail_480_url: 'https://example.com/thumb1.jpg',
    title: 'Test Video 1',
  },
  {
    created_time: 1640995300,
    duration: 180,
    id: 'x2345678',
    thumbnail_480_url: 'https://example.com/thumb2.jpg',
    title: 'Test Video 2',
  },
  {
    created_time: 1640995400,
    duration: 240,
    id: 'x3456789',
    thumbnail_480_url: 'https://example.com/thumb3.jpg',
    title: 'Test Video 3',
  },
  {
    created_time: 1640995500,
    duration: 300,
    id: 'x4567890',
    thumbnail_480_url: 'https://example.com/thumb4.jpg',
    title: 'Test Video 4',
  },
];

describe('GalleryPresentation', () => {
  const defaultProps = {
    aspectRatio: '3/4',
    hasMore: false,
    loading: false,
    onLoadMore: jest.fn(),
    playerId: 'x1bsog',
    videos: mockVideos,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    window.dailymotion = mockDailymotionAPI;
  });

  describe('Rendering', () => {
    it('renders gallery with default props', () => {
      const { container } = render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders gallery with custom aspect ratio', () => {
      const { container } = render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio="16/9"
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders loading state', () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={[]}
          />
        </TestWrapper>,
      );
      expect(screen.getByText('Loading Local Legends...')).toBeInTheDocument();
    });

    it('renders empty state when no videos and not loading', () => {
      const { container } = render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={false}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={[]}
          />
        </TestWrapper>,
      );
      expect(container.querySelector('ul')).toBeEmptyDOMElement();
    });

    it('displays correct number of videos', () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );
      const videos = screen.getAllByRole('button', { name: /Duration:/ });
      expect(videos).toHaveLength(4);
    });

    it('formats duration correctly', () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );
      expect(screen.getByText('02:00')).toBeInTheDocument();
      expect(screen.getByText('03:00')).toBeInTheDocument();
      expect(screen.getByText('04:00')).toBeInTheDocument();
      expect(screen.getByText('05:00')).toBeInTheDocument();
    });

    it('renders "More {galleryTitle}" button when hasMore is true', () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );
      expect(
        screen.getByRole('button', { name: 'Show more videos' }),
      ).toBeInTheDocument();
    });

    // eslint-disable-next-line @stylistic/max-len
    it('does not render "More {galleryTitle}" button when hasMore is false', () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={false}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );
      expect(
        screen.queryByRole('button', { name: 'Show more videos' }),
      ).not.toBeInTheDocument();
    });

    it('disables load more button when loading', () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore
            loading
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );
      const button = screen.getByRole('button', {
        name: 'Show more videos',
      });
      expect(button).toBeDisabled();
      expect(button.textContent).toBe('Loading...');
    });
  });

  describe('User Interactions', () => {
    it('calls onLoadMore when "More {galleryTitle}" is clicked', () => {
      const onLoadMore = jest.fn();
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
            accessToken: 'test token',
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore
            loading={defaultProps.loading}
            onLoadMore={onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const loadMoreButton = screen.getByRole('button', {
        name: 'Show more videos',
      });
      fireEvent.click(loadMoreButton);

      expect(onLoadMore).toHaveBeenCalledTimes(1);
    });

    it('opens modal when video is clicked', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(
          screen.getByRole('button', { name: 'Close video' }),
        ).toBeInTheDocument();
      });
    });

    it('creates player when modal opens', async () => {
      const createPlayerSpy = jest.spyOn(window.dailymotion, 'createPlayer');

      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(createPlayerSpy).toHaveBeenCalledWith('dailyPlayer', {
          params: {
            mute: false,
            syndicationKey: '',
          },
          video: 'x1234567',
        });
      });
    });

    it('creates player with adConfig when modal opens', async () => {
      const createPlayerSpy = jest.spyOn(window.dailymotion, 'createPlayer');

      render(
        <TestWrapper
          store={createStore(() => ({
            conf: {
              googleAdManagerNetworkIdentifier: 'networkIdentifier',
            },
            features: {
              adServing: {
                data: {
                  doubleClickSite: 'doubleClickSite',
                },
                enabled: true,
              },
            },
            settings: {
              viewType: 'viewType',
            },
            story: {
              id: 'storyId',
            },
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            includeAdConfig
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(createPlayerSpy).toHaveBeenCalledWith('dailyPlayer', {
          params: {
            customConfig: {
              dynamiciu: '/networkIdentifier/doubleClickSite',
              keyvalues: 'ctype=viewType&pageid=storyId',
            },
            mute: false,
            syndicationKey: '',
          },
          video: 'x1234567',
        } satisfies DMPlayerOptions);
      });
    });

    it('closes modal when close button is clicked', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      const closeButton = screen.getByRole('button', { name: 'Close video' });
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('closes modal when clicking overlay', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      const overlay = screen.getByRole('presentation');
      fireEvent.click(overlay);

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('closes modal on Escape key', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      fireEvent.keyDown(document, { key: 'Escape' });

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('pauses video when modal closes', async () => {
      // Create a fresh mock player for this test
      const testPlayer = {
        ...mockPlayer,
        pause: jest.fn(),
        play: jest.fn(),
      };

      // Mock createPlayer to return our test player
      // eslint-disable-next-line @stylistic/max-len
      (window.dailymotion.createPlayer as jest.Mock).mockResolvedValue(
        testPlayer,
      );

      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        // eslint-disable-next-line @typescript-eslint/unbound-method
        expect(window.dailymotion.createPlayer).toHaveBeenCalled();
      });

      await waitFor(() => {
        expect(testPlayer.play).toHaveBeenCalled();
      });

      const closeButton = screen.getByRole('button', { name: 'Close video' });
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(testPlayer.pause).toHaveBeenCalled();
      });
    });

    it('switches video content when different video is selected', async () => {
      // Create a fresh mock player for this test
      const testPlayer = {
        ...mockPlayer,
        loadContent: jest.fn(),
        play: jest.fn(),
      };

      // Mock createPlayer to return our test player
      // eslint-disable-next-line @stylistic/max-len
      (window.dailymotion.createPlayer as jest.Mock).mockResolvedValue(
        testPlayer,
      );

      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      // Open modal with first video
      const videos = screen.getAllByRole('button', { name: /Duration:/ });
      fireEvent.click(videos[0]);

      await waitFor(() => {
        // eslint-disable-next-line @typescript-eslint/unbound-method
        expect(window.dailymotion.createPlayer).toHaveBeenCalledWith(
          'dailyPlayer',
          expect.objectContaining({
            video: 'x1234567',
          }),
        );
      });

      // Close modal
      fireEvent.click(screen.getByRole('button', { name: 'Close video' }));

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });

      // Reset mocks but keep our test player
      jest.clearAllMocks();
      // eslint-disable-next-line @stylistic/max-len
      (window.dailymotion.createPlayer as jest.Mock).mockResolvedValue(
        testPlayer,
      );

      // Open modal with second video
      fireEvent.click(videos[1]);

      await waitFor(() => {
        expect(testPlayer.loadContent).toHaveBeenCalledWith({
          video: 'x2345678',
        });
      });
    });
  });

  describe('Modal Behavior', () => {
    it('prevents body scroll when modal is open', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      expect(document.body.style.overflow).toBe('');

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(document.body.style.overflow).toBe('hidden');
      });

      const closeButton = screen.getByRole('button', { name: 'Close video' });
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(document.body.style.overflow).toBe('');
      });
    });

    it('stops propagation when clicking inside modal', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      const modalContent = screen.getByRole('dialog');
      const clickEvent = new MouseEvent('click', { bubbles: true });
      const stopPropagationSpy = jest.spyOn(clickEvent, 'stopPropagation');

      fireEvent(modalContent, clickEvent);

      expect(stopPropagationSpy).toHaveBeenCalled();
    });

    it('focuses modal when opened', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        const modal = screen.getByRole('dialog');
        expect(modal).toHaveFocus();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes on modal', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        const modal = screen.getByRole('dialog');
        expect(modal).toHaveAttribute('aria-modal', 'true');
        expect(modal).toHaveAttribute('tabIndex', '-1');
      });
    });

    it('has proper ARIA label on close button', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        const closeButton = screen.getByRole('button', {
          name: 'Close video',
        });
        expect(closeButton).toHaveAttribute('aria-label', 'Close video');
      });
    });

    it('has proper alt text on thumbnails', () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const images = screen.getAllByRole('img');
      expect(images[0]).toHaveAttribute(
        'alt',
        'Test Video 1. Duration: 02:00',
      );
      expect(images[1]).toHaveAttribute(
        'alt',
        'Test Video 2. Duration: 03:00',
      );
    });
  });

  describe('Responsive Behavior', () => {
    it('renders with correct aspect ratio from props', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio="16/9"
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      const playerContainer =
        document.getElementById('dailyPlayer')?.parentElement;
      expect(playerContainer).toBeInTheDocument();

      // Check the inline style object
      const style = playerContainer
        ? window.getComputedStyle(playerContainer)
        : null;
      expect(style?.aspectRatio || playerContainer?.style.aspectRatio).toBe(
        '16/9',
      );
    });

    it('uses default aspect ratio when not provided', async () => {
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
          }))}
        >
          <GalleryPresentation
            aspectRatio={defaultProps.aspectRatio}
            hasMore={defaultProps.hasMore}
            loading={defaultProps.loading}
            onLoadMore={defaultProps.onLoadMore}
            playerId={defaultProps.playerId}
            videos={defaultProps.videos}
          />
        </TestWrapper>,
      );

      const firstVideo = screen.getAllByRole('button', {
        name: /Duration:/,
      })[0];
      fireEvent.click(firstVideo);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      const playerContainer =
        document.getElementById('dailyPlayer')?.parentElement;
      expect(playerContainer).toBeInTheDocument();

      // Check the inline style object
      const style = playerContainer
        ? window.getComputedStyle(playerContainer)
        : null;
      expect(style?.aspectRatio || playerContainer?.style.aspectRatio).toBe(
        '3/4',
      );
    });
  });
});
