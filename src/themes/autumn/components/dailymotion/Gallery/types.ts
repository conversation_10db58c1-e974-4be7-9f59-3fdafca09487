export interface Video {
  created_time: number;
  duration: number;
  id: string;
  thumbnail_480_url: string;
  title: string;
}

export interface DailymotionGalleryProps {
  aspectRatio?: string;
  galleryTitle?: string;
  includeAdConfig?: boolean;
  numberOfVideo?: number;
  playerId?: string;
  playlistId?: string;
  showDuration?: boolean;
  showTitle?: boolean;
}

export interface GalleryPresentationProps {
  aspectRatio?: string;
  galleryTitle?: string;
  hasMore: boolean;
  includeAdConfig?: boolean;
  loading: boolean;
  onLoadMore: () => void;
  playerId?: string;
  showDuration?: boolean;
  showTitle?: boolean;
  videos: Video[];
}

export interface ModalProps {
  aspectRatio: string;
  isOpen: boolean;
  onClose: () => void;
  playerContainerRef: React.RefObject<HTMLDivElement | null>;
  selectedVideo: Video | null;
}

export interface ApiResponse {
  has_more: boolean;
  list: Video[];
  total: number;
}
