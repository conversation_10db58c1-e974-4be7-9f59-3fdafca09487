'use client';

import clsx from 'clsx';
import Script from 'next/script';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { useDailymotionAdConfig } from '../../storyElements/services/Dailymotion/hooks';

import type { GalleryPresentationProps, ModalProps, Video } from './types';

function VideoModal({
  aspectRatio,
  isOpen,
  onClose,
  playerContainerRef,
  selectedVideo,
}: ModalProps): React.ReactElement | null {
  const modalContentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen && modalContentRef.current) {
      modalContentRef.current.focus();
    }
  }, [isOpen]);

  if (!isOpen || !selectedVideo) {
    return null;
  }

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/85 p-5"
      onClick={onClose}
      role="presentation"
    >
      {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-noninteractive-element-interactions */}
      <div
        aria-modal="true"
        className="relative flex max-h-[90vh] w-auto max-w-[90vw] flex-col overflow-hidden rounded-3xl bg-black shadow-md"
        onClick={(e) => e.stopPropagation()}
        ref={modalContentRef}
        role="dialog"
        tabIndex={-1}
      >
        <button
          aria-label="Close video"
          className="absolute right-4 top-4 z-10 flex size-10 cursor-pointer items-center justify-center rounded-full border-none bg-black/60 text-3xl text-white transition-all duration-200 ease-in-out"
          onClick={onClose}
          type="button"
        >
          ×
        </button>
        {/* modal video wrapper */}
        <div
          className="relative mx-auto h-[80vh] w-full max-w-[80vh] bg-black [&>div]:absolute [&>div]:inset-0 [&>div]:size-full [&_iframe]:!h-full [&_iframe]:!w-full"
          style={{ aspectRatio }}
        >
          <div id="dailyPlayer" ref={playerContainerRef} />
        </div>
      </div>
    </div>
  );
}

// Format duration from seconds to MM:SS
function formatDuration(seconds: number): string {
  const date = new Date(seconds * 1000);
  return date.toISOString().substring(14, 19);
}

function VideoGrid({
  aspectRatio = '3/4',
  onVideoClick,
  showDuration = true,
  showTitle = false,
  videos,
}: {
  aspectRatio: string;
  onVideoClick: (video: Video) => void;
  showDuration: boolean;
  showTitle: boolean;
  videos: Video[];
}): React.ReactElement {
  return (
    <ul
      className={clsx('m-0 grid grid-cols-1 gap-5 p-0', {
        'md:grid-cols-2': aspectRatio === '16/9',
        'min-[375px]:grid-cols-2 sm:grid-cols-3 lg:grid-cols-4':
          aspectRatio === '3/4' || aspectRatio !== '16/9',
      })}
    >
      {videos.map((video) => (
        <li
          className="w-full cursor-pointer overflow-hidden rounded-2xl bg-transparent"
          key={video.id}
        >
          {/* video button */}
          <button
            className="m-0 block w-full cursor-pointer border-none bg-transparent p-0 text-left"
            onClick={() => onVideoClick(video)}
            type="button"
          >
            {/* thumbnail wrapper */}
            <div
              className="relative w-full overflow-hidden rounded-2xl"
              style={{ aspectRatio }}
            >
              {/* thumbnail image */}
              <img
                alt={`${video.title}. Duration: ${formatDuration(
                  video.duration,
                )}`}
                className="absolute left-0 top-0 size-full object-cover object-top transition-transform duration-200"
                loading="lazy"
                src={video.thumbnail_480_url}
              />
              <div
                className={clsx(
                  'absolute left-0 top-0 flex size-full items-end justify-between bg-gradient-to-t to-transparent p-3 md:p-4',
                  {
                    'from-black/35': aspectRatio === '16/9',
                    'from-black/75':
                      aspectRatio === '3/4' || aspectRatio !== '16/9',
                  },
                )}
              >
                {/* Play icon */}
                <div className="flex aspect-square size-12 items-center justify-center rounded-full bg-white opacity-70">
                  <svg
                    className="size-8 fill-black opacity-50"
                    viewBox="0 0 24 24"
                  >
                    <path d="M8 5v14l11-7z" />
                  </svg>
                </div>
                {/* Title */}
                {showTitle && (
                  <span
                    className="ml-3 flex items-center place-self-end text-sm font-semibold text-white md:text-base"
                    style={{
                      textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)',
                    }}
                  >
                    {video.title}
                  </span>
                )}
                {/* Duration */}
                {/* TODO: Ask UI/UX for adjustments if 
                    showTitle and showDuration must both be true */}
                {showDuration && (
                  <span
                    className="flex h-8 items-center text-sm font-semibold text-white"
                    style={{
                      textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)',
                    }}
                  >
                    {formatDuration(video.duration)}
                  </span>
                )}
              </div>
            </div>
          </button>
        </li>
      ))}
    </ul>
  );
}

function GalleryPresentation({
  aspectRatio = '3/4',
  galleryTitle = 'Local Legends',
  hasMore,
  includeAdConfig = false,
  loading,
  onLoadMore,
  playerId,
  showDuration = true,
  showTitle = false,
  videos,
}: GalleryPresentationProps): React.ReactElement {
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [playerReady, setPlayerReady] = useState(false);
  const playerRef = useRef<DMPlayer | null>(null);
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const dailymotionAdConfig = useDailymotionAdConfig();

  const handleVideoClick = useCallback(
    (video: Video) => {
      setSelectedVideo(video);
      setIsModalOpen(true);

      setTimeout(() => {
        if (playerRef.current && playerReady) {
          playerRef.current.loadContent({ video: video.id });
          playerRef.current.play();
        } else if (window.dailymotion && playerContainerRef.current) {
          // Create player if it doesn't exist
          window.dailymotion
            .createPlayer('dailyPlayer', {
              params: {
                ...(includeAdConfig &&
                  dailymotionAdConfig && {
                    customConfig: dailymotionAdConfig,
                  }),
                mute: false,
                syndicationKey: '',
              },
              video: video.id,
            })
            .then((player) => {
              playerRef.current = player;
              player.play();
            })
            .catch((error: unknown) => {
              console.error('Error creating player during video click', error);
            });
        }
      }, 100);
    },
    [playerReady, dailymotionAdConfig, includeAdConfig],
  );

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    if (playerRef.current) {
      playerRef.current.pause();
    }
  }, []);

  // Initialize player for modal
  useEffect(() => {
    if (
      isModalOpen &&
      selectedVideo &&
      playerReady &&
      !playerRef.current &&
      playerContainerRef.current
    ) {
      window.dailymotion
        .createPlayer('dailyPlayer', {
          params: {
            ...(includeAdConfig &&
              dailymotionAdConfig && {
                customConfig: dailymotionAdConfig,
              }),
            mute: false,
            syndicationKey: '',
          },
          video: selectedVideo.id,
        })
        .then((player) => {
          playerRef.current = player;
        })
        .catch((error: unknown) => {
          console.error('Error creating player during modal open', error);
        });
    }
  }, [
    isModalOpen,
    selectedVideo,
    playerReady,
    includeAdConfig,
    dailymotionAdConfig,
  ]);

  // Clean up player when modal closes
  useEffect(() => {
    if (!isModalOpen && playerRef.current) {
      // DMPlayer doesn't have destroy, just clear the reference
      playerRef.current = null;
    }
  }, [isModalOpen]);

  return (
    <>
      {playerId && (
        <Script
          onLoad={() => setPlayerReady(true)}
          src={`https://geo.dailymotion.com/libs/player/${playerId}.js`}
          strategy="afterInteractive"
        />
      )}

      <div>
        {/* Video Modal */}
        <VideoModal
          aspectRatio={aspectRatio}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          playerContainerRef={playerContainerRef}
          selectedVideo={selectedVideo}
        />

        {/* Playlist Section */}
        <section className="m-auto flex flex-col gap-5 pt-6">
          {/* Videos Grid */}
          {loading && videos.length === 0 ? (
            <div className="py-8 text-center">Loading {galleryTitle}...</div>
          ) : (
            <>
              <VideoGrid
                aspectRatio={aspectRatio}
                onVideoClick={handleVideoClick}
                showDuration={showDuration}
                showTitle={showTitle}
                videos={videos}
              />

              {/* See More Button */}
              {hasMore && (
                <div className="text-center">
                  <button
                    aria-label="Show more videos"
                    className="cursor-pointer rounded-2xl border border-gray-900 px-10 py-3 text-base font-semibold text-black shadow-sm transition-all duration-300 ease-in-out disabled:cursor-not-allowed disabled:opacity-60"
                    disabled={loading}
                    onClick={onLoadMore}
                    type="button"
                  >
                    {loading ? 'Loading...' : `More ${galleryTitle}`}
                  </button>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </>
  );
}

export default GalleryPresentation;
