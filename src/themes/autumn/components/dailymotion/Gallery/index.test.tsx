import { render, renderHook, screen, waitFor } from '@testing-library/react';

import { useDailymotionPlaylist } from './hooks';

import DailymotionGallery from '.';

import type { ApiResponse, GalleryPresentationProps, Video } from './types';

// Mock the presentational component
jest.mock('./GalleryPresentation', () => ({
  __esModule: true,
  default: jest.fn(
    ({ hasMore, loading, onLoadMore, videos }: GalleryPresentationProps) => (
      <div data-testid="gallery-presentation">
        {loading && videos.length === 0 && <div>Loading...</div>}
        {videos.map((video: Video) => (
          <div data-testid={`video-${video.id}`} key={video.id}>
            {video.title}
          </div>
        ))}
        {hasMore && (
          <button onClick={onLoadMore} type="button">
            Load More
          </button>
        )}
      </div>
    ),
  ),
}));

// Mock fetch
global.fetch = jest.fn();

const mockVideos: Video[] = [
  {
    created_time: 1640995200,
    duration: 120,
    id: 'x1234567',
    thumbnail_480_url: 'https://example.com/thumb1.jpg',
    title: 'Test Video 1',
  },
  {
    created_time: 1640995300,
    duration: 180,
    id: 'x2345678',
    thumbnail_480_url: 'https://example.com/thumb2.jpg',
    title: 'Test Video 2',
  },
  {
    created_time: 1640995400,
    duration: 240,
    id: 'x3456789',
    thumbnail_480_url: 'https://example.com/thumb3.jpg',
    title: 'Test Video 3',
  },
  {
    created_time: 1640995500,
    duration: 300,
    id: 'x4567890',
    thumbnail_480_url: 'https://example.com/thumb4.jpg',
    title: 'Test Video 4',
  },
];

const mockApiResponse: ApiResponse = {
  has_more: true,
  list: mockVideos,
  total: 10,
};

describe('useDailymotionPlaylist', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      json: jest.fn().mockResolvedValue(mockApiResponse),
      ok: true,
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('fetches videos on mount when playlistId is provided', async () => {
    const { result } = renderHook(() => useDailymotionPlaylist('x7xqvs', 4));

    expect(result.current.loading).toBe(true);
    expect(result.current.videos).toEqual([]);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.videos).toEqual(mockVideos);
      expect(result.current.hasMore).toBe(true);
    });

    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('playlist/x7xqvs/videos'),
    );
  });

  it('does not fetch when playlistId is not provided', () => {
    renderHook(() => useDailymotionPlaylist(undefined, 4));

    expect(fetch).not.toHaveBeenCalled();
  });

  it('loads more videos when loadMore is called', async () => {
    const { result } = renderHook(() => useDailymotionPlaylist('x7xqvs', 4));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    const moreVideos: Video[] = [
      {
        created_time: 1640995600,
        duration: 150,
        id: 'x5678901',
        thumbnail_480_url: 'https://example.com/thumb5.jpg',
        title: 'Test Video 5',
      },
    ];

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      json: jest.fn().mockResolvedValue({
        has_more: false,
        list: moreVideos,
        total: 5,
      }),
      ok: true,
    });

    await result.current.loadMore();

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.videos).toHaveLength(5);
      expect(result.current.hasMore).toBe(false);
    });

    expect(fetch).toHaveBeenCalledTimes(2);
    expect(fetch).toHaveBeenNthCalledWith(
      2,
      expect.stringContaining('page=2'),
    );
  });

  it('handles fetch errors gracefully', async () => {
    const consoleError = jest
      .spyOn(console, 'error')
      .mockImplementation(() => {});

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
    });

    const { result } = renderHook(() => useDailymotionPlaylist('x7xqvs', 4));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.videos).toEqual([]);
    });

    expect(consoleError).toHaveBeenCalledWith(
      'Error fetching playlist videos:',
      expect.any(Error),
    );

    consoleError.mockRestore();
  });

  it('uses correct number of videos in API call', async () => {
    renderHook(() => useDailymotionPlaylist('x7xqvs', 8));

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(expect.stringContaining('limit=8'));
    });
  });
});

describe('DailymotionGallery Container', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      json: jest.fn().mockResolvedValue(mockApiResponse),
      ok: true,
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders with default props', async () => {
    render(<DailymotionGallery playerId="x1bsog" playlistId="x7xqvs" />);

    expect(screen.getByTestId('gallery-presentation')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
      mockVideos.forEach((video) => {
        expect(screen.getByTestId(`video-${video.id}`)).toBeInTheDocument();
      });
    });
  });

  it('does not fetch videos when playlistId is not provided', () => {
    render(<DailymotionGallery playerId="x1bsog" />);

    expect(fetch).not.toHaveBeenCalled();
    expect(screen.getByTestId('gallery-presentation')).toBeInTheDocument();
  });
});
