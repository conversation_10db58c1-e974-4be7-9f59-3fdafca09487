'use client';

import GalleryPresentation from './GalleryPresentation';
import { useDailymotionPlaylist } from './hooks';

import type { DailymotionGalleryProps } from './types';

function DailymotionGallery({
  aspectRatio = '3/4',
  galleryTitle = 'Local Legends',
  includeAdConfig = false,
  numberOfVideo = 4,
  playerId,
  playlistId,
  showDuration = true,
  showTitle = false,
}: DailymotionGalleryProps): React.ReactElement {
  const { hasMore, loadMore, loading, videos } = useDailymotionPlaylist(
    playlistId,
    numberOfVideo,
  );

  return (
    <GalleryPresentation
      aspectRatio={aspectRatio}
      galleryTitle={galleryTitle}
      hasMore={hasMore}
      includeAdConfig={includeAdConfig}
      loading={loading}
      onLoadMore={loadMore}
      playerId={playerId}
      showDuration={showDuration}
      showTitle={showTitle}
      videos={videos}
    />
  );
}

export default DailymotionGallery;
export { default as GalleryPresentation } from './GalleryPresentation';
export type { ApiResponse, DailymotionGalleryProps, Video } from './types';
