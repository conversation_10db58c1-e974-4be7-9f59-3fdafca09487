// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`GalleryPresentation Rendering renders gallery with custom aspect ratio 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div>
    <section
      class="m-auto flex flex-col gap-5 pt-6"
    >
      <ul
        class="m-0 grid grid-cols-1 gap-5 p-0 md:grid-cols-2"
      >
        <li
          class="w-full cursor-pointer overflow-hidden rounded-2xl bg-transparent"
        >
          <button
            class="m-0 block w-full cursor-pointer border-none bg-transparent p-0 text-left"
            type="button"
          >
            <div
              class="relative w-full overflow-hidden rounded-2xl"
            >
              <img
                alt="Test Video 1. Duration: 02:00"
                class="absolute left-0 top-0 size-full object-cover object-top transition-transform duration-200"
                loading="lazy"
                src="https://example.com/thumb1.jpg"
              />
              <div
                class="absolute left-0 top-0 flex size-full items-end justify-between bg-gradient-to-t to-transparent p-3 md:p-4 from-black/35"
              >
                <div
                  class="flex aspect-square size-12 items-center justify-center rounded-full bg-white opacity-70"
                >
                  <svg
                    class="size-8 fill-black opacity-50"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M8 5v14l11-7z"
                    />
                  </svg>
                </div>
                <span
                  class="flex h-8 items-center text-sm font-semibold text-white"
                  style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);"
                >
                  02:00
                </span>
              </div>
            </div>
          </button>
        </li>
        <li
          class="w-full cursor-pointer overflow-hidden rounded-2xl bg-transparent"
        >
          <button
            class="m-0 block w-full cursor-pointer border-none bg-transparent p-0 text-left"
            type="button"
          >
            <div
              class="relative w-full overflow-hidden rounded-2xl"
            >
              <img
                alt="Test Video 2. Duration: 03:00"
                class="absolute left-0 top-0 size-full object-cover object-top transition-transform duration-200"
                loading="lazy"
                src="https://example.com/thumb2.jpg"
              />
              <div
                class="absolute left-0 top-0 flex size-full items-end justify-between bg-gradient-to-t to-transparent p-3 md:p-4 from-black/35"
              >
                <div
                  class="flex aspect-square size-12 items-center justify-center rounded-full bg-white opacity-70"
                >
                  <svg
                    class="size-8 fill-black opacity-50"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M8 5v14l11-7z"
                    />
                  </svg>
                </div>
                <span
                  class="flex h-8 items-center text-sm font-semibold text-white"
                  style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);"
                >
                  03:00
                </span>
              </div>
            </div>
          </button>
        </li>
        <li
          class="w-full cursor-pointer overflow-hidden rounded-2xl bg-transparent"
        >
          <button
            class="m-0 block w-full cursor-pointer border-none bg-transparent p-0 text-left"
            type="button"
          >
            <div
              class="relative w-full overflow-hidden rounded-2xl"
            >
              <img
                alt="Test Video 3. Duration: 04:00"
                class="absolute left-0 top-0 size-full object-cover object-top transition-transform duration-200"
                loading="lazy"
                src="https://example.com/thumb3.jpg"
              />
              <div
                class="absolute left-0 top-0 flex size-full items-end justify-between bg-gradient-to-t to-transparent p-3 md:p-4 from-black/35"
              >
                <div
                  class="flex aspect-square size-12 items-center justify-center rounded-full bg-white opacity-70"
                >
                  <svg
                    class="size-8 fill-black opacity-50"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M8 5v14l11-7z"
                    />
                  </svg>
                </div>
                <span
                  class="flex h-8 items-center text-sm font-semibold text-white"
                  style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);"
                >
                  04:00
                </span>
              </div>
            </div>
          </button>
        </li>
        <li
          class="w-full cursor-pointer overflow-hidden rounded-2xl bg-transparent"
        >
          <button
            class="m-0 block w-full cursor-pointer border-none bg-transparent p-0 text-left"
            type="button"
          >
            <div
              class="relative w-full overflow-hidden rounded-2xl"
            >
              <img
                alt="Test Video 4. Duration: 05:00"
                class="absolute left-0 top-0 size-full object-cover object-top transition-transform duration-200"
                loading="lazy"
                src="https://example.com/thumb4.jpg"
              />
              <div
                class="absolute left-0 top-0 flex size-full items-end justify-between bg-gradient-to-t to-transparent p-3 md:p-4 from-black/35"
              >
                <div
                  class="flex aspect-square size-12 items-center justify-center rounded-full bg-white opacity-70"
                >
                  <svg
                    class="size-8 fill-black opacity-50"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M8 5v14l11-7z"
                    />
                  </svg>
                </div>
                <span
                  class="flex h-8 items-center text-sm font-semibold text-white"
                  style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);"
                >
                  05:00
                </span>
              </div>
            </div>
          </button>
        </li>
      </ul>
    </section>
  </div>
</div>
`;

exports[`GalleryPresentation Rendering renders gallery with default props 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div>
    <section
      class="m-auto flex flex-col gap-5 pt-6"
    >
      <ul
        class="m-0 grid grid-cols-1 gap-5 p-0 min-[375px]:grid-cols-2 sm:grid-cols-3 lg:grid-cols-4"
      >
        <li
          class="w-full cursor-pointer overflow-hidden rounded-2xl bg-transparent"
        >
          <button
            class="m-0 block w-full cursor-pointer border-none bg-transparent p-0 text-left"
            type="button"
          >
            <div
              class="relative w-full overflow-hidden rounded-2xl"
            >
              <img
                alt="Test Video 1. Duration: 02:00"
                class="absolute left-0 top-0 size-full object-cover object-top transition-transform duration-200"
                loading="lazy"
                src="https://example.com/thumb1.jpg"
              />
              <div
                class="absolute left-0 top-0 flex size-full items-end justify-between bg-gradient-to-t to-transparent p-3 md:p-4 from-black/75"
              >
                <div
                  class="flex aspect-square size-12 items-center justify-center rounded-full bg-white opacity-70"
                >
                  <svg
                    class="size-8 fill-black opacity-50"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M8 5v14l11-7z"
                    />
                  </svg>
                </div>
                <span
                  class="flex h-8 items-center text-sm font-semibold text-white"
                  style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);"
                >
                  02:00
                </span>
              </div>
            </div>
          </button>
        </li>
        <li
          class="w-full cursor-pointer overflow-hidden rounded-2xl bg-transparent"
        >
          <button
            class="m-0 block w-full cursor-pointer border-none bg-transparent p-0 text-left"
            type="button"
          >
            <div
              class="relative w-full overflow-hidden rounded-2xl"
            >
              <img
                alt="Test Video 2. Duration: 03:00"
                class="absolute left-0 top-0 size-full object-cover object-top transition-transform duration-200"
                loading="lazy"
                src="https://example.com/thumb2.jpg"
              />
              <div
                class="absolute left-0 top-0 flex size-full items-end justify-between bg-gradient-to-t to-transparent p-3 md:p-4 from-black/75"
              >
                <div
                  class="flex aspect-square size-12 items-center justify-center rounded-full bg-white opacity-70"
                >
                  <svg
                    class="size-8 fill-black opacity-50"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M8 5v14l11-7z"
                    />
                  </svg>
                </div>
                <span
                  class="flex h-8 items-center text-sm font-semibold text-white"
                  style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);"
                >
                  03:00
                </span>
              </div>
            </div>
          </button>
        </li>
        <li
          class="w-full cursor-pointer overflow-hidden rounded-2xl bg-transparent"
        >
          <button
            class="m-0 block w-full cursor-pointer border-none bg-transparent p-0 text-left"
            type="button"
          >
            <div
              class="relative w-full overflow-hidden rounded-2xl"
            >
              <img
                alt="Test Video 3. Duration: 04:00"
                class="absolute left-0 top-0 size-full object-cover object-top transition-transform duration-200"
                loading="lazy"
                src="https://example.com/thumb3.jpg"
              />
              <div
                class="absolute left-0 top-0 flex size-full items-end justify-between bg-gradient-to-t to-transparent p-3 md:p-4 from-black/75"
              >
                <div
                  class="flex aspect-square size-12 items-center justify-center rounded-full bg-white opacity-70"
                >
                  <svg
                    class="size-8 fill-black opacity-50"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M8 5v14l11-7z"
                    />
                  </svg>
                </div>
                <span
                  class="flex h-8 items-center text-sm font-semibold text-white"
                  style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);"
                >
                  04:00
                </span>
              </div>
            </div>
          </button>
        </li>
        <li
          class="w-full cursor-pointer overflow-hidden rounded-2xl bg-transparent"
        >
          <button
            class="m-0 block w-full cursor-pointer border-none bg-transparent p-0 text-left"
            type="button"
          >
            <div
              class="relative w-full overflow-hidden rounded-2xl"
            >
              <img
                alt="Test Video 4. Duration: 05:00"
                class="absolute left-0 top-0 size-full object-cover object-top transition-transform duration-200"
                loading="lazy"
                src="https://example.com/thumb4.jpg"
              />
              <div
                class="absolute left-0 top-0 flex size-full items-end justify-between bg-gradient-to-t to-transparent p-3 md:p-4 from-black/75"
              >
                <div
                  class="flex aspect-square size-12 items-center justify-center rounded-full bg-white opacity-70"
                >
                  <svg
                    class="size-8 fill-black opacity-50"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M8 5v14l11-7z"
                    />
                  </svg>
                </div>
                <span
                  class="flex h-8 items-center text-sm font-semibold text-white"
                  style="text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);"
                >
                  05:00
                </span>
              </div>
            </div>
          </button>
        </li>
      </ul>
    </section>
  </div>
</div>
`;
