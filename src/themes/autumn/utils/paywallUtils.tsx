import React from 'react';

export function shouldApplyPaywall(
  pianoFeature: { enabled: boolean },
  usePaywall: boolean,
  hasPianoPaywall: boolean,
): boolean {
  return pianoFeature.enabled && usePaywall && hasPianoPaywall;
}

export function applyPaywallHiding(
  component: React.ReactElement | null,
  index: number,
  itemsShownBehindPaywall: { fadeIdx: number },
  hasPaywall: boolean,
): React.ReactElement | null {
  if (!component) return null;
  if (index > itemsShownBehindPaywall.fadeIdx) {
    if (hasPaywall) {
      return null;
    }
    return (
      <div className="hidden" key={index}>
        {component}
      </div>
    );
  }
  return component;
}
