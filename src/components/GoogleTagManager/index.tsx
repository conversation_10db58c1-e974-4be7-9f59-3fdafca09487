'use client';

import { useEffect, useState } from 'react';
import TagManager from 'react-gtm-module';

import { useAppSelector } from 'store/hooks';
import { MEMBER_TYPE_DATA, PianoSubscriberType } from 'types/Piano';
import { getDeviceTypeFromWidth } from 'util/device';
import { usePageHierarchy } from 'util/hooks';
import { camelKeysToSnake } from 'util/string';

import { setGtmReady } from './ready';

import type React from 'react';
import type { TagManagerArgs } from 'react-gtm-module';
import type { MemberTypeData } from 'types/Piano';

type DataLayerValue = string | boolean | string[] | null | undefined;
type DataLayerRecord = Record<string, DataLayerValue>;
export type DataLayer = Record<string, DataLayerValue | DataLayerRecord>;

function extractGtmValues(envVars: string) {
  return Object.fromEntries(new URLSearchParams(envVars));
}

interface Props {
  version: string;
}

export default function GoogleTagManager({
  version,
}: Props): React.ReactElement | null {
  const conf = useAppSelector((state) => state.conf);
  const { primaryPage, secondaryPage } = usePageHierarchy();
  const authorName = useAppSelector((state) => state.author.name);
  const googleTagManager = useAppSelector(
    (state) => state.features.googleTagManager,
  );
  const settings = useAppSelector((state) => state.settings);
  const story = useAppSelector((state) => state.story);
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const enablePianoABTesting =
    pianoFeature.enabled && pianoFeature.data.isAbTesting;
  const pianoABTestingVariant = useAppSelector(
    (state) => state.piano.pianoABTestingVariant,
  );
  const [initialized, setInitialized] = useState(false);
  const abTest = useAppSelector((state) => state.settings.abTest);

  useEffect(() => {
    if (!initialized) {
      setInitialized(true);

      if (googleTagManager.enabled && googleTagManager.data?.containerId) {
        const isHomePage = settings.viewType === 'homepage';
        const isStory = !!story.id;

        let pageType;
        if (isHomePage) {
          pageType = 'home';
        } else if (isStory) {
          pageType = 'article';
        } else {
          pageType = 'index';
        }

        let memberID = '';
        let memberRole = '';
        let memberOrigin = '';
        let memberType = PianoSubscriberType.VISITOR;
        if (window.localStorage !== undefined) {
          const memberTypeData = window.localStorage.getItem(MEMBER_TYPE_DATA);
          let memberTypeObj: MemberTypeData | null = null;
          try {
            memberTypeObj = memberTypeData
              ? (JSON.parse(memberTypeData) as MemberTypeData)
              : null;
          } catch (e) {
            console.error(e);
          }

          if (
            memberTypeObj &&
            memberTypeObj.memberID &&
            memberTypeObj.memberType
          ) {
            memberID = memberTypeObj.memberID;
            memberType = memberTypeObj.memberType;
          }

          if (memberTypeObj && memberTypeObj.memberRole) {
            memberRole = memberTypeObj.memberRole;
          }
          if (memberTypeObj && memberTypeObj.memberOrigin) {
            memberOrigin = memberTypeObj.memberOrigin;
          }
        }

        // Extract mdY format from iso dt string.
        const datePublished =
          isStory && story.publishFrom
            ? story.publishFrom.split('T')[0].split('-').reverse().join('')
            : '';
        const dataLayer: DataLayer = {
          abTest: abTest
            ? JSON.stringify(
                camelKeysToSnake(abTest as unknown as Record<string, string>),
              )
            : undefined,
          author: isStory ? story.byline || '' : authorName || '',
          brand: conf.name,
          businessName: '',
          client: {
            deviceType: getDeviceTypeFromWidth(),
          },
          contentTier: isStory ? story.contentTier : '',
          datePublished,
          datePublishedIso: isStory ? story.publishFrom : '',
          dateUpdatedIso: isStory ? story.updatedOn : '',
          googleAnalyticsId: conf.gaId1,
          googleAnalyticsRollupId: conf.gaId2,
          pageType,
          primaryPage: primaryPage?.name || '',
          secondaryPage: secondaryPage?.name || '',
          session: {
            adBlocked: '',
            privateBrowsingMode: '',
          },
          storyId: isStory ? story.id : '',
          storySource: isStory ? (story.orgName ?? '') : '',
          storyTags: isStory ? story.tags : [],
          subs: {
            memberID,
            memberType,
            meterCount: '',
            meterPeriodEnd: '',
            meterPeriodStart: '',
            ...(memberType === PianoSubscriberType.ENTERPRISE && {
              memberOrigin,
              memberRole,
            }),
          },
          version,
          wordCount: isStory ? (story.wordCount?.toString() ?? '') : '',
          wordCountRange: isStory ? (story.wordCountRange ?? '') : '',
        };

        if (isStory && story.pubStatus !== 'p') {
          dataLayer.storyIsDraft = true;
        }

        if (enablePianoABTesting) {
          dataLayer.abTest = pianoABTestingVariant;
        }

        const tagManagerArgs: TagManagerArgs = {
          dataLayer,
          gtmId: googleTagManager.data.containerId,
        };

        const envVars = extractGtmValues(googleTagManager.data.envVars);
        if (envVars.gtm_auth) {
          tagManagerArgs.auth = envVars.gtm_auth;
        }
        if (envVars.gtm_preview) {
          tagManagerArgs.preview = envVars.gtm_preview;
        }

        TagManager.initialize(tagManagerArgs);
        setGtmReady();
      }
    }
  }, [
    abTest,
    conf,
    enablePianoABTesting,
    googleTagManager,
    initialized,
    primaryPage,
    secondaryPage,
    pianoABTestingVariant,
    setInitialized,
    settings,
    story,
    version,
    authorName,
  ]);

  return null;
}
