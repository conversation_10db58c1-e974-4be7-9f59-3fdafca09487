'use client';

export enum Ga4EventType {
  SignUp = 'sign_up',
  Login = 'login',
  ResetPasswordFail = 'reset_password_fail',
  GaSearch = 'ga_search',
  PaywallExpression = 'paywall_impression',
  PaywallInteraction = 'paywall_interaction',
  NewsletterSignup = 'newsletter_signup',
}

export enum ImpressionType {
  PaywallOffer = 'paywall_offer',
}

export enum InteractionType {
  ClickEmail = 'click_email',
  ClickLogin = 'click_login',
  ClickLoginApple = 'click_login_apple',
  ClickLoginGoogle = 'click_login_google',
  ClickLoginFacebook = 'click_login_facebook',
  ClickSubscribe = 'click_subscribe',
}

export enum EventStatus {
  Failed = 'failed',
  Successful = 'successful',
}
