type GtmReadyCallback = () => void | Promise<void>;

export const { onGtmReady, setGtmReady } = (function gtmReadyWrapper() {
  const callbacks: GtmReadyCallback[] = [];
  let gtmReady = false;

  function setGtmReadyInner() {
    gtmReady = true;
    callbacks.forEach((cb) => {
      cb()?.catch(console.error);
    });
  }
  function onGtmReadyInner(callback: GtmReadyCallback) {
    if (gtmReady) {
      callback()?.catch(console.error);
      return;
    }
    callbacks.push(callback);
  }
  return {
    onGtmReady: onGtmReadyInner,
    setGtmReady: setGtmReadyInner,
  };
})();
