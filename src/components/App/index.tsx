'use client';

import clsx from 'clsx';
import dynamic from 'next/dynamic';
import React, { useEffect } from 'react';
import { Toaster } from 'react-hot-toast';

import CaptchaV3 from 'components/CaptchaV3';
import Chartbeat from 'components/Chartbeat';
import CustomerDataPlatform from 'components/CustomerDataPlatform';
import GoogleExtendAccess from 'components/GoogleExtendAccess';
import GoogleTagManager from 'components/GoogleTagManager';
import Mantis from 'components/Mantis';
import Near from 'components/Near';
import Piano from 'components/Piano';
import PushNotifications from 'components/PushNotifications';
import Retently from 'components/Retently';
import RoyMorganAnalytics from 'components/RoyMorganAnalytics';
import { ModalPortal } from 'components/editmode/Modal';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { RenderMode } from 'store/slices/conf';
import runtimeSlice from 'store/slices/runtime';
import templates from 'templates/templates';
import AdFixus from 'themes/autumn/components/ads/AdFixus';
import AdProvider from 'themes/autumn/components/ads/AdProvider';
import BranchIODeepLink from 'themes/autumn/components/features/BranchIODeepLink';
import MicrosoftClarity from 'themes/autumn/components/features/MicrosoftClarity';

import styles from './index.module.css';

const Container = dynamic(() => import('components/editmode/Container'));
const Sidebar: React.ComponentType = dynamic(
  () => import('components/editmode/Sidebar'),
);

function App(): React.ReactElement {
  // Not using `useAccessToken` hook, to determine if we display an error
  const token = useAppSelector((state) => state.accessToken);
  const mode = useAppSelector((state) => state.conf.mode);
  const adServing = useAppSelector((state) => state.features.adServing);
  const theme = useAppSelector((state) => state.themeDir);
  const template = useAppSelector((state) => state.layoutTemplate);

  const isGoogleExtendedAccessArticle = useAppSelector(
    (state) => state.story.isGoogleExtendedAccessArticle,
  );

  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(runtimeSlice.actions.setIsClientSide(true));
  }, [dispatch]);

  const templateEntry = templates[theme]?.[template];
  const Component = templateEntry?.component;
  if (!Component) {
    return (
      <div>
        Invalid Template: <code>{template}</code> for theme{' '}
        <code>{theme}</code>
      </div>
    );
  }

  if (!token && mode === RenderMode.EDIT) {
    return (
      <div>
        No token provided in editmode. Please set <code>MONZA_USERNAME</code>{' '}
        and <code>MONZA_PASSWORD</code> in <code>.env.local</code>
      </div>
    );
  }

  const gtmVersion = `${theme}1`;

  const thirdPartyContent = (
    <>
      <Piano options={templateEntry.pianoOptions} />
      <Retently />
      <Chartbeat />
      <CustomerDataPlatform />
      <MicrosoftClarity />
      <GoogleTagManager version={gtmVersion} />
      <PushNotifications />
      {isGoogleExtendedAccessArticle && <GoogleExtendAccess />}
      <Near />
      <CaptchaV3 />
      <Mantis />
      <AdFixus />
      <BranchIODeepLink />
    </>
  );

  let content = (
    <div className={clsx('bg-white font-inter', styles.body)}>
      <Toaster
        containerClassName="top-14 md:top-18"
        containerStyle={{ top: undefined }}
        position="top-center"
      />
      {mode === RenderMode.EDIT && <ModalPortal />}
      {mode === RenderMode.EDIT ? (
        <div className="flex flex-row">
          <div className="max-w-full grow">
            <Component />
          </div>
          <div className="shrink-0">
            <Sidebar />
          </div>
        </div>
      ) : (
        <Component />
      )}
      {thirdPartyContent}
      {mode !== RenderMode.EDIT && <RoyMorganAnalytics />}
    </div>
  );

  if (adServing.enabled) {
    content = <AdProvider>{content}</AdProvider>;
  }

  if (mode === RenderMode.EDIT) {
    return <Container>{content}</Container>;
  }

  return content;
}

export default App;
