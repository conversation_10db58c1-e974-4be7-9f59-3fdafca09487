import Script from 'next/script';

import { useAppSelector } from 'store/hooks';

export default function CustomerDataPlatform() {
  const isEnabled = useAppSelector(
    (state) => state.features.customerDataPlatform.enabled,
  );

  if (!isEnabled) {
    return null;
  }

  return (
    <Script
      async
      // eslint-disable-next-line @stylistic/max-len
      src="https://cdn.c360a.salesforce.com/beacon/c360a/4fdc8e81-3d9f-453d-af7c-903178e3f119/scripts/c360a.min.js"
      strategy="lazyOnload"
    />
  );
}
