'use client';

import { CookiesProvider } from 'react-cookie';
import { ParallaxProvider } from 'react-scroll-parallax';

import BookmarkProvider from 'components/Bookmark/BookmarkProvider';
import StoreProvider from 'store/StoreProvider';
import { type RootState } from 'store/store';

type Props = React.PropsWithChildren<{
  context: Partial<RootState>;
}>;

export default function Providers({ children, context }: Props) {
  return (
    <StoreProvider context={context}>
      <CookiesProvider>
        <ParallaxProvider>
          <BookmarkProvider>{children}</BookmarkProvider>
        </ParallaxProvider>
      </CookiesProvider>
    </StoreProvider>
  );
}
