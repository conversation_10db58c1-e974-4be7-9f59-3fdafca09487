import { BreadcrumbJsonLd } from 'next-seo';

import { useAppSelector } from 'store/hooks';
import { usePageHierarchy } from 'util/hooks';
import { pageUrlPath, seoUrlPath } from 'util/page';

import type { Page } from 'store/slices/pages';
import type { Story } from 'types/Story';

function getItemListElements(
  host: string,
  story: Story,
  title: string,
  primaryPage: Page | null,
  secondaryPage: Page | null,
) {
  const itemListElements = [
    {
      '@type': 'ListItem',
      item: `https://${host}/`,
      name: 'Home',
      position: 1,
    },
  ];
  if (primaryPage && primaryPage.url !== '/')
    itemListElements.push({
      '@type': 'ListItem',
      item: primaryPage.url
        ? `https://${host}${pageUrlPath(primaryPage.url)}`
        : '',
      name: primaryPage.name,
      position: itemListElements.length + 1,
    });
  if (secondaryPage)
    itemListElements.push({
      '@type': 'ListItem',
      item: secondaryPage.url
        ? `https://${host}${pageUrlPath(secondaryPage.url)}`
        : '',
      name: secondaryPage.name,
      position: itemListElements.length + 1,
    });
  if (story.id)
    itemListElements.push({
      '@type': 'ListItem',
      item: seoUrlPath(`https://${host}${story.url}`),
      name: title,
      position: itemListElements.length + 1,
    });
  return itemListElements;
}

interface BreadcrumbProps {
  title: string;
}

export default function Breadcrumb({
  title,
}: BreadcrumbProps): React.ReactElement {
  const host = useAppSelector((state) => state.settings.host);
  const story = useAppSelector((state) => state.story);
  const { primaryPage, secondaryPage } = usePageHierarchy();

  return (
    <BreadcrumbJsonLd
      itemListElements={getItemListElements(
        host,
        story,
        title,
        primaryPage,
        secondaryPage,
      )}
      useAppDir
    />
  );
}
