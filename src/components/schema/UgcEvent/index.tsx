import { EventJsonLd } from 'next-seo';

import { useAppSelector } from 'store/hooks';
import {
  ImageResizeMode,
  TransformOutputFormat,
  storyImageUrl,
} from 'util/image';

interface UgcEventProps {
  seoDescription?: string;
  seoTitle: string;
}

export default function UgcEvent({ seoDescription, seoTitle }: UgcEventProps) {
  const domain = useAppSelector((state) => state.conf.domain);
  const ugc = useAppSelector((state) => state.ugc);
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);

  if (!ugc.ugcDetail?.id) return null;

  const {
    canonicalUrl,
    endDatetime,
    images,
    location,
    organiserDetails,
    startDatetime,
  } = ugc.ugcDetail;

  const organizerName = organiserDetails?.name ?? '';
  const websiteUrl = organiserDetails?.websiteUrl ?? '';

  const fullCanonicalUrl = `https://${domain}${canonicalUrl}/`;

  const imageUrls: string[] = images.map((uri) =>
    storyImageUrl({
      fit: ImageResizeMode.MAX,
      height: 640,
      image: { uri },
      outputFormat: TransformOutputFormat.JPG,
      transformUrl,
      width: 640,
    }),
  );

  const isVirtual = !location || location.match(/online|website/i);

  const organizer = {
    '@type': 'VirtualLocation',
    name: organizerName,
    url: websiteUrl,
  };

  const locationSchema = isVirtual
    ? {
        '@type': 'VirtualLocation',
        url: websiteUrl,
      }
    : {
        // NOTE: Not really reliably possible to extract the place name
        // from the location, so we'll just use location for place name
        // and post address name.
        '@type': 'Place',
        address: {
          '@type': 'PostalAddress',
          name: location,
        },
        name: location,
      };

  return (
    <EventJsonLd
      description={seoDescription}
      endDate={endDatetime}
      images={imageUrls}
      location={locationSchema}
      name={seoTitle}
      organizer={organizer}
      startDate={startDatetime}
      url={fullCanonicalUrl}
      useAppDir
    />
  );
}
