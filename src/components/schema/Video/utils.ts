/* eslint-disable import/prefer-default-export */
import { Service, StoryElementType } from 'types/Story';

import type { GenericElement, Story } from 'types/Story';

export function isVideoStory(
  view: string,
  story: Story,
): [boolean, GenericElement | undefined] {
  if (view !== 'story' || story.elements?.length !== 1)
    return [false, undefined];
  const firstEl = story.elements?.[0];
  const isDMVideo =
    firstEl &&
    firstEl.type === StoryElementType.Generic &&
    firstEl.service === Service.Dailymotion;
  return [isDMVideo, firstEl as GenericElement];
}
