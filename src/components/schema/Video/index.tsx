import { VideoJsonLd } from 'next-seo';

import { useAppSelector } from 'store/hooks';

import { isVideoStory } from './utils';

const durationRegex = /(\d+):(\d+):(\d+)/;

export default function GoogleVideoStoryMarkup(): React.ReactElement | null {
  const view = useAppSelector((state) => state.settings.viewType);
  const story = useAppSelector((state) => state.story);
  const siteName = useAppSelector((state) => state.conf.name);
  const host = useAppSelector((state) => state.settings.host);
  const logoSvgOnly = useAppSelector((state) => state.conf.logoSvgOnly);
  const publisherLogo = `https://${host}${logoSvgOnly}`;
  const [isDMVideo, DMelement] = isVideoStory(view, story);
  if (!isDMVideo || !DMelement) return null;
  const name = story.title;
  const datePublished = story.publishFrom;
  const dateModified = story.updatedOn;
  const { description, meta, serviceId } = DMelement;
  const durationUnits = durationRegex.exec(meta?.duration || '');
  let duration = 'PT';
  if (durationUnits) {
    const [, hours, minutes, seconds] = durationUnits;
    if (hours.replaceAll('0', '')) duration += `${hours}H`;
    if (minutes.replaceAll('0', '')) duration += `${minutes}M`;
    if (seconds.replaceAll('0', '')) duration += `${seconds}S`;
  }
  const publisher = {
    '@id': host,
    '@type': 'Organization',
    logo: {
      '@type': 'ImageObject',
      url: publisherLogo,
    },
    name: siteName,
  };

  return (
    <VideoJsonLd
      dateModified={dateModified}
      datePublished={datePublished}
      description={description}
      duration={duration}
      embedUrl={`https://geo.dailymotion.com/player.html?video=${serviceId}`}
      name={name}
      publisher={publisher}
      thumbnailUrls={[meta?.thumbnail || '']}
      uploadDate={datePublished}
      useAppDir
    />
  );
}
