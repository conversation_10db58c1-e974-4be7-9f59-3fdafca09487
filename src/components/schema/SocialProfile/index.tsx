import { SocialProfileJsonLd } from 'next-seo';

import { useAppSelector } from 'store/hooks';
import buildSocialUrls from 'util/social/buildSocialUrls';

export default function SocialProfile() {
  const { domain, name } = useAppSelector((state) => state.conf);
  const facebookUrl = useAppSelector((state) => state.conf.facebookUrl);
  const twitterUsername = useAppSelector(
    (state) => state.conf.twitterUsername,
  );
  const youtubeUrl = useAppSelector((state) => state.conf.youtubeUrl);
  const instagramUsername = useAppSelector(
    (state) => state.conf.instagramUsername,
  );

  const socialProfileUrl = buildSocialUrls({
    facebookUrl,
    instagramUsername,
    twitterUsername,
    youtubeUrl,
  });

  return (
    <SocialProfileJsonLd
      legalName={name}
      name={name}
      sameAs={socialProfileUrl}
      type="Organization"
      url={`https://${domain}/`}
      useAppDir
    />
  );
}
