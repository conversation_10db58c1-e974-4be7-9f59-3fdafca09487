import { NewsArticleJsonLd } from 'next-seo';

import { useAppSelector } from 'store/hooks';
import { authorToUrl } from 'themes/autumn/components/stories/AuthorLink/utils';
import { getStoryTags } from 'themes/autumn/templates/stories/common';
import { StoryElementType } from 'types/Story';
import { usePageHierarchy } from 'util/hooks';
import { hasValidURI, storyImageUrl } from 'util/image';
import { seoUrlPath } from 'util/page';
import { titleCase } from 'util/string';

import type { ImageElement, StoryAuthor } from 'types/Story';
import type { ValidImageWithURI } from 'util/image';

interface NewsArticleProps {
  seoDescription?: string;
  seoTitle: string;
}

function getStoryAuthor(authors: StoryAuthor[], host: string) {
  const authorData = authors.map((author) => ({
    name: author.name,
    url: `https://${host}${authorToUrl(author)}`,
  }));
  return authorData;
}

export default function NewsArticle({
  seoDescription,
  seoTitle,
}: NewsArticleProps) {
  const siteName = useAppSelector((state) => state.conf.name);
  const domain = useAppSelector((state) => state.conf.domain);
  const host = useAppSelector((state) => state.settings.host);
  const story = useAppSelector((state) => state.story);
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);
  const logoSvgOnly = useAppSelector((state) => state.conf.logoSvgOnly);
  const primaryPageName = usePageHierarchy().primaryPage?.name;

  if (!story.id) return null;

  const alternateTitle = seoTitle !== story.title ? story.title : null;
  const storyImages = story.elements
    ? story.elements
        .filter(
          (element) =>
            element.type === StoryElementType.Image ||
            element.type === StoryElementType.Gallery,
        )
        .map((element) =>
          element.type === StoryElementType.Image ? element : element.elements,
        )
        .flat()
        .filter((image) => hasValidURI(image))
        .map((image: ImageElement) =>
          storyImageUrl({
            height: image.height,
            image: image as ValidImageWithURI,
            transformUrl,
            width: image.width,
          }),
        )
    : [];

  const creativeWork = {
    '@type': ['CreativeWork', 'Product'],
    name: siteName,
    productID: `${domain.split('.').slice(1).join('.')}/subscribe/`,
  };

  const authors = story.authors.length
    ? getStoryAuthor(story.authors, host)
    : titleCase(story.byline);
  const dateCreated = [story.updatedOn, story.publishFrom].sort()[0];
  const publisherLogo = `https://${host}${logoSvgOnly}`;
  const absoluteCanonicalUrl = story.canonicalUrl?.startsWith('/')
    ? `https://${host}${story.canonicalUrl}`
    : story.canonicalUrl;
  const absoluteStoryUrl =
    seoUrlPath(absoluteCanonicalUrl) ||
    seoUrlPath(`https://${host}${story.storyUrl}`);
  const section: string = primaryPageName || '';
  const isArticleFree = story.contentTier === 'free';

  return (
    <NewsArticleJsonLd
      alternativeHeadline={alternateTitle || undefined}
      authorName={authors}
      body={story.bodyText}
      dateCreated={dateCreated}
      dateModified={story.updatedOn}
      datePublished={story.publishFrom}
      description={seoDescription || story.summary}
      images={storyImages}
      isAccessibleForFree={isArticleFree}
      isPartOf={creativeWork}
      keywords={getStoryTags(story).join(',')}
      publisherLogo={publisherLogo}
      publisherName={siteName}
      section={section}
      title={seoTitle}
      url={absoluteStoryUrl}
      useAppDir
      wordCount={story.wordCount}
    />
  );
}
