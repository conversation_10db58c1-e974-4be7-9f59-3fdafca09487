import { ListboxSubType, StoryElementType } from 'types/Story';
import { stripTags } from 'util/device';

import type { Story } from 'types/Story';

export default function buildAnswerTextFromStory(story: Story): string {
  if (story.bodyText) {
    return stripTags(story.bodyText).trim();
  }

  const elements = story.elements ?? [];

  const parts = elements.reduce<string[]>((acc, element) => {
    switch (element.type) {
      case StoryElementType.Paragraph: {
        const paragraph = element;
        const text = stripTags(paragraph.text).trim();
        if (text) acc.push(text);
        break;
      }
      case StoryElementType.List: {
        const list = element;
        const listText = list.items
          .filter((item): item is string => typeof item === 'string')
          .map((item) => stripTags(item).trim())
          .filter(Boolean)
          .join('\n');
        if (listText) acc.push(listText);
        break;
      }
      case StoryElementType.Listbox: {
        const listbox = element;
        if (listbox.subType === ListboxSubType.Webbox) {
          const { content, headline } = listbox;
          const combined = [headline, content]
            .filter(Boolean)
            .map((s) => stripTags(String(s)).trim())
            .filter(Boolean)
            .join('\n');
          if (combined) acc.push(combined);
        } else if (listbox.subType === ListboxSubType.Factbox) {
          const { headline, items } = listbox;
          const listText = items
            .map((s) => stripTags(s).trim())
            .filter(Boolean)
            .join('\n');
          const combined = [
            headline ? stripTags(headline).trim() : '',
            listText,
          ]
            .filter(Boolean)
            .join('\n');
          if (combined) acc.push(combined);
        }
        break;
      }
      case StoryElementType.Heading: {
        const heading = element;
        const text = stripTags(heading.text).trim();
        if (text) acc.push(text);
        break;
      }
      default:
        break;
    }
    return acc;
  }, []);

  return parts.join('\n\n').trim();
}
