import FAQPage from 'components/schema/FAQPage';
import { useAppSelector } from 'store/hooks';

import buildAnswerTextFromStory from './utils';

import type { FaqItem } from 'types/ZoneItems';

interface HelpCentreProps {
  seoTitle?: string;
}

export default function HelpCentreSchema({
  seoTitle,
}: HelpCentreProps): React.ReactElement | null {
  const story = useAppSelector((state) => state.story);

  if (!story?.id) return null;

  const question = story.title || seoTitle || '';
  const answer = buildAnswerTextFromStory(story);

  if (!question || !answer) return null;

  const items: FaqItem[] = [[question, answer]];

  return <FAQPage items={items} />;
}
