import { OrganizationJsonLd } from 'next-seo';

import { useAppSelector } from 'store/hooks';
import buildSocialUrls from 'util/social/buildSocialUrls';

export default function Organization(): React.ReactElement {
  const { domain, name } = useAppSelector((state) => state.conf);
  const facebookUrl = useAppSelector((state) => state.conf.facebookUrl);
  const twitterUsername = useAppSelector(
    (state) => state.conf.twitterUsername,
  );
  const youtubeUrl = useAppSelector((state) => state.conf.youtubeUrl);
  const instagramUsername = useAppSelector(
    (state) => state.conf.instagramUsername,
  );

  const socialProfileUrl = buildSocialUrls({
    facebookUrl,
    instagramUsername,
    twitterUsername,
    youtubeUrl,
  });

  return (
    <OrganizationJsonLd
      contactPoint={[
        {
          '@type': 'ContactPoint',
          areaServed: 'AU',
          availableLanguage: ['en'],
          contactType: 'customer support',
          hoursAvailable: {
            '@type': 'OpeningHoursSpecification',
            closes: '17:00',
            dayOfWeek: [
              'Monday',
              'Tuesday',
              'Wednesday',
              'Thursday',
              'Friday',
            ],
            opens: '09:00',
          },
          telephone: '+61-1300-131-095',
        },
      ]}
      name={name}
      sameAs={socialProfileUrl}
      url={`https://${domain}/`}
      useAppDir
    />
  );
}
