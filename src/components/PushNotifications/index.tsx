'use client';

import Script from 'next/script';
import React, { useEffect, useState } from 'react';
import { useCookies } from 'react-cookie';
import {
  browserVersion,
  isChrome,
  isFirefox,
  isOpera,
  isSafari,
} from 'react-device-detect';

import { useAppSelector } from 'store/hooks';

import type { AirshipPlugin, AirshipSDK } from 'types/airship';

const dateNowPlusSeconds = (seconds: number): Date => {
  // eslint-disable-next-line rulesdir/prefer-use-date
  const today = new Date();
  // eslint-disable-next-line rulesdir/prefer-use-date
  const future = new Date();
  future.setTime(today.getTime() + seconds * 1000);
  return future;
};

export default function PushNotifications(): React.ReactElement | null {
  const [softAskDebouncing, setSoftAskDebouncing] = useState(false);
  const pushNotificationsFeature = useAppSelector(
    (state) => state.features.pushNotifications,
  );
  const piano = useAppSelector((state) => state.piano);
  const { host, staticUrl } = useAppSelector((state) => state.settings);
  const { name, publication } = useAppSelector((state) => state.conf);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const story = useAppSelector((state) => state.story);

  const cookieConf = {
    // Time to display Airship soft ask again
    // if deny to accept notification
    askAgainDelay: pushNotificationsFeature.enabled
      ? pushNotificationsFeature.data.askAgain * 24 * 60 * 60
      : 0,
    // Time to limit the request to airship soft ask
    remoteRequest: 2 * 60 * 60,
    // Soft ask cookie key
    softAskKey: `airship_notification_${publication}`,
    viewedArticleExpire: 90 * 24 * 60 * 60,
    // viewed article cookie key
    viewedArticleKey: `airship_articles_${publication}`,
  };

  const [cookie, setCookie] = useCookies([
    cookieConf.softAskKey,
    cookieConf.viewedArticleKey,
  ]);

  const softAsk = {
    accept: 'Keep me updated',
    deny: 'Maybe Later',
    message:
      'We use notifications to send alerts for important news as it happens. Choose KEEP ME UPDATED, then ALLOW our notifications.',
    title: `Get alerts on breaking stories from ${name}`,
  };

  const notificationRegisterStatus = (): string => {
    const notificationCookie: string | null =
      (cookie[cookieConf.softAskKey] as string) || null;

    // Non Piano user, denied receive notifications
    if (!notificationCookie) {
      return '';
    }

    // Piano user - accepted notifications
    if (notificationCookie === '' && piano.user && piano.hasAccess) {
      return piano.user.sub;
    }
    // Not Piano user - accepted receive notification
    return 'no';
  };

  const registerAsPianoUser = (sdk: AirshipSDK) => {
    if (piano.user) {
      // Add piano tags in airship
      if (sdk.channel?.tags.has('piano') === false) {
        sdk.channel.tags.set(['piano']);
      }
    } else if (sdk.channel?.tags.has('piano')) {
      // Remove tag piano
      sdk.channel.tags.set([]);
    }

    // Cookie to limit the times of sending
    // requests to the airship server
    setCookie(cookieConf.softAskKey, piano.user?.sub || '', {
      expires: dateNowPlusSeconds(cookieConf.remoteRequest),
      path: '/',
    });
  };

  const registerPushNotification = () => {
    if (pushNotificationsFeature.enabled && window.UA) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      window.UA.then((sdk: AirshipSDK) => {
        if (
          notificationRegisterStatus() !== 'no' &&
          sdk.isSupported &&
          sdk.canRegister &&
          ((isSafari && parseInt(browserVersion, 10) >= 11) ||
            (isFirefox && parseInt(browserVersion, 10) >= 72) ||
            (isChrome && parseInt(browserVersion, 10) >= 71) ||
            (isOpera && parseInt(browserVersion, 10) >= 58))
        ) {
          // Doco: https://docs.airship.com/platform/web/plugins/html-prompt/
          // eslint-disable-next-line @typescript-eslint/no-floating-promises
          sdk.plugins
            .load(
              'html-prompt',
              'https://aswpsdkus.com/notify/v1/ua-html-prompt.min.js',
              {
                appearDelay: 6000,
                // Airship shows dialog again after dismissing
                // or ignoring the prompt
                askAgainDelay: cookieConf.askAgainDelay,
                auto: false,
                i18n: {
                  en: {
                    accept: softAsk.accept,
                    deny: softAsk.deny,
                    message: softAsk.message,
                    title: softAsk.title,
                  },
                },
                logo: pushNotificationsFeature.data.notificationIcon,
                // eslint-disable-next-line @stylistic/max-len
                stylesheet: `https://${host}${staticUrl}stylesheets/airship.css`,
              },
            )
            .then((plugin: AirshipPlugin) => {
              plugin.prompt();
            });
        }

        // Update tag and cookie if necessary
        registerAsPianoUser(sdk);

        // Add airship event listener
        sdk.addEventListener(
          'channel',
          () => {
            if (!softAskDebouncing && sdk.channel?.channel_id) {
              setSoftAskDebouncing(true);
              registerAsPianoUser(sdk);
            }
          },
          { once: true },
        );
      });
    }
  };

  const pushNotification = (): void => {
    // Give extra time for the UA object loading
    setTimeout(() => {
      registerPushNotification();
    }, 5000);
  };

  const displayRegisterPushNotification = () => {
    if (!pushNotificationsFeature.enabled) {
      return;
    }

    let articleCookies = String(cookie[cookieConf.viewedArticleKey]) || null;
    const articles = articleCookies ? articleCookies.split('|') : [];
    const { displayThreshold } = pushNotificationsFeature.data;

    // Only display notification soft ask in article page for the first time
    if (viewType === 'story' && story.id !== '') {
      if (articleCookies) {
        // display notification soft ask
        if (articles.includes('-1')) {
          pushNotification();
          return;
        }

        if (articles.length >= displayThreshold - 1) {
          // Threshold reached
          // Set -1 and ask for notification every time user view article
          setCookie(cookieConf.viewedArticleKey, -1, {
            expires: dateNowPlusSeconds(cookieConf.viewedArticleExpire),
            path: '/',
          });
          // Display soft ask notification
          pushNotification();
        } else if (articles.indexOf(story.id) === -1) {
          articleCookies += `|${story.id}`;
          setCookie(cookieConf.viewedArticleKey, articleCookies, {
            expires: dateNowPlusSeconds(cookieConf.viewedArticleExpire),
            path: '/',
          });
        }
      } else {
        if (displayThreshold <= 1) {
          pushNotification();
          return;
        }

        setCookie(cookieConf.viewedArticleKey, story.id, {
          expires: dateNowPlusSeconds(cookieConf.viewedArticleExpire),
          path: '/',
        });
      }
    } else if (articleCookies && articles.includes('-1')) {
      // Display soft ask on homepage and index page
      // if deny notifications at first time
      pushNotification();
    }
  };

  const isPushNotificationView = (view: string) =>
    ['homepage', 'story', 'section', 'index'].includes(view);

  useEffect(() => {
    if (
      piano.initialized &&
      pushNotificationsFeature.enabled &&
      isPushNotificationView(viewType)
    ) {
      displayRegisterPushNotification();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [piano.initialized, pushNotificationsFeature.enabled, viewType]);

  if (!pushNotificationsFeature.enabled || !isPushNotificationView(viewType)) {
    return null;
  }

  return (
    <Script id="airship-script" strategy="lazyOnload">
      {`// 86acbd31cd7c09cf30acb66d2fbedc91daa48b86:1583452315.2491171
        !function(n,t,c,e,r){var u,i,o=[],a=[],f={then:s,catch:function(n){
        return s(!1,n)},_async_setup:function(n){var t;try{t=n(r)}catch(n){
        return i=n,void h(a,n)}t.then((function(n){h(o,u=n)})).catch((
        function(n){i=n,h(defferedErrors,i)}))}};n[e]=f
        ;var d=t.createElement("script");function h(n,t){for(
        var c=0;c<n.length;c++)p(n[c],t)}function s(n,t){
        return n&&(u?p(n,u):o.push(n)),
        t&&(i?p(t,i):a.push(t)),f}function p(t,c){n.setTimeout((
        function(){t(c)}),0)}d.src=c,d.async=!0,d.id="_uasdk",
        d.rel=e,t.head.appendChild(d)
        }(window,document,'https://aswpsdkus.com/notify/v1/ua-sdk.min.js',
          'UA', {
            vapidPublicKey: '${pushNotificationsFeature.data.vapidPublicKey}',
            websitePushId: '${pushNotificationsFeature.data.websitePushId}',
            appKey: '${pushNotificationsFeature.data.appKey}',
            token: '${pushNotificationsFeature.data.appToken}'
          });
        `}
    </Script>
  );
}
