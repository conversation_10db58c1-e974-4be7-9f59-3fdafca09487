import Script from 'next/script';

import { useAppSelector } from 'store/hooks';

export default function CaptchaV3() {
  const reCaptchaFeature = useAppSelector(
    (state) => state.features.recaptchaV3,
  );

  if (!reCaptchaFeature.enabled || !reCaptchaFeature.data.publicApiKey) {
    return null;
  }

  return (
    <Script
      async
      // eslint-disable-next-line @stylistic/max-len
      src={`https://www.google.com/recaptcha/enterprise.js?render=${reCaptchaFeature.data.publicApiKey}`}
    />
  );
}
