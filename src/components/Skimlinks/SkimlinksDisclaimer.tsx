import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';

export default function SkimlinksDisclaimer() {
  const skimlinksFeature = useAppSelector((state) => state.features.skimlinks);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const isHomePage = viewType === 'homepage';

  if (!skimlinksFeature.enabled || !isHomePage) {
    return null;
  }

  return (
    <div className="w-full font-inter text-xs leading-6 text-gray-300 md:my-2 md:py-8">
      We collect information about the content (including advertisements) you
      interact with across this site. This helps us make advertising and
      content more relevant for you across the ACM network. You can find out
      more about our{' '}
      <Link
        className="text-blue-400 hover:text-blue-600"
        href="/conditions-of-use/"
        noStyle
      >
        conditions of use here.
      </Link>{' '}
      Sometimes articles you engage with will help you find a product and make
      your shopping experience easier. We may receive compensation from
      affiliate partners if you choose to make a purchase through the links on
      our site.
    </div>
  );
}
