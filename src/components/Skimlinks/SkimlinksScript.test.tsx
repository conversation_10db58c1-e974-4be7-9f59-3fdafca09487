import { render } from '@testing-library/react';
import React from 'react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import Skimlinks from '.';

jest.mock(
  'next/script',
  () =>
    // eslint-disable-next-line react/display-name, @typescript-eslint/no-explicit-any, func-names
    function ({ children, ...props }: any) {
      // eslint-disable-next-line react/jsx-props-no-spreading
      return <script {...props}>{children}</script>;
    },
);

describe('SkimlinksScript Component', () => {
  it('should render with key', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        skimlinks: {
          data: {
            isStoryBlacklisted: false,
            key: '1234567890',
          },
          enabled: true,
        },
      },
      story: {
        ...state.story,
        pubStatus: 'p',
        tags: ['affiliate'],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Skimlinks />
      </TestWrapper>,
    );

    expect(container).toMatchSnapshot();
  });

  it('should not render if story is not an affiliate type', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        skimlinks: {
          data: {
            isStoryBlacklisted: false,
            key: '1234567890',
          },
          enabled: true,
        },
      },
      story: {
        ...state.story,
        tags: ['story-sponsored'],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Skimlinks />
      </TestWrapper>,
    );

    expect(container).toMatchSnapshot();
  });

  it('should not render if story is blacklisted', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        skimlinks: {
          data: {
            isStoryBlacklisted: true,
            key: '1234567890',
          },
          enabled: true,
        },
      },
      story: {
        ...state.story,
        tags: ['affiliate'],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Skimlinks />
      </TestWrapper>,
    );

    expect(container).toMatchSnapshot();
  });

  it('should not render if not enabled', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        skimlinks: {
          data: {
            isStoryBlacklisted: false,
            key: '1234567890',
          },
          enabled: false,
        },
      },
      story: {
        ...state.story,
        tags: ['affiliate'],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Skimlinks />
      </TestWrapper>,
    );

    expect(container).toMatchSnapshot();
  });

  it('should not render if key is empty', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        skimlinks: {
          data: {
            isStoryBlacklisted: false,
            key: '',
          },
          enabled: true,
        },
      },
      story: {
        ...state.story,
        tags: ['affiliate'],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Skimlinks />
      </TestWrapper>,
    );

    expect(container).toMatchSnapshot();
  });

  it('should not render if key is null', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        skimlinks: {
          data: {
            isStoryBlacklisted: false,
            key: null,
          },
          enabled: true,
        },
      },
      story: {
        ...state.story,
        tags: ['affiliate'],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Skimlinks />
      </TestWrapper>,
    );

    expect(container).toMatchSnapshot();
  });

  it('should not render if story is draft', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        skimlinks: {
          data: {
            isStoryBlacklisted: false,
            key: '1234567',
          },
          enabled: true,
        },
      },
      story: {
        ...state.story,
        pubStatus: 'd',
        tags: ['affiliate'],
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <Skimlinks />
      </TestWrapper>,
    );

    expect(container).toMatchSnapshot();
  });
});
