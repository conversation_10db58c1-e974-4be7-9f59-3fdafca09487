// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SkimlinksScript Component should not render if key is empty 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
  </div>
</div>
`;

exports[`SkimlinksScript Component should not render if key is null 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
  </div>
</div>
`;

exports[`SkimlinksScript Component should not render if not enabled 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
  </div>
</div>
`;

exports[`SkimlinksScript Component should not render if story is blacklisted 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
  </div>
</div>
`;

exports[`SkimlinksScript Component should not render if story is draft 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
  </div>
</div>
`;

exports[`SkimlinksScript Component should not render if story is not an affiliate type 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
  </div>
</div>
`;

exports[`SkimlinksScript Component should render with key 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <script
      src="https://s.skimresources.com/js/1234567890.skimlinks.js"
    />
  </div>
</div>
`;
