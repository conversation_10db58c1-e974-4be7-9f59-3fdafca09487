import Script from 'next/script';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import { skimlinkScriptUrl } from 'util/url';

export default function Skimlinks(): React.ReactElement | null {
  const skimlinks = useAppSelector((state) => state.features.skimlinks);
  const tags = useAppSelector((state) => state.story.tags);
  const isStoryDraft: boolean = useAppSelector(
    (state) => state.story.pubStatus !== 'p',
  );

  // should only work for affiliate stories
  const isStoryAnAffiliateType: boolean = tags.includes('affiliate');

  if (
    !skimlinks.enabled ||
    !skimlinks.data.key ||
    !isStoryAnAffiliateType ||
    skimlinks.data.isStoryBlacklisted ||
    isStoryDraft
  ) {
    return null;
  }

  return <Script src={skimlinkScriptUrl(skimlinks.data.key)} />;
}
