/* eslint-disable no-underscore-dangle */

'use client';

import type { ChartBeatSubscriberType } from 'types/chartbeat';

export function chartBeatSubscriberEngagement(
  subscriberType: ChartBeatSubscriberType,
) {
  window._cbq = window._cbq || [];
  window._cbq.push(['_acct', subscriberType]);
}

export function chartBeatVideoEngagement(player: DMPlayer) {
  window._cbv = window._cbv || [];
  window._cbv.push(player);
}

export function chartbeatCBVStrategy(strategy: typeof DMStrategy) {
  window._cbv_strategies = window._cbv_strategies || [];
  window._cbv_strategies.push(strategy);
}
