/* eslint-disable no-underscore-dangle */

'use client';

import Script from 'next/script';
import React, { useEffect, useState } from 'react';
import slugify from 'slugify';

import { useAppSelector } from 'store/hooks';
import { usePageHierarchy } from 'util/hooks';

export default function Chartbeat(): React.ReactElement | null {
  const headlineTestingFeature = useAppSelector(
    (state) => state.features.headlineTesting,
  );
  const chartbeatUid = useAppSelector((state) => state.conf.chartbeatUid);
  const chartbeatDomain = useAppSelector(
    (state) => state.conf.chartbeatDomain,
  );
  const story = useAppSelector((state) => state.story);
  const { primaryPage, secondaryPage } = usePageHierarchy();

  const viewType = useAppSelector((state) => state.settings.viewType);
  const isHomePage = viewType === 'homepage';

  const [initialized, setInitialized] = useState(false);
  const [headlineTestingStyleRendered, setHeadlineTestingStyleRendered] =
    useState(false);

  const shouldLoadChartbeat = (): boolean => !story.id || story.publishable;

  useEffect(() => {
    if (
      !headlineTestingStyleRendered &&
      headlineTestingFeature.enabled &&
      isHomePage
    ) {
      document.head.insertAdjacentHTML(
        'beforeend',
        `<style id="chartbeat-flicker-control-style">
          .custom-chartbeat-headline-test { 
            visibility: hidden; 
          }
        </style>`,
      );
      window.setTimeout(() => {
        document.getElementById('chartbeat-flicker-control-style')?.remove();
      }, 1000);
      setHeadlineTestingStyleRendered(true);
    }
  }, [
    headlineTestingStyleRendered,
    setHeadlineTestingStyleRendered,
    headlineTestingFeature.enabled,
    isHomePage,
  ]);

  useEffect(() => {
    if (!initialized) {
      setInitialized(true);
      window._sf_async_config = window._sf_async_config || {};
      window._sf_async_config.uid = chartbeatUid;
      window._sf_async_config.domain = chartbeatDomain;
      window._sf_async_config.useCanonical = true;
      window._sf_async_config.title = story.title;
      if (story.byline) {
        window._sf_async_config.authors = story.byline;
      }
      window._sf_async_config.flickerControl = false;
      // avoid tracking video ads for video engagement
      window._sf_async_config.autoDetect = false;
      if (story.url) {
        // Extract `/story/ID` part
        window._sf_async_config.path = `${story.url
          .split('/')
          .slice(0, 3)
          .join('/')}/`;
      }
      const sections = [];
      if (secondaryPage?.name) {
        sections.push(
          slugify(secondaryPage.name).replaceAll('-', '').toLowerCase(),
        );
      }
      if (primaryPage?.name) {
        sections.push(
          slugify(primaryPage.name).replaceAll('-', '').toLowerCase(),
        );
      }
      if (story.id) {
        sections.push('story');
      }
      // TODO: Add section `business` here after AUD-2138 implementation
      if (story.elementTypes.includes('gallery')) {
        sections.push('gallery');
      }
      window._sf_async_config.sections = sections.join(',');
    }
  }, [
    chartbeatUid,
    chartbeatDomain,
    story,
    primaryPage,
    secondaryPage,
    initialized,
    setInitialized,
  ]);

  if (!shouldLoadChartbeat()) {
    return null;
  }

  return (
    <>
      {headlineTestingFeature.enabled && isHomePage && (
        <Script async src="https://static.chartbeat.com/js/chartbeat_mab.js" />
      )}
      {initialized && (
        <Script
          async
          src="https://static.chartbeat.com/js/chartbeat_video.js"
        />
      )}
    </>
  );
}
