import { fireEvent, render, screen, waitFor } from '@testing-library/react';

import { RenderMode } from 'store/slices/conf';
import { createStore } from 'store/store';
import { ZoneItemType } from 'types/ZoneItems';
import { TestWrapper } from 'util/jest';

import ZoneItemForm from '.';

import type { EnhancedStore } from '@reduxjs/toolkit';
import type { RootState } from 'store/store';

describe('<ZoneItemForm />', () => {
  it('should render a form for each zone item type', () => {
    expect.assertions(40);

    const store = createStore((state) => ({
      ...state,
      accessToken: '123',
    }));

    Object.values(ZoneItemType).forEach((type) => {
      const { container } = render(
        <TestWrapper store={store}>
          <ZoneItemForm
            onClose={() => {}}
            pageId={1}
            siteId={1}
            type={type}
            zone="main"
            zoneItemId={1}
          />
        </TestWrapper>,
      );

      expect(container.firstChild).toMatchSnapshot();
    });
  });
});

describe('story list form', () => {
  let store: EnhancedStore<RootState>;

  const data = {
    flipStoryDisplay: true,
    isHeroImage: true,
    largeLeadStory: true,
  };

  beforeEach(() => {
    store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        accessToken: '123',
        mode: RenderMode.NORMAL,
      },
    }));
  });

  it('hide is_hero_image checkbox when large_lead_article uncheck', () => {
    expect.assertions(3);

    const { container, getByTestId } = render(
      <TestWrapper store={store}>
        <ZoneItemForm
          data={data}
          onClose={() => {}}
          pageId={1}
          siteId={1}
          type={ZoneItemType.StoryList}
          zone="main"
          zoneItemId={1}
        />
      </TestWrapper>,
    );

    const isHeroImage = getByTestId('isHeroImage') as HTMLInputElement;
    const isHeroImageClassList =
      isHeroImage?.parentElement?.parentElement?.parentElement?.classList;
    expect(isHeroImageClassList?.contains('hidden')).toBe(false);

    const largeLeadStory = getByTestId('largeLeadStory') as HTMLInputElement;
    fireEvent.click(largeLeadStory);
    expect(isHeroImageClassList?.contains('hidden')).toBe(true);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('test form validation', async () => {
    expect.assertions(3);

    const { container } = render(
      <TestWrapper store={store}>
        <ZoneItemForm
          data={data}
          onClose={() => {}}
          pageId={11}
          siteId={11}
          type={ZoneItemType.StoryList}
          zone="main"
          zoneItemId={1}
        />
      </TestWrapper>,
    );

    const limit = screen.getByTestId('limit');
    fireEvent.input(limit, { target: { value: '30' } });

    const saveButton = screen.getByTestId('save');
    fireEvent.submit(saveButton);

    await waitFor(() =>
      expect(
        screen
          .getByTestId('limit')
          .nextElementSibling?.classList.contains('text-red-500'),
      ).toBe(true),
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
