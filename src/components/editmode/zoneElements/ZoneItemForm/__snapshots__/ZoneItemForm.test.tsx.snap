// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`<ZoneItemForm /> should render a form for each zone item type 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="advertisement5.html"
            >
              Billboard (desktop) & Mrec (mobile)
            </option>
            <option
              value="explore.html"
            >
              Explore Travel search widget
            </option>
            <option
              value="explore_banner.html"
            >
              Explore Travel search widget for banner
            </option>
            <option
              value="farmbuy_featured_properties_carousel.html"
            >
              FarmBuy Featured Properties Carousel
            </option>
            <option
              value="farmbuywidget.html"
            >
              FarmBuy Widget
            </option>
            <option
              value="advertisement1.html"
            >
              Leaderboard
            </option>
            <option
              value="advertisement4.html"
            >
              Leaderboard or Mobile
            </option>
            <option
              value="advertisement2.html"
            >
              Mrec
            </option>
            <option
              value="advertisement3.html"
            >
              Mrec or Leaderboard
            </option>
            <option
              value="advertisement6.html"
            >
              Noticeboard
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="position"
        >
          Position
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="position"
            id="position"
            name="position"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            Set to zero for automatic numbering
          </p>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 2`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="meet_the_team.html"
            >
              Meet the Team
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="authorCollection"
        >
          Author Collection
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            disabled=""
            id="authorCollection"
            name="authorCollection"
          >
            <option>
              Loading...
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 3`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default_banner.html"
            >
              Default Banner
            </option>
            <option
              value="explore_destination_header_banner.html"
            >
              Explore Destination Header Banner
            </option>
            <option
              value="explore_homepage_banner.html"
            >
              Explore Homepage Banner
            </option>
            <option
              value="parallax_banner.html"
            >
              Parallax Banner
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="backgroundUrl"
        >
          Background url
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="backgroundUrl"
            id="backgroundUrl"
            name="backgroundUrl"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="subTitle"
        >
          Sub title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="subTitle"
            id="subTitle"
            name="subTitle"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="openNewWindow"
              id="openNewWindow"
              name="openNewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="openNewWindow"
            >
              Open new window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url"
        >
          Url
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url"
            id="url"
            name="url"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 4`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default.html"
            >
              Default
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title1"
        >
          Slide 1 - Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title1"
            id="title1"
            name="title1"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="description1"
        >
          Slide 1 - Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="description1"
            id="description1"
            name="description1"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url1"
        >
          Slide 1 - URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url1"
            id="url1"
            name="url1"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="image1"
        >
          Slide 1 - Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="image1"
            id="image1"
            name="image1"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="openNewWindow1"
              id="openNewWindow1"
              name="openNewWindow1"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="openNewWindow1"
            >
              Slide 1 - Open in new window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title2"
        >
          Slide 2 - Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title2"
            id="title2"
            name="title2"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="description2"
        >
          Slide 2 - Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="description2"
            id="description2"
            name="description2"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url2"
        >
          Slide 2 - URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url2"
            id="url2"
            name="url2"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="image2"
        >
          Slide 2 - Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="image2"
            id="image2"
            name="image2"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="openNewWindow2"
              id="openNewWindow2"
              name="openNewWindow2"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="openNewWindow2"
            >
              Slide 2 - Open in new window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title3"
        >
          Slide 3 - Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title3"
            id="title3"
            name="title3"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="description3"
        >
          Slide 3 - Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="description3"
            id="description3"
            name="description3"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url3"
        >
          Slide 3 - URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url3"
            id="url3"
            name="url3"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="image3"
        >
          Slide 3 - Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="image3"
            id="image3"
            name="image3"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="openNewWindow3"
              id="openNewWindow3"
              name="openNewWindow3"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="openNewWindow3"
            >
              Slide 3 - Open in new window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title4"
        >
          Slide 4 - Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title4"
            id="title4"
            name="title4"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="description4"
        >
          Slide 4 - Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="description4"
            id="description4"
            name="description4"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url4"
        >
          Slide 4 - URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url4"
            id="url4"
            name="url4"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="image4"
        >
          Slide 4 - Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="image4"
            id="image4"
            name="image4"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="openNewWindow4"
              id="openNewWindow4"
              name="openNewWindow4"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="openNewWindow4"
            >
              Slide 4 - Open in new window
            </label>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 5`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="index.html"
            >
              Default
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="video_id"
        >
          Video ID
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="video_id"
            id="video_id"
            name="video_id"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="description"
        >
          Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="description"
            id="description"
            name="description"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 6`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="business_promo_main.html"
            >
              Local Ad Unit (Main)
            </option>
            <option
              value="business_promo.html"
            >
              Local Ad Unit (Sidebar)
            </option>
            <option
              value="ownlocal.html"
            >
              OwnLocal business listings
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="numberOfItems"
        >
          Number of items
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="numberOfItems"
            name="numberOfItems"
          >
            <option
              value="3"
            >
              3
            </option>
            <option
              value="6"
            >
              6
            </option>
            <option
              value="9"
            >
              9
            </option>
            <option
              value="12"
            >
              12
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 7`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default.html"
            >
              Default
            </option>
            <option
              value="tributes_funeral.html"
            >
              Tributes & Funeral Notices
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="title"
            name="title"
          >
            <option
              value="3"
            >
              3
            </option>
            <option
              value="6"
            >
              6
            </option>
            <option
              value="9"
            >
              9
            </option>
            <option
              value="12"
            >
              12
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="classifiedListId"
        >
          Classified list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="classifiedListId"
            name="classifiedListId"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            name="limit"
            type="number"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="offset"
        >
          Offset
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="offset"
            id="offset"
            name="offset"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            The offset refers to the position that the classifiedlist will begin. 
Example - if offset is set to 4, the first classified shown will be the 4th story from that classifiedlist
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="pinnedClassifiedsOnly"
              id="pinnedClassifiedsOnly"
              name="pinnedClassifiedsOnly"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="pinnedClassifiedsOnly"
            >
              Pinned classifieds only
            </label>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 8`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default.html"
            >
              Default
            </option>
            <option
              value="with_promoted_link.html"
            >
              With promoted cluster site link
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="storyListId"
        >
          Story list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            disabled=""
            id="storyListId"
            name="storyListId"
          >
            <option>
              Loading...
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="fromOrganisation"
        >
          From organisation
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="fromOrganisation"
            id="fromOrganisation"
            name="fromOrganisation"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="offset"
        >
          Offset
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="offset"
            id="offset"
            name="offset"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            The offset refers to the position that the storylist will begin. 
Example - if offset is set to 4, the first story shown will be the 4th story from that storylist
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            name="limit"
            type="number"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="useCanonicalUrl"
              id="useCanonicalUrl"
              name="useCanonicalUrl"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="useCanonicalUrl"
            >
              Use canonical URL
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Use the story's canonical URL instead of opening on this site, and open in a new window.
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 9`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default.html"
            >
              Default
            </option>
            <option
              value="exploretravel.html"
            >
              Explore Travel
            </option>
            <option
              value="noticeboard-quicklinks.html"
            >
              Noticeboard Quick Links
            </option>
            <option
              value="puzzle-grid.html"
            >
              Puzzle Grid
            </option>
            <option
              value="share-photos-widget.html"
            >
              Share Photos Widget
            </option>
            <option
              value="share-story-widget.html"
            >
              Share Story Widget
            </option>
            <option
              value="standard.html"
            >
              Standard HTML
            </option>
            <option
              value="terms-and-conditions.html"
            >
              Terms and Conditions
            </option>
            <option
              value="whatson-newsletter-widget.html"
            >
              What's On Newsletter Widget
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="code"
        >
          Code
        </label>
        <div
          class="mt-1"
        >
          <textarea
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="code"
            id="code"
            name="code"
            rows="5"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 10`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="featured_comments.html"
            >
              Featured comments
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 11`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="gallery.html"
            >
              Gallery
            </option>
            <option
              value="gallery_weather.html"
            >
              Gallery Weather
            </option>
            <option
              value="video_shorts.html"
            >
              Video shorts
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="player_list_id"
        >
          Player list id
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="player_list_id"
            id="player_list_id"
            name="player_list_id"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="number_of_video"
        >
          Number of Video
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="number_of_video"
            id="number_of_video"
            name="number_of_video"
            type="number"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url"
        >
          url
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url"
            id="url"
            name="url"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 12`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="index.html"
            >
              Default
            </option>
            <option
              value="index_mop.html"
            >
              MOP
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 13`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="list_all.html"
            >
              DPE List - List All
            </option>
            <option
              value="list_no_pagination.html"
            >
              DPE List - Section
            </option>
            <option
              value="index.html"
            >
              DPE List - Today's Paper
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Items per page
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            name="limit"
            type="number"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="dpeId"
        >
          DPE ID
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="dpeId"
            id="dpeId"
            name="dpeId"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 14`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="index.html"
            >
              E-Mags
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Items per page
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            name="limit"
            type="number"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 15`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="carousel_deals.html"
            >
              Carousel
            </option>
            <option
              value="carousel_deals_v2.html"
            >
              CarouselV2
            </option>
            <option
              value="two_column_grid_deals.html"
            >
              Grid Deals - 2 Columns
            </option>
            <option
              value="offer_list_inline.html"
            >
              List Inline
            </option>
            <option
              value="sidebar_deal.html"
            >
              Sidebar Deal
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="apiFilterById"
        >
          Filter Deal by Id
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="apiFilterById"
            id="apiFilterById"
            name="apiFilterById"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="apiFilterByDestination"
        >
          Filter Deal by Destination
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="apiFilterByDestination"
            id="apiFilterByDestination"
            name="apiFilterByDestination"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 16`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="deals_widget.html"
            >
              Deals Widget
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="description"
        >
          Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="description"
            id="description"
            name="description"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="buttonText"
        >
          Button Text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="buttonText"
            id="buttonText"
            name="buttonText"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url"
        >
          URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url"
            id="url"
            name="url"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 17`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default.html"
            >
              Default
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 18`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="index.html"
            >
              Default
            </option>
            <option
              value="inspiring_cinematic_banner.html"
            >
              Inspiring Cinematic Banner
            </option>
            <option
              value="inspiring_static_featured.html"
            >
              Inspiring Static Featured
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="description"
        >
          Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="description"
            id="description"
            name="description"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url"
        >
          URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url"
            id="url"
            name="url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="desktopImage"
        >
          Desktop Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="desktopImage"
            id="desktopImage"
            name="desktopImage"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="tabletImage"
        >
          Tablet Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="tabletImage"
            id="tabletImage"
            name="tabletImage"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="mobileImage"
        >
          Mobile Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="mobileImage"
            id="mobileImage"
            name="mobileImage"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 19`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="index.html"
            >
              Default
            </option>
            <option
              value="explore.html"
            >
              Explore
            </option>
            <option
              value="echidna.html"
            >
              The Echidna
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 20`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="allhomes_strap_heading.html"
            >
              Allhomes Strap Heading
            </option>
            <option
              value="car_expert_strap_heading.html"
            >
              Car Expert Strap Heading
            </option>
            <option
              value="community_photos_heading.html"
            >
              Community Photos Heading
            </option>
            <option
              value="heading1.html"
            >
              Custom Heading
            </option>
            <option
              value="heading2.html"
            >
              Custom Heading with Background
            </option>
            <option
              value="drive_strap_heading.html"
            >
              Drive Strap Heading
            </option>
            <option
              value="explore_centered_strap_heading.html"
            >
              Explore Centered Strap Heading
            </option>
            <option
              value="explore_page_heading_with_subtitle.html"
            >
              Explore Page Heading with subtitle
            </option>
            <option
              value="explore_page_no_link_heading.html"
            >
              Explore Page No Link Heading
            </option>
            <option
              value="explore_strap_heading.html"
            >
              Explore Strap Heading
            </option>
            <option
              value="explore_travel_strap_heading.html"
            >
              Explore Travel Strap Heading
            </option>
            <option
              value="explore_travel_v2_centered_strap_heading.html"
            >
              Explore Travel V2 Centered Strap Heading
            </option>
            <option
              value="farm_online_strap_heading.html"
            >
              Farm Online Strap Heading
            </option>
            <option
              value="farm_weekly_strap_heading.html"
            >
              Farm Weekly Strap Heading
            </option>
            <option
              value="good_fruit_and_veg_strap_heading.html"
            >
              Good Fruit & Veg Strap Heading
            </option>
            <option
              value="heading_without_link.html"
            >
              Heading without link
            </option>
            <option
              value="north_queensland_register_strap_heading.html"
            >
              North Queensland Register Strap Heading
            </option>
            <option
              value="page_heading.html"
            >
              Page Heading
            </option>
            <option
              value="queensland_country_life_strap_heading.html"
            >
              Queensland Country Life Strap Heading
            </option>
            <option
              value="rev_page_heading.html"
            >
              REV Page Heading
            </option>
            <option
              value="signpost_heading.html"
            >
              Signpost Heading
            </option>
            <option
              value="stock_and_land_strap_heading.html"
            >
              Stock & Land Strap Heading
            </option>
            <option
              value="stock_journal_strap_heading.html"
            >
              Stock Journal Strap Heading
            </option>
            <option
              value="the_land_strap_heading.html"
            >
              The Land Strap Heading
            </option>
            <option
              value="the_senior_strap_heading.html"
            >
              The Senior Strap Heading
            </option>
            <option
              value="ugc_contact_us.html"
            >
              UGC Contact Us
            </option>
            <option
              value="ugc_trade_box.html"
            >
              UGC Trade Box
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="heading"
        >
          Heading
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="heading"
            id="heading"
            name="heading"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="openNewWindow"
              id="openNewWindow"
              name="openNewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="openNewWindow"
            >
              Open in a new window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url"
        >
          Url
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url"
            id="url"
            name="url"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 21`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default.html"
            >
              Default
            </option>
            <option
              value="iframe.html"
            >
              Iframe
            </option>
            <option
              value="jotform.html"
            >
              JotForm
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url"
        >
          URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url"
            id="url"
            name="url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="style"
        >
          Style
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="style"
            id="style"
            name="style"
            type="text"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            Custom CSS
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="allow"
        >
          Allow
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="allow"
            id="allow"
            name="allow"
            type="text"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            Allow feature policy options for the iframe
          </p>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 22`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="explore_travel.html"
            >
              Explore Travel Image
            </option>
            <option
              value="left_aligned.html"
            >
              Left Aligned Image
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="desktopImage"
        >
          Desktop Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="desktopImage"
            id="desktopImage"
            name="desktopImage"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="tabletImage"
        >
          Tablet Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="tabletImage"
            id="tabletImage"
            name="tabletImage"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="mobileImage"
        >
          Mobile Image
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="mobileImage"
            id="mobileImage"
            name="mobileImage"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="alt"
        >
          Alt
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="alt"
            id="alt"
            name="alt"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="openNewWindow"
              id="openNewWindow"
              name="openNewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="openNewWindow"
            >
              Open New Window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url"
        >
          Url
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url"
            id="url"
            name="url"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 23`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default.html"
            >
              Help Centre
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="description"
        >
          Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="description"
            id="description"
            name="description"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="url"
        >
          Url
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="url"
            id="url"
            name="url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="buttonText"
        >
          Button text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="buttonText"
            id="buttonText"
            name="buttonText"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 24`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="shortcuts_strap.html"
            >
              Shortcuts Strap
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="brandColor"
        >
          Brand Colour
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="brandColor"
            name="brandColor"
          >
            <option
              value="white"
            >
              White
            </option>
            <option
              value="black"
            >
              Black
            </option>
            <option
              value="custom"
            >
              Custom
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="customColor"
        >
          Custom Colour
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="customColor"
            id="customColor"
            name="customColor"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="fontColor"
        >
          Font Colour
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="fontColor"
            name="fontColor"
          >
            <option
              value="dark"
            >
              Dark
            </option>
            <option
              value="light"
            >
              Light
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="brandImage"
        >
          Brand Image
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="brandImage"
            name="brandImage"
          >
            <option
              value="standard"
            >
              Standard
            </option>
            <option
              value="reversed"
            >
              Reversed
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="estYear"
        >
          Est. Year
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="estYear"
            id="estYear"
            name="estYear"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut1Label"
        >
          Shortcut 1 Label
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut1Label"
            id="shortcut1Label"
            name="shortcut1Label"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut1Description"
        >
          Shortcut 1 Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut1Description"
            id="shortcut1Description"
            name="shortcut1Description"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut1Url"
        >
          Shortcut 1 URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut1Url"
            id="shortcut1Url"
            name="shortcut1Url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut1Icon"
        >
          Shortcut 1 Icon
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut1Icon"
            id="shortcut1Icon"
            name="shortcut1Icon"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut1Cta"
        >
          Shortcut 1 CTA
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut1Cta"
            id="shortcut1Cta"
            name="shortcut1Cta"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut1Badge"
        >
          Shortcut 1 Badge
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut1Badge"
            id="shortcut1Badge"
            name="shortcut1Badge"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="shortcut1NewWindow"
              id="shortcut1NewWindow"
              name="shortcut1NewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="shortcut1NewWindow"
            >
              Shortcut 1 Open in new window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut2Label"
        >
          Shortcut 2 Label
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut2Label"
            id="shortcut2Label"
            name="shortcut2Label"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut2Description"
        >
          Shortcut 2 Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut2Description"
            id="shortcut2Description"
            name="shortcut2Description"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut2Url"
        >
          Shortcut 2 URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut2Url"
            id="shortcut2Url"
            name="shortcut2Url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut2Icon"
        >
          Shortcut 2 Icon
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut2Icon"
            id="shortcut2Icon"
            name="shortcut2Icon"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut2Cta"
        >
          Shortcut 2 CTA
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut2Cta"
            id="shortcut2Cta"
            name="shortcut2Cta"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut2Badge"
        >
          Shortcut 2 Badge
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut2Badge"
            id="shortcut2Badge"
            name="shortcut2Badge"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="shortcut2NewWindow"
              id="shortcut2NewWindow"
              name="shortcut2NewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="shortcut2NewWindow"
            >
              Shortcut 2 Open in new window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut3Label"
        >
          Shortcut 3 Label
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut3Label"
            id="shortcut3Label"
            name="shortcut3Label"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut3Description"
        >
          Shortcut 3 Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut3Description"
            id="shortcut3Description"
            name="shortcut3Description"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut3Url"
        >
          Shortcut 3 URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut3Url"
            id="shortcut3Url"
            name="shortcut3Url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut3Icon"
        >
          Shortcut 3 Icon
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut3Icon"
            id="shortcut3Icon"
            name="shortcut3Icon"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut3Cta"
        >
          Shortcut 3 CTA
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut3Cta"
            id="shortcut3Cta"
            name="shortcut3Cta"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut3Badge"
        >
          Shortcut 3 Badge
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut3Badge"
            id="shortcut3Badge"
            name="shortcut3Badge"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="shortcut3NewWindow"
              id="shortcut3NewWindow"
              name="shortcut3NewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="shortcut3NewWindow"
            >
              Shortcut 3 Open in new window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut4Label"
        >
          Shortcut 4 Label
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut4Label"
            id="shortcut4Label"
            name="shortcut4Label"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut4Description"
        >
          Shortcut 4 Description
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut4Description"
            id="shortcut4Description"
            name="shortcut4Description"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut4Url"
        >
          Shortcut 4 URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut4Url"
            id="shortcut4Url"
            name="shortcut4Url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut4Icon"
        >
          Shortcut 4 Icon
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut4Icon"
            id="shortcut4Icon"
            name="shortcut4Icon"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut4Cta"
        >
          Shortcut 4 CTA
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut4Cta"
            id="shortcut4Cta"
            name="shortcut4Cta"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="shortcut4Badge"
        >
          Shortcut 4 Badge
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="shortcut4Badge"
            id="shortcut4Badge"
            name="shortcut4Badge"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="shortcut4NewWindow"
              id="shortcut4NewWindow"
              name="shortcut4NewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="shortcut4NewWindow"
            >
              Shortcut 4 Open in new window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink1Text"
        >
          External Link 1 Text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink1Text"
            id="externalLink1Text"
            name="externalLink1Text"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink1Url"
        >
          External Link 1 URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink1Url"
            id="externalLink1Url"
            name="externalLink1Url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="externalLink1NewWindow"
              id="externalLink1NewWindow"
              name="externalLink1NewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="externalLink1NewWindow"
            >
              External Link 1 New Window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink1Icon"
        >
          External Link 1 Icon
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink1Icon"
            id="externalLink1Icon"
            name="externalLink1Icon"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink2Text"
        >
          External Link 2 Text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink2Text"
            id="externalLink2Text"
            name="externalLink2Text"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink2Url"
        >
          External Link 2 URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink2Url"
            id="externalLink2Url"
            name="externalLink2Url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="externalLink2NewWindow"
              id="externalLink2NewWindow"
              name="externalLink2NewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="externalLink2NewWindow"
            >
              External Link 2 New Window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink2Icon"
        >
          External Link 2 Icon
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink2Icon"
            id="externalLink2Icon"
            name="externalLink2Icon"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink3Text"
        >
          External Link 3 Text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink3Text"
            id="externalLink3Text"
            name="externalLink3Text"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink3Url"
        >
          External Link 3 URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink3Url"
            id="externalLink3Url"
            name="externalLink3Url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="externalLink3NewWindow"
              id="externalLink3NewWindow"
              name="externalLink3NewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="externalLink3NewWindow"
            >
              External Link 3 New Window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink3Icon"
        >
          External Link 3 Icon
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink3Icon"
            id="externalLink3Icon"
            name="externalLink3Icon"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink4Text"
        >
          External Link 4 Text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink4Text"
            id="externalLink4Text"
            name="externalLink4Text"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink4Url"
        >
          External Link 4 URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink4Url"
            id="externalLink4Url"
            name="externalLink4Url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="externalLink4NewWindow"
              id="externalLink4NewWindow"
              name="externalLink4NewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="externalLink4NewWindow"
            >
              External Link 4 New Window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink4Icon"
        >
          External Link 4 Icon
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink4Icon"
            id="externalLink4Icon"
            name="externalLink4Icon"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink5Text"
        >
          External Link 5 Text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink5Text"
            id="externalLink5Text"
            name="externalLink5Text"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink5Url"
        >
          External Link 5 URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink5Url"
            id="externalLink5Url"
            name="externalLink5Url"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="externalLink5NewWindow"
              id="externalLink5NewWindow"
              name="externalLink5NewWindow"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="externalLink5NewWindow"
            >
              External Link 5 New Window
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="externalLink5Icon"
        >
          External Link 5 Icon
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="externalLink5Icon"
            id="externalLink5Icon"
            name="externalLink5Icon"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="advertisingUrl"
        >
          Advertising URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="advertisingUrl"
            id="advertisingUrl"
            name="advertisingUrl"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 25`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="card_grid.html"
            >
              Card Grid
            </option>
            <option
              value="collection_carousel.html"
            >
              Collection Carousel
            </option>
            <option
              value="explore-collection-sub-navigation-strip.html"
            >
              Explore Collection Sub Navigation Strip
            </option>
            <option
              value="four_column_title_list.html"
            >
              Four Column Title List
            </option>
            <option
              value="horizontal_strip.html"
            >
              Horizontal Strip
            </option>
            <option
              value="image_card_list.html"
            >
              Image Card List
            </option>
            <option
              value="pill_page_navigation.html"
            >
              Pill Page Navigation
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="collection"
        >
          Collection
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            disabled=""
            id="collection"
            name="collection"
          >
            <option>
              Loading...
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 26`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="grade.html"
            >
              Grade
            </option>
            <option
              value="season.html"
            >
              Season
            </option>
            <option
              value="upcomingMatches.html"
            >
              Upcoming Matches
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="organisationId"
        >
          Organisation Id
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="organisationId"
            id="organisationId"
            name="organisationId"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="seasonId"
        >
          Season Id
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="seasonId"
            id="seasonId"
            name="seasonId"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="grade1Id"
        >
          Grade #1 Id
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="grade1Id"
            id="grade1Id"
            name="grade1Id"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="grade2Id"
        >
          Grade #2 Id
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="grade2Id"
            id="grade2Id"
            name="grade2Id"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="leagueUrl"
        >
          League URL
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="leagueUrl"
            id="leagueUrl"
            name="leagueUrl"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="leagueName"
        >
          League name
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="leagueName"
            id="leagueName"
            name="leagueName"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="leagueImageId"
        >
          League image ID
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="leagueImageId"
            id="leagueImageId"
            name="leagueImageId"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 27`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default.html"
            >
              Default
            </option>
            <option
              value="echidna.html"
            >
              The Echidna
            </option>
            <option
              value="today_paper_alert.html"
            >
              Today's Paper Alert
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="headingText"
        >
          Heading text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="headingText"
            id="headingText"
            name="headingText"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="text"
        >
          Support text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="text"
            id="text"
            name="text"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="tags"
        >
          Tags
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="tags"
            id="tags"
            name="tags"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="subscribeVisible"
              id="subscribeVisible"
              name="subscribeVisible"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="subscribeVisible"
            >
              Widget visible
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              If unchecked, widget will not be visible to non-subscribers
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="formData"
        >
          Form data
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="formData"
            id="formData"
            name="formData"
            type="text"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            Additional query string data for the form action
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="marketingCloudUrl"
        >
          Marketing cloud url
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="marketingCloudUrl"
            id="marketingCloudUrl"
            name="marketingCloudUrl"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 28`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="car_expert_strap_heading.html"
            >
              Car Expert Strap Heading
            </option>
            <option
              value="explore_page_heading_with_subtitle.html"
            >
              Explore Page Heading with subtitle
            </option>
            <option
              value="explore_travel_strap_heading.html"
            >
              Explore Travel Strap Heading
            </option>
            <option
              value="farmbuy_strap_heading.html"
            >
              FarmBuy Strap Heading
            </option>
            <option
              value="strap_heading_with_subtitle.html"
            >
              Heading with subtitle (Explore Travel)
            </option>
            <option
              value="mop_strap_heading.html"
            >
              MOP Heading
            </option>
            <option
              value="page_navigation.html"
            >
              Page Navigation
            </option>
            <option
              value="rev_strap_heading.html"
            >
              REV Strap Heading
            </option>
            <option
              value="strap_heading.html"
            >
              Strap Heading
            </option>
            <option
              value="echidna_strap_heading.html"
            >
              The Echidna Strap Heading
            </option>
            <option
              value="echidna_view_all.html"
            >
              The Echidna View All
            </option>
            <option
              value="video_strap_heading.html"
            >
              Video Strap Heading
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="page"
        >
          Page
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            disabled=""
            id="page"
            name="page"
          >
            <option>
              Loading...
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="subtitle"
        >
          Subtitle
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="subtitle"
            id="subtitle"
            name="subtitle"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 29`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="newsletter.html"
            >
              Newsletters
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 30`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="rev_widget.html"
            >
              Default
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 31`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="share_and_bookmark.html"
            >
              Default
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 32`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="upcomingMatches.html"
            >
              Upcoming Matches
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 33`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="3ColListViewLoadMore.html"
            >
              3Col Articles with Load More
            </option>
            <option
              value="3ColFeatureArticles.html"
            >
              3ColFeatureArticles
            </option>
            <option
              value="allhomes.html"
            >
              All Homes
            </option>
            <option
              value="allhomes-featured"
            >
              All Homes Featured
            </option>
            <option
              value="author-stories-listview.html"
            >
              Author Stories List View
            </option>
            <option
              value="businessfeature.html"
            >
              Business Feature (ESOV)
            </option>
            <option
              value="carousel.html"
            >
              Carousel
            </option>
            <option
              value="carousel-center-mode.html"
            >
              Carousel Center Mode
            </option>
            <option
              value="dailymotion-list.html"
            >
              Dailymotion - List
            </option>
            <option
              value="domain.html"
            >
              Domain
            </option>
            <option
              value="drive.html"
            >
              Drive
            </option>
            <option
              value="explore.html"
            >
              Explore
            </option>
            <option
              value="explore-travel-carousel.html"
            >
              Explore Travel Carousel
            </option>
            <option
              value="explore-travel-infinite-scroll.html"
            >
              Explore Travel Infinite Scroll
            </option>
            <option
              value="explore-travel-latest-and-most-viewed.html"
            >
              Explore Travel Latest and Most Viewed
            </option>
            <option
              value="featuredPhotos.html"
            >
              Featured Photos (Community Index)
            </option>
            <option
              value="featuredPhotosWidget.html"
            >
              Featured Photos (Widget)
            </option>
            <option
              value="help-centre-listview.html"
            >
              Help Centre List View
            </option>
            <option
              value="inline.html"
            >
              Inline List View
            </option>
            <option
              value="listview-left-images.html"
            >
              Left images list View
            </option>
            <option
              value="listview.html"
            >
              List View
            </option>
            <option
              value="listview-load-more.html"
            >
              List View with Load More
            </option>
            <option
              value="listview-load-more-left-images.html"
            >
              List View with Load More - Left Images
            </option>
            <option
              value="local-partner-widget.html"
            >
              Local Partner Widget
            </option>
            <option
              value="local-partner-load-more.html"
            >
              Local Partner with Load More
            </option>
            <option
              value="main.html"
            >
              Main
            </option>
            <option
              value="most-asked-questions.html"
            >
              Most Asked Questions
            </option>
            <option
              value="newswell.html"
            >
              Newswell
            </option>
            <option
              value="newswellexplore.html"
            >
              Newswell (Explore)
            </option>
            <option
              value="parallax.html"
            >
              Parallax
            </option>
            <option
              value="realestateview.html"
            >
              Real Estate View
            </option>
            <option
              value="scoresanddraws.html"
            >
              Score And Draws
            </option>
            <option
              value="sports_hub.html"
            >
              Sports Hub
            </option>
            <option
              value="echidna.html"
            >
              The Echidna - Article
            </option>
            <option
              value="echidna_cartoon.html"
            >
              The Echidna - Cartoon
            </option>
            <option
              value="echidna_cartoon_list.html"
            >
              The Echidna - Cartoon List
            </option>
            <option
              value="echidna_list.html"
            >
              The Echidna - List
            </option>
            <option
              value="echidna_paginated_list.html"
            >
              The Echidna - Paginated list
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="storyListId"
        >
          Story list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            disabled=""
            id="storyListId"
            name="storyListId"
          >
            <option>
              Loading...
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="urlParams"
        >
          URL parameters
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="urlParams"
            id="urlParams"
            name="urlParams"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="label"
        >
          Label
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="label"
            id="label"
            name="label"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="offset"
        >
          Offset
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="offset"
            id="offset"
            name="offset"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            The offset refers to the position that the storylist will begin. 
Example - if offset is set to 4, the first story shown will be the 4th story from that storylist
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            max="20"
            min="1"
            name="limit"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            The number of the stories will be shown (value range 1 to 20)
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="summaryOptions"
        >
          Summary: select option for story list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="summaryOptions"
            name="summaryOptions"
          >
            <option
              value="E"
            >
              Enable(only show summary that is enabled in Newsnow)
            </option>
            <option
              value="A"
            >
              Always show(override settings in Newsnow)
            </option>
            <option
              value="D"
            >
              hide summary; override settings in Newsnow
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="largeLeadStory"
              id="largeLeadStory"
              name="largeLeadStory"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="largeLeadStory"
            >
              Show lead story
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6 hidden"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="isHeroImage"
              id="isHeroImage"
              name="isHeroImage"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="isHeroImage"
            >
              Toggle large image on lead story
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="flipStoryDisplay"
              id="flipStoryDisplay"
              name="flipStoryDisplay"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="flipStoryDisplay"
            >
              Flip story display
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Put the large story on the right
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="pinnedStoriesOnly"
              id="pinnedStoriesOnly"
              name="pinnedStoriesOnly"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="pinnedStoriesOnly"
            >
              Pinned stories only
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="allowAds"
              id="allowAds"
              name="allowAds"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="allowAds"
            >
              Allow ads
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Display ads if the layout supports it
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="useCanonicalUrl"
              id="useCanonicalUrl"
              name="useCanonicalUrl"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="useCanonicalUrl"
            >
              Use canonical URL
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Use the story's canonical URL instead of opening on this site, and open in a new window.
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="disableBottomBorder"
              id="disableBottomBorder"
              name="disableBottomBorder"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="disableBottomBorder"
            >
              Disable bottom border
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Hide the bottom border line
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 34`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="explore-travel-carousel.html"
            >
              Explore Travel Collection Carousel
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="collection"
        >
          Collection
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            disabled=""
            id="collection"
            name="collection"
          >
            <option>
              Loading...
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            max="20"
            min="1"
            name="limit"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            The number of the stories will be shown (value range 1 to 20)
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="tags"
        >
          Tags
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="tags"
            id="tags"
            name="tags"
            type="text"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            Comma separated list of tags that get merged with each storylist in the collection to provide extra context
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="useCanonicalUrl"
              id="useCanonicalUrl"
              name="useCanonicalUrl"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="useCanonicalUrl"
            >
              Use canonical URL
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Use the story's canonical URL instead of opening on this site, and open in a new window.
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 35`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="text1.html"
            >
              Text
            </option>
            <option
              value="text2.html"
            >
              Text with background
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="text"
        >
          Text
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="text"
            id="text"
            name="text"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 36`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="default.html"
            >
              Default
            </option>
            <option
              value="numbered.html"
            >
              Numbered
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="storyListId"
        >
          Story list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            disabled=""
            id="storyListId"
            name="storyListId"
          >
            <option>
              Loading...
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="urlParams"
        >
          URL parameters
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="urlParams"
            id="urlParams"
            name="urlParams"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            name="limit"
            type="number"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="pinnedStoriesOnly"
              id="pinnedStoriesOnly"
              name="pinnedStoriesOnly"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="pinnedStoriesOnly"
            >
              Pinned stories only
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="allowAds"
              id="allowAds"
              name="allowAds"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="allowAds"
            >
              Allow ads
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Display ads if the layout supports it
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="useCanonicalUrl"
              id="useCanonicalUrl"
              name="useCanonicalUrl"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="useCanonicalUrl"
            >
              Use canonical URL
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Use the story's canonical URL instead of opening on this site, and open in a new window.
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 37`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="traffic.html"
            >
              Live Traffic
            </option>
            <option
              value="rfs.html"
            >
              RFS widget
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            min="1"
            name="limit"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            Limit the number of listings shown
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="title"
            id="title"
            name="title"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="range"
        >
          Distance range
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="range"
            id="range"
            name="range"
            type="text"
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 38`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="local-expert.html"
            >
              Local Expert
            </option>
            <option
              value="grid-view.html"
            >
              Photos (Grid)
            </option>
            <option
              value="photos-widget.html"
            >
              Photos Widget
            </option>
            <option
              value="whats-on.html"
            >
              What's On (Vertical Calendar)
            </option>
            <option
              value="whats-on-widget.html"
            >
              What's On Widget
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="title"
        >
          Title
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="title"
            name="title"
          >
            <option
              value="3"
            >
              3
            </option>
            <option
              value="6"
            >
              6
            </option>
            <option
              value="9"
            >
              9
            </option>
            <option
              value="12"
            >
              12
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="ugcListId"
        >
          UGC list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="ugcListId"
            name="ugcListId"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            name="limit"
            type="number"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="offset"
        >
          Offset
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="offset"
            id="offset"
            name="offset"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            The offset refers to the position that the ugclist will begin. 
Example - if offset is set to 4, the first ugc shown will be the 4th ugc from that ugclist
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="pinnedUgcOnly"
              id="pinnedUgcOnly"
              name="pinnedUgcOnly"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="pinnedUgcOnly"
            >
              Pinned ugc only
            </label>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 39`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="viewjobs.html"
            >
              Default
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            min="1"
            name="limit"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            Limit the number of listings shown
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="offset"
        >
          Offset
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="offset"
            id="offset"
            name="offset"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            The offset in the listings to start from
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="stickyFeatured"
              id="stickyFeatured"
              name="stickyFeatured"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="stickyFeatured"
            >
              Featured listings appear first
            </label>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`<ZoneItemForm /> should render a form for each zone item type 40`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="weathercard.html"
            >
              Weather card
            </option>
            <option
              value="weathercard-with-border.html"
            >
              Weather card with border
            </option>
            <option
              value="weatherzone.html"
            >
              WeatherZone
            </option>
          </select>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`story list form hide is_hero_image checkbox when large_lead_article uncheck 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="3ColListViewLoadMore.html"
            >
              3Col Articles with Load More
            </option>
            <option
              value="3ColFeatureArticles.html"
            >
              3ColFeatureArticles
            </option>
            <option
              value="allhomes.html"
            >
              All Homes
            </option>
            <option
              value="allhomes-featured"
            >
              All Homes Featured
            </option>
            <option
              value="author-stories-listview.html"
            >
              Author Stories List View
            </option>
            <option
              value="businessfeature.html"
            >
              Business Feature (ESOV)
            </option>
            <option
              value="carousel.html"
            >
              Carousel
            </option>
            <option
              value="carousel-center-mode.html"
            >
              Carousel Center Mode
            </option>
            <option
              value="dailymotion-list.html"
            >
              Dailymotion - List
            </option>
            <option
              value="domain.html"
            >
              Domain
            </option>
            <option
              value="drive.html"
            >
              Drive
            </option>
            <option
              value="explore.html"
            >
              Explore
            </option>
            <option
              value="explore-travel-carousel.html"
            >
              Explore Travel Carousel
            </option>
            <option
              value="explore-travel-infinite-scroll.html"
            >
              Explore Travel Infinite Scroll
            </option>
            <option
              value="explore-travel-latest-and-most-viewed.html"
            >
              Explore Travel Latest and Most Viewed
            </option>
            <option
              value="featuredPhotos.html"
            >
              Featured Photos (Community Index)
            </option>
            <option
              value="featuredPhotosWidget.html"
            >
              Featured Photos (Widget)
            </option>
            <option
              value="help-centre-listview.html"
            >
              Help Centre List View
            </option>
            <option
              value="inline.html"
            >
              Inline List View
            </option>
            <option
              value="listview-left-images.html"
            >
              Left images list View
            </option>
            <option
              value="listview.html"
            >
              List View
            </option>
            <option
              value="listview-load-more.html"
            >
              List View with Load More
            </option>
            <option
              value="listview-load-more-left-images.html"
            >
              List View with Load More - Left Images
            </option>
            <option
              value="local-partner-widget.html"
            >
              Local Partner Widget
            </option>
            <option
              value="local-partner-load-more.html"
            >
              Local Partner with Load More
            </option>
            <option
              value="main.html"
            >
              Main
            </option>
            <option
              value="most-asked-questions.html"
            >
              Most Asked Questions
            </option>
            <option
              value="newswell.html"
            >
              Newswell
            </option>
            <option
              value="newswellexplore.html"
            >
              Newswell (Explore)
            </option>
            <option
              value="parallax.html"
            >
              Parallax
            </option>
            <option
              value="realestateview.html"
            >
              Real Estate View
            </option>
            <option
              value="scoresanddraws.html"
            >
              Score And Draws
            </option>
            <option
              value="sports_hub.html"
            >
              Sports Hub
            </option>
            <option
              value="echidna.html"
            >
              The Echidna - Article
            </option>
            <option
              value="echidna_cartoon.html"
            >
              The Echidna - Cartoon
            </option>
            <option
              value="echidna_cartoon_list.html"
            >
              The Echidna - Cartoon List
            </option>
            <option
              value="echidna_list.html"
            >
              The Echidna - List
            </option>
            <option
              value="echidna_paginated_list.html"
            >
              The Echidna - Paginated list
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="storyListId"
        >
          Story list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            disabled=""
            id="storyListId"
            name="storyListId"
          >
            <option>
              Loading...
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="urlParams"
        >
          URL parameters
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="urlParams"
            id="urlParams"
            name="urlParams"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="label"
        >
          Label
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="label"
            id="label"
            name="label"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="offset"
        >
          Offset
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="offset"
            id="offset"
            name="offset"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            The offset refers to the position that the storylist will begin. 
Example - if offset is set to 4, the first story shown will be the 4th story from that storylist
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="limit"
            id="limit"
            max="20"
            min="1"
            name="limit"
            type="number"
          />
          <p
            class="whitespace-pre-line text-gray-500"
          >
            The number of the stories will be shown (value range 1 to 20)
          </p>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="summaryOptions"
        >
          Summary: select option for story list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="summaryOptions"
            name="summaryOptions"
          >
            <option
              value="E"
            >
              Enable(only show summary that is enabled in Newsnow)
            </option>
            <option
              value="A"
            >
              Always show(override settings in Newsnow)
            </option>
            <option
              value="D"
            >
              hide summary; override settings in Newsnow
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="largeLeadStory"
              id="largeLeadStory"
              name="largeLeadStory"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="largeLeadStory"
            >
              Show lead story
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6 hidden"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="isHeroImage"
              id="isHeroImage"
              name="isHeroImage"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="isHeroImage"
            >
              Toggle large image on lead story
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="flipStoryDisplay"
              id="flipStoryDisplay"
              name="flipStoryDisplay"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="flipStoryDisplay"
            >
              Flip story display
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Put the large story on the right
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="pinnedStoriesOnly"
              id="pinnedStoriesOnly"
              name="pinnedStoriesOnly"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="pinnedStoriesOnly"
            >
              Pinned stories only
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="allowAds"
              id="allowAds"
              name="allowAds"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="allowAds"
            >
              Allow ads
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Display ads if the layout supports it
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="useCanonicalUrl"
              id="useCanonicalUrl"
              name="useCanonicalUrl"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="useCanonicalUrl"
            >
              Use canonical URL
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Use the story's canonical URL instead of opening on this site, and open in a new window.
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="disableBottomBorder"
              id="disableBottomBorder"
              name="disableBottomBorder"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="disableBottomBorder"
            >
              Disable bottom border
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Hide the bottom border line
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;

exports[`story list form test form validation 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <form
    class="shadow sm:overflow-hidden sm:rounded-md"
    novalidate=""
  >
    <div
      class="space-y-6 bg-white px-4 py-5 sm:p-6"
    >
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="template"
        >
          Template
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="template"
            name="template"
          >
            <option
              value="3ColListViewLoadMore.html"
            >
              3Col Articles with Load More
            </option>
            <option
              value="3ColFeatureArticles.html"
            >
              3ColFeatureArticles
            </option>
            <option
              value="allhomes.html"
            >
              All Homes
            </option>
            <option
              value="allhomes-featured"
            >
              All Homes Featured
            </option>
            <option
              value="author-stories-listview.html"
            >
              Author Stories List View
            </option>
            <option
              value="businessfeature.html"
            >
              Business Feature (ESOV)
            </option>
            <option
              value="carousel.html"
            >
              Carousel
            </option>
            <option
              value="carousel-center-mode.html"
            >
              Carousel Center Mode
            </option>
            <option
              value="dailymotion-list.html"
            >
              Dailymotion - List
            </option>
            <option
              value="domain.html"
            >
              Domain
            </option>
            <option
              value="drive.html"
            >
              Drive
            </option>
            <option
              value="explore.html"
            >
              Explore
            </option>
            <option
              value="explore-travel-carousel.html"
            >
              Explore Travel Carousel
            </option>
            <option
              value="explore-travel-infinite-scroll.html"
            >
              Explore Travel Infinite Scroll
            </option>
            <option
              value="explore-travel-latest-and-most-viewed.html"
            >
              Explore Travel Latest and Most Viewed
            </option>
            <option
              value="featuredPhotos.html"
            >
              Featured Photos (Community Index)
            </option>
            <option
              value="featuredPhotosWidget.html"
            >
              Featured Photos (Widget)
            </option>
            <option
              value="help-centre-listview.html"
            >
              Help Centre List View
            </option>
            <option
              value="inline.html"
            >
              Inline List View
            </option>
            <option
              value="listview-left-images.html"
            >
              Left images list View
            </option>
            <option
              value="listview.html"
            >
              List View
            </option>
            <option
              value="listview-load-more.html"
            >
              List View with Load More
            </option>
            <option
              value="listview-load-more-left-images.html"
            >
              List View with Load More - Left Images
            </option>
            <option
              value="local-partner-widget.html"
            >
              Local Partner Widget
            </option>
            <option
              value="local-partner-load-more.html"
            >
              Local Partner with Load More
            </option>
            <option
              value="main.html"
            >
              Main
            </option>
            <option
              value="most-asked-questions.html"
            >
              Most Asked Questions
            </option>
            <option
              value="newswell.html"
            >
              Newswell
            </option>
            <option
              value="newswellexplore.html"
            >
              Newswell (Explore)
            </option>
            <option
              value="parallax.html"
            >
              Parallax
            </option>
            <option
              value="realestateview.html"
            >
              Real Estate View
            </option>
            <option
              value="scoresanddraws.html"
            >
              Score And Draws
            </option>
            <option
              value="sports_hub.html"
            >
              Sports Hub
            </option>
            <option
              value="echidna.html"
            >
              The Echidna - Article
            </option>
            <option
              value="echidna_cartoon.html"
            >
              The Echidna - Cartoon
            </option>
            <option
              value="echidna_cartoon_list.html"
            >
              The Echidna - Cartoon List
            </option>
            <option
              value="echidna_list.html"
            >
              The Echidna - List
            </option>
            <option
              value="echidna_paginated_list.html"
            >
              The Echidna - Paginated list
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="storyListId"
        >
          Story list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            disabled=""
            id="storyListId"
            name="storyListId"
          >
            <option>
              Error retrieving choices
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="urlParams"
        >
          URL parameters
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="urlParams"
            id="urlParams"
            name="urlParams"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="label"
        >
          Label
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
            data-testid="label"
            id="label"
            name="label"
            type="text"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="offset"
        >
          Offset
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-red-300 text-red-900 placeholder:text-red-300 focus:border-red-500 focus:outline-none focus:ring-red-500"
            data-testid="offset"
            id="offset"
            name="offset"
            type="number"
          />
          <p
            class="mt-2 text-sm text-red-500"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="limit"
        >
          Limit
        </label>
        <div
          class="mt-1"
        >
          <input
            class="block w-full rounded-md shadow-sm sm:text-sm border-red-300 text-red-900 placeholder:text-red-300 focus:border-red-500 focus:outline-none focus:ring-red-500"
            data-testid="limit"
            id="limit"
            max="20"
            min="1"
            name="limit"
            type="number"
          />
          <p
            class="mt-2 text-sm text-red-500"
          />
        </div>
      </div>
      <div
        class="mb-6"
      >
        <label
          class="block text-sm font-medium text-gray-700"
          for="summaryOptions"
        >
          Summary: select option for story list
        </label>
        <div
          class="mt-1"
        >
          <select
            class="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
            id="summaryOptions"
            name="summaryOptions"
          >
            <option
              value="E"
            >
              Enable(only show summary that is enabled in Newsnow)
            </option>
            <option
              value="A"
            >
              Always show(override settings in Newsnow)
            </option>
            <option
              value="D"
            >
              hide summary; override settings in Newsnow
            </option>
          </select>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="largeLeadStory"
              id="largeLeadStory"
              name="largeLeadStory"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="largeLeadStory"
            >
              Show lead story
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="isHeroImage"
              id="isHeroImage"
              name="isHeroImage"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="isHeroImage"
            >
              Toggle large image on lead story
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="flipStoryDisplay"
              id="flipStoryDisplay"
              name="flipStoryDisplay"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="flipStoryDisplay"
            >
              Flip story display
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Put the large story on the right
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="pinnedStoriesOnly"
              id="pinnedStoriesOnly"
              name="pinnedStoriesOnly"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="pinnedStoriesOnly"
            >
              Pinned stories only
            </label>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="allowAds"
              id="allowAds"
              name="allowAds"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="allowAds"
            >
              Allow ads
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Display ads if the layout supports it
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="useCanonicalUrl"
              id="useCanonicalUrl"
              name="useCanonicalUrl"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="useCanonicalUrl"
            >
              Use canonical URL
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Use the story's canonical URL instead of opening on this site, and open in a new window.
            </p>
          </div>
        </div>
      </div>
      <div
        class="mb-6"
      >
        <div
          class="flex items-start"
        >
          <div
            class="flex h-5 items-center"
          >
            <input
              class="h-4 w-4 text-blue-600 border-gray-300 rounded mr-2 border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              data-testid="disableBottomBorder"
              id="disableBottomBorder"
              name="disableBottomBorder"
              type="checkbox"
            />
          </div>
          <div
            class="ml-3 text-sm"
          >
            <label
              class="block text-sm font-medium text-gray-700"
              for="disableBottomBorder"
            >
              Disable bottom border
            </label>
            <p
              class="whitespace-pre-line text-gray-500"
            >
              Hide the bottom border line
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6"
    >
      <button
        class="rounded-md border border-transparent bg-red-600 p-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        data-testid="delete"
        title="Delete"
        type="button"
      >
        <i>
          {"icon":{"prefix":"fas","iconName":"trash-can"},"fixedWidth":true}
        </i>
        <span>
           Delete
        </span>
      </button>
      <div
        class="text-right"
      >
        <button
          class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          data-testid="save"
          type="submit"
        >
          Save
        </button>
        <button
          class="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
`;
