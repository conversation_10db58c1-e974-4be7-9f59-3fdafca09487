'use client';

import { ErrorMessage } from '@hookform/error-message';
import clsx from 'clsx';
import React, { useCallback, useRef } from 'react';
import { useForm } from 'react-hook-form';

import makeToast from 'components/editmode/ui/toast';
import ChoiceField from 'components/editmode/zoneElements/ChoiceField';
import zoneItemTypeInfo from 'components/editmode/zoneElements/zoneItemInfo';
import { useAppDispatch } from 'store/hooks';
import zoneItemsSlice from 'store/slices/zoneItems';
import DeleteButton from 'themes/autumn/components/zone/DeleteButton';
import { ZoneItemType } from 'types/ZoneItems';
import { useAccessToken } from 'util/hooks';
import {
  createZoneItem,
  deleteZoneItem,
  fetchPages,
  fetchStoriesForStoryList,
  updateZoneItem,
} from 'util/organization/suzuka';
import { camelKeysToSnake, camelToWords } from 'util/string';

import type {
  MenuListZoneItem,
  StoryListZoneItem,
  ZoneItemBase,
} from 'types/ZoneItems';

interface Props {
  data?: Record<string, number | string | boolean>;
  id?: number;
  isEditOnly?: boolean;
  onClose: (submit: boolean) => void;
  pageId: number;
  siteId: number;
  type: ZoneItemType;
  zone: string;
  zoneItemId: number;
}

function nameToLabel(name: string): string {
  const label = camelToWords(name);

  return label.charAt(0).toUpperCase() + label.slice(1);
}

const typeToInputType = {
  boolean: 'checkbox',
  multiline: 'textarea',
  number: 'number',
  string: 'text',
};

const inputTypeClasses: Record<string, string> = {
  checkbox: 'h-4 w-4 text-blue-600 border-gray-300 rounded mr-2',
};

export default function ZoneItemForm({
  data,
  id,
  isEditOnly = false,
  onClose,
  pageId,
  siteId,
  type,
  zone,
  zoneItemId,
}: Props): React.ReactElement {
  const dispatch = useAppDispatch();
  const token = useAccessToken();

  const form = useRef<HTMLFormElement>(null);

  // Build the default values from the field names. The data object may also
  // contain data outside the editable fields, which we don't want
  const defaultValues = zoneItemTypeInfo[type].fields?.reduce(
    (values, fieldOrName) => {
      const field =
        typeof fieldOrName === 'string'
          ? {
              name: fieldOrName,
            }
          : fieldOrName;

      return {
        ...values,
        [field.name]: data?.[field.name],
      };
    },
    {},
  );

  const {
    formState: { errors },
    handleSubmit,
    register,
    resetField,
    watch,
  } = useForm<Record<string, boolean | number | string>>({
    defaultValues,
  });

  const onSubmit = handleSubmit(async (fieldValues) => {
    const snakeKeysData = camelKeysToSnake(
      fieldValues,
    ) as unknown as ZoneItemBase;

    let elementId;
    let storeId;

    try {
      if (id) {
        elementId = id;
        storeId = zoneItemId;
        await updateZoneItem(id, type, snakeKeysData);
      } else {
        ({ elementId, zoneItemId: storeId } = await createZoneItem(
          type,
          zone,
          pageId,
          siteId,
          snakeKeysData,
        ));
      }
    } catch {
      makeToast('Failed to save zone item');
      return;
    }

    if (
      type === ZoneItemType.StoryList ||
      type === ZoneItemType.ClusteredStoryList ||
      type === ZoneItemType.TitledStoryList
    ) {
      const targetValues = ['storyListId', 'limit', 'offset'];
      if (targetValues.some((value) => value in fieldValues)) {
        const storyListData =
          fieldValues as unknown as StoryListZoneItem['zoneItemData'];
        storyListData.stories = await fetchStoriesForStoryList({
          limit: storyListData.limit ?? undefined,
          offset: (storyListData.offset ?? 1) - 1,
          siteId,
          storyListId: storyListData.storyListId,
          useCanonicalUrl: storyListData.useCanonicalUrl,
        });
      }
    } else if (type === ZoneItemType.MenuList) {
      const menuListData =
        fieldValues as unknown as MenuListZoneItem['zoneItemData'];
      const pages = await fetchPages(siteId, token);
      const page = pages.find((p) => p.id === menuListData.page);

      if (!page) {
        makeToast('Could not find page');
        return;
      }

      menuListData.name = page.name;
      menuListData.url = page.link;
    }

    onClose(true);

    if (
      type === ZoneItemType.Navigation ||
      type === ZoneItemType.DPECard ||
      type === ZoneItemType.DPEList
    ) {
      window.location.reload();
      return;
    }

    dispatch(
      zoneItemsSlice.actions.updateZoneItem({
        data: fieldValues,
        elementId,
        newId: storeId,
        zoneItemId,
      }),
    );
  });

  const onCancel = useCallback(() => onClose(false), [onClose]);

  function submitHandler(event: React.FormEvent) {
    onSubmit(event).catch(() => {});
  }

  return (
    <form
      className="shadow sm:overflow-hidden sm:rounded-md"
      noValidate
      onSubmit={submitHandler}
      ref={form}
    >
      <div className="space-y-6 bg-white px-4 py-5 sm:p-6">
        {zoneItemTypeInfo[type].fields?.map((fieldOrName) => {
          const field =
            typeof fieldOrName === 'string'
              ? {
                  name: fieldOrName,
                }
              : fieldOrName;

          const inputType = typeToInputType[field.type || 'string'];
          const shouldInlineInput =
            inputType === 'checkbox' || inputType === 'radio';

          const dependentName = field.dependent;
          const isDependentChecked = dependentName
            ? watch(dependentName)
            : true;

          const hasError = field.name in errors;

          // Add extra default validation for number fields
          const rules = {
            valueAsNumber: field.type === 'number',
            ...field.rules,
            required: field.type === 'number' ? true : field.rules?.required,
          };

          let input;

          if (field.choices) {
            input = (
              <ChoiceField
                choices={field.choices}
                onLoad={resetField}
                // eslint-disable-next-line react/jsx-props-no-spreading
                {...register(field.name, rules)}
              />
            );
          } else {
            const classes = clsx(
              inputTypeClasses[inputType] ||
                'block w-full rounded-md shadow-sm sm:text-sm',
              {
                'border-gray-300 text-gray-900': !hasError,
                'border-red-300 text-red-900 placeholder:text-red-300':
                  hasError,
                'focus:border-blue-500 focus:ring-blue-500': !hasError,
                'focus:border-red-500 focus:outline-none focus:ring-red-500':
                  hasError,
              },
            );
            input = (
              <>
                {inputType === 'textarea' ? (
                  <textarea
                    className={classes}
                    cols={field.cols}
                    data-testid={field.name}
                    id={field.name}
                    rows={field.rows}
                    // eslint-disable-next-line react/jsx-props-no-spreading
                    {...register(field.name, rules)}
                  />
                ) : (
                  <input
                    className={classes}
                    data-testid={field.name}
                    id={field.name}
                    max={field.max}
                    min={field.min}
                    type={inputType}
                    // eslint-disable-next-line react/jsx-props-no-spreading
                    {...register(field.name, rules)}
                  />
                )}
                <ErrorMessage
                  errors={errors}
                  name={field.name}
                  render={({ message }) => (
                    <p className="mt-2 text-sm text-red-500">{message}</p>
                  )}
                />
              </>
            );
          }

          const label = (
            <label
              className="block text-sm font-medium text-gray-700"
              htmlFor={field.name}
            >
              {field.label || nameToLabel(field.name)}
            </label>
          );

          const helpText = field.helpText && !hasError && (
            <p className="whitespace-pre-line text-gray-500">
              {field.helpText}
            </p>
          );

          return (
            <div
              className={clsx('mb-6', {
                hidden: !isDependentChecked,
              })}
              key={field.name}
            >
              {shouldInlineInput ? (
                <div className="flex items-start">
                  <div className="flex h-5 items-center">{input}</div>
                  <div className="ml-3 text-sm">
                    {label}
                    {helpText}
                  </div>
                </div>
              ) : (
                <>
                  {label}
                  <div className="mt-1">
                    {input}
                    {helpText}
                  </div>
                </>
              )}
            </div>
          );
        })}
      </div>
      <div className="flex flex-wrap items-center justify-between gap-y-2 bg-gray-50 px-4 py-3 sm:px-6">
        {!isEditOnly && (
          <DeleteButton
            isExpandToRight
            onDelete={async () => {
              if (id) {
                try {
                  await deleteZoneItem(id, type);
                } catch {
                  makeToast('Could not delete zone item');
                  return;
                }
              }
              dispatch(zoneItemsSlice.actions.deleteZoneItem({ zoneItemId }));
            }}
            showText
          />
        )}
        <div className="text-right">
          <button
            className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            data-testid="save"
            type="submit"
          >
            Save
          </button>
          <button
            className="ml-2 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            onClick={onCancel}
            type="button"
          >
            Cancel
          </button>
        </div>
      </div>
    </form>
  );
}
