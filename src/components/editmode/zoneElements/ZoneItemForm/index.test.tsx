import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { ZoneItemType } from 'types/ZoneItems';
import { TestWrapper } from 'util/jest';

import ZoneItemForm from '.';

describe('<ZoneItemForm />', () => {
  it('should render a form for each zone item type', () => {
    expect.assertions(40);

    const store = createStore((state) => ({
      ...state,
      accessToken: '123',
    }));

    Object.values(ZoneItemType).forEach((type) => {
      const { container } = render(
        <TestWrapper store={store}>
          <ZoneItemForm
            onClose={() => {}}
            pageId={1}
            siteId={1}
            type={type}
            zone="main"
            zoneItemId={1}
          />
        </TestWrapper>,
      );

      expect(container.firstChild).toMatchSnapshot();
    });
  });
});
