import { useDrag } from 'react-dnd';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import zoneItemTypeInfo from 'components/editmode/zoneElements/zoneItemInfo';
import { DragItemType } from 'util/DraggableUtils';

import type { ZoneItemType } from 'types/ZoneItems';

interface Props {
  type: ZoneItemType;
}

export default function ZoneItemButton({ type }: Props) {
  const [, drag] = useDrag(() => ({
    item: { type },
    options: {
      dropEffect: 'copy',
    },
    type: DragItemType.ZONE_ITEM,
  }));

  const { icon, name } = zoneItemTypeInfo[type] || {};

  return drag(
    <div className="flex w-full cursor-move flex-col items-center border bg-white p-2 text-center">
      <FontAwesomeIcon className="mb-2" icon={icon} size="2x" />
      {name}
    </div>,
  );
}
