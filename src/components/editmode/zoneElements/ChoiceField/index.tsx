'use client';

import React, { forwardRef, useEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import { useAccessToken } from 'util/hooks';

type FieldChoices =
  | string[]
  | number[]
  | [string, string][]
  | [number, string][];

interface Props {
  choices:
    | FieldChoices
    | ((siteId: number, token: string) => Promise<FieldChoices>);
  name: string;
  onBlur: React.FocusEventHandler<HTMLSelectElement>;
  onChange: React.ChangeEventHandler<HTMLSelectElement>;
  onLoad: (name: string) => void;
}

function ChoiceField(
  { choices, name, onBlur, onChange, onLoad }: Props,
  ref: null | React.ForwardedRef<HTMLSelectElement>,
): React.ReactElement {
  const siteId = useAppSelector((state) => state.settings.siteId);
  const token = useAccessToken();

  const [isLoading, setLoading] = useState(false);
  const [hasError, setError] = useState(false);
  const [loadedChoices, setChoices] = useState<FieldChoices | undefined>(
    typeof choices === 'function' ? undefined : choices,
  );

  useEffect(() => {
    if (loadedChoices || typeof choices !== 'function') {
      return;
    }

    if (!isLoading) {
      setLoading(true);
      return;
    }

    choices(siteId, token)
      .then((data) => {
        setLoading(false);
        setChoices(data);
      })
      .catch(() => {
        setError(true);
      });
  }, [choices, isLoading, loadedChoices, onLoad, siteId, token]);

  useEffect(() => {
    if (typeof choices !== 'function') {
      return;
    }

    // Set the value once the choices have loaded, otherwise the first
    // option is selected
    onLoad(name);
  }, [choices, name, onLoad]);

  return (
    <select
      className="block w-full max-w-lg rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:max-w-xs sm:text-sm"
      disabled={!loadedChoices}
      id={name}
      name={name}
      onBlur={onBlur}
      onChange={onChange}
      ref={loadedChoices ? ref : undefined}
    >
      {loadedChoices ? (
        loadedChoices.map((item) => {
          let optionValue;
          let label;

          if (Array.isArray(item)) {
            [optionValue, label] = item;
          } else {
            optionValue = item;
            label = item;
          }

          return (
            <option key={optionValue} value={optionValue}>
              {label}
            </option>
          );
        })
      ) : (
        <option>{hasError ? 'Error retrieving choices' : 'Loading...'}</option>
      )}
    </select>
  );
}

export default forwardRef(ChoiceField);
