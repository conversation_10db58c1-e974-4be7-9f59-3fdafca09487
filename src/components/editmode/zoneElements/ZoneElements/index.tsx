import ZoneItemButton from 'components/editmode/zoneElements/ZoneItemButton';
import { ZoneItemType } from 'types/ZoneItems';

export default function ZoneElements(): React.ReactElement {
  return (
    <div className="-mx-2 flex flex-wrap text-sm">
      {Object.values(ZoneItemType).map((type) => (
        <div className="my-2 w-1/3 px-2" key={type}>
          <ZoneItemButton type={type} />
        </div>
      ))}
    </div>
  );
}
