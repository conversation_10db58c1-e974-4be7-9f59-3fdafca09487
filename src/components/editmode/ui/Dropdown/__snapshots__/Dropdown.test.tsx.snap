// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`button renders 1`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <button
    class="w-full cursor-pointer truncate border border-gray-200 bg-white py-1 pl-2 pr-6 text-left hover:border-blue-400"
    type="button"
  >
    Option 1
  </button>
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all","size":"sm"}
    </i>
  </button>
</div>
`;

exports[`button renders 2`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <input
    class="w-full overflow-hidden text-ellipsis border border-gray-200 bg-white py-1 pl-2 pr-6 focus:border-blue-400 focus:outline-none"
    data-testid="filter-input"
    placeholder="Option 1"
    value=""
  />
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all rotate-180","size":"sm"}
    </i>
  </button>
  <div
    class="absolute top-full z-50 flex max-h-40 flex-col overflow-y-auto rounded-md bg-transparent shadow-lg left-0"
  >
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-gray-200"
      type="button"
    >
      Option 1
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 2 Filter
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 3 Filter
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 4
    </button>
  </div>
</div>
`;

exports[`button renders 3`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <button
    class="w-full cursor-pointer truncate border border-gray-200 bg-white py-1 pl-2 pr-6 text-left hover:border-blue-400"
    type="button"
  >
    Option 3 Filter
  </button>
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all","size":"sm"}
    </i>
  </button>
</div>
`;

exports[`button renders 4`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <input
    class="w-full overflow-hidden text-ellipsis border border-gray-200 bg-white py-1 pl-2 pr-6 focus:border-blue-400 focus:outline-none"
    data-testid="filter-input"
    placeholder="Option 3 Filter"
    value=""
  />
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all rotate-180","size":"sm"}
    </i>
  </button>
  <div
    class="absolute top-full z-50 flex max-h-40 flex-col overflow-y-auto rounded-md bg-transparent shadow-lg left-0"
  >
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 1
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 2 Filter
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-gray-200"
      type="button"
    >
      Option 3 Filter
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 4
    </button>
  </div>
</div>
`;

exports[`button renders 5`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <button
    class="w-full cursor-pointer truncate border border-gray-200 bg-white py-1 pl-2 pr-6 text-left hover:border-blue-400"
    type="button"
  >
    Option 3 Filter
  </button>
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all","size":"sm"}
    </i>
  </button>
</div>
`;

exports[`button renders 6`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <input
    class="w-full overflow-hidden text-ellipsis border border-gray-200 bg-white py-1 pl-2 pr-6 focus:border-blue-400 focus:outline-none"
    data-testid="filter-input"
    placeholder="Option 3 Filter"
    value=""
  />
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all rotate-180","size":"sm"}
    </i>
  </button>
  <div
    class="absolute top-full z-50 flex max-h-40 flex-col overflow-y-auto rounded-md bg-transparent shadow-lg left-0"
  >
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 1
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 2 Filter
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-gray-200"
      type="button"
    >
      Option 3 Filter
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 4
    </button>
  </div>
</div>
`;

exports[`button renders 7`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <input
    class="w-full overflow-hidden text-ellipsis border border-gray-200 bg-white py-1 pl-2 pr-6 focus:border-blue-400 focus:outline-none"
    data-testid="filter-input"
    placeholder="Option 3 Filter"
    value="Filter"
  />
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all rotate-180","size":"sm"}
    </i>
  </button>
  <div
    class="absolute top-full z-50 flex max-h-40 flex-col overflow-y-auto rounded-md bg-transparent shadow-lg left-0"
  >
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 2 Filter
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-gray-200"
      type="button"
    >
      Option 3 Filter
    </button>
  </div>
</div>
`;

exports[`button renders 8`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <button
    class="w-full cursor-pointer truncate border border-gray-200 bg-white py-1 pl-2 pr-6 text-left hover:border-blue-400"
    type="button"
  >
    Option 2 Filter
  </button>
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all","size":"sm"}
    </i>
  </button>
</div>
`;

exports[`button renders with alignLeft 1`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <button
    class="w-full cursor-pointer truncate border border-gray-200 bg-white py-1 pl-2 pr-6 text-left hover:border-blue-400"
    type="button"
  >
    Option 1
  </button>
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all","size":"sm"}
    </i>
  </button>
</div>
`;

exports[`button renders with alignLeft 2`] = `
<div
  class="relative select-none "
  tabindex="-1"
>
  <input
    class="w-full overflow-hidden text-ellipsis border border-gray-200 bg-white py-1 pl-2 pr-6 focus:border-blue-400 focus:outline-none"
    data-testid="filter-input"
    placeholder="Option 1"
    value=""
  />
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all rotate-180","size":"sm"}
    </i>
  </button>
  <div
    class="absolute top-full z-50 flex max-h-40 flex-col overflow-y-auto rounded-md bg-transparent shadow-lg left-0"
  >
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-gray-200"
      type="button"
    >
      Option 1
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 2 Filter
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 3 Filter
    </button>
    <button
      class="cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none first:rounded-t-md last:rounded-b-md bg-white"
      type="button"
    >
      Option 4
    </button>
  </div>
</div>
`;

exports[`button renders with className 1`] = `
<div
  class="relative select-none m-3 p-2"
  tabindex="-1"
>
  <button
    class="w-full cursor-pointer truncate border border-gray-200 bg-white py-1 pl-2 pr-6 text-left hover:border-blue-400"
    type="button"
  >
    Option 1
  </button>
  <button
    aria-label="Open"
    class="absolute right-0 top-0 h-full px-2 focus:outline-none"
    data-testid="dropdown-chevron"
    type="button"
  >
    <i>
      {"icon":{"prefix":"fas","iconName":"chevron-down"},"className":"size-full cursor-pointer text-gray-300 transition-all","size":"sm"}
    </i>
  </button>
</div>
`;
