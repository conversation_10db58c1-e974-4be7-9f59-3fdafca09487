'use client';

import { faChevronDown } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import React, { useEffect, useState } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

export interface DropdownOption {
  idx: string | number;
  text: string;
}

export interface DropdownProps {
  alignLeft?: boolean;
  className?: string;
  options: DropdownOption[];
  selected: string | number;
  setSelected: (id: string | number) => void;
}

export default function Dropdown({
  alignLeft = true,
  className,
  options,
  selected,
  setSelected,
}: DropdownProps): React.ReactElement {
  const [open, setOpen] = useState(false);
  const [filter, setFilter] = useState('');
  const [selectedScroll, setSelectedScroll] = useState<number | null>(null);
  const selectedOption = options.find((option) => option.idx === selected);

  const onBlur = (e: React.FocusEvent<HTMLDivElement>) => {
    if (e.relatedTarget) {
      if (e.relatedTarget instanceof HTMLElement) {
        let current = e.relatedTarget;
        if (current === e.currentTarget) {
          return;
        }

        while (current.parentElement) {
          if (current.parentElement === e.currentTarget) {
            return;
          }

          current = current.parentElement;
        }
      }
    }

    setOpen(false);
  };

  useEffect(() => {
    if (!open) {
      setFilter('');
    }
  }, [open, setFilter]);

  return (
    <div
      className={`relative select-none ${className ?? ''}`}
      onBlur={onBlur}
      tabIndex={-1}
    >
      {!open && (
        <button
          className="w-full cursor-pointer truncate border border-gray-200 bg-white py-1 pl-2 pr-6 text-left hover:border-blue-400"
          onClick={() => setOpen(true)}
          type="button"
        >
          {selectedOption?.text ?? 'Unable to find selected item'}
        </button>
      )}

      {open && (
        <input
          className="w-full overflow-hidden text-ellipsis border border-gray-200 bg-white py-1 pl-2 pr-6 focus:border-blue-400 focus:outline-none"
          data-testid="filter-input"
          onChange={(e) => setFilter(e.currentTarget.value)}
          placeholder={selectedOption?.text}
          ref={(ref) => {
            ref?.focus();
          }}
          value={filter}
        />
      )}

      <button
        aria-label="Open"
        className="absolute right-0 top-0 h-full px-2 focus:outline-none"
        data-testid="dropdown-chevron"
        onClick={() => setOpen(!open)}
        type="button"
      >
        <FontAwesomeIcon
          className={clsx(
            'size-full cursor-pointer text-gray-300 transition-all',
            {
              'rotate-180': open,
            },
          )}
          icon={faChevronDown}
          size="sm"
        />
      </button>
      {open && (
        <div
          className={clsx(
            'absolute top-full z-50 flex max-h-40 flex-col overflow-y-auto rounded-md bg-transparent shadow-lg',
            alignLeft ? 'left-0' : 'right-0',
          )}
          ref={(ref) => {
            // eslint-disable-next-line no-param-reassign
            if (ref && selectedScroll) ref.scrollTop = selectedScroll;
          }}
        >
          {options
            .filter(
              (option) => filter.length === 0 || option.text.includes(filter),
            )
            .map((option) => (
              <button
                className={clsx(
                  'cursor-pointer whitespace-nowrap px-2 py-1 hover:bg-blue-100 focus:outline-none',
                  'first:rounded-t-md last:rounded-b-md',
                  selected === option.idx ? 'bg-gray-200' : 'bg-white',
                )}
                key={option.idx}
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  setSelected(option.idx);
                  setOpen(false);
                  setSelectedScroll(
                    e.currentTarget.parentElement?.scrollTop ?? null,
                  );
                }}
                type="button"
              >
                {option.text}
              </button>
            ))}
        </div>
      )}
    </div>
  );
}
