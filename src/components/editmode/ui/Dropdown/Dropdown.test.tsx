import { fireEvent, render } from '@testing-library/react';
import React from 'react';

import Dropdown from '.';

import type { DropdownOption } from '.';

const options: DropdownOption[] = [
  {
    idx: 1,
    text: 'Option 1',
  },
  {
    idx: 2,
    text: 'Option 2 Filter',
  },
  {
    idx: 3,
    text: 'Option 3 Filter',
  },
  {
    idx: 4,
    text: 'Option 4',
  },
];

describe('button', () => {
  it('renders', () => {
    expect.assertions(10);

    const setSelected = jest.fn();
    const { container, getByTestId, getByText, rerender } = render(
      <Dropdown
        options={options}
        selected={options[0].idx}
        setSelected={setSelected}
      />,
    );

    setSelected.mockImplementation((id: string | number) => {
      rerender(
        <Dropdown options={options} selected={id} setSelected={setSelected} />,
      );
    });

    const firstChild = container.firstChild as ChildNode;
    expect(firstChild).toMatchSnapshot();

    // Press to open menu
    const selectedOptionButton = getByText(options[0].text);
    fireEvent.click(selectedOptionButton);
    expect(firstChild).toMatchSnapshot();

    // Select a seperate option by clicking its entry in dropdown
    const optionButton = getByText(options[2].text);
    fireEvent.click(optionButton);
    expect(firstChild).toMatchSnapshot();
    expect(setSelected.mock.calls).toHaveLength(1);

    // Open menu by clicking chevron
    const dropdownArrow1 = getByTestId('dropdown-chevron');
    fireEvent.click(dropdownArrow1);
    expect(firstChild).toMatchSnapshot();

    // Close menu by clicking chevron
    const dropdownArrow2 = getByTestId('dropdown-chevron');
    fireEvent.click(dropdownArrow2);
    expect(firstChild).toMatchSnapshot();

    // Click dropdown (text set to new selected value) to open menu
    const newSelectedOptionButton = getByText(options[2].text);
    fireEvent.click(newSelectedOptionButton);
    expect(firstChild).toMatchSnapshot();

    // Filter input
    const filterInput = getByTestId('filter-input');
    fireEvent.change(filterInput, { target: { value: 'Filter' } });
    expect(firstChild).toMatchSnapshot();

    // Select Filtered input
    const filteredOptionButton = getByText(options[1].text);
    fireEvent.click(filteredOptionButton);
    expect(firstChild).toMatchSnapshot();
    expect(setSelected.mock.calls).toHaveLength(2);
  });

  it('renders with alignLeft', () => {
    expect.assertions(2);

    const setSelected = jest.fn();
    const { container, getByText } = render(
      <Dropdown
        alignLeft
        options={options}
        selected={options[0].idx}
        setSelected={setSelected}
      />,
    );

    const firstChild = container.firstChild as ChildNode;
    expect(firstChild).toMatchSnapshot();

    // Press to open menu
    const selectedOptionButton = getByText(options[0].text);
    fireEvent.click(selectedOptionButton);
    expect(firstChild).toMatchSnapshot();
  });

  it('renders with className', () => {
    expect.assertions(1);

    const setSelected = jest.fn();
    const { container } = render(
      <Dropdown
        className="m-3 p-2"
        options={options}
        selected={options[0].idx}
        setSelected={setSelected}
      />,
    );

    const firstChild = container.firstChild as ChildNode;
    expect(firstChild).toMatchSnapshot();
  });
});
