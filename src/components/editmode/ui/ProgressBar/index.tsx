import { faCheckCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

interface ProgressBarProps {
  className?: string;
  progress: number;
}

export default function ProgressBar({
  className,
  progress,
}: ProgressBarProps): React.ReactElement {
  const progressClamp = Math.max(0, Math.min(1, progress));
  return (
    <div
      className={`flex h-4 w-full flex-row items-center ${className ?? ''}`}
    >
      <div className="grow">
        <div
          className={`h-2 rounded-md ${
            progressClamp === 1 ? 'bg-green-700' : 'bg-blue-500'
          }`}
          style={{ width: `${((progressClamp + 0.03) / 1.03) * 100}%` }}
        />
      </div>
      {progressClamp === 1 ? (
        <FontAwesomeIcon
          className="ml-2 text-green-700"
          icon={faCheckCircle}
        />
      ) : (
        <div className="ml-2 text-sm">{Math.ceil(progressClamp * 100)}%</div>
      )}
    </div>
  );
}
