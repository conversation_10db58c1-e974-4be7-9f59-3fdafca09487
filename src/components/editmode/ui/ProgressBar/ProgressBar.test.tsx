import { render } from '@testing-library/react';
import React from 'react';

import ProgressBar from '.';

describe('button', () => {
  it('renders with 0%', () => {
    expect.assertions(1);

    const { container } = render(<ProgressBar progress={0.0} />);

    const firstChild = container.firstChild as ChildNode;
    expect(firstChild).toMatchSnapshot();
  });

  it('renders with 34%', () => {
    expect.assertions(1);

    const { container } = render(<ProgressBar progress={1 / 3} />);

    const firstChild = container.firstChild as ChildNode;
    expect(firstChild).toMatchSnapshot();
  });

  it('renders with 66%', () => {
    expect.assertions(1);

    const { container } = render(<ProgressBar progress={2 / 3} />);

    const firstChild = container.firstChild as ChildNode;
    expect(firstChild).toMatchSnapshot();
  });

  it('renders with 100%', () => {
    expect.assertions(1);

    const { container } = render(<ProgressBar progress={1.0} />);

    const firstChild = container.firstChild as ChildNode;
    expect(firstChild).toMatchSnapshot();
  });

  it('renders with className', () => {
    expect.assertions(1);

    const { container } = render(
      <ProgressBar className="m-3 p-2" progress={1.0} />,
    );

    const firstChild = container.firstChild as ChildNode;
    expect(firstChild).toMatchSnapshot();
  });
});
