// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`button renders with 0% 1`] = `
<div
  class="flex h-4 w-full flex-row items-center "
>
  <div
    class="grow"
  >
    <div
      class="h-2 rounded-md bg-blue-500"
      style="width: 2.9126213592233006%;"
    />
  </div>
  <div
    class="ml-2 text-sm"
  >
    0
    %
  </div>
</div>
`;

exports[`button renders with 34% 1`] = `
<div
  class="flex h-4 w-full flex-row items-center "
>
  <div
    class="grow"
  >
    <div
      class="h-2 rounded-md bg-blue-500"
      style="width: 35.27508090614886%;"
    />
  </div>
  <div
    class="ml-2 text-sm"
  >
    34
    %
  </div>
</div>
`;

exports[`button renders with 66% 1`] = `
<div
  class="flex h-4 w-full flex-row items-center "
>
  <div
    class="grow"
  >
    <div
      class="h-2 rounded-md bg-blue-500"
      style="width: 67.63754045307444%;"
    />
  </div>
  <div
    class="ml-2 text-sm"
  >
    67
    %
  </div>
</div>
`;

exports[`button renders with 100% 1`] = `
<div
  class="flex h-4 w-full flex-row items-center "
>
  <div
    class="grow"
  >
    <div
      class="h-2 rounded-md bg-green-700"
      style="width: 100%;"
    />
  </div>
  <i>
    {"icon":{"prefix":"fas","iconName":"circle-check"},"className":"ml-2 text-green-700"}
  </i>
</div>
`;

exports[`button renders with className 1`] = `
<div
  class="flex h-4 w-full flex-row items-center m-3 p-2"
>
  <div
    class="grow"
  >
    <div
      class="h-2 rounded-md bg-green-700"
      style="width: 100%;"
    />
  </div>
  <i>
    {"icon":{"prefix":"fas","iconName":"circle-check"},"className":"ml-2 text-green-700"}
  </i>
</div>
`;
