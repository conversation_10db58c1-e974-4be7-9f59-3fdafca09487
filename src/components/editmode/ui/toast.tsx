import { faTimes } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';
import toast from 'react-hot-toast';

import type { Toast, ToastOptions } from 'react-hot-toast';

export default function makeToast(
  message: string,
  options: ToastOptions = {
    position: 'top-left',
  },
): string {
  return toast.custom(
    (t: Toast) => (
      <div
        className={clsx(
          'pointer-events-auto w-full max-w-sm rounded-lg bg-white shadow-lg',
          'overflow-hidden ring-1 ring-black/5',
          {
            'animate-enter': t.visible,
            'animate-leave': !t.visible,
          },
        )}
      >
        <div className="p-4">
          <div className="flex items-center">
            <div className="flex w-0 flex-1 justify-between">
              <p className="w-0 flex-1 text-sm font-medium text-gray-900">
                {message}
              </p>
            </div>
            <div className="ml-4 flex shrink-0">
              <button
                className="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                onClick={() => toast.dismiss(t.id)}
                type="button"
              >
                <span className="sr-only">Close</span>
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
          </div>
        </div>
      </div>
    ),
    options,
  );
}
