import clsx from 'clsx';

interface ButtonProps {
  children: React.ReactNode;
  disabled?: boolean;
  onClick: React.MouseEventHandler<HTMLButtonElement>;
}

export default function Button({
  children,
  disabled = false,
  onClick,
}: ButtonProps): React.ReactElement {
  return (
    <button
      className={clsx(
        'w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none',
        {
          'pointer-events-none border border-gray-300 bg-transparent text-gray-300':
            disabled,
        },
      )}
      onClick={onClick}
      type="button"
    >
      {children}
    </button>
  );
}
