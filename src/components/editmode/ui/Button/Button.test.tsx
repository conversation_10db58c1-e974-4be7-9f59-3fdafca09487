import { fireEvent, render } from '@testing-library/react';

import Button from '.';

describe('button', () => {
  it('renders', () => {
    expect.assertions(2);

    const onClick = jest.fn();
    const { container } = render(<Button onClick={onClick}>Test</Button>);

    expect(container.firstChild).toMatchSnapshot();

    fireEvent.click(container.firstChild as ChildNode);
    expect(onClick.mock.calls).toHaveLength(1);
  });
});
