// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`imageSearch renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="pt-4 text-sm"
  >
    <div
      class="flex flex-row"
    >
      <button
        class="flex grow items-center justify-center border-b-2 border-blue-500 text-blue-500 select-none text-center focus:outline-none"
        type="button"
      >
        Library
      </button>
      <button
        class="flex grow items-center justify-center border-b-1 border-gray-600 pb-px select-none text-center focus:outline-none"
        type="button"
      >
        Uploads
      </button>
    </div>
    <div>
      <div
        class=""
      >
        <div
          class="px-4 py-8"
        >
          <div
            class="absolute left-0 top-0 z-10 size-full bg-white/50"
          />
          <div
            class="relative"
          >
            <input
              class="w-full border-1 border-gray-200 px-2 py-1 focus:border-blue-400 focus:outline-none focus:ring-1"
              placeholder="Search..."
              value=""
            />
            <i>
              {"icon":{"prefix":"fas","iconName":"magnifying-glass"},"className":"pointer-events-none absolute right-2 top-2.5 text-gray-300"}
            </i>
          </div>
          <button
            class="w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none"
            type="button"
          >
            Search
          </button>
          <div
            class="mt-1 flex justify-between text-xs"
          >
            <button
              class="text-blue-500 hover:text-blue-400 focus:outline-none"
              type="button"
            >
              Reset Filters
            </button>
            <span>
              0
               results
            </span>
          </div>
          <div
            class="mt-4 flex justify-end"
          >
            <button
              class="flex size-8 items-center justify-center border-1 border-gray-300 bg-white text-lg text-gray-500 hover:z-10 hover:border-blue-400 hover:text-blue-400 focus:outline-none rounded-r-md"
              data-testid="library-page-next"
              type="button"
            >
              <i>
                {"icon":{"prefix":"fas","iconName":"chevron-right"},"size":"xs"}
              </i>
            </button>
          </div>
          <div
            class="grid grid-cols-3"
          />
        </div>
      </div>
      <div
        class="h-0 overflow-hidden"
      >
        <div
          class="pb-8 pt-4"
        >
          <div
            class="mb-2.5 grid grid-cols-3 gap-2.5 px-1 "
          >
            <button
              class="flex size-24 select-none flex-col items-center justify-center border-1 border-dashed border-gray-300 bg-white hover:border-blue-400 hover:text-blue-400 focus:outline-none"
              type="button"
            >
              <input
                class="hidden"
                data-testid="add-image-input"
                multiple=""
                type="file"
              />
              <div>
                +
              </div>
              <div>
                Add
              </div>
            </button>
          </div>
          <button
            class="w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none pointer-events-none border border-gray-300 bg-transparent text-gray-300"
            type="button"
          >
            <i>
              {"icon":{"prefix":"fas","iconName":"upload"},"className":"mr-2"}
            </i>
            Start Upload
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;
