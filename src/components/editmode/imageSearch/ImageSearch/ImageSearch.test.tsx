import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import ImageSearch from '.';

describe('imageSearch', () => {
  it('renders', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearch />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
