'use client';

import { useCallback, useEffect, useState } from 'react';

import ImageSearchLibrary from '../ImageSearchLibrary';
import ImageSearchUploads from '../ImageSearchUploads';

import type { SidebarAccordionChildProps } from 'components/editmode/SidebarAccordion';

enum ImageSearchTabType {
  LIBRARY = 'Library',
  UPLOADS = 'Uploads',
}

interface ImageSearchTabButtonProps {
  children: React.ReactNode;
  onClick: () => void;
  selected: boolean;
}

function ImageSearchTabButton({
  children,
  onClick,
  selected,
}: ImageSearchTabButtonProps): React.ReactElement {
  return (
    <button
      className={`flex grow items-center justify-center ${
        selected
          ? 'border-b-2 border-blue-500 text-blue-500'
          : 'border-b-1 border-gray-600 pb-px'
      } select-none text-center focus:outline-none`}
      onClick={onClick}
      type="button"
    >
      {children}
    </button>
  );
}

interface ImageSearchTabContentProps {
  children: React.ReactNode;
  selected: boolean;
}

function ImageSearchTabContent({
  children,
  selected,
}: ImageSearchTabContentProps): React.ReactElement {
  return (
    <div className={selected ? '' : 'h-0 overflow-hidden'}>{children}</div>
  );
}

type ImageSearchProps = SidebarAccordionChildProps;

export default function ImageSearch({
  expanded,
}: ImageSearchProps): React.ReactElement {
  const [hasInitialLoad, setHasInitialLoad] = useState(false);
  const [selectedTab, setSelectedTab] = useState<ImageSearchTabType>(
    ImageSearchTabType.LIBRARY,
  );

  useEffect(() => {
    if (expanded && !hasInitialLoad) {
      // Initialize
      setHasInitialLoad(true);
    }
  }, [expanded, hasInitialLoad, setHasInitialLoad]);

  const [forceResetSearch, setForceResetSearch] = useState(false);

  useEffect(() => {
    if (forceResetSearch) {
      setForceResetSearch(false);
    }
  }, [forceResetSearch, setForceResetSearch]);

  const refreshOnUpload = useCallback(() => {
    setSelectedTab(ImageSearchTabType.LIBRARY);
    setForceResetSearch(true);
  }, [setSelectedTab, setForceResetSearch]);

  return (
    <div className="pt-4 text-sm">
      <div className="flex flex-row">
        <ImageSearchTabButton
          onClick={() => setSelectedTab(ImageSearchTabType.LIBRARY)}
          selected={selectedTab === ImageSearchTabType.LIBRARY}
        >
          Library
        </ImageSearchTabButton>
        <ImageSearchTabButton
          onClick={() => setSelectedTab(ImageSearchTabType.UPLOADS)}
          selected={selectedTab === ImageSearchTabType.UPLOADS}
        >
          Uploads
        </ImageSearchTabButton>
      </div>
      <div>
        <ImageSearchTabContent
          selected={selectedTab === ImageSearchTabType.LIBRARY}
        >
          <ImageSearchLibrary
            expanded={expanded}
            forceReset={forceResetSearch}
          />
        </ImageSearchTabContent>
        <ImageSearchTabContent
          selected={selectedTab === ImageSearchTabType.UPLOADS}
        >
          <ImageSearchUploads onUpload={refreshOnUpload} />
        </ImageSearchTabContent>
      </div>
    </div>
  );
}
