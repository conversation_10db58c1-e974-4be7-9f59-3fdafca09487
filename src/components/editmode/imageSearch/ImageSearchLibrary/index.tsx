'use client';

import {
  faChevronLeft,
  faChevronRight,
  faSearch,
} from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { useDrag } from 'react-dnd';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import Button from 'components/editmode/ui/Button';
import { useAppSelector } from 'store/hooks';
import { DragItemType } from 'util/DraggableUtils';
import { useAccessToken } from 'util/hooks';
import {
  ImageResizeMode,
  calculateDefaultCropConfig,
  hasValidURI,
  storyImageUrl,
} from 'util/image';
import { searchImages } from 'util/organization/valencia';

import type { SidebarAccordionChildProps } from 'components/editmode/SidebarAccordion';
import type { StoryImage } from 'types/Story';
import type { SearchedImageObject } from 'util/organization/valencia';

const PAGE_SIZE = 18;

interface ImagePageButtonProps {
  children: React.ReactNode;
  className?: string;
  onClick: () => void;
  testId: string;
}

function ImagePageButton({
  children,
  className,
  onClick,
  testId,
}: ImagePageButtonProps) {
  return (
    <button
      className={clsx(
        'flex size-8 items-center justify-center border-1 border-gray-300',
        'bg-white text-lg text-gray-500',
        'hover:z-10 hover:border-blue-400 hover:text-blue-400',
        'focus:outline-none',
        className,
      )}
      data-testid={testId}
      onClick={onClick}
      type="button"
    >
      {children}
    </button>
  );
}

interface LibraryImageProps {
  image: SearchedImageObject;
  url: string;
}

function LibraryImage({ image, url }: LibraryImageProps): React.ReactElement {
  const [, drag] = useDrag(
    () => ({
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
      item: {
        cropConfig: calculateDefaultCropConfig({
          sourceHeight: image.meta.height,
          sourceWidth: image.meta.width,
        }),
        description: image.description,
        height: image.meta.height,
        id: image.id,
        printLead: false,
        title: image.title,
        uri: image.uri,
        webLead: false,
        width: image.meta.width,
      } as StoryImage,
      type: DragItemType.IMAGE,
    }),
    [image],
  );

  return (
    <div
      className="cursor-pointer p-2"
      data-testid="library-image"
      key={image.resourceUri}
    >
      {drag(
        <img
          alt={image.title}
          data-testid={`${image.id}`}
          height="100"
          src={url}
          width="100"
        />,
      )}
    </div>
  );
}

type ImageSearchLibraryProps = SidebarAccordionChildProps & {
  forceReset: boolean;
};

export default function ImageSearchLibrary({
  expanded,
  forceReset,
}: ImageSearchLibraryProps): React.ReactElement {
  const token = useAccessToken();
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);

  const [hasInitialLoad, setHasInitialLoad] = useState(false);
  const [hasForceReset, setHasForceReset] = useState(false);
  const [loading, setLoading] = useState(true);
  const [images, setImages] = useState<SearchedImageObject[]>([]);
  const [query, setQuery] = useState('');
  const [page, setPage] = useState(0);
  const [searchedQuery, setSearchedQuery] = useState('');

  useEffect(() => {
    if (hasForceReset && !forceReset) {
      setHasForceReset(false);
    }

    if (forceReset && !hasForceReset) {
      setHasForceReset(true);
      setLoading(true);
      setQuery('');
      setPage(0);
      setSearchedQuery('');
      searchImages({
        limit: PAGE_SIZE,
        offset: 0,
        query: '',
        token,
      })
        .then((res) => {
          setLoading(false);
          setImages(res.objects);
        })
        .catch(() => {});
    }
  }, [
    token,
    forceReset,
    hasForceReset,
    setHasForceReset,
    setLoading,
    setQuery,
    setPage,
    setSearchedQuery,
  ]);

  useEffect(() => {
    if (expanded && !hasInitialLoad) {
      setLoading(true);
      setHasInitialLoad(true);
      searchImages({
        limit: PAGE_SIZE,
        offset: 0,
        query: '',
        token,
      })
        .then((res) => {
          setLoading(false);
          setImages(res.objects);
        })
        .catch(() => {});
    }
  }, [token, expanded, hasInitialLoad, setHasInitialLoad, setImages]);

  function updatePage(nextPage: number): void {
    setLoading(true);
    setPage(nextPage);
    searchImages({
      limit: PAGE_SIZE,
      offset: nextPage * PAGE_SIZE,
      query: searchedQuery,
      token,
    })
      .then((res) => {
        setLoading(false);
        setImages(res.objects);
      })
      .catch(() => {});
  }

  const search = (): void => {
    setLoading(true);
    setPage(0);
    setSearchedQuery(query);
    searchImages({
      limit: PAGE_SIZE,
      offset: 0,
      query,
      token,
    })
      .then((res) => {
        setLoading(false);
        setImages(res.objects);
      })
      .catch(() => {});
  };

  return (
    <div className="px-4 py-8">
      {loading && (
        <div className="absolute left-0 top-0 z-10 size-full bg-white/50" />
      )}

      <div className="relative">
        <input
          className="w-full border-1 border-gray-200 px-2 py-1 focus:border-blue-400 focus:outline-none focus:ring-1"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setQuery(e.target.value);
          }}
          placeholder="Search..."
          value={query}
        />

        <FontAwesomeIcon
          className="pointer-events-none absolute right-2 top-2.5 text-gray-300"
          icon={faSearch}
        />
      </div>

      <Button onClick={search}>Search</Button>

      <div className="mt-1 flex justify-between text-xs">
        <button
          className="text-blue-500 hover:text-blue-400 focus:outline-none"
          onClick={() => {
            setQuery('');
          }}
          type="button"
        >
          Reset Filters
        </button>
        <span>{images.length} results</span>
      </div>

      <div className="mt-4 flex justify-end">
        {page > 0 && (
          <ImagePageButton
            className="-mr-px rounded-l-md"
            onClick={() => {
              updatePage(page - 1);
            }}
            testId="library-page-prev"
          >
            <FontAwesomeIcon icon={faChevronLeft} size="xs" />
          </ImagePageButton>
        )}
        <ImagePageButton
          className="rounded-r-md"
          onClick={() => {
            updatePage(page + 1);
          }}
          testId="library-page-next"
        >
          <FontAwesomeIcon icon={faChevronRight} size="xs" />
        </ImagePageButton>
      </div>

      <div className="grid grid-cols-3">
        {images.map((image) => {
          if (hasValidURI(image)) {
            const url = storyImageUrl({
              fit: ImageResizeMode.MAX,
              height: 100,
              image,
              transformUrl,
              width: 100,
            });
            return <LibraryImage image={image} key={image.id} url={url} />;
          }
          return null;
        })}
      </div>
    </div>
  );
}
