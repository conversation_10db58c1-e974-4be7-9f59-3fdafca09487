/* eslint-disable @typescript-eslint/require-await */

import { fireEvent, render, waitFor } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';
import * as valencia from 'util/organization/valencia';
import { keysToCamel } from 'util/string';

import data from './ImageSearchLibrary.testdata.json';

import ImageSearchLibrary from '.';

describe('imageSearchLibrary', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('doesnt call api while not expanded', () => {
    expect.assertions(2);

    const searchImagesMock = jest.spyOn(valencia, 'searchImages');

    const { container } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchLibrary expanded={false} forceReset={false} />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
    expect(searchImagesMock).toHaveBeenCalledTimes(0);
  });

  it('calls api when expanded', async () => {
    expect.assertions(7);
    const searchImagesMock = jest.spyOn(valencia, 'searchImages');
    searchImagesMock.mockImplementation(
      async ({ offset }) =>
        keysToCamel(
          data[offset.toString() as keyof typeof data],
        ) as valencia.SearchImagesResponse,
    );

    const { container, findAllByTestId, findByTestId } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchLibrary expanded forceReset={false} />
      </TestWrapper>,
    );

    await findByTestId(data[0].objects[0].id);
    await expect(findAllByTestId('library-image')).resolves.toHaveLength(18);
    expect(container.firstChild).toMatchSnapshot();
    expect(searchImagesMock).toHaveBeenCalledTimes(1);
    {
      const { limit, offset, query, token } =
        searchImagesMock.mock.calls[0][0];
      expect(limit).toBe(18);
      expect(offset).toBe(0);
      expect(query).toBe('');
      expect(token).toBe('test token');
    }
  });

  it('updates offset when page buttons are pressed', async () => {
    expect.assertions(14);

    const searchImagesMock = jest.spyOn(valencia, 'searchImages');
    searchImagesMock.mockImplementation(
      async ({ offset }) =>
        keysToCamel(
          data[offset.toString() as keyof typeof data],
        ) as valencia.SearchImagesResponse,
    );

    const { container, findAllByTestId, findByTestId, getByTestId } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchLibrary expanded forceReset={false} />
      </TestWrapper>,
    );

    await findByTestId(data[0].objects[0].id);

    // Press next button
    const nextButton = getByTestId('library-page-next');
    fireEvent.click(nextButton);
    await findByTestId(data[18].objects[0].id);
    await waitFor(() =>
      expect(findAllByTestId('library-image')).resolves.toHaveLength(17),
    );
    expect(container.firstChild).toMatchSnapshot();
    expect(searchImagesMock).toHaveBeenCalledTimes(2);
    {
      const { limit, offset, query, token } =
        searchImagesMock.mock.calls[1][0];
      expect(limit).toBe(18);
      expect(offset).toBe(18);
      expect(query).toBe('');
      expect(token).toBe('test token');
    }

    // Press prev button
    const prevButton = getByTestId('library-page-prev');
    fireEvent.click(prevButton);
    await findByTestId(data[0].objects[0].id);
    await expect(findAllByTestId('library-image')).resolves.toHaveLength(18);
    expect(container.firstChild).toMatchSnapshot();
    expect(searchImagesMock).toHaveBeenCalledTimes(3);
    {
      const { limit, offset, query, token } =
        searchImagesMock.mock.calls[2][0];
      expect(limit).toBe(18);
      expect(offset).toBe(0);
      expect(query).toBe('');
      expect(token).toBe('test token');
    }
  });

  it('doesnt include query on next page before submission', async () => {
    expect.assertions(9);

    const searchImagesMock = jest.spyOn(valencia, 'searchImages');
    searchImagesMock.mockImplementation(
      async ({ offset }) =>
        keysToCamel(
          data[offset.toString() as keyof typeof data],
        ) as valencia.SearchImagesResponse,
    );

    const {
      container,
      findAllByTestId,
      findByTestId,
      getByPlaceholderText,
      getByTestId,
    } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchLibrary expanded forceReset={false} />
      </TestWrapper>,
    );

    await findByTestId(data[0].objects[0].id);

    // Type in query
    const queryInput = getByPlaceholderText('Search...') as HTMLInputElement;
    fireEvent.change(queryInput, { target: { value: 'Test query' } });
    expect(queryInput.value).toBe('Test query');
    expect(container.firstChild).toMatchSnapshot();

    // Press next button and search shouldnt include query
    const nextButton = getByTestId('library-page-next');
    fireEvent.click(nextButton);
    await findByTestId(data[18].objects[0].id);
    await waitFor(() =>
      expect(findAllByTestId('library-image')).resolves.toHaveLength(17),
    );
    expect(container.firstChild).toMatchSnapshot();
    expect(searchImagesMock).toHaveBeenCalledTimes(2);
    {
      const { limit, offset, query, token } =
        searchImagesMock.mock.calls[1][0];
      expect(limit).toBe(18);
      expect(offset).toBe(18);
      expect(query).toBe('');
      expect(token).toBe('test token');
    }
  });

  it('resets page on query submission', async () => {
    expect.assertions(7);

    const searchImagesMock = jest.spyOn(valencia, 'searchImages');
    searchImagesMock.mockImplementation(
      async ({ offset }) =>
        keysToCamel(
          data[offset.toString() as keyof typeof data],
        ) as valencia.SearchImagesResponse,
    );

    const {
      container,
      findAllByTestId,
      findByTestId,
      getByPlaceholderText,
      getByTestId,
      getByText,
    } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchLibrary expanded forceReset={false} />
      </TestWrapper>,
    );

    await findByTestId(data[0].objects[0].id);

    // Type in query
    const queryInput = getByPlaceholderText('Search...') as HTMLInputElement;
    fireEvent.change(queryInput, { target: { value: 'Test query' } });

    // Press next button and search shouldnt include query
    const nextButton = getByTestId('library-page-next');
    fireEvent.click(nextButton);
    await findByTestId(data[18].objects[0].id);

    // Search with query (should reset page to 0)
    const searchButton = getByText('Search');
    searchButton.click();
    await findByTestId(data[0].objects[0].id);
    await waitFor(() =>
      expect(findAllByTestId('library-image')).resolves.toHaveLength(18),
    );
    expect(container.firstChild).toMatchSnapshot();
    expect(searchImagesMock).toHaveBeenCalledTimes(3);
    {
      const { limit, offset, query, token } =
        searchImagesMock.mock.calls[2][0];
      expect(limit).toBe(18);
      expect(offset).toBe(0);
      expect(query).toBe('Test query');
      expect(token).toBe('test token');
    }
  });

  it('includes query on next page after submission', async () => {
    expect.assertions(7);

    const searchImagesMock = jest.spyOn(valencia, 'searchImages');
    searchImagesMock.mockImplementation(
      async ({ offset }) =>
        keysToCamel(
          data[offset.toString() as keyof typeof data],
        ) as valencia.SearchImagesResponse,
    );

    const {
      container,
      findAllByTestId,
      findByTestId,
      getByPlaceholderText,
      getByTestId,
      getByText,
    } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchLibrary expanded forceReset={false} />
      </TestWrapper>,
    );

    await findByTestId(data[0].objects[0].id);

    // Type in query
    const queryInput = getByPlaceholderText('Search...') as HTMLInputElement;
    fireEvent.change(queryInput, { target: { value: 'Test query' } });

    // Press next button and search shouldnt include query
    const nextButton = getByTestId('library-page-next');
    fireEvent.click(nextButton);
    await findByTestId(data[18].objects[0].id);

    // Search with query (should reset page to 0)
    const searchButton = getByText('Search');
    searchButton.click();
    await findByTestId(data[0].objects[0].id);

    // Next page with filter
    fireEvent.click(nextButton);
    await findByTestId(data[18].objects[0].id);
    await waitFor(() =>
      expect(findAllByTestId('library-image')).resolves.toHaveLength(17),
    );
    expect(container.firstChild).toMatchSnapshot();
    expect(searchImagesMock).toHaveBeenCalledTimes(4);
    {
      const { limit, offset, query, token } =
        searchImagesMock.mock.calls[3][0];
      expect(limit).toBe(18);
      expect(offset).toBe(18);
      expect(query).toBe('Test query');
      expect(token).toBe('test token');
    }
  });

  it('resets on forceReset', async () => {
    expect.assertions(7);

    const searchImagesMock = jest.spyOn(valencia, 'searchImages');
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test token',
    }));

    const {
      container,
      findAllByTestId,
      findByTestId,
      getByPlaceholderText,
      getByTestId,
      getByText,
      rerender,
    } = render(
      <TestWrapper store={store}>
        <ImageSearchLibrary expanded forceReset={false} />
      </TestWrapper>,
    );

    searchImagesMock.mockImplementation(
      async ({ offset }) =>
        keysToCamel(
          data[offset.toString() as keyof typeof data],
        ) as valencia.SearchImagesResponse,
    );

    await findByTestId(data[0].objects[0].id);

    // Type in query
    const queryInput = getByPlaceholderText('Search...') as HTMLInputElement;
    fireEvent.change(queryInput, { target: { value: 'Test query' } });

    // Search with query (should reset page to 0)
    const searchButton = getByText('Search');
    searchButton.click();
    await findByTestId(data[0].objects[0].id);

    // Press next button
    const nextButton = getByTestId('library-page-next');
    fireEvent.click(nextButton);
    await findByTestId(data[18].objects[0].id);

    // Force reset
    rerender(
      <TestWrapper store={store}>
        <ImageSearchLibrary expanded forceReset />
      </TestWrapper>,
    );
    await findByTestId(data[0].objects[0].id);
    await expect(findAllByTestId('library-image')).resolves.toHaveLength(18);
    expect(container.firstChild).toMatchSnapshot();
    expect(searchImagesMock).toHaveBeenCalledTimes(4);
    {
      const { limit, offset, query, token } =
        searchImagesMock.mock.calls[3][0];
      expect(limit).toBe(18);
      expect(offset).toBe(0);
      expect(query).toBe('');
      expect(token).toBe('test token');
    }
  });
});
