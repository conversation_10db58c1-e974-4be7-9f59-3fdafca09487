'use client';

import { faEye, faTrashAlt } from '@fortawesome/free-regular-svg-icons';
import { faUpload } from '@fortawesome/free-solid-svg-icons';
import { useCallback, useRef, useState } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import Modal from 'components/editmode/Modal';
import Button from 'components/editmode/ui/Button';
import ProgressBar from 'components/editmode/ui/ProgressBar';
import { useAppSelector } from 'store/hooks';
import { useAccessToken } from 'util/hooks';
import { uploadFile } from 'util/organization/valencia';

interface PreviewImageProps {
  image: string;
  onRemove: () => void;
}

function PreviewImage({ image, onRemove }: PreviewImageProps) {
  const [previewing, setPreviewing] = useState(false);
  const hideModal = useCallback(() => setPreviewing(false), [setPreviewing]);

  return (
    <div
      className="group relative flex size-24 items-center justify-center rounded-md border border-gray-300 p-2"
      data-testid="preview-image"
    >
      {previewing && (
        <Modal onRequestClose={hideModal} width={520}>
          <img alt="Preview" className="size-full" src={image} />
        </Modal>
      )}
      <div className="pointer-events-none absolute left-0 top-0 flex size-full flex-row items-center justify-center bg-gray-900/50 text-white opacity-0 transition-all group-hover:pointer-events-auto group-hover:opacity-100">
        <button
          aria-label="Preview"
          className="mr-3 focus:outline-none"
          data-testid="preview-image-preview"
          onClick={() => setPreviewing(true)}
          type="button"
        >
          <FontAwesomeIcon icon={faEye} size="lg" />
        </button>
        <button
          aria-label="Delete"
          className="focus:outline-none"
          data-testid="preview-image-remove"
          onClick={onRemove}
          type="button"
        >
          <FontAwesomeIcon icon={faTrashAlt} size="lg" />
        </button>
      </div>
      <img
        alt="Uploaded"
        className="size-full"
        data-testid={image}
        src={image}
      />
    </div>
  );
}

interface AddImageProps {
  addFiles: (addedFiles: File[]) => void;
}

function AddImage({ addFiles }: AddImageProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <button
      className="flex size-24 select-none flex-col items-center justify-center border-1 border-dashed border-gray-300 bg-white hover:border-blue-400 hover:text-blue-400 focus:outline-none"
      onClick={() => inputRef.current?.click()}
      type="button"
    >
      <input
        className="hidden"
        data-testid="add-image-input"
        multiple
        onInput={(e) => {
          const { files } = e.currentTarget;
          if (files) {
            addFiles(Array.prototype.slice.call(files) as File[]);
          }
        }}
        ref={inputRef}
        type="file"
      />
      <div>+</div>
      <div>Add</div>
    </button>
  );
}

interface ImageSearchUploadsProps {
  onUpload: () => void;
}

export default function ImageSearchUploads({
  onUpload,
}: ImageSearchUploadsProps): React.ReactElement {
  const token = useAccessToken();
  const user = useAppSelector((state) => state.tokenInfo.user);

  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [files, setFiles] = useState<File[]>([]);
  const [images, setImages] = useState<string[]>([]);

  const addFiles = useCallback(
    (addedFiles: File[]) => {
      setFiles([...files, ...addedFiles]);
      setImages([
        ...images,
        ...addedFiles.map((file) => URL.createObjectURL(file)),
      ]);
    },
    [files, setFiles, images, setImages],
  );

  function removeFile(idx: number) {
    setFiles(files.filter((_, i) => i !== idx));
    setImages(images.filter((_, i) => i !== idx));
  }

  const upload = useCallback(async () => {
    if (files.length > 0 && user) {
      setUploading(true);
      setUploadProgress(0);

      // Batch uploads in groups of four
      for (let idx = 0; idx < files.length; idx += 4) {
        const promises: Promise<unknown>[] = [];
        for (
          let idxOffset = 0;
          idx + idxOffset < files.length && idxOffset < 4;
          idxOffset++
        ) {
          promises.push(
            uploadFile({
              token,
              upload: {
                file: files[idx + idxOffset],
              },
              username: user.username,
            }),
          );
        }

        // eslint-disable-next-line no-await-in-loop
        await Promise.all(promises);
        setUploadProgress((idx + 4) / files.length);

        if (idx + 4 >= files.length) {
          setTimeout(() => {
            setUploading(false);
            setFiles([]);
            setImages([]);
            onUpload();
          }, 1000);
        }
      }
    }
  }, [
    files,
    token,
    user,
    setUploading,
    setUploadProgress,
    setFiles,
    setImages,
    onUpload,
  ]);

  return (
    <div className="pb-8 pt-4">
      <div
        className={`mb-2.5 grid grid-cols-3 gap-2.5 px-1 ${
          uploading ? 'pointer-events-none opacity-50' : ''
        }`}
      >
        {images.map((image, idx) => (
          <PreviewImage
            image={image}
            key={idx} // eslint-disable-line react/no-array-index-key
            onRemove={() => removeFile(idx)}
          />
        ))}
        <AddImage addFiles={addFiles} />
      </div>
      <Button
        disabled={uploading || files.length === 0}
        onClick={() => {
          upload().catch(() => {});
        }}
      >
        <FontAwesomeIcon className="mr-2" icon={faUpload} />
        Start Upload
      </Button>
      {uploading && <ProgressBar className="mt-2" progress={uploadProgress} />}
    </div>
  );
}
