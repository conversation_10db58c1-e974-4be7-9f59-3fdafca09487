// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`imageSearchUploads handles adding a single item 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="pb-8 pt-4"
  >
    <div
      class="mb-2.5 grid grid-cols-3 gap-2.5 px-1 "
    >
      <div
        class="group relative flex size-24 items-center justify-center rounded-md border border-gray-300 p-2"
        data-testid="preview-image"
      >
        <div
          class="pointer-events-none absolute left-0 top-0 flex size-full flex-row items-center justify-center bg-gray-900/50 text-white opacity-0 transition-all group-hover:pointer-events-auto group-hover:opacity-100"
        >
          <button
            aria-label="Preview"
            class="mr-3 focus:outline-none"
            data-testid="preview-image-preview"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"eye"},"size":"lg"}
            </i>
          </button>
          <button
            aria-label="Delete"
            class="focus:outline-none"
            data-testid="preview-image-remove"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"trash-can"},"size":"lg"}
            </i>
          </button>
        </div>
        <img
          alt="Uploaded"
          class="size-full"
          data-testid="http://localhost/file_one.png"
          src="http://localhost/file_one.png"
        />
      </div>
      <button
        class="flex size-24 select-none flex-col items-center justify-center border-1 border-dashed border-gray-300 bg-white hover:border-blue-400 hover:text-blue-400 focus:outline-none"
        type="button"
      >
        <input
          class="hidden"
          data-testid="add-image-input"
          multiple=""
          type="file"
        />
        <div>
          +
        </div>
        <div>
          Add
        </div>
      </button>
    </div>
    <button
      class="w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none"
      type="button"
    >
      <i>
        {"icon":{"prefix":"fas","iconName":"upload"},"className":"mr-2"}
      </i>
      Start Upload
    </button>
  </div>
</div>
`;

exports[`imageSearchUploads handles adding multiple items 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="pb-8 pt-4"
  >
    <div
      class="mb-2.5 grid grid-cols-3 gap-2.5 px-1 "
    >
      <div
        class="group relative flex size-24 items-center justify-center rounded-md border border-gray-300 p-2"
        data-testid="preview-image"
      >
        <div
          class="pointer-events-none absolute left-0 top-0 flex size-full flex-row items-center justify-center bg-gray-900/50 text-white opacity-0 transition-all group-hover:pointer-events-auto group-hover:opacity-100"
        >
          <button
            aria-label="Preview"
            class="mr-3 focus:outline-none"
            data-testid="preview-image-preview"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"eye"},"size":"lg"}
            </i>
          </button>
          <button
            aria-label="Delete"
            class="focus:outline-none"
            data-testid="preview-image-remove"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"trash-can"},"size":"lg"}
            </i>
          </button>
        </div>
        <img
          alt="Uploaded"
          class="size-full"
          data-testid="http://localhost/file_one.png"
          src="http://localhost/file_one.png"
        />
      </div>
      <div
        class="group relative flex size-24 items-center justify-center rounded-md border border-gray-300 p-2"
        data-testid="preview-image"
      >
        <div
          class="pointer-events-none absolute left-0 top-0 flex size-full flex-row items-center justify-center bg-gray-900/50 text-white opacity-0 transition-all group-hover:pointer-events-auto group-hover:opacity-100"
        >
          <button
            aria-label="Preview"
            class="mr-3 focus:outline-none"
            data-testid="preview-image-preview"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"eye"},"size":"lg"}
            </i>
          </button>
          <button
            aria-label="Delete"
            class="focus:outline-none"
            data-testid="preview-image-remove"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"trash-can"},"size":"lg"}
            </i>
          </button>
        </div>
        <img
          alt="Uploaded"
          class="size-full"
          data-testid="http://localhost/file_two.png"
          src="http://localhost/file_two.png"
        />
      </div>
      <button
        class="flex size-24 select-none flex-col items-center justify-center border-1 border-dashed border-gray-300 bg-white hover:border-blue-400 hover:text-blue-400 focus:outline-none"
        type="button"
      >
        <input
          class="hidden"
          data-testid="add-image-input"
          multiple=""
          type="file"
        />
        <div>
          +
        </div>
        <div>
          Add
        </div>
      </button>
    </div>
    <button
      class="w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none"
      type="button"
    >
      <i>
        {"icon":{"prefix":"fas","iconName":"upload"},"className":"mr-2"}
      </i>
      Start Upload
    </button>
  </div>
</div>
`;

exports[`imageSearchUploads handles removing a single item 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="pb-8 pt-4"
  >
    <div
      class="mb-2.5 grid grid-cols-3 gap-2.5 px-1 "
    >
      <div
        class="group relative flex size-24 items-center justify-center rounded-md border border-gray-300 p-2"
        data-testid="preview-image"
      >
        <div
          class="pointer-events-none absolute left-0 top-0 flex size-full flex-row items-center justify-center bg-gray-900/50 text-white opacity-0 transition-all group-hover:pointer-events-auto group-hover:opacity-100"
        >
          <button
            aria-label="Preview"
            class="mr-3 focus:outline-none"
            data-testid="preview-image-preview"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"eye"},"size":"lg"}
            </i>
          </button>
          <button
            aria-label="Delete"
            class="focus:outline-none"
            data-testid="preview-image-remove"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"trash-can"},"size":"lg"}
            </i>
          </button>
        </div>
        <img
          alt="Uploaded"
          class="size-full"
          data-testid="http://localhost/file_one.png"
          src="http://localhost/file_one.png"
        />
      </div>
      <button
        class="flex size-24 select-none flex-col items-center justify-center border-1 border-dashed border-gray-300 bg-white hover:border-blue-400 hover:text-blue-400 focus:outline-none"
        type="button"
      >
        <input
          class="hidden"
          data-testid="add-image-input"
          multiple=""
          type="file"
        />
        <div>
          +
        </div>
        <div>
          Add
        </div>
      </button>
    </div>
    <button
      class="w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none"
      type="button"
    >
      <i>
        {"icon":{"prefix":"fas","iconName":"upload"},"className":"mr-2"}
      </i>
      Start Upload
    </button>
  </div>
</div>
`;

exports[`imageSearchUploads handles removing all items of multiple 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="pb-8 pt-4"
  >
    <div
      class="mb-2.5 grid grid-cols-3 gap-2.5 px-1 "
    >
      <div
        class="group relative flex size-24 items-center justify-center rounded-md border border-gray-300 p-2"
        data-testid="preview-image"
      >
        <div
          class="pointer-events-none absolute left-0 top-0 flex size-full flex-row items-center justify-center bg-gray-900/50 text-white opacity-0 transition-all group-hover:pointer-events-auto group-hover:opacity-100"
        >
          <button
            aria-label="Preview"
            class="mr-3 focus:outline-none"
            data-testid="preview-image-preview"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"eye"},"size":"lg"}
            </i>
          </button>
          <button
            aria-label="Delete"
            class="focus:outline-none"
            data-testid="preview-image-remove"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"trash-can"},"size":"lg"}
            </i>
          </button>
        </div>
        <img
          alt="Uploaded"
          class="size-full"
          data-testid="http://localhost/file_two.png"
          src="http://localhost/file_two.png"
        />
      </div>
      <button
        class="flex size-24 select-none flex-col items-center justify-center border-1 border-dashed border-gray-300 bg-white hover:border-blue-400 hover:text-blue-400 focus:outline-none"
        type="button"
      >
        <input
          class="hidden"
          data-testid="add-image-input"
          multiple=""
          type="file"
        />
        <div>
          +
        </div>
        <div>
          Add
        </div>
      </button>
    </div>
    <button
      class="w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none"
      type="button"
    >
      <i>
        {"icon":{"prefix":"fas","iconName":"upload"},"className":"mr-2"}
      </i>
      Start Upload
    </button>
  </div>
</div>
`;

exports[`imageSearchUploads handles removing first item of multiple 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="pb-8 pt-4"
  >
    <div
      class="mb-2.5 grid grid-cols-3 gap-2.5 px-1 "
    >
      <div
        class="group relative flex size-24 items-center justify-center rounded-md border border-gray-300 p-2"
        data-testid="preview-image"
      >
        <div
          class="pointer-events-none absolute left-0 top-0 flex size-full flex-row items-center justify-center bg-gray-900/50 text-white opacity-0 transition-all group-hover:pointer-events-auto group-hover:opacity-100"
        >
          <button
            aria-label="Preview"
            class="mr-3 focus:outline-none"
            data-testid="preview-image-preview"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"eye"},"size":"lg"}
            </i>
          </button>
          <button
            aria-label="Delete"
            class="focus:outline-none"
            data-testid="preview-image-remove"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"trash-can"},"size":"lg"}
            </i>
          </button>
        </div>
        <img
          alt="Uploaded"
          class="size-full"
          data-testid="http://localhost/file_one.png"
          src="http://localhost/file_one.png"
        />
      </div>
      <div
        class="group relative flex size-24 items-center justify-center rounded-md border border-gray-300 p-2"
        data-testid="preview-image"
      >
        <div
          class="pointer-events-none absolute left-0 top-0 flex size-full flex-row items-center justify-center bg-gray-900/50 text-white opacity-0 transition-all group-hover:pointer-events-auto group-hover:opacity-100"
        >
          <button
            aria-label="Preview"
            class="mr-3 focus:outline-none"
            data-testid="preview-image-preview"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"eye"},"size":"lg"}
            </i>
          </button>
          <button
            aria-label="Delete"
            class="focus:outline-none"
            data-testid="preview-image-remove"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"trash-can"},"size":"lg"}
            </i>
          </button>
        </div>
        <img
          alt="Uploaded"
          class="size-full"
          data-testid="http://localhost/file_two.png"
          src="http://localhost/file_two.png"
        />
      </div>
      <button
        class="flex size-24 select-none flex-col items-center justify-center border-1 border-dashed border-gray-300 bg-white hover:border-blue-400 hover:text-blue-400 focus:outline-none"
        type="button"
      >
        <input
          class="hidden"
          data-testid="add-image-input"
          multiple=""
          type="file"
        />
        <div>
          +
        </div>
        <div>
          Add
        </div>
      </button>
    </div>
    <button
      class="w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none"
      type="button"
    >
      <i>
        {"icon":{"prefix":"fas","iconName":"upload"},"className":"mr-2"}
      </i>
      Start Upload
    </button>
  </div>
</div>
`;

exports[`imageSearchUploads opens modal on clicking preview 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="pb-8 pt-4"
  >
    <div
      class="mb-2.5 grid grid-cols-3 gap-2.5 px-1 "
    >
      <div
        class="group relative flex size-24 items-center justify-center rounded-md border border-gray-300 p-2"
        data-testid="preview-image"
      >
        <div
          class="pointer-events-none absolute left-0 top-0 flex size-full flex-row items-center justify-center bg-gray-900/50 text-white opacity-0 transition-all group-hover:pointer-events-auto group-hover:opacity-100"
        >
          <button
            aria-label="Preview"
            class="mr-3 focus:outline-none"
            data-testid="preview-image-preview"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"eye"},"size":"lg"}
            </i>
          </button>
          <button
            aria-label="Delete"
            class="focus:outline-none"
            data-testid="preview-image-remove"
            type="button"
          >
            <i>
              {"icon":{"prefix":"far","iconName":"trash-can"},"size":"lg"}
            </i>
          </button>
        </div>
        <img
          alt="Uploaded"
          class="size-full"
          data-testid="http://localhost/file_one.png"
          src="http://localhost/file_one.png"
        />
      </div>
      <button
        class="flex size-24 select-none flex-col items-center justify-center border-1 border-dashed border-gray-300 bg-white hover:border-blue-400 hover:text-blue-400 focus:outline-none"
        type="button"
      >
        <input
          class="hidden"
          data-testid="add-image-input"
          multiple=""
          type="file"
        />
        <div>
          +
        </div>
        <div>
          Add
        </div>
      </button>
    </div>
    <button
      class="w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none"
      type="button"
    >
      <i>
        {"icon":{"prefix":"fas","iconName":"upload"},"className":"mr-2"}
      </i>
      Start Upload
    </button>
  </div>
</div>
`;

exports[`imageSearchUploads renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="pb-8 pt-4"
  >
    <div
      class="mb-2.5 grid grid-cols-3 gap-2.5 px-1 "
    >
      <button
        class="flex size-24 select-none flex-col items-center justify-center border-1 border-dashed border-gray-300 bg-white hover:border-blue-400 hover:text-blue-400 focus:outline-none"
        type="button"
      >
        <input
          class="hidden"
          data-testid="add-image-input"
          multiple=""
          type="file"
        />
        <div>
          +
        </div>
        <div>
          Add
        </div>
      </button>
    </div>
    <button
      class="w-full bg-blue-500 py-1 text-center text-white hover:bg-blue-400 focus:outline-none pointer-events-none border border-gray-300 bg-transparent text-gray-300"
      type="button"
    >
      <i>
        {"icon":{"prefix":"fas","iconName":"upload"},"className":"mr-2"}
      </i>
      Start Upload
    </button>
  </div>
</div>
`;
