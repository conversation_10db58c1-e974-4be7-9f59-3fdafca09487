/* eslint-disable @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import * as fs from 'fs';
import * as path from 'path';

import { fireEvent, render, waitFor } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';
import * as valencia from 'util/organization/valencia';
import { keysToCamel } from 'util/string';

import ImageSearchUploads from '.';

const data = JSON.parse(
  fs
    .readFileSync(path.join(__dirname, './ImageSearchUploads.testdata.json'))
    .toString(),
);

describe('imageSearchUploads', () => {
  const file1 = new File(['(⌐□_□)'], 'file_one.png', {
    type: 'image/png',
  });

  const file2 = new File(['(⌐□_□)'], 'file_two.png', {
    type: 'image/png',
  });

  it('renders', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchUploads onUpload={() => {}} />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('handles adding a single item', () => {
    expect.assertions(3);

    const onUpload = jest.fn();

    const { container, getAllByTestId, getByTestId } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchUploads onUpload={onUpload} />
      </TestWrapper>,
    );

    const input = getByTestId('add-image-input');
    fireEvent.input(input, { target: { files: [file1] } });

    expect(container.firstChild).toMatchSnapshot();
    expect(getAllByTestId('preview-image')).toHaveLength(1);
    expect(onUpload).toHaveBeenCalledTimes(0);
  });

  it('handles removing a single item', async () => {
    expect.assertions(5);

    const onUpload = jest.fn();

    const { container, getAllByTestId, getByTestId, queryAllByTestId } =
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
            accessToken: 'test token',
          }))}
        >
          <ImageSearchUploads onUpload={onUpload} />
        </TestWrapper>,
      );

    const input = getByTestId('add-image-input');
    fireEvent.input(input, { target: { files: [file1] } });

    const removals = getAllByTestId('preview-image-remove');
    await waitFor(() => expect(removals).toHaveLength(1));
    removals[0].click();

    expect(container.firstChild).toMatchSnapshot();
    await waitFor(() =>
      expect(queryAllByTestId('preview-image')).toHaveLength(0),
    );
    expect(onUpload).toHaveBeenCalledTimes(0);
  });

  it('handles adding multiple items', () => {
    expect.assertions(3);

    const onUpload = jest.fn();

    const { container, getAllByTestId, getByTestId } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchUploads onUpload={onUpload} />
      </TestWrapper>,
    );

    const input = getByTestId('add-image-input');
    fireEvent.input(input, { target: { files: [file1, file2] } });

    expect(container.firstChild).toMatchSnapshot();
    expect(getAllByTestId('preview-image')).toHaveLength(2);
    expect(onUpload).toHaveBeenCalledTimes(0);
  });

  it('handles removing first item of multiple', async () => {
    expect.assertions(6);

    const onUpload = jest.fn();

    const { container, getAllByTestId, getByTestId } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchUploads onUpload={onUpload} />
      </TestWrapper>,
    );

    const input = getByTestId('add-image-input');
    fireEvent.input(input, { target: { files: [file1, file2] } });

    const removals = getAllByTestId('preview-image-remove');
    expect(removals).toHaveLength(2);
    removals[0].click();

    expect(getByTestId(`http://localhost/${file2.name}`)).toBeTruthy();
    expect(container.firstChild).toMatchSnapshot();
    await waitFor(() =>
      expect(getAllByTestId('preview-image')).toHaveLength(1),
    );
    expect(onUpload).toHaveBeenCalledTimes(0);
  });

  it('handles removing all items of multiple', async () => {
    expect.assertions(7);

    const onUpload = jest.fn();

    const { container, getAllByTestId, getByTestId, queryAllByTestId } =
      render(
        <TestWrapper
          store={createStore((state) => ({
            ...state,
            accessToken: 'test token',
          }))}
        >
          <ImageSearchUploads onUpload={onUpload} />
        </TestWrapper>,
      );

    const input = getByTestId('add-image-input');
    fireEvent.input(input, { target: { files: [file1, file2] } });

    let removals = getAllByTestId('preview-image-remove');
    expect(removals).toHaveLength(2);
    removals[0].click();

    await waitFor(() => {
      removals = getAllByTestId('preview-image-remove');
      return expect(removals).toHaveLength(1);
    });
    removals[0].click();

    expect(container.firstChild).toMatchSnapshot();
    await waitFor(() =>
      expect(queryAllByTestId('preview-image')).toHaveLength(0),
    );
    expect(onUpload).toHaveBeenCalledTimes(0);
  });

  it('opens modal on clicking preview', () => {
    expect.assertions(3);

    const onUpload = jest.fn();

    const { container, getAllByTestId, getByTestId } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
        }))}
      >
        <ImageSearchUploads onUpload={onUpload} />
      </TestWrapper>,
    );

    const input = getByTestId('add-image-input');
    fireEvent.input(input, { target: { files: [file1] } });

    const previews = getAllByTestId('preview-image-preview');
    expect(previews).toHaveLength(1);
    previews[0].click();

    expect(container.firstChild).toMatchSnapshot();
    expect(onUpload).toHaveBeenCalledTimes(0);
  });

  it('sends all images to valencia & calls onUpload', async () => {
    expect.hasAssertions();

    const onUpload = jest.fn();
    const uploadFileMock = jest.spyOn(valencia, 'uploadFile');
    uploadFileMock.mockImplementation(
      async () => keysToCamel(data.response) as valencia.UploadFileResponse[],
    );

    const { getByTestId, getByText } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          accessToken: 'test token',
          tokenInfo: {
            ...state.tokenInfo,
            user: {
              dateJoined: '2019-08-07T06:44:11.867Z',
              email: '<EMAIL>',
              firstName: 'First',
              groups: [],
              id: 1,
              isActive: true,
              isStaff: true,
              isSuperuser: true,
              lastLogin: '2021-07-12T06:50:54.238Z',
              lastName: 'Last',
              password: 'password',
              userPermissions: [],
              username: 'test_username',
            },
          },
        }))}
      >
        <ImageSearchUploads onUpload={onUpload} />
      </TestWrapper>,
    );

    const input = getByTestId('add-image-input');
    fireEvent.input(input, { target: { files: [file1, file2] } });

    const submit = getByText('Start Upload');
    submit.click();

    await waitFor(() => expect(uploadFileMock).toHaveBeenCalledTimes(2));
    await waitFor(() => expect(onUpload).toHaveBeenCalledTimes(1), {
      // Uploads are delayed, in batches of 4, so allow extra wait time
      timeout: 2000,
    });
  });
});
