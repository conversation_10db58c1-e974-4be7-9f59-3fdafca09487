'use client';

import { faSearch } from '@fortawesome/free-solid-svg-icons';
import React, { useCallback, useEffect, useState } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import Dropdown from 'components/editmode/ui/Dropdown';
import { useAppSelector } from 'store/hooks';
import { useAccessToken } from 'util/hooks';
import {
  fetchTokenInfo,
  fetchUsersForOrganization,
} from 'util/organization/monza';
import { searchStories } from 'util/organization/silverstone';

import Button from '../../ui/Button';
import SearchedStoryEntryEditmode from '../SearchedStoryEntryEditmode';

import type { SidebarAccordionChildProps } from '../../SidebarAccordion';
import type { DropdownOption } from 'components/editmode/ui/Dropdown';
import type { SearchedStory } from 'util/organization/silverstone';

const timeOptions: DropdownOption[] = [
  { idx: '', text: 'Any Time' },
  { idx: '-1d', text: 'Past Day' },
  { idx: '-1w', text: 'Past Week' },
  { idx: '-1m', text: 'Past Month' },
  { idx: '-1y', text: 'Past Year' },
];

const allUsersOption: DropdownOption = {
  idx: '',
  text: 'All Users',
};

type StorySearchProps = SidebarAccordionChildProps;

export default function StorySearch({
  expanded,
}: StorySearchProps): React.ReactElement {
  const orgId = useAppSelector((state) => state.conf.organizationId);
  const orgName = useAppSelector((state) => state.conf.name);
  const token = useAccessToken();

  const [hasInitialLoad, setHasInitialLoad] = useState(false);
  const [loading, setLoading] = useState(true);
  const [orgUsers, setOrgUsers] = useState<DropdownOption[]>([allUsersOption]);
  const [sourceOrgs, setSourceOrgs] = useState<DropdownOption[]>([
    {
      idx: orgId,
      text: orgName,
    },
  ]);

  const [query, setQuery] = useState('');

  const [selectedUser, setSelectedUser] = useState<string | number>(
    allUsersOption.idx,
  );
  const [selectedTime, setSelectedTime] = useState<string | number>(
    timeOptions[0].idx,
  );
  const [selectedOrg, setSelectedOrg] = useState<string | number>(orgId);

  const [searchedQuery, setSearchedQuery] = useState('');

  const [searchedUser, setSearchedUser] = useState<string | number>(
    allUsersOption.idx,
  );
  const [searchedTime, setSearchedTime] = useState<string | number>(
    timeOptions[0].idx,
  );
  const [searchedOrg, setSearchedOrg] = useState<string | number>(orgId);

  const [page, setPage] = useState(0);
  const [stories, setStories] = useState<SearchedStory[]>([]);

  useEffect(() => {
    fetchUsersForOrganization({ orgId, token })
      .then((res) => {
        setOrgUsers([
          allUsersOption,
          ...(res.results?.map((user) => ({
            idx: user.id,
            text: `${user.firstName} ${user.lastName}`,
          })) ?? []),
        ]);
      })
      .catch(() => {});
  }, [orgId, token, setOrgUsers]);

  useEffect(() => {
    fetchTokenInfo({ token })
      .then((res) => {
        setSourceOrgs(
          res.organizations.map((org) => ({
            idx: org.organization.id.toString(),
            text: org.organization.name,
          })),
        );
      })
      .catch(() => {});
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (!expanded || hasInitialLoad) {
      return;
    }
    setHasInitialLoad(true);
    setLoading(true);
    setPage(1);
    searchStories({
      organization: searchedOrg.toString(),
      page: 1,
      token,
    })
      .then((res) => {
        setStories(res.results);
        setLoading(false);
      })
      .catch(() => {}); // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [expanded, hasInitialLoad, setHasInitialLoad]);

  const onSearch = useCallback(() => {
    setLoading(true);
    setPage(1);
    setSearchedOrg(selectedOrg);
    setSearchedQuery(query);
    setSearchedTime(selectedTime);
    setSearchedUser(selectedUser);

    searchStories({
      organization: selectedOrg.toString(),
      page: 1,
      query: query || undefined,
      token,
      updatedOn: selectedTime.toString() || undefined,
      userId: selectedUser.toString() || undefined,
    })
      .then((res) => {
        setStories(res.results);
        setLoading(false);
      })
      .catch(() => {});
  }, [
    setLoading,
    setPage,
    setSearchedOrg,
    setSearchedQuery,
    setSearchedTime,
    setSearchedUser,
    selectedOrg,
    selectedTime,
    selectedUser,
    token,
    query,
    setStories,
  ]);

  const onMore = useCallback(() => {
    setLoading(true);
    setPage(page + 1);
    searchStories({
      organization: searchedOrg.toString(),
      page: page + 1,
      query: searchedQuery || undefined,
      token,
      updatedOn: searchedTime.toString() || undefined,
      userId: searchedUser.toString() || undefined,
    })
      .then((res) => {
        setStories([...stories, ...res.results]);
        setLoading(false);
      })
      .catch(() => {});
  }, [
    setLoading,
    setPage,
    page,
    searchedOrg,
    searchedTime,
    searchedUser,
    searchedQuery,
    setStories,
    stories,
    token,
  ]);

  const REVOrg = sourceOrgs.filter(
    (org) => org.text.toLowerCase() === 'real estate view',
  );

  return (
    <div className="pb-10 text-sm">
      {loading && (
        <div className="absolute left-0 top-0 z-10 size-full bg-white/50" />
      )}

      <div className="relative">
        <input
          className="w-full border border-gray-200 px-2 py-1 focus:border-blue-400 focus:outline-none focus:ring-1"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setQuery(e.target.value);
          }}
          placeholder="Search Stories..."
          value={query}
        />

        <FontAwesomeIcon
          className="pointer-events-none absolute right-2 top-2.5 text-gray-300"
          icon={faSearch}
        />
      </div>
      <div className="flex flex-row">
        <Dropdown
          className="w-3/12"
          options={orgUsers}
          selected={selectedUser}
          setSelected={setSelectedUser}
        />
        <Dropdown
          className="w-4/12"
          options={timeOptions}
          selected={selectedTime}
          setSelected={setSelectedTime}
        />
        <Dropdown
          alignLeft={false}
          className="w-5/12"
          options={sourceOrgs}
          selected={selectedOrg}
          setSelected={setSelectedOrg}
        />
      </div>

      <Button onClick={onSearch}>Search</Button>

      <div>
        {stories.length === 0 && (
          <div className="mt-4 text-gray-600">No Stories Found</div>
        )}
        {stories.map((story) => (
          <SearchedStoryEntryEditmode
            REVOrg={REVOrg}
            className="my-3"
            key={story.id}
            story={story}
          />
        ))}
        {stories.length > 0 && <Button onClick={onMore}>More</Button>}
      </div>
    </div>
  );
}
