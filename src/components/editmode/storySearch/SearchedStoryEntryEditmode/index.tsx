import { faBars, faInfo } from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import { useDrag } from 'react-dnd';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import makeToast from 'components/editmode/ui/toast';
import { useAppSelector } from 'store/hooks';
import { DragItemType } from 'util/DraggableUtils';
import { hasValidURI, storyImageUrl } from 'util/image';

import type { DropdownOption } from 'components/editmode/ui/Dropdown';
import type { SearchedStory } from 'util/organization/silverstone';

interface SearchedStoryProps {
  REVOrg: DropdownOption[];
  className?: string;
  story: SearchedStory;
}

export default function SearchedStoryEntryEditmode({
  REVOrg,
  className,
  story,
}: SearchedStoryProps) {
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);
  const imageUrl =
    story.leadImage && hasValidURI(story.leadImage)
      ? storyImageUrl({
          height: 60,
          image: story.leadImage,
          transformUrl,
          width: 80,
        })
      : undefined;

  const [, drag, dragPreview] = useDrag(() => ({
    item: {
      storyId: story.id,
    },
    type: DragItemType.STORY,
  }));

  const { canonicalUrl, organization, tags } = story;
  const isDraggable =
    REVOrg.length > 0 &&
    REVOrg[0].idx === organization &&
    tags.includes('rev-only')
      ? Boolean(canonicalUrl)
      : true;

  const handleClick = () =>
    isDraggable
      ? null
      : makeToast(
          'This story cannot be added as it is missing the Canonical URL. Please add the Canonical URL and try again.',
          {
            position: 'top-center',
          },
        );

  const button = (
    <button
      aria-label={isDraggable ? 'Drag to pin' : 'Click for info'}
      // eslint-disable-next-line @stylistic/max-len
      className={`mr-2 rounded-md border border-transparent bg-gray-200 p-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200 focus:ring-offset-2 ${
        isDraggable ? 'cursor-move' : 'cursor-pointer'
      }`}
      onClick={handleClick}
      title={isDraggable ? 'Drag to pin' : 'Click for info'}
      type="button"
    >
      <FontAwesomeIcon fixedWidth icon={isDraggable ? faBars : faInfo} />
    </button>
  );

  const element = (
    <div
      className={clsx(
        'relative flex min-h-24 w-full flex-row rounded-md bg-white p-3',
        'text-sm',
        className,
      )}
    >
      {isDraggable ? drag(button) : button}
      <div className="absolute right-3 top-1 text-xs font-semibold">
        {new Date(story.publishFrom).toLocaleDateString('en-AU')}
      </div>
      {story.leadImage && (
        <div className="mr-3 mt-3 w-20 shrink-0">
          <img
            alt={story.leadImage.title}
            height={60}
            src={imageUrl}
            width={80}
          />
        </div>
      )}
      <div className="mt-3">
        <div>
          {story.byline ||
            (story.createdByDetail &&
              `${story.createdByDetail.firstName} ` +
                `${story.createdByDetail.lastName}`)}
        </div>
        <div className="text-gray-700">{story.title}</div>
      </div>
    </div>
  );

  if (isDraggable) {
    return dragPreview(element);
  }

  return element;
}
