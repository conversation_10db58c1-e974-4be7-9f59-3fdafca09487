'use client';

import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import React, { useState } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

export interface SidebarAccordionChildProps {
  expanded?: boolean;
}

interface SidebarAccordionProps {
  children: React.ReactElement<SidebarAccordionChildProps>;
  title: string;
}

export default function SidebarAccordion({
  children,
  title,
}: SidebarAccordionProps): React.ReactElement {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className="border-b-1 border-gray-200 bg-gray-100">
      <button
        className="w-full border-b-1 border-gray-200 px-4 text-left leading-9 focus:outline-none"
        onClick={() => setExpanded(!expanded)}
        type="button"
      >
        <div className="flex flex-row items-center">
          <FontAwesomeIcon
            className={`mr-2 text-gray-500 transition-all ${
              expanded ? 'rotate-90' : 'rotate-0'
            }`}
            icon={faChevronRight}
            size="xs"
          />
          {title}
        </div>
      </button>
      <div
        className={`relative h-full ${
          expanded ? 'py-2' : 'max-h-0 overflow-hidden py-0'
        } px-4 transition-all`}
      >
        {React.cloneElement(children, {
          expanded,
        })}
      </div>
    </div>
  );
}
