'use client';

import {
  faMinusSquare,
  faPlusSquare,
} from '@fortawesome/free-regular-svg-icons';
import {
  faDesktop,
  faEye,
  faMobileAlt,
  faTabletAlt,
} from '@fortawesome/free-solid-svg-icons';
import clsx from 'clsx';
import React, { useEffect } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import ZoneElements from 'components/editmode/zoneElements/ZoneElements';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import editmodeSlice, { LayoutSize } from 'store/slices/editmode';
import tokenInfoSlice from 'store/slices/tokenInfo';
import { useAccessToken } from 'util/hooks';
import { fetchTokenInfo } from 'util/organization/monza';

import SidebarAccordion from '../SidebarAccordion';
import ImageSearch from '../imageSearch/ImageSearch';
import StorySearch from '../storySearch/StorySearch';

const SIDEBAR_WIDTH = 350;

interface CircledIconProps {
  children: React.ReactElement<React.SVGAttributes<SVGSVGElement>>;
  targetLayout: LayoutSize;
}

function CircledIcon({
  children,
  targetLayout,
}: CircledIconProps): React.ReactElement {
  const { layout } = useAppSelector((state) => state.editmode);
  const dispatch = useAppDispatch();

  return (
    <button
      className={clsx(
        'group mr-1 size-8 rounded-full border border-gray-400',
        'flex cursor-pointer items-center justify-center hover:border-blue-400',
        {
          'border-blue-400': layout === targetLayout,
        },
      )}
      onClick={() => {
        dispatch(editmodeSlice.actions.setLayout(targetLayout));
        if ([LayoutSize.MOBILE, LayoutSize.TABLET].includes(targetLayout)) {
          dispatch(editmodeSlice.actions.setCollapsed(true));
        }
        if (window.parent) {
          window.parent.postMessage(targetLayout, '*');
        }
      }}
      type="button"
    >
      {React.cloneElement(children, {
        className: `${
          layout === targetLayout ? 'text-blue-400' : ''
        } group-hover:text-blue-400`,
      })}
    </button>
  );
}

export default function Sidebar(): React.ReactElement {
  const token = useAccessToken();
  const siteUrl = useAppSelector((state) => state.settings.host);
  const layoutSize = useAppSelector((state) => state.editmode.layout);
  const dispatch = useAppDispatch();

  const collapsed = useAppSelector(
    (state) => state.editmode.sidebar.collapsed,
  );

  const isZoneView = useAppSelector((state) => state.editmode.isZoneView);

  useEffect(() => {
    fetchTokenInfo({ token })
      .then((res) => {
        dispatch(tokenInfoSlice.actions.set(res));
      })
      .catch(() => {});
  }, [token, dispatch]);

  const tabs = [false, true];

  return (
    <div style={{ width: collapsed ? 0 : SIDEBAR_WIDTH }}>
      <div className="fixed right-0 top-0 z-20 h-full border-l-1 border-gray-400 bg-white shadow-2xl transition-all">
        <button
          aria-label={collapsed ? 'Expand' : 'Collapse'}
          className="absolute -left-9 top-0 flex h-10 w-9 items-center justify-center border border-t-0 border-gray-400 bg-gray-200 shadow-2xl focus:outline-none"
          onClick={() => dispatch(editmodeSlice.actions.toggleCollapsed())}
          type="button"
        >
          <FontAwesomeIcon
            color="rgba(0, 0, 0, .5)"
            icon={collapsed ? faPlusSquare : faMinusSquare}
            size="lg"
          />
        </button>
        <div
          className="h-full overflow-hidden transition-all"
          style={{ width: collapsed ? 0 : SIDEBAR_WIDTH }}
        >
          <div
            className="relative flex h-full flex-col overflow-hidden"
            style={{ right: collapsed ? -SIDEBAR_WIDTH : undefined }}
          >
            <div className="flex flex-row items-center justify-between p-4">
              <button
                className="rounded-md bg-blue-500 px-4 leading-8 text-white hover:bg-blue-400 focus:outline-none"
                onClick={() => window.open(`//${siteUrl}`, '_blank')}
                type="button"
              >
                <FontAwesomeIcon color="white" icon={faEye} /> Live Site
              </button>
              <div className="flex flex-row items-center justify-center">
                <span className="mr-2 select-none">Layout: </span>
                <CircledIcon targetLayout={LayoutSize.MOBILE}>
                  <FontAwesomeIcon icon={faMobileAlt} />
                </CircledIcon>
                <CircledIcon targetLayout={LayoutSize.TABLET}>
                  <FontAwesomeIcon icon={faTabletAlt} />
                </CircledIcon>
                <CircledIcon targetLayout={LayoutSize.DESKTOP}>
                  <FontAwesomeIcon icon={faDesktop} />
                </CircledIcon>
              </div>
            </div>
            {layoutSize === LayoutSize.DESKTOP && (
              <>
                <div className="border-b border-gray-200 pl-2">
                  <nav aria-label="Tabs" className="-mb-px flex space-x-8">
                    {tabs.map((tab) => {
                      const isCurrentTab = isZoneView === tab;
                      return (
                        <button
                          aria-current={isCurrentTab}
                          className={clsx(
                            'relative whitespace-nowrap border-b-2 px-3 py-4 text-sm font-medium outline-none',
                            {
                              'border-black font-semibold': isCurrentTab,
                              'border-transparent': !isCurrentTab,
                            },
                          )}
                          key={tab.toString()}
                          onClick={() =>
                            !isCurrentTab &&
                            dispatch(editmodeSlice.actions.toggleZoneView())
                          }
                          type="button"
                        >
                          <span className="uppercase">
                            {tab ? 'Zone' : 'Story'}
                          </span>
                        </button>
                      );
                    })}
                  </nav>
                </div>

                <div className="mt-1 grow overflow-y-auto">
                  <div className={clsx({ hidden: !isZoneView })}>
                    <SidebarAccordion title="Zone Elements">
                      <ZoneElements />
                    </SidebarAccordion>
                  </div>
                  <div className={clsx({ hidden: isZoneView })}>
                    <SidebarAccordion title="Search Stories">
                      <StorySearch />
                    </SidebarAccordion>
                  </div>
                  <div className={clsx({ hidden: isZoneView })}>
                    <SidebarAccordion title="Story Media">
                      <ImageSearch />
                    </SidebarAccordion>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
