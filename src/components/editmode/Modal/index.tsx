'use client';

import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { useEffect } from 'react';
import ReactDOM from 'react-dom';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { useAppDispatch, useAppSelector } from 'store/hooks';
import editmodeSlice from 'store/slices/editmode';

const MODAL_PORTAL_ID = 'modal-portal';

interface ModalCommonProps {
  children: React.ReactNode;
  height?: number | string;
  rounded?: boolean;
  width?: number | string;
}

type ModalProps = ModalCommonProps &
  (
    | {
        onRequestClose: () => void;
        showCloseIcon?: true;
      }
    | {
        onRequestClose?: never;
        showCloseIcon: false;
      }
  );

export default function Modal({
  children,
  height,
  onRequestClose,
  rounded = true,
  showCloseIcon = true,
  width,
}: ModalProps): React.ReactElement {
  const element = document.getElementById(MODAL_PORTAL_ID);
  if (!element) {
    throw new Error('Unable to find modal portal');
  }

  const dispatch = useAppDispatch();
  useEffect(() => {
    dispatch(editmodeSlice.actions.incrementModalCount());
    return () => {
      dispatch(editmodeSlice.actions.decrementModalCount());
    };
  }, [dispatch]);

  return ReactDOM.createPortal(
    <div className="fixed right-0 top-0 z-50 h-screen w-screen bg-gray-900/60">
      <div
        className={`absolute left-1/2 top-1/2 p-4 ${
          rounded ? 'rounded-md' : ''
        } -translate-x-1/2 -translate-y-1/2 bg-white shadow-2xl`}
        style={{ height, width }}
      >
        {showCloseIcon && (
          <button
            aria-label="Close"
            className="absolute right-0 top-0 flex size-6 -translate-y-1/2 translate-x-1/2 items-center justify-center rounded-full border-2 border-gray-800 bg-white"
            onClick={onRequestClose}
            type="button"
          >
            <FontAwesomeIcon className="text-gray-800" icon={faTimes} />
          </button>
        )}
        {children}
      </div>
    </div>,
    element,
  );
}

export function ModalPortal(): React.ReactElement {
  const modalCount = useAppSelector((state) => state.editmode.modalCount);
  useEffect(() => {
    document.body.classList.toggle('overflow-hidden', modalCount > 0);
  }, [modalCount]);
  return <div className="z-50" id={MODAL_PORTAL_ID} />;
}
