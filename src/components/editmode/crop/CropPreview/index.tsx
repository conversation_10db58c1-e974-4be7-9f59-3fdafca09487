'use client';

import { useEffect, useRef } from 'react';

import { calculateFocalCrop } from 'util/image';

import type { StoryImageCropConfig } from 'types/Story';

interface CropPreviewProps {
  canvasHeight: number;
  canvasWidth: number;
  cropConfig: StoryImageCropConfig;
  image: HTMLImageElement | undefined;
  title: string;
}

function getTransform(
  canvasWidth: number,
  canvasHeight: number,
  cropWidth: number,
  cropHeight: number,
  focalX: number,
  focalY: number,
  scale: number,
) {
  const aspect = canvasWidth / canvasHeight;
  return calculateFocalCrop({
    aspect,
    focalX,
    focalY,
    height: cropHeight,
    scale,
    width: cropWidth,
  });
}

export default function CropPreview({
  canvasHeight,
  canvasWidth,
  cropConfig,
  image,
  title,
}: CropPreviewProps): React.ReactElement {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !image) {
      return;
    }

    const { cropHeight, cropWidth, cropX, cropY, focalX, focalY, scale } =
      cropConfig;
    const cropFocalX = focalX - cropX;
    const cropFocalY = focalY - cropY;
    const transform = getTransform(
      canvasWidth,
      canvasHeight,
      cropWidth ?? 0,
      cropHeight ?? 0,
      cropFocalX,
      cropFocalY,
      scale ?? 1,
    );
    let { topX, topY } = transform;
    const { height, width } = transform;

    const canvasContext = canvas.getContext('2d');
    if (!canvasContext) {
      return;
    }

    canvas.width = canvasWidth;
    topX += cropX;
    topY += cropY;
    const scaleX = canvasWidth / width;
    const scaleY = canvasHeight / height;
    canvasContext.scale(scaleX, scaleY);
    canvasContext.translate(-topX, -topY);
    canvasContext.drawImage(
      image,
      0,
      0,
      image.width,
      image.height,
      0,
      0,
      image.width,
      image.height,
    );
  }, [image, cropConfig, canvasHeight, canvasWidth]);

  return (
    <div className="relative">
      <div className="absolute left-0 top-0 bg-gray-900/60 px-2 py-1 text-sm text-white">
        {title}
      </div>
      <canvas height={canvasHeight} ref={canvasRef} width={canvasWidth} />
    </div>
  );
}
