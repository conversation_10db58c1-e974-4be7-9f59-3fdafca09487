'use client';

import { useCallback, useEffect, useState } from 'react';

import type { SetStateAction } from 'react';
import type { Crop } from 'react-image-crop';

export interface Focus {
  x: number;
  y: number;
}

interface FocusPointProps {
  cropperElement: HTMLDivElement | null;
  focus: Focus;
  pixelCrop: Crop | undefined;
  setFocus: React.Dispatch<SetStateAction<Focus>>;
}

export default function FocusPoint({
  cropperElement,
  focus,
  pixelCrop,
  setFocus,
}: FocusPointProps): React.ReactElement {
  const [dragging, setDragging] = useState(false);

  const handleTouchMouseMove = useCallback(
    (e: MouseEvent | TouchEvent) => {
      const x =
        (e as MouseEvent).clientX ?? (e as TouchEvent).touches[0]?.clientX;
      const y =
        (e as MouseEvent).clientY ?? (e as TouchEvent).touches[0]?.clientY;
      const cropX = pixelCrop?.x ?? 0;
      const cropY = pixelCrop?.y ?? 0;
      const cropWidth = pixelCrop?.width ?? 0;
      const cropHeight = pixelCrop?.height ?? 0;
      const origin = cropperElement?.getBoundingClientRect();

      if (origin && dragging) {
        let focalX = x - origin.x;
        let focalY = y - origin.y;

        if (focalX < cropX) {
          focalX = cropX;
        }
        if (focalX > cropX + cropWidth) {
          focalX = cropX + cropWidth;
        }
        if (focalY < cropY) {
          focalY = cropY;
        }
        if (focalY > cropY + cropHeight) {
          focalY = cropY + cropHeight;
        }

        if (setFocus) {
          setFocus({
            x: (100 * focalX) / origin.width,
            y: (100 * focalY) / origin.height,
          });
        }
      }
    },
    [dragging, pixelCrop, cropperElement, setFocus],
  );

  const handleTouchMouseDown = () => {
    setDragging(true);
  };

  const handleTouchMouseUp = useCallback(() => {
    setDragging(false);
  }, [setDragging]);

  useEffect(() => {
    document.addEventListener('mousemove', handleTouchMouseMove);
    document.addEventListener('touchmove', handleTouchMouseMove);
    document.addEventListener('mouseup', handleTouchMouseUp);
    document.addEventListener('touchend', handleTouchMouseUp);
    document.addEventListener('touchcancel', handleTouchMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleTouchMouseMove);
      document.removeEventListener('touchmove', handleTouchMouseMove);
      document.removeEventListener('mouseup', handleTouchMouseUp);
      document.removeEventListener('touchend', handleTouchMouseUp);
      document.removeEventListener('touchcancel', handleTouchMouseUp);
    };
  }, [
    dragging,
    pixelCrop,
    setFocus,
    handleTouchMouseMove,
    handleTouchMouseUp,
  ]);

  return (
    <div
      aria-label="Focus point"
      className="absolute"
      onKeyDown={() => {}}
      onMouseDown={handleTouchMouseDown}
      onTouchStart={handleTouchMouseDown}
      role="button"
      style={{ left: `${focus.x}%`, top: `${focus.y}%` }}
      tabIndex={0}
    >
      <div className="absolute left-0 top-0 z-50 size-20 -translate-x-1/2 -translate-y-1/2 cursor-pointer rounded-full border-3 border-white bg-white/25 shadow-lg">
        <div className="absolute left-1/2 top-1/2 h-2 w-0.5 -translate-x-1/2 -translate-y-1/2 bg-white/100" />
        <div className="absolute left-1/2 top-1/2 h-0.5 w-2 -translate-x-1/2 -translate-y-1/2 bg-white/100" />
      </div>
    </div>
  );
}
