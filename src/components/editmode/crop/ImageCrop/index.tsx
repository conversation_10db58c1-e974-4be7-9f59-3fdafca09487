'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import ReactCrop from 'react-image-crop';

import 'react-image-crop/dist/ReactCrop.css';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import zoneItemsSlice from 'store/slices/zoneItems';
import { useAccessToken } from 'util/hooks';
import { hasValidURI, storyImageUrl } from 'util/image';
import { fetchStories, updateStoryImage } from 'util/organization/suzuka';

import Modal from '../../Modal';
import CropPreview from '../CropPreview';
import FocusPoint from '../FocusPoint';

import type { Focus } from '../FocusPoint';
import type { Crop } from 'react-image-crop';
import type { StoryImage, StoryImageCropConfig } from 'types/Story';

const CONTAINER_WIDTH = 595;
const CONTAINER_HEIGHT = 450;
const CONTAINER_PADDING_X = 64;
const CONTAINER_PADDING_Y = 64;
const INNER_WIDTH = CONTAINER_WIDTH - CONTAINER_PADDING_X * 2;
const INNER_HEIGHT = CONTAINER_HEIGHT - CONTAINER_PADDING_Y * 2;

interface ImageCropProps {
  image: StoryImage;
  requestClose: () => void;
  storyId: string;
  storyListId: number;
}

function createStoryImageCropConfig(
  image: StoryImage,
  crop: Crop | undefined,
  focus: Focus,
): StoryImageCropConfig {
  return {
    cropHeight: Math.floor(((crop?.height ?? 0) * image.height) / 100),
    cropWidth: Math.floor(((crop?.width ?? 0) * image.width) / 100),
    cropX: Math.floor(((crop?.x ?? 0) * image.width) / 100),
    cropY: Math.floor(((crop?.y ?? 0) * image.height) / 100),
    focalX: Math.floor((focus.x * image.width) / 100),
    focalY: Math.floor((focus.y * image.height) / 100),
    scale: image.cropConfig?.scale ?? 1,
  };
}

export default function ImageCrop({
  image,
  requestClose,
  storyId,
  storyListId,
}: ImageCropProps): React.ReactElement {
  const siteId = useAppSelector((state) => state.settings.siteId);
  const token = useAccessToken();
  const dispatch = useAppDispatch();

  const wrapperRef = useRef<HTMLDivElement>(null);

  const { transformUrl } = useAppSelector((state) => state.settings);
  const imageUrl = hasValidURI(image)
    ? storyImageUrl({
        ignoreCrop: true,
        image,
        transformUrl,
      })
    : undefined;

  // Crop
  const [crop, setCrop] = useState<Crop>();
  const [existingCrop, setExistingCrop] = useState<Crop>();
  const [pixelCrop, setPixelCrop] = useState<Crop>();

  useEffect(() => {
    const { cropConfig, height, width } = image;
    if (!cropConfig) {
      setExistingCrop(undefined);
      return;
    }

    const { cropHeight, cropWidth, cropX, cropY } = cropConfig;
    const scaled: Crop = {
      height: (100 * (cropHeight ?? 0)) / height,
      unit: '%',
      width: (100 * (cropWidth ?? 0)) / width,
      x: (100 * cropX) / width,
      y: (100 * cropY) / height,
    };
    setExistingCrop(scaled);
  }, [image, setExistingCrop]);

  useEffect(() => {
    setCrop(existingCrop);
  }, [existingCrop, setCrop]);

  // Focus
  const [focus, setFocus] = useState<Focus>({
    x: 0,
    y: 0,
  });
  const [existingFocus, setExistingFocus] = useState<Focus>({
    x: 0,
    y: 0,
  });

  useEffect(() => {
    setExistingFocus({
      x: (100 * (image.cropConfig?.focalX ?? image.width / 2)) / image.width,
      y: (100 * (image.cropConfig?.focalY ?? image.height / 2)) / image.height,
    });
  }, [image, setExistingFocus]);

  useEffect(() => {
    setFocus(existingFocus);
  }, [existingFocus, setFocus]);

  // Description
  const [description, setDescription] = useState('');
  const [existingDescription, setExistingDescription] = useState('');

  useEffect(() => {
    setExistingDescription(image.description);
  }, [image.description, setExistingDescription]);

  useEffect(() => {
    setDescription(existingDescription);
  }, [existingDescription, setDescription]);

  // Load image
  const [loadedImage, setLoadedImage] = useState<HTMLImageElement>();

  useEffect(() => {
    if (imageUrl) {
      const img = new Image();
      img.onload = () => {
        setLoadedImage(img);
      };
      img.src = imageUrl;
    }
  }, [imageUrl]);

  const onChange = useCallback(
    (newPixelCrop: Crop, newPercentCrop: Crop) => {
      const { height, width } = newPixelCrop;
      if (height !== 0 && width !== 0) {
        setCrop(newPercentCrop);
        setPixelCrop(newPixelCrop);
      }
    },
    [setCrop],
  );

  function reset() {
    setCrop(existingCrop);
    setFocus(existingFocus);
    setDescription(existingDescription);

    const bounds = wrapperRef.current?.getBoundingClientRect();
    if (bounds) {
      setPixelCrop({
        height: ((existingCrop?.height ?? 0) * bounds.height) / 100,
        unit: 'px',
        width: ((existingCrop?.width ?? 0) * bounds.width) / 100,
        x: ((existingCrop?.x ?? 0) * bounds.width) / 100,
        y: ((existingCrop?.y ?? 0) * bounds.height) / 100,
      });
    }
  }

  function update() {
    const cropConfig: StoryImageCropConfig = createStoryImageCropConfig(
      image,
      crop,
      focus,
    );

    updateStoryImage({
      cropConfig,
      description,
      image,
      siteId,
      storyId,
      token,
    })
      .then(() =>
        fetchStories({
          siteId,
          storyListId,
          token,
        }),
      )
      .then((response) => {
        dispatch(zoneItemsSlice.actions.updateStoryList(response));
        requestClose();
      })
      .catch(() => {});
  }

  const storyImageCropConfig = createStoryImageCropConfig(image, crop, focus);

  // React Crop image scale
  const [reactCropScale, setReactCropScale] = useState<{
    height: number;
    width: number;
  }>({ height: 0, width: 0 });

  const onCropImageLoaded = useCallback(
    (event: React.SyntheticEvent<HTMLImageElement>) => {
      const croppingImage = event.currentTarget;
      const { naturalHeight: height, naturalWidth: width } = croppingImage;

      if (width / height > INNER_WIDTH / INNER_HEIGHT) {
        setReactCropScale({
          height: height * (INNER_WIDTH / width),
          width: INNER_WIDTH,
        });
      } else {
        setReactCropScale({
          height: INNER_HEIGHT,
          width: width * (INNER_HEIGHT / height),
        });
      }

      setPixelCrop({
        height: ((crop?.height ?? 0) * height) / 100,
        unit: 'px',
        width: ((crop?.width ?? 0) * width) / 100,
        x: ((crop?.x ?? 0) * width) / 100,
        y: ((crop?.y ?? 0) * height) / 100,
      });
    },
    [crop, setReactCropScale],
  );

  return (
    <Modal height="100vh" rounded={false} showCloseIcon={false} width="100vw">
      <div className="flex select-none flex-row justify-center">
        <div className="flex flex-col">
          <div
            className="relative flex flex-row items-center justify-center bg-gray-200 p-16"
            style={{ height: CONTAINER_HEIGHT, width: CONTAINER_WIDTH }}
          >
            <div className="absolute left-0 top-0 bg-gray-900/60 px-2 py-1 text-sm text-white">
              CROP
            </div>

            <div className="absolute right-0 top-0 text-sm">
              <button
                className="rounded-l-md border border-gray-300 bg-white p-2 py-1.5 font-semibold text-gray-600 transition-all hover:border-blue-400 hover:text-blue-400 focus:outline-none"
                onClick={reset}
                type="button"
              >
                RESET
              </button>
              <button
                className="rounded-r-md border border-l-0 border-gray-300 bg-blue-500 p-2 py-1.5 pr-4 font-bold text-white transition-all hover:bg-blue-400 focus:outline-none"
                onClick={update}
                type="submit"
              >
                OK
              </button>
            </div>

            {imageUrl && (
              <ReactCrop
                crop={crop}
                keepSelection
                onChange={onChange}
                style={{
                  height: reactCropScale.height,
                  width: reactCropScale.width,
                }}
              >
                <img alt="" onLoad={onCropImageLoaded} src={imageUrl} />
                <div
                  className="pointer-events-none absolute right-0 top-0 size-full"
                  ref={wrapperRef}
                />
                <FocusPoint
                  cropperElement={wrapperRef.current}
                  focus={focus}
                  pixelCrop={pixelCrop}
                  setFocus={setFocus}
                />
              </ReactCrop>
            )}
          </div>

          <textarea
            className="mt-2 text-sm text-gray-600"
            onChange={(e) => setDescription(e.target.value)}
            value={description}
          />
        </div>

        <div className="mx-8 border-l-1 border-gray-200" />

        {image.cropConfig && (
          <div className="flex flex-col">
            <CropPreview
              canvasHeight={177}
              canvasWidth={315}
              cropConfig={storyImageCropConfig}
              image={loadedImage}
              title="Landscape"
            />
            <div className="mt-3 flex flex-row">
              <CropPreview
                canvasHeight={260}
                canvasWidth={160}
                cropConfig={storyImageCropConfig}
                image={loadedImage}
                title="Portrait"
              />
              <div className="ml-3 flex flex-col">
                <CropPreview
                  canvasHeight={130}
                  canvasWidth={130}
                  cropConfig={storyImageCropConfig}
                  image={loadedImage}
                  title="Square"
                />
                <div className="mt-3">
                  <CropPreview
                    canvasHeight={72}
                    canvasWidth={130}
                    cropConfig={storyImageCropConfig}
                    image={loadedImage}
                    title="Thumbnail"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
