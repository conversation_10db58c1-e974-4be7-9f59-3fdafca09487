import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

interface Props {
  children: React.ReactNode;
}

export default function Container({ children }: Props) {
  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex justify-center bg-gray-200">
        <div className="w-full bg-white">{children}</div>
      </div>
    </DndProvider>
  );
}
