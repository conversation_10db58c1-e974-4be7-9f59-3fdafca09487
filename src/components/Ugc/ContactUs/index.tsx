import { faEnvelope, faPhone } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import Link from 'themes/autumn/components/generic/Link';

export default function ContactUs(): React.ReactElement {
  return (
    <div className="rounded-lg bg-yellow-50 py-4 pl-4 font-inter text-gray-900 md:p-4">
      <h2 className="mb-2 pr-4 font-merriweather font-bold @md:text-2xl md:pr-0">
        Post Your Ad Today and Reach Your Community!
      </h2>
      <div className="hidden grow text-sm font-medium @md:block">
        Post your ad for tributes, funerals, jobs, real estate, services and
        celebrations.
        <span className="ml-1 font-bold">Reach the right audience fast</span>
      </div>
      <div className="mt-4 flex flex-row items-start overflow-hidden">
        <div className="flex w-3/5 flex-col justify-start @md:hidden">
          <div className="mb-2 text-lg font-bold">Speak to us Now</div>
          <div className="grow text-sm">
            <div className="mb-2 min-h-12">
              <FontAwesomeIcon className="mr-2" icon={faPhone} />
              <span className="font-semibold">Call:</span>
              <a className="underline" href="tel:1300618237">
                1300 618 237
              </a>
            </div>
            <Link
              className="inline-block w-4/5 rounded-md bg-amber-300 py-2 text-center text-sm font-bold transition-colors hover:bg-amber-400"
              href="mailto:<EMAIL>"
              noStyle
            >
              Email us
            </Link>
          </div>
        </div>
        <div className="w-2/5 shrink-0 @md:w-auto">
          <div className="size-40 overflow-hidden rounded-full bg-rose-300">
            <img
              alt="Contact Us"
              className="size-full object-cover"
              // eslint-disable-next-line @stylistic/max-len
              src="https://cdn.newsnow.io/241145837/2367d0fa-c0a6-410d-bdad-45a6afe6545c.jpeg"
            />
          </div>
        </div>
        <div className="ml-6 mt-4 hidden text-sm @md:block">
          <div className="mb-2 text-lg font-bold">Speak to us Now</div>
          <div className="mb-2">
            <FontAwesomeIcon className="mr-2" icon={faPhone} />
            <span className="font-semibold">Call:</span>
            <a className="ml-2 text-gray-600" href="tel:1300618237">
              1300 618 237
            </a>
          </div>
          <div className="mb-4">
            <FontAwesomeIcon className="mr-2" icon={faEnvelope} />
            <span className="font-semibold">Email:</span>
            <a
              className="ml-2 text-gray-600"
              href="mailto:<EMAIL>"
            >
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
      <div className="mt-4 flex flex-row items-center">
        <Link
          className="mx-4 hidden w-1/3 rounded-3xl bg-amber-300 p-3 text-center text-sm font-bold transition-colors hover:bg-amber-400 @md:block"
          href="mailto:<EMAIL>"
          noStyle
        >
          Contact us
        </Link>
        <div className="hidden w-2/3 items-center justify-start text-sm font-semibold md:block">
          Or post an ad with&nbsp;
          <a className="underline" href="https://addirect.com.au/">
            AD direct
          </a>
        </div>
      </div>
    </div>
  );
}
