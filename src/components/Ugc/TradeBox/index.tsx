import Link from 'themes/autumn/components/generic/Link';

export default function TradeBox(): React.ReactElement | null {
  return (
    <div className="hidden rounded-lg bg-blue-350 py-4 pl-4 font-inter text-gray-900 @md:p-6">
      <h2 className="mb-2 pr-4 font-merriweather font-bold @md:text-2xl md:pr-0">
        Tools Down: Boost Your Business Online - Fast & Easy!
      </h2>
      <div className="mt-4 flex flex-row overflow-hidden @md:gap-4">
        <div className="flex w-3/5 flex-col justify-start @md:hidden">
          <div className="mb-4 text-sm">
            Stand Out in Trades & Services! <br />
            Boost visibility, attract customers, and generation leads—list your
            business today!
          </div>
          <div className="flex justify-start">
            <Link
              className="rounded-md bg-blue-600 px-14 py-2 text-center text-sm font-bold text-white transition-colors hover:bg-blue-700"
              href="mailto:<EMAIL>"
              noStyle
            >
              Email us
            </Link>
          </div>
        </div>
        <div className="w-2/5 shrink-0 @md:w-auto">
          <div className="size-40 overflow-hidden rounded-full @md:size-36">
            <img
              alt="Grow your business"
              className="size-full object-cover"
              // eslint-disable-next-line @stylistic/max-len
              src="https://cdn.newsnow.io/241145837/9dc40111-21d0-401c-94b7-d26cd52536c8.jpeg"
            />
          </div>
        </div>
        <div className="mt-4 hidden grow text-base @md:block">
          <div className="mb-2">
            <span className="font-bold">Stand Out Online:</span>
            <span className="ml-2 text-gray-600">
              List your business in our customised Trades and Services
              directory
            </span>
          </div>
          <div className="mb-2">
            <span className="font-bold">Be Found First:</span>
            <span className="ml-2 text-gray-600">
              Increase your visibility and attract more customers.
            </span>
          </div>
          <div className="mb-2">
            <span className="font-bold">Generate Leads:</span>
            <span className="ml-2 text-gray-600">
              Be where your customers are looking and elevate your business
            </span>
          </div>
        </div>
      </div>
      <div className="mt-6 hidden flex-row @md:flex">
        <div className="flex w-2/3 items-center justify-start text-lg font-bold">
          Speak to us - Take charge of your business growth today!
        </div>
        <div className="flex w-1/3 items-center justify-center">
          <Link
            className="ml-4 w-full rounded-3xl bg-blue-600 p-3 text-center text-sm font-bold text-white transition-colors hover:bg-blue-700 md:mx-0 md:py-1 md:pl-2 md:pr-0 lg:ml-4 lg:px-3 lg:py-4"
            href="mailto:<EMAIL>"
            noStyle
          >
            Grow your business
          </Link>
        </div>
      </div>
    </div>
  );
}
