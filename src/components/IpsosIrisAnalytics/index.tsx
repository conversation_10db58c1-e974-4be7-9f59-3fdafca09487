import Script from 'next/script';

import { useAppSelector } from 'store/hooks';
import { usePageHierarchy } from 'util/hooks';

export default function IpsosIrisAnalytics(): React.ReactElement | null {
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  const feature = useAppSelector((state) => state.features.ipsosIrisAnalytics);
  const { primaryPage } = usePageHierarchy();
  const viewType = useAppSelector((state) => state.settings.viewType);
  const isHomePage = viewType === 'homepage';

  if (!feature.enabled || !isClientSide) return null;

  const {
    classifiedsSectionId,
    commentSectionId,
    defaultSectionId,
    homepageSectionId,
    jobsSectionId,
    newsSectionId,
    sportSectionId,
    whatsonSectionId,
  } = feature.data;

  const filteredData = Object.fromEntries(
    Object.entries(feature.data).filter(([, v]) => v !== ''),
  );

  const sectionId = (() => {
    if (Object.keys(filteredData).length === 1) {
      return defaultSectionId;
    }

    if (isHomePage) {
      return homepageSectionId;
    }

    if (primaryPage?.name) {
      switch (primaryPage.name.toLowerCase()) {
        case 'news':
          return newsSectionId;
        case 'classifieds':
          return classifiedsSectionId;
        case 'jobs':
          return jobsSectionId;
        case 'sport':
          return sportSectionId;
        case 'comment':
          return commentSectionId;
        case "what's on":
          return whatsonSectionId;
        default:
          return defaultSectionId;
      }
    }
    return defaultSectionId;
  })();

  if (!sectionId) return null;

  return (
    <Script
      async
      id="ipsos-iris-analytics"
      src={`https://au-script.dotmetrics.net/door.js?id=${sectionId}`}
      strategy="lazyOnload"
    />
  );
}
