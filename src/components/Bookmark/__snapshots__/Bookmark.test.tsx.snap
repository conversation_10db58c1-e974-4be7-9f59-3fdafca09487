// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BookmarkButton Component Rendering matches snapshot for anonymous user 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="relative"
      data-headlessui-state=""
    >
      <div
        class="relative"
      >
        <div
          aria-controls="headlessui-popover-panel-:r4:"
          aria-expanded="false"
          data-headlessui-state=""
          id="headlessui-popover-button-:r2:"
        >
          <button
            aria-label="Bookmark"
            class="flex items-center justify-center rounded-full border border-gray-300 px-6 py-4 font-inter text-xs leading-4 disabled:opacity-50"
            data-testid="bookmark-button"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-bookmark size-4"
              data-icon="bookmark"
              data-prefix="far"
              focusable="false"
              role="img"
              viewBox="0 0 384 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 48C0 21.5 21.5 0 48 0l0 48 0 393.4 130.1-92.9c8.3-6 19.6-6 27.9 0L336 441.4 336 48 48 48 48 0 336 0c26.5 0 48 21.5 48 48l0 440c0 9-5 17.2-13 21.3s-17.6 3.4-24.9-1.8L192 397.5 37.9 507.5c-7.3 5.2-16.9 5.9-24.9 1.8S0 497 0 488L0 48z"
                fill="currentColor"
              />
            </svg>
            <span
              class="ml-2"
            >
              Test Bookmark
            </span>
          </button>
        </div>
        <div
          class="absolute inset-x-0 h-9"
        />
        <div
          class="absolute top-[calc(100%+7px)] z-10 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white hidden left-10"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`BookmarkButton Component Rendering matches snapshot for authenticated user 1`] = `
<div>
  <div>
    <div
      class="z-50"
      id="modal-portal"
    />
    <div
      class="relative inline-block"
    >
      <button
        aria-label="Bookmark"
        class="flex items-center justify-center rounded-full border border-gray-300 px-6 py-4 font-inter text-xs leading-4 disabled:opacity-50"
        data-testid="bookmark-button"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-bookmark size-4"
          data-icon="bookmark"
          data-prefix="far"
          focusable="false"
          role="img"
          viewBox="0 0 384 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0 48C0 21.5 21.5 0 48 0l0 48 0 393.4 130.1-92.9c8.3-6 19.6-6 27.9 0L336 441.4 336 48 48 48 48 0 336 0c26.5 0 48 21.5 48 48l0 440c0 9-5 17.2-13 21.3s-17.6 3.4-24.9-1.8L192 397.5 37.9 507.5c-7.3 5.2-16.9 5.9-24.9 1.8S0 497 0 488L0 48z"
            fill="currentColor"
          />
        </svg>
        <span
          class="ml-2"
        >
          Test Bookmark
        </span>
      </button>
      <div
        class="absolute left-10 top-[calc(100%+7px)] z-10 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white"
      />
      <div
        class="absolute top-[calc(100%+7px)] z-[5] mt-[9px] flex h-auto w-64 rounded-md border-1 border-gray-200 bg-white px-4 py-2 shadow-md transition duration-200 ease-out"
      >
        <div
          class="self-start pb-1 font-inter text-sm text-gray-900"
        >
          <div
            class="mb-2 text-base font-semibold text-gray-900"
          >
            Add to My Saved List
          </div>
          <div
            class="mb-4 text-sm font-normal text-gray-900"
          >
            Add this to your saved list and come back to them anytime.
          </div>
          <div
            class="flex items-center justify-between"
          >
            <div
              class="text-sm font-medium text-gray-900 underline"
              role="button"
              tabindex="0"
            >
              Dismiss
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
