import { faBookmark } from '@fortawesome/free-regular-svg-icons';
import { faBookmark as faBookmarkSolid } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useCallback, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';

import OnboardingTooltip from 'components/Onboarding/Tooltip';
import { useAppSelector } from 'store/hooks';
import { redirectToRegister } from 'util/auth';
import { setGtmDataLayer } from 'util/gtm';
import { useWindowHref } from 'util/hooks';

import VisitorPopover from './components/VisitorPopover';
import {
  BOOKMARK_GTM_EVENT,
  ONBOARDING_BOOKMARK_DISMISSED_KEY,
  QUEUED_BOOKMARK_KEY,
} from './constants';
import { useBookmark } from './hooks';
import { setBookmarkDismissed } from './utils';

import type { QueuedBookmark } from './types';
import type { UserBookmarkResourceType } from 'types/sepang-types/bookmark';

interface BookmarkButtonProps {
  containerClassName?: string;
  metadata?: Record<string, unknown>;
  resourceId: string;
  resourceType: UserBookmarkResourceType;
  title?: string;
}

interface PianoUserBookmarkButtonProps extends BookmarkButtonProps {
  showOnboarding?: boolean;
}

interface ConditionalBookmarkButtonProps extends BookmarkButtonProps {
  showOnboarding?: boolean;
}

interface ButtonProps {
  className?: string;
  disabled?: boolean;
  isBookmarked: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  ref?: React.RefObject<HTMLButtonElement | null>;
  title?: string;
}

const Button = ({
  className,
  disabled,
  isBookmarked,
  onClick,
  ref,
  title,
}: ButtonProps) => (
  <button
    aria-label="Bookmark"
    className={twMerge(
      'flex items-center justify-center rounded-full border border-gray-300 px-6 py-4 font-inter text-xs leading-4 disabled:opacity-50',
      className,
    )}
    data-testid="bookmark-button"
    disabled={disabled}
    onClick={onClick}
    ref={ref}
    type="button"
  >
    <FontAwesomeIcon
      className="size-4"
      icon={isBookmarked ? faBookmarkSolid : faBookmark}
    />
    {title && <span className="ml-2">{title}</span>}
  </button>
);

function PianoUserBookmarkButton({
  containerClassName,
  metadata,
  resourceId,
  resourceType,
  showOnboarding,
  title,
}: PianoUserBookmarkButtonProps): React.ReactElement | null {
  const { initialized: pianoInitialized, user } = useAppSelector(
    (state) => state.piano,
  );
  const { siteId } = useAppSelector((state) => state.settings);
  const {
    bookmarkState: { initialised, isBookmarked, isLoading },
    createBookmark,
    ref,
    toggleBookmark,
  } = useBookmark(resourceType, resourceId, {
    ...metadata,
    site_id: siteId,
  });

  useEffect(() => {
    if (pianoInitialized && user && initialised && window.localStorage) {
      const value = window.localStorage.getItem(QUEUED_BOOKMARK_KEY);
      if (value) {
        const {
          resourceId: queuedResourceId,
          resourceType: queuedResourceType,
        } = JSON.parse(value) as QueuedBookmark;
        if (
          queuedResourceId === resourceId &&
          queuedResourceType === resourceType
        ) {
          window.localStorage.removeItem(QUEUED_BOOKMARK_KEY);
          createBookmark();
        }
      }
    }
  }, [
    pianoInitialized,
    user,
    initialised,
    resourceId,
    resourceType,
    createBookmark,
    isBookmarked,
  ]);

  const handleClick = useCallback(() => {
    toggleBookmark();
    setBookmarkDismissed();
  }, [toggleBookmark]);

  return (
    <OnboardingTooltip
      action="Add to My Saved List"
      // eslint-disable-next-line @stylistic/max-len
      description="Add this to your saved list and come back to them anytime."
      isReady={initialised && !isLoading && !isBookmarked && !!showOnboarding}
      localStorageKey={ONBOARDING_BOOKMARK_DISMISSED_KEY}
      trigger={
        <Button
          className={containerClassName}
          disabled={
            !initialised ||
            isLoading ||
            !pianoInitialized ||
            !user ||
            !user.valid
          }
          isBookmarked={isBookmarked}
          onClick={handleClick}
          ref={ref}
          title={title}
        />
      }
    />
  );
}

function AnonBookmarkButton({
  containerClassName,
  metadata,
  resourceId,
  resourceType,
  title,
}: BookmarkButtonProps) {
  const url = useWindowHref();
  const setQueuedBookmark = useCallback(() => {
    if (window.localStorage) {
      const value = JSON.stringify({
        metadata,
        resourceId,
        resourceType,
      });
      window.localStorage.setItem(QUEUED_BOOKMARK_KEY, value);
    }
  }, [metadata, resourceId, resourceType]);
  const onLogin = useCallback(() => {
    setQueuedBookmark();
    redirectToRegister(url, { referrer: 'bookmark' });
    setGtmDataLayer({
      data: {
        label: 'login',
        section: 'hover',
      },
      event: BOOKMARK_GTM_EVENT,
    });
  }, [setQueuedBookmark, url]);
  const onRegister = useCallback(() => {
    setQueuedBookmark();
    redirectToRegister(url, { referrer: 'bookmark' });
    setGtmDataLayer({
      data: {
        label: 'register',
        section: 'hover',
      },
      event: BOOKMARK_GTM_EVENT,
    });
  }, [setQueuedBookmark, url]);

  return (
    <VisitorPopover
      handleLogin={onLogin}
      handleRegister={onRegister}
      iconOnly={!title}
      trigger={
        <Button
          className={containerClassName}
          isBookmarked={false}
          title={title}
        />
      }
    />
  );
}

export default function BookmarkButton({
  containerClassName,
  metadata,
  resourceId,
  resourceType,
  showOnboarding = false,
  title,
}: ConditionalBookmarkButtonProps) {
  const { initialized: pianoInitialized, user } = useAppSelector(
    (state) => state.piano,
  );
  const userBookmarksFeatureEnabled = useAppSelector(
    (state) => state.features.userBookmarks.enabled,
  );
  const pianoFeature = useAppSelector((state) => state.features.piano);

  if (!userBookmarksFeatureEnabled || !pianoFeature.enabled) {
    return null;
  }

  const showAnonButton = pianoInitialized && !user;

  return showAnonButton ? (
    <AnonBookmarkButton
      containerClassName={containerClassName}
      metadata={metadata}
      resourceId={resourceId}
      resourceType={resourceType}
      title={title}
    />
  ) : (
    <PianoUserBookmarkButton
      containerClassName={containerClassName}
      metadata={metadata}
      resourceId={resourceId}
      resourceType={resourceType}
      showOnboarding={showOnboarding}
      title={title}
    />
  );
}
