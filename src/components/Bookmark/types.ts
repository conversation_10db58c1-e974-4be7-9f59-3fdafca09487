import type { KeysToCamel } from 'types/format';
import type {
  UserBookmark,
  UserBookmarkResourceType,
} from 'types/sepang-types/bookmark';

export type Bookmark = KeysToCamel<UserBookmark>;

export type QueuedBookmark = {
  metadata?: Pick<Bookmark, 'metadata'>;
  resourceId: string;
  resourceType: UserBookmarkResourceType;
};

export type ResourceItem = {
  resourceId: string;
  resourceType: string;
};

export interface BookmarkState {
  data: Bookmark | null;
  initialised: boolean;
  isBookmarked: boolean;
  isLoading: boolean;
}

export interface BookmarkContextType {
  register: (
    element: Element,
    resource: ResourceItem,
    initBookmark: (state: BookmarkState) => void,
  ) => void;
  subscribeToBookmark: (
    resourceType: string,
    resourceId: string,
    callback: (state: BookmarkState) => void,
  ) => void;
  unregister: (element: Element) => void;
  unsubscribeFromBookmark: (
    resourceType: string,
    resourceId: string,
    callback: (state: BookmarkState) => void,
  ) => void;
  updateBookmark: (
    resourceType: string,
    resourceId: string,
    action: 'create' | 'delete',
    metadata?: Record<string, unknown>,
  ) => Promise<void>;
}

export interface ResourceElementMap {
  initBookmark: (state: BookmarkState) => void;
  resource: ResourceItem;
}
