import { fireEvent, render, screen } from '@testing-library/react';

import VisitorPopover from './VisitorPopover';

describe('VisitorPopover', () => {
  const mockHandleLogin = jest.fn();
  const mockHandleRegister = jest.fn();
  const mockTrigger = <button type="button">Click me</button>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the trigger element', () => {
    render(
      <VisitorPopover
        handleLogin={mockHandleLogin}
        handleRegister={mockHandleRegister}
        trigger={mockTrigger}
      />,
    );

    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('shows popover content on hover', () => {
    render(
      <VisitorPopover
        handleLogin={mockHandleLogin}
        handleRegister={mockHandleRegister}
        trigger={mockTrigger}
      />,
    );

    const trigger = screen.getByText('Click me');
    fireEvent.mouseEnter(trigger);

    expect(screen.getByText('Login')).toBeInTheDocument();
    expect(screen.getByText('create a free account')).toBeInTheDocument();
    expect(screen.getByText('My Saved List')).toBeInTheDocument();
  });

  it('calls handleLogin when login link is clicked', () => {
    render(
      <VisitorPopover
        handleLogin={mockHandleLogin}
        handleRegister={mockHandleRegister}
        trigger={mockTrigger}
      />,
    );

    const trigger = screen.getByText('Click me');
    fireEvent.mouseEnter(trigger);

    const loginLink = screen.getByText('Login');
    fireEvent.click(loginLink);

    expect(mockHandleLogin).toHaveBeenCalledTimes(1);
  });

  it('calls handleRegister when register link is clicked', () => {
    render(
      <VisitorPopover
        handleLogin={mockHandleLogin}
        handleRegister={mockHandleRegister}
        trigger={mockTrigger}
      />,
    );

    const trigger = screen.getByText('Click me');
    fireEvent.mouseEnter(trigger);

    const registerLink = screen.getByText('create a free account');
    fireEvent.click(registerLink);

    expect(mockHandleRegister).toHaveBeenCalledTimes(1);
  });

  it('handles keyboard navigation for login', () => {
    render(
      <VisitorPopover
        handleLogin={mockHandleLogin}
        handleRegister={mockHandleRegister}
        trigger={mockTrigger}
      />,
    );

    const trigger = screen.getByText('Click me');
    fireEvent.mouseEnter(trigger);

    const loginLink = screen.getByText('Login');
    fireEvent.keyDown(loginLink, { key: 'Enter' });

    expect(mockHandleLogin).toHaveBeenCalledTimes(1);
  });

  it('handles keyboard navigation for register', () => {
    render(
      <VisitorPopover
        handleLogin={mockHandleLogin}
        handleRegister={mockHandleRegister}
        trigger={mockTrigger}
      />,
    );

    const trigger = screen.getByText('Click me');
    fireEvent.mouseEnter(trigger);

    const registerLink = screen.getByText('create a free account');
    fireEvent.keyDown(registerLink, { key: 'Enter' });

    expect(mockHandleRegister).toHaveBeenCalledTimes(1);
  });
});
