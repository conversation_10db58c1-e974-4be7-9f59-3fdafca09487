import { fireEvent, render, screen } from '@testing-library/react';

import OnboardingTooltip from 'components/Onboarding/Tooltip';

import { ONBOARDING_BOOKMARK_DISMISSED_KEY } from '../constants';

const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    clear: jest.fn(() => {
      store = {};
    }),
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('OnboardingTooltip', () => {
  beforeEach(() => {
    localStorageMock.clear();
    jest.clearAllMocks();
  });

  const mockTrigger = <button type="button">Test Trigger</button>;
  const action = 'Add to My Saved List';
  // eslint-disable-next-line @stylistic/max-len
  const description =
    'Add this to your saved list and come back to them anytime.';

  it('should match snapshot when visible', () => {
    const { container } = render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={ONBOARDING_BOOKMARK_DISMISSED_KEY}
        trigger={mockTrigger}
      />,
    );
    expect(container).toMatchSnapshot();
  });

  it('should not show tooltip when not ready', () => {
    const { container } = render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady={false}
        localStorageKey={ONBOARDING_BOOKMARK_DISMISSED_KEY}
        trigger={mockTrigger}
      />,
    );
    expect(container).toMatchSnapshot();
  });

  it('should not show tooltip when dismissed', () => {
    localStorageMock.getItem.mockReturnValue('true');
    const { container } = render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={ONBOARDING_BOOKMARK_DISMISSED_KEY}
        trigger={mockTrigger}
      />,
    );
    expect(container).toMatchSnapshot();
  });

  it('should show tooltip when ready and not dismissed', () => {
    localStorageMock.getItem.mockReturnValue(null);
    render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={ONBOARDING_BOOKMARK_DISMISSED_KEY}
        trigger={mockTrigger}
      />,
    );
    expect(screen.getByText('Add to My Saved List')).toBeInTheDocument();
  });

  it('should dismiss tooltip when dismiss button is clicked', () => {
    localStorageMock.getItem.mockReturnValue(null);
    render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={ONBOARDING_BOOKMARK_DISMISSED_KEY}
        trigger={mockTrigger}
      />,
    );

    const dismissButton = screen.getByText('Dismiss');
    fireEvent.click(dismissButton);

    expect(screen.queryByText('Add to My Saved List')).not.toBeInTheDocument();
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      ONBOARDING_BOOKMARK_DISMISSED_KEY,
      'true',
    );
  });

  // eslint-disable-next-line @stylistic/max-len
  it('should dismiss tooltip when dismiss button is pressed with Enter key', () => {
    localStorageMock.getItem.mockReturnValue(null);
    render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={ONBOARDING_BOOKMARK_DISMISSED_KEY}
        trigger={mockTrigger}
      />,
    );

    const dismissButton = screen.getByText('Dismiss');
    fireEvent.keyDown(dismissButton, { key: 'Enter' });

    expect(screen.queryByText('Add to My Saved List')).not.toBeInTheDocument();
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      ONBOARDING_BOOKMARK_DISMISSED_KEY,
      'true',
    );
  });
});
