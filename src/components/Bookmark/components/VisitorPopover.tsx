import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';

interface VisitorPopoverProps {
  handleLogin: () => void;
  handleRegister: () => void;
  iconOnly?: boolean;
  trigger: React.ReactNode;
}

export default function VisitorPopover({
  handleLogin,
  handleRegister,
  iconOnly = false,
  trigger,
}: VisitorPopoverProps) {
  const [isHovered, setIsHovered] = useState(false);
  const scrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    if (!isHovered) {
      return;
    }

    const onScroll = () => {
      window.removeEventListener('scroll', onScroll);
      setIsHovered(false);

      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      scrollTimeoutRef.current = setTimeout(() => {
        setIsHovered(false);
      }, 100);
    };

    window.addEventListener('scroll', onScroll);
    // eslint-disable-next-line consistent-return
    return () => {
      window.removeEventListener('scroll', onScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [isHovered]);

  return (
    <Popover className="relative">
      <div
        className="relative"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <PopoverButton as="div">{trigger}</PopoverButton>
        <div className="absolute inset-x-0 h-9" />
        <div
          className={clsx(
            'absolute top-[calc(100%+7px)] z-10 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white',
            {
              hidden: !isHovered,
              'left-4': iconOnly,
              'left-10': !iconOnly,
            },
          )}
        />

        <PopoverPanel
          anchor={{ gap: 17, to: 'bottom start' }}
          className={clsx(
            'z-0 flex h-auto w-48 rounded-md border-1 border-gray-200 bg-white px-4 py-2 transition duration-200 ease-out',
            {
              hidden: !isHovered,
            },
          )}
          static
          transition
        >
          <span className="self-start pb-1 font-inter text-sm text-gray-900">
            <span
              className="text-blue-600"
              onClick={handleLogin}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  handleLogin();
                }
              }}
              role="button"
              tabIndex={0}
            >
              Login
            </span>{' '}
            or{' '}
            <span
              className="text-blue-600"
              onClick={handleRegister}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  handleRegister();
                }
              }}
              role="button"
              tabIndex={0}
            >
              create a free account
            </span>{' '}
            to save this to{' '}
            <span className="font-semibold italic"> My Saved List</span>
          </span>
        </PopoverPanel>
      </div>
    </Popover>
  );
}
