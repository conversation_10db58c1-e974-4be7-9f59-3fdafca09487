// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`OnboardingTooltip should match snapshot when visible 1`] = `
<div>
  <div
    class="relative inline-block"
  >
    <button
      type="button"
    >
      Test Trigger
    </button>
    <div
      class="absolute left-10 top-[calc(100%+7px)] z-10 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white"
    />
    <div
      class="absolute top-[calc(100%+7px)] z-[5] mt-[9px] flex h-auto w-64 rounded-md border-1 border-gray-200 bg-white px-4 py-2 shadow-md transition duration-200 ease-out"
    >
      <div
        class="self-start pb-1 font-inter text-sm text-gray-900"
      >
        <div
          class="mb-2 text-base font-semibold text-gray-900"
        >
          Add to My Saved List
        </div>
        <div
          class="mb-4 text-sm font-normal text-gray-900"
        >
          Add this to your saved list and come back to them anytime.
        </div>
        <div
          class="flex items-center justify-between"
        >
          <div
            class="text-sm font-medium text-gray-900 underline"
            role="button"
            tabindex="0"
          >
            Dismiss
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OnboardingTooltip should not show tooltip when dismissed 1`] = `
<div>
  <div
    class="relative inline-block"
  >
    <button
      type="button"
    >
      Test Trigger
    </button>
  </div>
</div>
`;

exports[`OnboardingTooltip should not show tooltip when not ready 1`] = `
<div>
  <div
    class="relative inline-block"
  >
    <button
      type="button"
    >
      Test Trigger
    </button>
  </div>
</div>
`;
