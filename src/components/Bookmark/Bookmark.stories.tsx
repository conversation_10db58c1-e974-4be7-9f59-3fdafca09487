import { useEffect } from 'react';

import { addPianoReadyAction } from 'components/Piano/ready';
import { CompleteProfileEnrichment } from 'store/slices/features';
import { createStore } from 'store/store';
import { UserBookmarkResourceType } from 'types/sepang-types/bookmark';
import { TestWrapper } from 'util/jest';

import BookmarkProvider from './BookmarkProvider';

import BookmarkButton from './index';

import type { Meta } from '@storybook/nextjs-vite';
import type { PianoTinypassInterface } from 'types/Piano';

export default {
  component: BookmarkButton,
  tags: ['!autodocs'],
  title: 'User Bookmark button',
} as Meta<typeof BookmarkButton>;

const createTestStore = (pianoUserOverrides = {}) =>
  createStore((state) => ({
    ...state,
    features: {
      ...state.features,
      piano: {
        data: {
          aid: '839kEFOYYB',
          articlePaywallHeadingText: '',
          betaResourceId: '',
          completeProfileEnrichments: CompleteProfileEnrichment.NONE,
          ctaVariant: '',
          customFields: {},
          enterpriseSubscriptions: [],
          hasSocialScreen: false,
          header: '',
          hideArticleAnnualSavingsPill: false,
          hideSubscriberSignposts: false,
          isAbTesting: false,
          isFullwidthRecommendationContentEnabled: false,
          isPremiumRequest: false,
          isRecommendationContentEnabled: false,
          registrationOnly: false,
          siteId: '',
          subColour: '',
          subHeader: '',
          supportAuthServerPaywall: false,
          supportLoginApple: false,
          supportLoginFacebook: false,
          supportLoginGoogle: false,
          supportMonthlyAnnualPaywall: false,
          supportPremiumExtended: false,
          supportPremiumSubscription: false,
          supportPrintBundle: false,
        },
        enabled: true,
      },
      userBookmarks: { enabled: true },
    },
    piano: {
      ...state.piano,
      initialized: true,
      ...pianoUserOverrides,
    },
    racetracks: {
      ...state.racetracks,
      sepangUrl: 'https://sepang.racetracks.docker',
    },
  }));

const userLoggedInStore = createTestStore({
  user: {
    aud: 'aud1234',
    confirmed: true,
    email: '<EMAIL>',
    email_confirmation_required: false,
    exp: 1716153600,
    family_name: 'Doe',
    firstName: 'John',
    given_name: 'John',
    iat: 1716153600,
    iss: 'https://sepang.racetracks.docker',
    jti: 'jti1234',
    lastName: 'Doe',
    login_timestamp: '2024-05-15T12:00:00Z',
    passwordType: 'password',
    sub: 'sub1234',
    uid: 'PNI09jSo3rby9fb',
    valid: true,
  },
});
const anonymousUserStore = createTestStore();

export const UserLoggedIn = () => {
  useEffect(() => {
    window.tp = [];
    (window.tp as PianoTinypassInterface).aid = '839kEFOYYB';
    (window.tp as PianoTinypassInterface).pianoId = {
      getToken: () =>
        'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1EPYC4nQ15dobXZu93IXtWDTpMzHZs3FCYnbEmsd5_s',
      getUser: () => ({
        aud: 'aud1234',
        confirmed: true,
        email: '<EMAIL>',
        email_confirmation_required: false,
        exp: 1716153600,
        family_name: 'Doe',
        firstName: 'John',
        given_name: 'John',
        iat: 1716153600,
        iss: 'https://sepang.racetracks.docker',
        jti: 'jti1234',
        lastName: 'Doe',
        login_timestamp: '2024-05-15T12:00:00Z',
        passwordType: 'password',
        sub: 'sub1234',
        uid: '1234',
        valid: true,
      }),
      init: () => {},
      isUserValid: () => true,
      loadExtendedUser: () => {},
      loginByToken: () => Promise.resolve(),
      logout: () => Promise.resolve(),
      show: () => {},
    };
    addPianoReadyAction();
    window.tp?.forEach(([act, fn]) => {
      if (act === 'init') {
        fn();
      }
    });
  });
  return (
    <TestWrapper store={userLoggedInStore}>
      <BookmarkProvider>
        <BookmarkButton
          metadata={{
            // eslint-disable-next-line @stylistic/max-len
            url: 'https://www.canberratimes.com.au/story/8906641/doctors-error-at-canberra-hospital-led-to-womans-death-inquest/',
          }}
          resourceId="8906641"
          resourceType={UserBookmarkResourceType.STORY}
          title="Save"
        />
        <div className="h-[400px] overflow-hidden overflow-y-auto">
          <div className="mt-[800px] flex gap-4">
            <BookmarkButton
              metadata={{
                url: 'https://www.canberratimes.com.au/top-stories/',
              }}
              resourceId="845678"
              resourceType={UserBookmarkResourceType.STORY}
              title="Save"
            />
            <BookmarkButton
              metadata={{
                url: 'https://www.canberratimes.com.au/news/',
              }}
              resourceId="599234"
              resourceType={UserBookmarkResourceType.PAGE}
              title="Save Page"
            />
          </div>
          <div className="mt-[1000px] flex gap-4">
            <BookmarkButton
              metadata={{
                // eslint-disable-next-line @stylistic/max-len
                url: 'https://www.canberratimes.com.au/story/8910730/uber-driver-muhammad-cheema-found-guilty-of-indecently-assaulting-passenger/?cs=14329',
              }}
              resourceId="8910730"
              resourceType={UserBookmarkResourceType.STORY}
              title="Save"
            />
            <BookmarkButton
              metadata={{
                url: 'https://www.canberratimes.com.au/sport/',
              }}
              resourceId="8745533"
              resourceType={UserBookmarkResourceType.PAGE}
              title="Save Page"
            />
            <BookmarkButton
              metadata={{
                // eslint-disable-next-line @stylistic/max-len
                url: 'https://www.canberratimes.com.au/story/8906641/doctors-error-at-canberra-hospital-led-to-womans-death-inquest/',
              }}
              resourceId="8906641"
              resourceType={UserBookmarkResourceType.STORY}
              title="Save"
            />
          </div>
        </div>
      </BookmarkProvider>
    </TestWrapper>
  );
};

export const AnonymousUser = () => (
  <TestWrapper store={anonymousUserStore}>
    <BookmarkProvider>
      <BookmarkButton
        metadata={{
          // eslint-disable-next-line @stylistic/max-len
          url: 'https://www.canberratimes.com.au/story/8906641/doctors-error-at-canberra-hospital-led-to-womans-death-inquest/',
        }}
        resourceId="8906641"
        resourceType={UserBookmarkResourceType.STORY}
        title="Save"
      />
      <div className="h-[400px] overflow-hidden overflow-y-auto">
        <div className="mt-[800px] flex gap-4">
          <BookmarkButton
            metadata={{
              url: 'https://www.canberratimes.com.au/top-stories/',
            }}
            resourceId="845678"
            resourceType={UserBookmarkResourceType.STORY}
            title="Save"
          />
          <BookmarkButton
            metadata={{
              url: 'https://www.canberratimes.com.au/news/',
            }}
            resourceId="599234"
            resourceType={UserBookmarkResourceType.PAGE}
            title="Save Page"
          />
        </div>
        <div className="mt-[1000px] flex gap-4">
          <BookmarkButton
            metadata={{
              // eslint-disable-next-line @stylistic/max-len
              url: 'https://www.canberratimes.com.au/story/8910730/uber-driver-muhammad-cheema-found-guilty-of-indecently-assaulting-passenger/?cs=14329',
            }}
            resourceId="8910730"
            resourceType={UserBookmarkResourceType.STORY}
            title="Save"
          />
          <BookmarkButton
            metadata={{
              url: 'https://www.canberratimes.com.au/sport/',
            }}
            resourceId="8745533"
            resourceType={UserBookmarkResourceType.PAGE}
            title="Save Page"
          />
          <BookmarkButton
            metadata={{
              // eslint-disable-next-line @stylistic/max-len
              url: 'https://www.canberratimes.com.au/story/8906641/doctors-error-at-canberra-hospital-led-to-womans-death-inquest/',
            }}
            resourceId="8906641"
            resourceType={UserBookmarkResourceType.STORY}
            title="Save"
          />
        </div>
      </div>
    </BookmarkProvider>
  </TestWrapper>
);
