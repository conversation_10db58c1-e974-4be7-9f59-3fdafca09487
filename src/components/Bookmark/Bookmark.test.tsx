import { fireEvent, render, screen } from '@testing-library/react';

import { CompleteProfileEnrichment } from 'store/slices/features';
import { createStore } from 'store/store';
import { UserBookmarkResourceType } from 'types/sepang-types/bookmark';
import { redirectToRegister } from 'util/auth';
import { TestWrapper } from 'util/jest';

import { BookmarkContext, useBookmark } from './hooks';

import BookmarkButton from './index';

import type { BookmarkState } from './types';

const createMockStore = (overrides = {}) =>
  createStore((state) => ({
    ...state,
    features: {
      ...state.features,
      piano: {
        data: {
          aid: 'test-aid',
          articlePaywallHeadingText: '',
          betaResourceId: '',
          completeProfileEnrichments: CompleteProfileEnrichment.NONE,
          ctaVariant: '',
          customFields: {},
          enterpriseSubscriptions: [],
          hasSocialScreen: false,
          header: '',
          hideArticleAnnualSavingsPill: false,
          hideSubscriberSignposts: false,
          isAbTesting: false,
          isFullwidthRecommendationContentEnabled: false,
          isPremiumRequest: false,
          isRecommendationContentEnabled: false,
          registrationOnly: false,
          siteId: '',
          subColour: '',
          subHeader: '',
          supportAuthServerPaywall: false,
          supportLoginApple: false,
          supportLoginFacebook: false,
          supportLoginGoogle: false,
          supportMonthlyAnnualPaywall: false,
          supportPremiumExtended: false,
          supportPremiumSubscription: false,
          supportPrintBundle: false,
        },
        enabled: true,
      },
      userBookmarks: { enabled: true },
    },
    piano: {
      ...state.piano,
      initialized: true,
      user: {
        ...state.piano.user,
        aud: 'aud1234',
        confirmed: true,
        email: '<EMAIL>',
        email_confirmation_required: false,
        exp: 1716153600,
        family_name: 'Doe',
        firstName: 'John',
        given_name: 'John',
        iat: 1716153600,
        iss: 'https://sepang.racetracks.docker',
        jti: 'jti1234',
        lastName: 'Doe',
        login_timestamp: '2024-05-15T12:00:00Z',
        passwordType: 'password',
        sub: 'sub1234',
        uid: '1234',
        valid: true,
      },
    },
    racetracks: {
      ...state.racetracks,
      sepangUrl: 'https://sepang.racetracks.docker',
    },
    ...overrides,
  }));

jest.mock('./sepang', () => ({
  batchFetchBookmarks: jest.fn(),
  createBookmark: jest.fn(),
  deleteBookmark: jest.fn(),
  fetchBookmark: jest.fn(),
}));

jest.mock('util/auth', () => ({
  redirectToLogin: jest.fn(),
  redirectToRegister: jest.fn(),
}));

const mockContextValue = {
  register: jest.fn(),
  subscribeToBookmark: jest.fn(),
  unregister: jest.fn(),
  unsubscribeFromBookmark: jest.fn(),
  updateBookmark: jest.fn(),
};

interface MockBookmarkHook {
  bookmarkState: {
    initialised: boolean;
    isBookmarked: boolean;
    isLoading: boolean;
  };
  createBookmark: jest.Mock;
  ref: { current: null };
  toggleBookmark: jest.Mock;
}

const mockUseBookmark = (overrides = {}) => ({
  bookmarkState: {
    data: null,
    initialised: true,
    isBookmarked: false,
    isLoading: false,
    ...overrides,
  } as BookmarkState,
  createBookmark: jest.fn(),
  ref: { current: null },
  toggleBookmark: jest.fn(),
});

// eslint-disable-next-line @typescript-eslint/no-unsafe-return
jest.mock('./hooks', () => ({
  ...jest.requireActual('./hooks'),
  useBookmark: jest.fn(() => mockUseBookmark()),
}));

describe('BookmarkButton', () => {
  const defaultProps = {
    resourceId: 'test-123',
    resourceType: UserBookmarkResourceType.STORY,
    showOnboarding: false,
    title: 'Test Bookmark',
  };

  const renderWithMockContext = (props = {}, storeOverrides = {}) => {
    const mergedProps = { ...defaultProps, ...props };
    return render(
      <TestWrapper store={createMockStore(storeOverrides)}>
        <BookmarkContext.Provider value={mockContextValue}>
          <BookmarkButton
            resourceId={mergedProps.resourceId}
            resourceType={mergedProps.resourceType}
            showOnboarding={mergedProps.showOnboarding}
            title={mergedProps.title}
          />
        </BookmarkContext.Provider>
      </TestWrapper>,
    );
  };

  describe('Component Rendering', () => {
    it('renders bookmark button correctly', () => {
      renderWithMockContext();
      const button = screen.getByTestId('bookmark-button');
      expect(button).toBeInTheDocument();
    });

    it('matches snapshot for authenticated user', () => {
      const { container } = renderWithMockContext({ showOnboarding: true });
      expect(container).toMatchSnapshot();
    });

    it('matches snapshot for anonymous user', () => {
      const { container } = renderWithMockContext(
        {},
        {
          piano: {
            initialized: true,
            user: null,
          },
        },
      );
      expect(container).toMatchSnapshot();
    });
  });

  describe('Piano User States', () => {
    it('does not show onboarding tooltip when showOnboarding is false', () => {
      const mockToggleBookmark = jest.fn();
      const mockUseBookmarkHook = {
        ...mockUseBookmark({
          isBookmarked: false,
        }),
        toggleBookmark: mockToggleBookmark,
      };
      (useBookmark as jest.Mock).mockReturnValue(mockUseBookmarkHook);

      renderWithMockContext({ showOnboarding: false });

      const dismissElement = screen.queryByText('Dismiss');
      expect(dismissElement).not.toBeInTheDocument();
    });

    it('shows onboarding tooltip when showOnboarding is true', () => {
      const mockToggleBookmark = jest.fn();
      const mockUseBookmarkHook = {
        ...mockUseBookmark({
          isBookmarked: false,
        }),
        toggleBookmark: mockToggleBookmark,
      };
      (useBookmark as jest.Mock).mockReturnValue(mockUseBookmarkHook);

      renderWithMockContext({ showOnboarding: true });

      const dismissElement = screen.getByText('Dismiss');
      expect(dismissElement).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('disables button when loading', () => {
      const mockUseBookmarkHook = mockUseBookmark({ isLoading: true });
      (useBookmark as jest.Mock).mockReturnValue(mockUseBookmarkHook);

      renderWithMockContext();
      const button = screen.getByTestId('bookmark-button');
      expect(button).toBeDisabled();
    });

    it('disables button when not initialized', () => {
      const mockUseBookmarkHook = mockUseBookmark({ initialised: false });
      (useBookmark as jest.Mock).mockReturnValue(mockUseBookmarkHook);

      renderWithMockContext();
      const button = screen.getByTestId('bookmark-button');
      expect(button).toBeDisabled();
    });
  });

  describe('Bookmark States', () => {
    it('shows bookmarked state correctly', () => {
      const mockUseBookmarkHook = mockUseBookmark({ isBookmarked: true });
      jest.mock('./hooks', () => ({
        useBookmark: (): MockBookmarkHook => mockUseBookmarkHook,
      }));

      renderWithMockContext();
      const button = screen.getByTestId('bookmark-button');
      expect(button).toHaveClass('border-gray-300');
    });

    it('handles bookmark toggle correctly', () => {
      const mockToggleBookmark = jest.fn();
      const mockUseBookmarkHook = {
        ...mockUseBookmark({
          isBookmarked: false,
        }),
        toggleBookmark: mockToggleBookmark,
      };
      (useBookmark as jest.Mock).mockReturnValue(mockUseBookmarkHook);

      renderWithMockContext({ showOnboarding: true });

      // Remove onboarding tooltip
      const dismissElement = screen.getByText('Dismiss');
      fireEvent.click(dismissElement);

      const button = screen.getByTestId('bookmark-button');
      fireEvent.click(button);

      expect(mockToggleBookmark).toHaveBeenCalled();
    });
  });

  describe('Anonymous User States', () => {
    it('renders visitor popover for anonymous users', () => {
      renderWithMockContext(
        {},
        {
          piano: {
            initialized: true,
            user: null,
          },
        },
      );

      const button = screen.getByTestId('bookmark-button');
      fireEvent.click(button);

      expect(screen.getByText('Login')).toBeInTheDocument();
      expect(screen.getByText('create a free account')).toBeInTheDocument();
      expect(screen.getByText('My Saved List')).toBeInTheDocument();
    });

    it('handles login redirect for anonymous users', () => {
      renderWithMockContext(
        {},
        {
          piano: {
            initialized: true,
            user: null,
          },
        },
      );

      const button = screen.getByTestId('bookmark-button');
      fireEvent.mouseEnter(button);

      const loginBtn = screen.getByText('Login');
      fireEvent.click(loginBtn);

      expect(redirectToRegister).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({ referrer: 'bookmark' }),
      );
    });

    it('handles register redirect for anonymous users', () => {
      renderWithMockContext(
        {},
        {
          piano: {
            initialized: true,
            user: null,
          },
        },
      );

      const button = screen.getByTestId('bookmark-button');
      fireEvent.mouseEnter(button);

      const registerBtn = screen.getByText('create a free account');
      fireEvent.click(registerBtn);

      expect(redirectToRegister).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({ referrer: 'bookmark' }),
      );
    });
  });

  describe('Feature Flags', () => {
    it('does not render when userBookmarks feature is disabled', () => {
      renderWithMockContext(
        {},
        {
          features: {
            piano: { enabled: true },
            userBookmarks: { enabled: false },
          },
        },
      );

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('does not render when piano feature is disabled', () => {
      renderWithMockContext(
        {},
        {
          features: {
            piano: { enabled: false },
            userBookmarks: { enabled: true },
          },
        },
      );

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });
  });
});
