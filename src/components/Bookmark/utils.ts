import { ONBOARDING_BOOKMARK_DISMISSED_KEY } from './constants';

import type { Bookmark, BookmarkState } from './types';

export function getBookmarkKey(resourceType: string, resourceId: string) {
  return `${resourceType}:${resourceId}`;
}

export function createBookmarkState(data: Bookmark | null): BookmarkState {
  return {
    data,
    initialised: true,
    isBookmarked: !!data,
    isLoading: false,
  };
}

export function setBookmarkDismissed() {
  window.localStorage.setItem(ONBOARDING_BOOKMARK_DISMISSED_KEY, 'true');
}
