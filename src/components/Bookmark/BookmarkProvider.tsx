import { type ReactNode, useCallback, useMemo } from 'react';

import Link from 'themes/autumn/components/generic/Link';
import { setGtmDataLayer } from 'util/gtm';
import { subToast } from 'util/newsletter';

import { BOOKMARK_GTM_EVENT } from './constants';
import {
  BookmarkContext,
  useBookmarkObserver,
  useBookmarkStates,
} from './hooks';
import { createBookmark, deleteBookmark } from './sepang';
import { getBookmarkKey } from './utils';

import type { BookmarkState, ResourceElementMap, ResourceItem } from './types';

interface BookmarkProviderProps {
  children: ReactNode;
}

function bookmarkSuccessToast() {
  return subToast(
    <div className="flex flex-row items-center gap-x-3">
      <svg className="shrink-0" fill="none" height="20" width="20">
        <path
          clipRule="evenodd"
          d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.707-9.293a1 1 0 0 0-1.414-1.414L9 10.586 7.707 9.293a1 1 0 0 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0l4-4Z"
          fill="#34D399"
          fillRule="evenodd"
        />
      </svg>
      <span>
        Saved. View in{' '}
        <Link
          className="font-semibold underline"
          href="/saved/"
          noStyle
          onClick={() => {
            setGtmDataLayer({
              data: {
                action: 'click',
                section: 'pop_up',
              },
              event: BOOKMARK_GTM_EVENT,
            });
          }}
        >
          My Saved List
        </Link>
      </span>
    </div>,
    {
      closeButtonStyle:
        'text-green-500 hover:text-green-600 focus:ring-green-600',
      containerStyle: 'bg-green-50',
      textColor: 'text-green-800',
    },
  );
}

export default function BookmarkProvider({ children }: BookmarkProviderProps) {
  const elementToResourceMap = useMemo(
    () => new WeakMap<Element, ResourceElementMap>(),
    [],
  );
  const { bookmarkStates, subscribers, updateBookmarkState } =
    useBookmarkStates();
  const { observer, pendingRegistrations } = useBookmarkObserver(
    elementToResourceMap,
    updateBookmarkState,
    bookmarkStates,
  );

  const register = useCallback(
    (
      element: Element,
      resource: ResourceItem,
      initBookmark: (state: BookmarkState) => void,
    ) => {
      if (!element) return;

      if (observer.current) {
        elementToResourceMap.set(element, { initBookmark, resource });
        observer.current.observe(element);
      } else {
        pendingRegistrations.current.push({ element, initBookmark, resource });
      }
    },
    [elementToResourceMap, observer, pendingRegistrations],
  );

  const unregister = useCallback(
    (element: Element) => {
      if (element && observer.current) {
        observer.current.unobserve(element);
        elementToResourceMap.delete(element);
      }
    },
    [elementToResourceMap, observer],
  );

  const updateBookmark = useCallback(
    async (
      resourceType: string,
      resourceId: string,
      action: 'create' | 'delete',
      metadata?: Record<string, unknown>,
    ) => {
      const key = getBookmarkKey(resourceType, resourceId);
      updateBookmarkState(resourceType, resourceId, {
        ...bookmarkStates.current.get(key)!,
        isLoading: true,
      });

      if (action === 'create') {
        const response = await createBookmark(
          resourceType,
          resourceId,
          metadata,
        );
        if (response.success) {
          updateBookmarkState(resourceType, resourceId, {
            data: response.data,
            initialised: true,
            isBookmarked: true,
            isLoading: false,
          });
          bookmarkSuccessToast();
          setGtmDataLayer({
            data: {
              action: 'click',
              label: 'save',
            },
            event: BOOKMARK_GTM_EVENT,
          });
        }
      } else {
        const response = await deleteBookmark(resourceType, resourceId);
        if (response) {
          updateBookmarkState(resourceType, resourceId, {
            data: null,
            initialised: true,
            isBookmarked: false,
            isLoading: false,
          });
          setGtmDataLayer({
            data: {
              action: 'click',
              label: 'remove',
            },
            event: BOOKMARK_GTM_EVENT,
          });
        }
      }
    },
    [updateBookmarkState, bookmarkStates],
  );

  const subscribeToBookmark = useCallback(
    (
      resourceType: string,
      resourceId: string,
      callback: (state: BookmarkState) => void,
    ) => {
      const key = getBookmarkKey(resourceType, resourceId);
      if (!subscribers.current.has(key)) {
        subscribers.current.set(key, new Set());
      }
      subscribers.current.get(key)?.add(callback);

      const currentState = bookmarkStates.current.get(key);
      if (currentState) {
        callback(currentState);
      }
    },
    [bookmarkStates, subscribers],
  );

  const unsubscribeFromBookmark = useCallback(
    (
      resourceType: string,
      resourceId: string,
      callback: (state: BookmarkState) => void,
    ) => {
      const key = getBookmarkKey(resourceType, resourceId);
      subscribers.current.get(key)?.delete(callback);
    },
    [subscribers],
  );

  const contextValue = useMemo(
    () => ({
      register,
      subscribeToBookmark,
      unregister,
      unsubscribeFromBookmark,
      updateBookmark,
    }),
    [
      register,
      unregister,
      subscribeToBookmark,
      unsubscribeFromBookmark,
      updateBookmark,
    ],
  );

  return (
    <BookmarkContext.Provider value={contextValue}>
      {children}
    </BookmarkContext.Provider>
  );
}
