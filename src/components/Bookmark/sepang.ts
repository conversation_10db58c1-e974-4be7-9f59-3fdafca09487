import { getPianoReady } from 'components/Piano/ready';
import { camel<PERSON>eysToSnake, keysToCamel } from 'util/string';

import type { ResourceItem } from './types';
import type { KeysToCamel } from 'types/format';
import type { GetUserBookmarksRequest } from 'types/sepang-types/request';
import type {
  ApiErrorResponse,
  BatchFetchUserBookmarksResponse,
  CreateUserBookmarkResponse,
  DeleteUserBookmarkResponse,
  GetUserBookmarkResponse,
  GetUserBookmarksResponse,
} from 'types/sepang-types/response';

async function getPianoUser() {
  const store = window.getStore();
  const userId = store.getState().piano.user?.uid;
  const pianoFeature = store.getState().features.piano;
  const token = (await getPianoReady()).pianoId.getToken();
  const aid = pianoFeature.enabled ? pianoFeature.data?.aid : '';
  const headers = {
    Authorization: `Bearer ${token}`,
    'X-Authorization-Aid': aid,
    'X-Authorization-Tenant': 'piano',
  };

  return {
    headers,
    userId,
  };
}

export async function createBookmark(
  resource_type: string,
  resource_id: string,
  metadata?: Record<string, unknown>,
): Promise<KeysToCamel<CreateUserBookmarkResponse> | ApiErrorResponse> {
  const store = window.getStore();
  const host = store.getState().racetracks.sepangUrl;
  const { domain } = store.getState().conf;
  const { headers, userId } = await getPianoUser();

  if (!userId) {
    return Promise.reject(new Error('No UserId'));
  }

  const response = await fetch(`${host}/api/v1/users/${userId}/bookmarks`, {
    body: JSON.stringify({
      domain,
      metadata,
      resource_id,
      resource_type,
    }),
    headers,
    method: 'POST',
  });

  if (!response.ok) {
    return (await response.json()) as Promise<ApiErrorResponse>;
  }

  return keysToCamel(await response.json()) as Promise<
    KeysToCamel<CreateUserBookmarkResponse>
  >;
}

export async function deleteBookmark(
  resource_type: string,
  resource_id: string,
): Promise<KeysToCamel<DeleteUserBookmarkResponse> | ApiErrorResponse> {
  const store = window.getStore();
  const host = store.getState().racetracks.sepangUrl;
  const { headers, userId } = await getPianoUser();

  if (!userId) {
    return Promise.reject(new Error('No UserId'));
  }

  const response = await fetch(
    `${host}/api/v1/users/${userId}/bookmarks/${resource_type}/${resource_id}`,
    {
      headers,
      method: 'DELETE',
    },
  );

  if (!response.ok) {
    return response.json() as Promise<ApiErrorResponse>;
  }

  return Promise.resolve(
    true as unknown as KeysToCamel<DeleteUserBookmarkResponse>,
  );
}

export async function fetchBookmarks(
  params: GetUserBookmarksRequest = {},
): Promise<KeysToCamel<GetUserBookmarksResponse> | ApiErrorResponse> {
  const store = window.getStore();
  const host = store.getState().racetracks.sepangUrl;
  const { headers, userId } = await getPianoUser();
  const queryParams = new URLSearchParams({ ...params });

  if (!userId) {
    return Promise.reject(new Error('No UserId'));
  }

  const response = await fetch(
    `${host}/api/v1/users/${userId}/bookmarks?${queryParams.toString()}`,
    {
      headers,
    },
  );

  if (!response.ok) {
    return response.json() as Promise<ApiErrorResponse>;
  }

  return keysToCamel(await response.json()) as Promise<
    KeysToCamel<GetUserBookmarksResponse>
  >;
}

export async function batchFetchBookmarks(
  items: ResourceItem[],
): Promise<KeysToCamel<BatchFetchUserBookmarksResponse> | ApiErrorResponse> {
  const store = window.getStore();
  const host = store.getState().racetracks.sepangUrl;
  const { headers, userId } = await getPianoUser();

  if (!userId) {
    return Promise.reject(new Error('No UserId'));
  }

  const response = await fetch(
    `${host}/api/v1/users/${userId}/bookmarks/batch-fetch`,
    {
      body: JSON.stringify({ items: items.map((t) => camelKeysToSnake(t)) }),
      headers,
      method: 'POST',
    },
  );

  if (!response.ok) {
    return response.json() as Promise<ApiErrorResponse>;
  }

  return keysToCamel(await response.json()) as Promise<
    KeysToCamel<BatchFetchUserBookmarksResponse>
  >;
}

export async function fetchBookmark(
  resourceType: string,
  resourceId: string,
): Promise<KeysToCamel<GetUserBookmarkResponse> | ApiErrorResponse> {
  const store = window.getStore();
  const host = store.getState().racetracks.sepangUrl;
  const { headers, userId } = await getPianoUser();

  if (!userId) {
    return Promise.reject(new Error('No UserId'));
  }

  const response = await fetch(
    `${host}/api/v1/users/${userId}/bookmarks/${resourceType}/${resourceId}`,
    {
      headers,
    },
  );

  if (!response.ok) {
    return response.json() as Promise<ApiErrorResponse>;
  }

  return keysToCamel(await response.json()) as Promise<
    KeysToCamel<GetUserBookmarkResponse>
  >;
}
