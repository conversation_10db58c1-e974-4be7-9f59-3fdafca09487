import { getPianoReady } from 'components/Piano/ready';
import { UserBookmarkResourceType } from 'types/sepang-types/bookmark';
import { ApiErrorCode } from 'types/sepang-types/response';

import {
  batchFetchBookmarks,
  createBookmark,
  deleteBookmark,
  fetchBookmark,
  fetchBookmarks,
} from './sepang';

import type { ResourceItem } from './types';
import type { Store } from 'redux';
import type {
  BatchFetchUserBookmarksResponse,
  CreateUserBookmarkResponse,
  GetUserBookmarkResponse,
  GetUserBookmarksResponse,
} from 'types/sepang-types/response';

interface MockStore extends Partial<Store> {
  dispatch: jest.Mock;
  getState: jest.Mock;
}

interface FetchCall {
  options: {
    body?: string;
    headers: Record<string, string>;
    method?: string;
  };
  url: string;
}

const mockStore: MockStore = {
  dispatch: jest.fn(),
  getState: jest.fn(),
};

jest.mock('components/Piano/ready', () => ({
  getPianoReady: jest.fn(),
}));

describe('Bookmark API Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    window.getStore = () => mockStore as Store;
    mockStore.getState.mockReturnValue({
      conf: {
        domain: 'test-domain',
      },
      features: {
        piano: {
          data: { aid: 'test-aid' },
          enabled: true,
        },
      },
      piano: {
        user: { uid: 'test-user-id' },
      },
      racetracks: {
        sepangUrl: 'https://sepang.example.com',
      },
    });

    (getPianoReady as jest.Mock).mockResolvedValue({
      pianoId: {
        getToken: () => 'test-token',
      },
    });

    global.fetch = jest.fn();
  });

  describe('createBookmark', () => {
    it('should create a bookmark successfully', async () => {
      const mockResponse: CreateUserBookmarkResponse = {
        data: {
          added_on: '2024-03-20',
          domain: 'test-domain',
          metadata: { title: 'Test' },
          resource_id: '123',
          resource_type: UserBookmarkResourceType.STORY,
          resource_type_and_id: 'story:123',
          user_id: 'test-user-id',
        },
        success: true,
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockResponse),
        ok: true,
      });

      const result = await createBookmark('story', '123', { title: 'Test' });

      expect(result).toEqual({
        data: {
          addedOn: '2024-03-20',
          domain: 'test-domain',
          metadata: { title: 'Test' },
          resourceId: '123',
          resourceType: UserBookmarkResourceType.STORY,
          resourceTypeAndId: 'story:123',
          userId: 'test-user-id',
        },
        success: true,
      });

      const expectedFetchCall: FetchCall = {
        options: {
          body: expect.any(String) as string,
          headers: {
            Authorization: 'Bearer test-token',
            'X-Authorization-Aid': 'test-aid',
            'X-Authorization-Tenant': 'piano',
          },
          method: 'POST',
        },
        url: 'https://sepang.example.com/api/v1/users/test-user-id/bookmarks',
      };

      expect(global.fetch).toHaveBeenCalledWith(
        expectedFetchCall.url,
        expectedFetchCall.options,
      );

      // Verify the body contents separately
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0] as [
        string,
        { body: string },
      ];
      const requestBody = JSON.parse(fetchCall[1].body) as {
        domain: string;
        metadata: Record<string, unknown>;
        resource_id: string;
        resource_type: UserBookmarkResourceType;
      };
      expect(requestBody).toEqual({
        domain: 'test-domain',
        metadata: { title: 'Test' },
        resource_id: '123',
        resource_type: UserBookmarkResourceType.STORY,
      });
    });

    it('should handle API errors', async () => {
      const mockError = {
        code: ApiErrorCode.VALIDATION_ERROR,
        details: [],
        message: 'Invalid request',
        success: false,
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockError),
        ok: false,
      });

      const result = await createBookmark('story', '123');
      expect(result).toEqual(mockError);
    });

    it('should reject when no userId is available', async () => {
      mockStore.getState.mockReturnValue({
        conf: {
          domain: 'test-domain',
        },
        features: {
          piano: {
            data: { aid: 'test-aid' },
            enabled: true,
          },
        },
        piano: { user: null },
        racetracks: {
          sepangUrl: 'https://sepang.example.com',
        },
      });

      await expect(createBookmark('story', '123')).rejects.toThrow(
        'No UserId',
      );
    });
  });

  describe('deleteBookmark', () => {
    it('should delete a bookmark successfully', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
      });

      const result = await deleteBookmark('story', '123');
      expect(result).toBe(true);

      const expectedFetchCall: FetchCall = {
        options: {
          headers: {
            Authorization: 'Bearer test-token',
            'X-Authorization-Aid': 'test-aid',
            'X-Authorization-Tenant': 'piano',
          },
          method: 'DELETE',
        },
        url:
          'https://sepang.example.com/api/v1/users/test-user-id/bookmarks/' +
          'story/123',
      };

      expect(global.fetch).toHaveBeenCalledWith(
        expectedFetchCall.url,
        expectedFetchCall.options,
      );
    });

    it('should handle API errors', async () => {
      const mockError = {
        code: ApiErrorCode.NOT_FOUND,
        details: [],
        message: 'Not found',
        success: false,
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockError),
        ok: false,
      });

      const result = await deleteBookmark('story', '123');
      expect(result).toEqual(mockError);
    });
  });

  describe('fetchBookmarks', () => {
    it('should fetch bookmarks successfully', async () => {
      const mockResponse: GetUserBookmarksResponse = {
        data: [
          {
            added_on: '2024-03-20',
            domain: 'test-domain',
            metadata: { title: 'Test' },
            resource_id: '123',
            resource_type: UserBookmarkResourceType.STORY,
            resource_type_and_id: 'story:123',
            user_id: 'test-user-id',
          },
        ],
        meta: {
          nextToken: null,
        },
        success: true,
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockResponse),
        ok: true,
      });

      const result = await fetchBookmarks({ limit: '10' });
      expect(result).toEqual({
        data: [
          {
            addedOn: '2024-03-20',
            domain: 'test-domain',
            metadata: { title: 'Test' },
            resourceId: '123',
            resourceType: UserBookmarkResourceType.STORY,
            resourceTypeAndId: 'story:123',
            userId: 'test-user-id',
          },
        ],
        meta: {
          nextToken: null,
        },
        success: true,
      });

      const expectedFetchCall: FetchCall = {
        options: {
          headers: {
            Authorization: 'Bearer test-token',
            'X-Authorization-Aid': 'test-aid',
            'X-Authorization-Tenant': 'piano',
          },
        },
        url:
          'https://sepang.example.com/api/v1/users/test-user-id/' +
          'bookmarks?limit=10',
      };

      expect(global.fetch).toHaveBeenCalledWith(
        expectedFetchCall.url,
        expectedFetchCall.options,
      );
    });

    it('should handle API errors', async () => {
      const mockError = {
        code: ApiErrorCode.INTERNAL_SERVER_ERROR,
        details: [],
        message: 'Server error',
        success: false,
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockError),
        ok: false,
      });

      const result = await fetchBookmarks();
      expect(result).toEqual(mockError);
    });
  });

  describe('batchFetchBookmarks', () => {
    it('should batch fetch bookmarks successfully', async () => {
      const mockResponse: BatchFetchUserBookmarksResponse = {
        data: [
          {
            added_on: '2024-03-20',
            domain: 'test-domain',
            metadata: { title: 'Test' },
            resource_id: '123',
            resource_type: UserBookmarkResourceType.STORY,
            resource_type_and_id: 'story:123',
            user_id: 'test-user-id',
          },
        ],
        success: true,
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockResponse),
        ok: true,
      });

      const items: ResourceItem[] = [
        { resourceId: '123', resourceType: UserBookmarkResourceType.STORY },
      ];
      const result = await batchFetchBookmarks(items);
      expect(result).toEqual({
        data: [
          {
            addedOn: '2024-03-20',
            domain: 'test-domain',
            metadata: { title: 'Test' },
            resourceId: '123',
            resourceType: UserBookmarkResourceType.STORY,
            resourceTypeAndId: 'story:123',
            userId: 'test-user-id',
          },
        ],
        success: true,
      });

      const expectedFetchCall: FetchCall = {
        options: {
          body: JSON.stringify({
            items: [
              {
                resource_id: '123',
                resource_type: UserBookmarkResourceType.STORY,
              },
            ],
          }),
          headers: {
            Authorization: 'Bearer test-token',
            'X-Authorization-Aid': 'test-aid',
            'X-Authorization-Tenant': 'piano',
          },
          method: 'POST',
        },
        url:
          'https://sepang.example.com/api/v1/users/test-user-id/' +
          'bookmarks/batch-fetch',
      };

      expect(global.fetch).toHaveBeenCalledWith(
        expectedFetchCall.url,
        expectedFetchCall.options,
      );
    });
  });

  describe('fetchBookmark', () => {
    it('should fetch a single bookmark successfully', async () => {
      const mockResponse: GetUserBookmarkResponse = {
        data: {
          added_on: '2024-03-20',
          domain: 'test-domain',
          metadata: { title: 'Test' },
          resource_id: '123',
          resource_type: UserBookmarkResourceType.STORY,
          resource_type_and_id: 'story:123',
          user_id: 'test-user-id',
        },
        success: true,
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockResponse),
        ok: true,
      });

      const result = await fetchBookmark('story', '123');
      expect(result).toEqual({
        data: {
          addedOn: '2024-03-20',
          domain: 'test-domain',
          metadata: { title: 'Test' },
          resourceId: '123',
          resourceType: UserBookmarkResourceType.STORY,
          resourceTypeAndId: 'story:123',
          userId: 'test-user-id',
        },
        success: true,
      });

      const expectedFetchCall: FetchCall = {
        options: {
          headers: {
            Authorization: 'Bearer test-token',
            'X-Authorization-Aid': 'test-aid',
            'X-Authorization-Tenant': 'piano',
          },
        },
        url:
          'https://sepang.example.com/api/v1/users/test-user-id/' +
          'bookmarks/story/123',
      };

      expect(global.fetch).toHaveBeenCalledWith(
        expectedFetchCall.url,
        expectedFetchCall.options,
      );
    });

    it('should handle API errors', async () => {
      const mockError = {
        code: ApiErrorCode.NOT_FOUND,
        details: [],
        message: 'Not found',
        success: false,
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        json: () => Promise.resolve(mockError),
        ok: false,
      });

      const result = await fetchBookmark('story', '123');
      expect(result).toEqual(mockError);
    });
  });
});
