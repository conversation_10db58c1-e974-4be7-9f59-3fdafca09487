import { act, renderHook } from '@testing-library/react';

import { createStore } from 'store/store';
import { UserBookmarkResourceType } from 'types/sepang-types/bookmark';
import { TestWrapper } from 'util/jest';

import BookmarkProvider from './BookmarkProvider';
import {
  BookmarkContext,
  useBookmark,
  useBookmarkObserver,
  useBookmarkStates,
} from './hooks';
import { fetchBookmark } from './sepang';

import type { BookmarkState, ResourceElementMap } from './types';
import type { UserBookmark } from 'types/sepang-types/bookmark';

jest.mock('./sepang', () => ({
  batchFetchBookmarks: jest.fn(),
  createBookmark: jest.fn(),
  deleteBookmark: jest.fn(),
  fetchBookmark: jest.fn(),
  fetchBookmarks: jest.fn(),
}));

const store = createStore((state) => ({
  ...state,
  features: {
    ...state.features,
    userBookmarks: { enabled: true },
  },
  piano: {
    ...state.piano,
    initialized: true,
    user: {
      ...state.piano.user,
      aud: 'test-aud',
      confirmed: true,
      email: '<EMAIL>',
      email_confirmation_required: false,
      exp: **********,
      family_name: 'Doe',
      firstName: 'John',
      given_name: 'John',
      iat: **********,
      iss: 'test-iss',
      jti: 'test-jti',
      lastName: 'Doe',
      login_timestamp: '2024-03-20T12:00:00Z',
      sub: 'test-sub',
      uid: '123',
      valid: true,
    },
  },
}));

describe('useBookmark', () => {
  const mockResource = {
    resourceId: 'test-123',
    resourceType: UserBookmarkResourceType.STORY,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('initializes with default state', () => {
    const { result } = renderHook(
      () => useBookmark(mockResource.resourceType, mockResource.resourceId),
      {
        wrapper: ({ children }) => (
          <TestWrapper store={store}>
            <BookmarkProvider>{children}</BookmarkProvider>
          </TestWrapper>
        ),
      },
    );

    expect(result.current.bookmarkState).toEqual({
      data: null,
      initialised: false,
      isBookmarked: false,
      isLoading: false,
    });
  });

  it('handles bookmark toggle', () => {
    const mockContext = {
      register: jest.fn(),
      subscribeToBookmark: jest.fn(),
      unregister: jest.fn(),
      unsubscribeFromBookmark: jest.fn(),
      updateBookmark: jest.fn().mockResolvedValue(undefined),
    };

    const { result } = renderHook(
      () =>
        useBookmark(mockResource.resourceType, mockResource.resourceId, {}),
      {
        wrapper: ({ children }) => (
          <TestWrapper store={store}>
            <BookmarkContext.Provider value={mockContext}>
              {children}
            </BookmarkContext.Provider>
          </TestWrapper>
        ),
      },
    );

    act(() => {
      result.current.toggleBookmark();
    });

    expect(mockContext.updateBookmark).toHaveBeenCalledWith(
      mockResource.resourceType,
      mockResource.resourceId,
      'create',
      {},
    );
  });
});

describe('useBookmarkStates', () => {
  it('initializes with empty state', () => {
    const { result } = renderHook(() => useBookmarkStates());

    expect(result.current.bookmarkStates.current).toBeInstanceOf(Map);
    expect(result.current.subscribers.current).toBeInstanceOf(Map);
    expect(typeof result.current.updateBookmarkState).toBe('function');
  });

  it('updates bookmark state correctly', () => {
    const { result } = renderHook(() => useBookmarkStates());
    const mockState: BookmarkState = {
      data: {
        addedOn: '2024-03-20T12:00:00Z',
        domain: 'example.com',
        resourceId: 'test-123',
        resourceType: UserBookmarkResourceType.STORY,
        resourceTypeAndId: 'story:test-123',
        userId: 'user-123',
      },
      initialised: true,
      isBookmarked: true,
      isLoading: false,
    };

    act(() => {
      result.current.updateBookmarkState('story', 'test-123', mockState);
    });

    expect(
      result.current.bookmarkStates.current.get('story:test-123'),
    ).toEqual(mockState);
  });
});

describe('useBookmarkObserver', () => {
  const mockElementToResourceMap = new WeakMap<Element, ResourceElementMap>();
  const mockUpdateBookmarkState = jest.fn();
  const mockBookmarkStates = { current: new Map() };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // eslint-disable-next-line jest/no-disabled-tests
  it('initializes observer correctly', () => {
    const { result } = renderHook(() =>
      useBookmarkObserver(
        mockElementToResourceMap,
        mockUpdateBookmarkState,
        mockBookmarkStates,
      ),
    );
    expect(result.current.pendingRegistrations.current).toEqual([]);
  });

  it('handles intersection observer callback', async () => {
    const mockBookmarkData: UserBookmark = {
      added_on: '2024-03-20T12:00:00Z',
      domain: 'example.com',
      resource_id: 'test-123',
      resource_type: UserBookmarkResourceType.STORY,
      resource_type_and_id: 'story:test-123',
      user_id: 'user-123',
    };
    (fetchBookmark as jest.Mock).mockResolvedValue({
      data: mockBookmarkData,
      success: true,
    });

    const mockIntersectionCallback = jest.fn();
    const mockIntersectionObserver = jest
      .fn()
      .mockImplementation((callback) => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        mockIntersectionCallback.mockImplementation(callback);
        return {
          disconnect: jest.fn(),
          observe: jest.fn(),
          unobserve: jest.fn(),
        };
      });

    // eslint-disable-next-line compat/compat
    window.IntersectionObserver = mockIntersectionObserver;

    renderHook(() =>
      useBookmarkObserver(
        mockElementToResourceMap,
        mockUpdateBookmarkState,
        mockBookmarkStates,
      ),
    );

    const mockElement = document.createElement('div');
    const mockResource = {
      resourceId: 'test-123',
      resourceType: UserBookmarkResourceType.STORY,
    };
    const mockInitBookmark = jest.fn();

    mockElementToResourceMap.set(mockElement, {
      initBookmark: mockInitBookmark,
      resource: mockResource,
    });

    const mockEntry = {
      isIntersecting: true,
      target: mockElement,
    };
    mockIntersectionCallback([mockEntry], { observe: jest.fn() });

    // Wait for async operations to complete
    await new Promise((resolve) => {
      setTimeout(resolve, 0);
    });

    expect(fetchBookmark).toHaveBeenCalledWith(
      mockResource.resourceType,
      mockResource.resourceId,
    );
    expect(mockUpdateBookmarkState).toHaveBeenCalledWith(
      mockResource.resourceType,
      mockResource.resourceId,
      expect.objectContaining({
        data: mockBookmarkData,
        initialised: true,
        isBookmarked: true,
        isLoading: false,
      }),
    );
    expect(mockInitBookmark).toHaveBeenCalledWith(
      expect.objectContaining({
        data: mockBookmarkData,
        initialised: true,
        isBookmarked: true,
        isLoading: false,
      }),
    );
  });
});
