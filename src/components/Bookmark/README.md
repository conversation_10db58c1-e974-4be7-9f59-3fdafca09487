# Bookmark Component

A React component that enables users to bookmark stories and pages within the application.

## Features

- Bookmark/unbookmark stories and pages
- Support for both authenticated and anonymous users
- Automatic bookmark creation after login/registration
- Onboarding tooltips for new users
- Batch fetching / lazy loading bookmarks

## Implementation Details

The Bookmark component is part of the Sepang project, which provides the backend for the bookmarking system. For detailed implementation information, please refer to:

- [Sepang Project Docs](https://gitlab.com/fairfax-acm/racetracks/sepang)
- [Solution Design Docs](https://ffxacm.atlassian.net/wiki/spaces/newsnow/pages/**********/User+page+bookmark+feature+DynamoDB+NextJS+Deno+API)

### Lazy load

Each bookmark component will lazy load when it becomes visible on the page through the BookmarkProvider and batch fetch its state if more efficient

## Story Updates

### Story retraction

The Bookmark component supports story retraction functionality. When a story is retracted, the bookmark system handles this behind the scenes via the lambda function in the sepang repo and removes it:

- [Story Retraction Documentation](https://ffxacm.atlassian.net/wiki/spaces/newsnow/pages/**********/User+page+bookmark+feature+DynamoDB+NextJS+Deno+API#Story-Retraction)

### Lead image / Title / Summary Updates

_Not Implemented at this point in time_

- If the lead image / title etc for a story is updated this wont be reflected on the bookmark automatically unless it is un-bookmarked and re-bookmarked.
- [Further info](https://ffxacm.atlassian.net/wiki/spaces/newsnow/pages/**********/User+page+bookmark+feature+DynamoDB+NextJS+Deno+API#Story-Updates)
