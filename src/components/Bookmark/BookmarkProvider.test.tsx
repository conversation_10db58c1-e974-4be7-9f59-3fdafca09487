import { act, render } from '@testing-library/react';
import { useContext } from 'react';

import BookmarkProvider from 'components/Bookmark/BookmarkProvider';
import { createStore } from 'store/store';
import { UserBookmarkResourceType } from 'types/sepang-types/bookmark';
import { TestWrapper } from 'util/jest';
import { subToast } from 'util/newsletter';

import { BookmarkContext, useBookmarkContext } from './hooks';
import { createBookmark, deleteBookmark } from './sepang';

import type { BookmarkContextType } from './types';
import type { PianoLoginUserData } from 'types/Piano';
import type { UserBookmark } from 'types/sepang-types/bookmark';

jest.mock('./sepang', () => ({
  createBookmark: jest.fn(),
  deleteBookmark: jest.fn(),
}));

jest.mock('util/newsletter', () => ({
  subToast: jest.fn(),
}));

const mockUser: PianoLoginUserData = {
  aud: 'test-aud',
  confirmed: true,
  email: '<EMAIL>',
  email_confirmation_required: false,
  exp: **********,
  family_name: '<PERSON><PERSON>',
  firstName: '<PERSON>',
  given_name: 'John',
  iat: **********,
  iss: 'test-iss',
  jti: 'test-jti',
  lastName: 'Doe',
  login_timestamp: '2024-03-20T12:00:00Z',
  sub: 'test-sub',
  uid: '123',
  valid: true,
};

const store = createStore((state) => ({
  ...state,
  features: {
    ...state.features,
    userBookmarks: { enabled: true },
  },
  piano: {
    ...state.piano,
    initialized: true,
    user: mockUser,
  },
}));

describe('BookmarkProvider', () => {
  const mockResource = {
    resourceId: 'test-123',
    resourceType: UserBookmarkResourceType.STORY,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders children correctly', () => {
    const { getByText } = render(
      <TestWrapper store={store}>
        <BookmarkContext.Provider value={null}>
          <div>Test Child</div>
        </BookmarkContext.Provider>
      </TestWrapper>,
    );
    expect(getByText('Test Child')).toBeInTheDocument();
  });

  it('provides bookmark context with required functions', () => {
    let contextValue: BookmarkContextType | null = null;
    const TestComponent = () => {
      contextValue = useBookmarkContext();
      return null;
    };

    render(
      <TestWrapper store={store}>
        <BookmarkProvider>
          <TestComponent />
        </BookmarkProvider>
      </TestWrapper>,
    );

    expect(contextValue).toHaveProperty('register');
    expect(contextValue).toHaveProperty('unregister');
    expect(contextValue).toHaveProperty('subscribeToBookmark');
    expect(contextValue).toHaveProperty('unsubscribeFromBookmark');
    expect(contextValue).toHaveProperty('updateBookmark');
  });

  it('handles bookmark creation successfully', async () => {
    const mockCreateResponse = {
      data: {
        added_on: '2024-03-20T12:00:00Z',
        domain: 'example.com',
        resource_id: 'test-123',
        resource_type: UserBookmarkResourceType.STORY,
        resource_type_and_id: 'STORY:test-123',
        user_id: 'user-123',
      } as UserBookmark,
      success: true,
    };
    (createBookmark as jest.Mock).mockResolvedValue(mockCreateResponse);

    let contextValue: BookmarkContextType | null = null;
    const TestComponent = () => {
      contextValue = useContext(BookmarkContext);
      return null;
    };

    render(
      <TestWrapper store={store}>
        <BookmarkProvider>
          <TestComponent />
        </BookmarkProvider>
      </TestWrapper>,
    );

    await act(async () => {
      await contextValue?.updateBookmark(
        mockResource.resourceType,
        mockResource.resourceId,
        'create',
      );
    });

    expect(createBookmark).toHaveBeenCalledWith(
      mockResource.resourceType,
      mockResource.resourceId,
      undefined,
    );
    expect(subToast).toHaveBeenCalled();
  });

  it('handles bookmark deletion successfully', async () => {
    (deleteBookmark as jest.Mock).mockResolvedValue(true);

    let contextValue: BookmarkContextType | null = null;
    const TestComponent = () => {
      contextValue = useContext(BookmarkContext);
      return null;
    };

    render(
      <TestWrapper store={store}>
        <BookmarkProvider>
          <TestComponent />
        </BookmarkProvider>
      </TestWrapper>,
    );

    await act(async () => {
      await contextValue?.updateBookmark(
        mockResource.resourceType,
        mockResource.resourceId,
        'delete',
      );
    });

    expect(deleteBookmark).toHaveBeenCalledWith(
      mockResource.resourceType,
      mockResource.resourceId,
    );
  });

  it('handles subscription and unsubscription correctly', async () => {
    const mockCallback = jest.fn();
    let contextValue: BookmarkContextType | null = null;
    const TestComponent = () => {
      contextValue = useContext(BookmarkContext);
      return null;
    };

    render(
      <TestWrapper store={store}>
        <BookmarkProvider>
          <TestComponent />
        </BookmarkProvider>
      </TestWrapper>,
    );

    act(() => {
      contextValue?.subscribeToBookmark(
        mockResource.resourceType,
        mockResource.resourceId,
        mockCallback,
      );
    });

    await act(async () => {
      await contextValue?.updateBookmark(
        mockResource.resourceType,
        mockResource.resourceId,
        'create',
      );
    });
    expect(mockCallback).toHaveBeenCalled();
    mockCallback.mockClear();

    act(() => {
      contextValue?.unsubscribeFromBookmark(
        mockResource.resourceType,
        mockResource.resourceId,
        mockCallback,
      );
    });
    await act(async () => {
      await contextValue?.updateBookmark(
        mockResource.resourceType,
        mockResource.resourceId,
        'create',
      );
    });
    expect(mockCallback).not.toHaveBeenCalled();
  });
});
