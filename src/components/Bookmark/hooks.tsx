import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';

import { useAppSelector } from 'store/hooks';

import { batchFetchBookmarks, fetchBookmark } from './sepang';
import { createBookmarkState, getBookmarkKey } from './utils';

import type {
  BookmarkContextType,
  BookmarkState,
  ResourceElementMap,
  ResourceItem,
} from './types';

export const BookmarkContext = createContext<BookmarkContextType | null>(null);

export function useBookmarkContext() {
  const context = useContext(BookmarkContext);
  if (!context) {
    throw new Error(
      'useBookmarkContext must be used within a BookmarkProvider',
    );
  }
  return context;
}

export function useBookmark(
  resourceType: string,
  resourceId: string,
  metadata?: Record<string, unknown>,
) {
  const [bookmarkState, setBookmarkState] = useState<BookmarkState>({
    data: null,
    initialised: false,
    isBookmarked: false,
    isLoading: false,
  });
  const { initialized: pianoInitialized, user } = useAppSelector(
    (state) => state.piano,
  );
  const userBookmarksFeatureEnabled = useAppSelector(
    (state) => state.features.userBookmarks.enabled,
  );

  const {
    register,
    subscribeToBookmark,
    unsubscribeFromBookmark,
    updateBookmark,
  } = useBookmarkContext();
  const ref = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (ref.current) {
      if (userBookmarksFeatureEnabled && pianoInitialized && user?.valid) {
        register(ref.current, { resourceId, resourceType }, setBookmarkState);
      }
    }
  }, [
    register,
    resourceType,
    resourceId,
    ref,
    userBookmarksFeatureEnabled,
    pianoInitialized,
    user,
  ]);

  useEffect(() => {
    subscribeToBookmark(resourceType, resourceId, setBookmarkState);
    return () =>
      unsubscribeFromBookmark(resourceType, resourceId, setBookmarkState);
  }, [resourceType, resourceId, subscribeToBookmark, unsubscribeFromBookmark]);

  const toggleBookmark = useCallback(() => {
    const updateType = bookmarkState.isBookmarked ? 'delete' : 'create';
    updateBookmark(resourceType, resourceId, updateType, metadata).catch(
      console.error,
    );
  }, [
    resourceType,
    resourceId,
    bookmarkState.isBookmarked,
    metadata,
    updateBookmark,
  ]);

  const createBookmark = useCallback(() => {
    if (!bookmarkState.isBookmarked && bookmarkState.initialised) {
      updateBookmark(resourceType, resourceId, 'create', metadata).catch(
        console.error,
      );
    }
  }, [
    resourceType,
    resourceId,
    updateBookmark,
    metadata,
    bookmarkState.isBookmarked,
    bookmarkState.initialised,
  ]);

  return { bookmarkState, createBookmark, ref, toggleBookmark };
}

export function useBookmarkStates() {
  const bookmarkStates = useRef(new Map<string, BookmarkState>());
  const subscribers = useRef(
    new Map<string, Set<(state: BookmarkState) => void>>(),
  );

  const updateBookmarkState = useCallback(
    (resourceType: string, resourceId: string, newState: BookmarkState) => {
      const key = getBookmarkKey(resourceType, resourceId);
      bookmarkStates.current.set(key, newState);
      subscribers.current.get(key)?.forEach((callback) => callback(newState));
    },
    [],
  );

  return { bookmarkStates, subscribers, updateBookmarkState };
}

export function useBookmarkObserver(
  elementToResourceMap: WeakMap<Element, ResourceElementMap>,
  updateBookmarkState: (
    resourceType: string,
    resourceId: string,
    state: BookmarkState,
  ) => void,
  bookmarkStates: React.RefObject<Map<string, BookmarkState>>,
) {
  const observer = useRef<IntersectionObserver | null>(null);
  const pendingRegistrations = useRef<
    Array<{
      element: Element;
      initBookmark: (state: BookmarkState) => void;
      resource: ResourceItem;
    }>
  >([]);

  useEffect(() => {
    if (!window.IntersectionObserver) return;

    const handleSingleBookmarkFetch = async (item: {
      data: ResourceElementMap;
      entry: IntersectionObserverEntry;
    }) => {
      const {
        data: {
          initBookmark,
          resource: { resourceId, resourceType },
        },
        entry,
      } = item;
      const result = await fetchBookmark(resourceType, resourceId);

      const newState = result.success
        ? createBookmarkState(result.data)
        : createBookmarkState(null);

      updateBookmarkState(resourceType, resourceId, newState);

      observer.current?.unobserve(entry.target);
      initBookmark(newState);
    };

    const handleBatchBookmarkFetch = async (
      items: Array<{
        data: ResourceElementMap;
        entry: IntersectionObserverEntry;
      }>,
    ) => {
      const results = await batchFetchBookmarks(
        items.map((i) => i.data.resource),
      );

      if (results.success) {
        items.forEach(
          ({
            data: {
              initBookmark,
              resource: { resourceId, resourceType },
            },
            entry,
          }) => {
            const bookmarkData =
              results.data.find(
                (bookmark) => bookmark.resourceId === resourceId,
              ) ?? null;

            const newState = createBookmarkState(bookmarkData);
            updateBookmarkState(resourceType, resourceId, newState);
            observer.current?.unobserve(entry.target);
            initBookmark(newState);
          },
        );
      }
    };

    // eslint-disable-next-line compat/compat
    observer.current = new IntersectionObserver(
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      async (entries) => {
        const seenKeys = new Set<string>();
        const itemsToFetch = entries
          .filter((entry) => {
            const data = elementToResourceMap.get(entry.target);
            if (!data) return false;

            // eslint-disable-next-line @stylistic/max-len
            const key = getBookmarkKey(
              data.resource.resourceType,
              data.resource.resourceId,
            );
            if (
              entry.isIntersecting &&
              !bookmarkStates.current.get(key)?.initialised &&
              !seenKeys.has(key)
            ) {
              seenKeys.add(key);
              return true;
            }
            return false;
          })
          .map((entry) => ({
            data: elementToResourceMap.get(entry.target)!,
            entry,
          }));

        if (itemsToFetch.length > 1) {
          await handleBatchBookmarkFetch(itemsToFetch);
        } else if (itemsToFetch.length === 1) {
          await handleSingleBookmarkFetch(itemsToFetch[0]);
        }
      },
      { threshold: 1 },
    );

    pendingRegistrations.current.forEach(
      ({ element, initBookmark, resource }) => {
        elementToResourceMap.set(element, { initBookmark, resource });
        observer.current?.observe(element);
      },
    );
    pendingRegistrations.current = [];

    // eslint-disable-next-line consistent-return
    return () => observer.current?.disconnect();
  }, [elementToResourceMap, updateBookmarkState, bookmarkStates]);

  return { observer, pendingRegistrations };
}
