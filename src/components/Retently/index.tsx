import Script from 'next/script';

import { useAppSelector } from 'store/hooks';
import { useDeviceTypeFromWidth } from 'util/hooks';

export default function Retently(): React.ReactElement | null {
  const retentlyFeature = useAppSelector((state) => state.features.retently);
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const initialized = useAppSelector((state) => state.piano.initialized);
  const hasSubscription = useAppSelector(
    (state) => state.piano.hasSubscription,
  );
  const email = useAppSelector((state) => state.piano.user?.email);
  const firstName = useAppSelector((state) => state.piano.user?.firstName);
  const lastName = useAppSelector((state) => state.piano.user?.lastName);
  const siteName = useAppSelector((state) => state.conf.name);
  const publication = useAppSelector((state) => state.conf.publication);
  const term = useAppSelector(
    (state) =>
      state.piano.conversions.find(
        (conversion) => conversion.term && conversion.term.name,
      )?.term.name,
  );
  const deviceType = useDeviceTypeFromWidth();

  if (
    !pianoFeature.enabled ||
    !initialized ||
    !retentlyFeature.enabled ||
    !email
  ) {
    return null;
  }

  return (
    <>
      <div
        data-campaign="regular"
        data-company={siteName}
        data-email={email}
        data-firstname={firstName}
        data-href="https://app.retently.com/api/remote/tracking/5dc37a35667ddf7c1d681292"
        data-lastname={lastName}
        data-prop-url={window.location.pathname}
        data-rel="dialog"
        data-tags={`api,inapp,${publication},${
          hasSubscription ? 'subscribed' : 'unsubscribed'
        },${deviceType},${term ?? ''}`}
        id="retently-survey-embed"
      />
      <Script
        defer
        id="retently-jssdk"
        src="https://cdn.retently.com/public/components/embed/sdk.min.js"
        strategy="lazyOnload"
      />
    </>
  );
}
