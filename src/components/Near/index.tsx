import Script from 'next/script';

import { useAppSelector } from 'store/hooks';

export default function Near() {
  const pianoEnabled = useAppSelector((state) => state.features.piano.enabled);
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const userId = useAppSelector((state) => state.piano.user?.uid);

  // If Piano is enabled, wait for it to be initialized before loading the Near
  // script, so that we can include the user ID if available
  if (pianoEnabled && !pianoInitialized) {
    return null;
  }

  let src = 'https://pixel.zprk.io/v5/pixeljs/MiD6CKfJvE.js';

  if (pianoEnabled && userId) {
    src += `?consent=0&GDPR=0&fpid=${userId}`;
  }

  // Since it's a tracker, load as lazily as possible
  return <Script src={src} strategy="lazyOnload" />;
}
