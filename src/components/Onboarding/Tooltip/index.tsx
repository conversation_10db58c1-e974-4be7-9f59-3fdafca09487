import clsx from 'clsx';
import { useEffect, useState } from 'react';

interface OnboardingTooltipProps {
  action: string;
  description: string;
  isReady: boolean;
  localStorageKey: string;
  trigger: React.ReactElement;
}

function setDismissed(key: string) {
  if (!key.trim()) return;
  window.localStorage.setItem(key, 'true');
}

export default function OnboardingTooltip({
  action,
  description,
  isReady,
  localStorageKey,
  trigger,
}: OnboardingTooltipProps) {
  const [initialised, setInitialised] = useState(isReady);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isReady) {
      setInitialised(true);
    }
  }, [isReady]);

  useEffect(() => {
    if (!initialised) {
      return;
    }
    const value = window.localStorage.getItem(localStorageKey);
    if (value === null) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [initialised, localStorageKey]);

  return (
    <div className="relative inline-block">
      {trigger}
      {initialised && isVisible && (
        <>
          <div
            className={clsx(
              'absolute left-10 top-[calc(100%+7px)] z-10 size-5 -translate-x-1/2 rotate-45 border-l-1 border-t-1 border-gray-200 bg-white',
              {
                hidden: !isVisible,
              },
            )}
          />
          <div
            className={clsx(
              'absolute top-[calc(100%+7px)] z-[5] mt-[9px] flex h-auto w-64 rounded-md border-1 border-gray-200 bg-white px-4 py-2 shadow-md transition duration-200 ease-out',
              {
                hidden: !isVisible,
              },
            )}
          >
            <div className="self-start pb-1 font-inter text-sm text-gray-900">
              <div className="mb-2 text-base font-semibold text-gray-900">
                {action}
              </div>
              <div className="mb-4 text-sm font-normal text-gray-900">
                {description}
              </div>
              <div className="flex items-center justify-between">
                <div
                  className="text-sm font-medium text-gray-900 underline"
                  onClick={() => {
                    setIsVisible(false);
                    setDismissed(localStorageKey);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      setIsVisible(false);
                      setDismissed(localStorageKey);
                    }
                  }}
                  role="button"
                  tabIndex={0}
                >
                  Dismiss
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
