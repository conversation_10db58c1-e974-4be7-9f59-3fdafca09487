import { fireEvent, render, screen } from '@testing-library/react';

import OnboardingTooltip from './index';

const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    clear: jest.fn(() => {
      store = {};
    }),
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('OnboardingTooltip', () => {
  beforeEach(() => {
    localStorageMock.clear();
    jest.clearAllMocks();
  });

  const mockTrigger = <button type="button">Test Trigger</button>;
  const action = 'Action Text here';
  const description = 'Description text here.';
  const localStorageKey = 'onboardingTestKey';

  it('should match snapshot when visible', () => {
    const { container } = render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={localStorageKey}
        trigger={mockTrigger}
      />,
    );
    expect(container).toMatchSnapshot();
  });

  it('should not show tooltip when not ready', () => {
    const { container } = render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady={false}
        localStorageKey={localStorageKey}
        trigger={mockTrigger}
      />,
    );
    expect(container).toMatchSnapshot();
  });

  it('should not show tooltip when dismissed', () => {
    localStorageMock.getItem.mockReturnValue('true');
    const { container } = render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={localStorageKey}
        trigger={mockTrigger}
      />,
    );
    expect(container).toMatchSnapshot();
  });

  it('should show tooltip when ready and not dismissed', () => {
    localStorageMock.getItem.mockReturnValue(null);
    render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={localStorageKey}
        trigger={mockTrigger}
      />,
    );
    expect(screen.getByText('Action Text here')).toBeInTheDocument();
  });

  it('should dismiss tooltip when dismiss button is clicked', () => {
    localStorageMock.getItem.mockReturnValue(null);
    render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={localStorageKey}
        trigger={mockTrigger}
      />,
    );

    const dismissButton = screen.getByText('Dismiss');
    fireEvent.click(dismissButton);

    expect(screen.queryByText('Action Text here')).not.toBeInTheDocument();
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      localStorageKey,
      'true',
    );
  });

  // eslint-disable-next-line @stylistic/max-len
  it('should dismiss tooltip when dismiss button is pressed with Enter key', () => {
    localStorageMock.getItem.mockReturnValue(null);
    render(
      <OnboardingTooltip
        action={action}
        description={description}
        isReady
        localStorageKey={localStorageKey}
        trigger={mockTrigger}
      />,
    );

    const dismissButton = screen.getByText('Dismiss');
    fireEvent.keyDown(dismissButton, { key: 'Enter' });

    expect(screen.queryByText('Action Text here')).not.toBeInTheDocument();
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      localStorageKey,
      'true',
    );
  });
});
