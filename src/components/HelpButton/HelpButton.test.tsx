import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import HelpButton from '.';

describe('<HelpButton />', () => {
  it('renders', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={createStore()}>
        <HelpButton show />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
