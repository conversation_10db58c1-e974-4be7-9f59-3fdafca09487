import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import Component from '.';

import type { Meta, StoryObj } from '@storybook/nextjs-vite';

const meta: Meta<typeof Component> = {
  component: Component,
  title: 'Components/Help button',
};

export default meta;

type Story = StoryObj<typeof Component>;

export const Default: Story = {
  render: () => (
    <TestWrapper store={createStore()}>
      <Component show />
    </TestWrapper>
  ),
};
