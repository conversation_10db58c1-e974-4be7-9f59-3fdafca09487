import Script from 'next/script';

import { useAppSelector } from 'store/hooks';

export default function Mantis(): React.ReactElement | null {
  const enabled = useAppSelector(
    (state) =>
      state.features.adServing.enabled &&
      state.features.adServing.data.useMantis,
  );
  const isStory = useAppSelector((state) => !!state.story.id);

  if (!enabled || !isStory) {
    return null;
  }

  return (
    <Script async src="https://loader.mantis-intelligence.com/acm/loader.js" />
  );
}
