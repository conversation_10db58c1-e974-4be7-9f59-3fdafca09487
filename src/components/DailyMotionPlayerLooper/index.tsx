import Script from 'next/script';

import { useAppSelector } from 'store/hooks';

import type { FC } from 'react';

export interface DailyMotionPlayerLooperProps {
  onPlayerReady?: (player: DMPlayer) => void;
  videoId: string;
}

const DailyMotionPlayerLooper: FC<DailyMotionPlayerLooperProps> = ({
  onPlayerReady,
  videoId,
}) => {
  const playerId = useAppSelector(
    (state) => state.conf.dailymotionPlayerIdForLooperVideos,
  );

  return (
    <>
      <Script
        async
        onLoad={() => {
          if (window.dailymotion) {
            window.dailymotion
              .createPlayer('looper-video', {
                params: {
                  loop: true,
                  mute: true,
                  syndicationKey: '',
                },
                video: videoId,
              })
              .then((dmPlayer) => {
                onPlayerReady?.(dmPlayer);
              })
              .catch((e) => {
                console.error(e);
              });
          }
        }}
        src={`https://geo.dailymotion.com/libs/player/${playerId}.js`}
      />
      <div id="looper-video" />
    </>
  );
};

export default DailyMotionPlayerLooper;
