'use client';

import React, { useEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';

function setLiveRampAdditionalData(email: string) {
  // Then set additional data with the email
  window.atsenvelopemodule?.setAdditionalData({
    id: email,
    type: 'email',
  });
}

export default function LiveRamp(): React.ReactElement | null {
  const liveRampEnabled = useAppSelector(
    (state) => state.features.liveramp.enabled,
  );
  const pianoEnabled = useAppSelector((state) => state.features.piano.enabled);
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const userEmail = useAppSelector((state) => state.piano.user?.email);
  const [liveRampReady, setLiveRampReady] = useState(false);
  const [listenerAdded, setListenerAdded] = useState(false);
  const [isPianoConfigured, setIsPianoConfigured] = useState(false);

  useEffect(() => {
    if (!pianoEnabled || !pianoInitialized) return;

    if (
      typeof userEmail !== 'undefined' &&
      liveRampReady &&
      !isPianoConfigured
    ) {
      // Set additional Live Ramp data.
      // Needs to be added after piano user
      // data is available.
      setLiveRampAdditionalData(userEmail);
      setIsPianoConfigured(true);
    }
  }, [
    isPianoConfigured,
    pianoEnabled,
    pianoInitialized,
    liveRampReady,
    userEmail,
  ]);

  const setLoaded = () => {
    setLiveRampReady(true);
  };

  useEffect(() => {
    if (listenerAdded || liveRampReady) return;

    // If Liveramp script takes a long time to load then
    // we add an event listener to know when is ready
    if (typeof window.atsenvelopemodule === 'undefined') {
      setListenerAdded(true);
      // eslint-disable-next-line @stylistic/max-len
      // https://docs.liveramp.com/privacy-manager/en/ats-js-functions-and-events.html
      window.addEventListener('envelopeModuleReady', setLoaded, {
        once: true,
      });
    } else {
      setLiveRampReady(true);
    }
  }, [listenerAdded, liveRampReady]);

  if (!liveRampEnabled) return null;

  return (
    <script
      defer
      src={
        'https://ats-wrapper.privacymanager.io/ats-modules/' +
        '************************************/ats.js'
      }
    />
  );
}
