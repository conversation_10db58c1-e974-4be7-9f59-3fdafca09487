import type {
  PianoPushAction,
  PianoPushActionsInterface,
  PianoReadyCallback,
  PianoTinypassInterface,
} from 'types/Piano';

export function pushPianoAction(action: PianoPushAction) {
  const tp: PianoPushActionsInterface = window.tp || [];
  tp.push(action);
  window.tp = tp;
}

export const { addPianoReadyAction, getPianoReady, onPianoReady } =
  (function pianoReadyWrapper() {
    const callbacks: PianoReadyCallback[] = [];
    let pianoReady = false;

    function pianoTinypassInterfaceReady(
      tp: unknown,
    ): tp is PianoTinypassInterface {
      return !!tp && typeof tp === 'object' && 'aid' in tp && !!tp.aid;
    }

    function setPianoReady() {
      if (!pianoTinypassInterfaceReady(window.tp)) {
        // Should never fire, but prevent an edge case from
        // causing piano to fire completely
        setTimeout(setPianoReady, 100);
        return;
      }

      const { tp } = window;

      pianoReady = true;
      callbacks.forEach((cb) => {
        cb(tp)?.catch(console.error);
      });
    }

    function addPianoReadyActionInner() {
      pushPianoAction([
        'init',
        () => {
          setPianoReady();
        },
      ]);
    }

    function onPianoReadyInner(callback: PianoReadyCallback) {
      if (pianoReady) {
        if (pianoTinypassInterfaceReady(window.tp)) {
          callback(window.tp)?.catch(console.error);
          return;
        }

        pianoReady = false;
        addPianoReadyActionInner();
      }

      callbacks.push(callback);
    }

    function getPianoReadyInner() {
      return new Promise<PianoTinypassInterface>((resolve) => {
        onPianoReadyInner((tp) => {
          resolve(tp);
        });
      });
    }

    return {
      addPianoReadyAction: addPianoReadyActionInner,
      getPianoReady: getPianoReadyInner,
      onPianoReady: onPianoReadyInner,
    };
  })();
