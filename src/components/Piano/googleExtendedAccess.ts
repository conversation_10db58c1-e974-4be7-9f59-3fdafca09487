import { debugGaaLog as debugLog } from 'components/GoogleExtendAccess/utils';
import googleExtendedAccessSlice from 'store/slices/googleExtendedAccess';
import pianoSlice from 'store/slices/piano';

import type {
  PianoMeterData,
  PianoSocialAuthResponse,
  PianoSocialLogin,
  PianoUserData,
  PianoUserProfileData,
} from 'types/Piano';

function onMeterActive(e: PianoMeterData) {
  debugLog(`meterActive ${JSON.stringify(e)}`);
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.setHasActiveMeter(true));
}

function onMeterExpire(e: PianoMeterData) {
  debugLog(`meterExpire ${JSON.stringify(e)}`);
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.setHasExpiredMeter(true));
}

async function fetchUserProfile(
  user: PianoUserData | null,
): Promise<PianoUserProfileData | null> {
  if (user === null) {
    return null;
  }
  debugLog(`fetchUserProfile ${JSON.stringify(user)}`);
  const params = new URLSearchParams({
    uid: user.sub,
  });
  const res = await fetch(`/api/user-profile/?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });
  return (await res.json()) as PianoUserProfileData;
}

function setUserProfile(userProfile: PianoUserProfileData | null): void {
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.setUserProfile(userProfile));
}

async function fetchSocialLogin(
  socialType: string,
  redirectUri: string,
): Promise<PianoSocialLogin> {
  const params = new URLSearchParams({
    redirect_uri: redirectUri,
    social_type: socialType,
  });
  const res = await fetch(`/api/social-login/?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });
  return (await res.json()) as PianoSocialLogin;
}

function setSocialLogin(socialLogin: PianoSocialLogin): void {
  debugLog(`socialLogin : ${JSON.stringify(socialLogin)}}`);
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.setSocialLogin(socialLogin.uri));
}

function getUrlWithoutCode(): string {
  const { hash, host, pathname, protocol, search } = window.location;
  const searchParams = new URLSearchParams(search);
  if (searchParams.has('response_id')) {
    searchParams.delete('response_id');
  }
  const paramsStr = searchParams.toString();
  const url = `${protocol}//${host}${pathname}?${paramsStr}${hash}`;
  debugLog(`Url without code : ${url}`);
  return url;
}

function clearOauthCodeFromUrl() {
  if (window.history.pushState) {
    window.history.pushState(null, '', getUrlWithoutCode());
  }
}

export function fetchAndSetUserProfile(user: PianoUserData | null) {
  fetchUserProfile(user)
    .then((userProfile: PianoUserProfileData | null) =>
      setUserProfile(userProfile),
    )
    .catch(console.error);
}

export async function fetchSocialAuthToken(
  code: string,
): Promise<PianoSocialAuthResponse> {
  const params = new URLSearchParams({ code });
  const res = await fetch(`/api/social-auth-token/?${params.toString()}`, {
    headers: {
      Accept: 'application/json',
    },
    method: 'GET',
  });
  return (await res.json()) as PianoSocialAuthResponse;
}

export function initialize() {
  const store = window.getStore();
  const state = store.getState();
  const { oauthCode } = state.googleExtendAccess;

  window.tp = window.tp || [];
  window.tp.push(['addHandler', 'meterActive', onMeterActive]);
  window.tp.push(['addHandler', 'meterExpired', onMeterExpire]);

  const { host, pathname, protocol, search } = window.location;
  const searchParams = new URLSearchParams(search);
  if (searchParams.has('response_id')) {
    const newOauthCode = searchParams.get('response_id') as string;
    if (oauthCode !== newOauthCode) {
      debugLog(`setOauthCode ${newOauthCode}`);
      store.dispatch(
        googleExtendedAccessSlice.actions.setOauthCode(newOauthCode),
      );
      clearOauthCodeFromUrl();
    }
  } else {
    const redirectUri = `${protocol}//${host}${pathname}${search}`;
    fetchSocialLogin('GOOGLE', redirectUri)
      .then(setSocialLogin)
      .catch(console.error);
  }
}
