import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import toast from 'react-hot-toast';

import Link from 'themes/autumn/components/generic/Link';
import { disableSharedReminder } from 'util/auth/shared-subs';
import { plural } from 'util/text';

import { onPianoReady } from '../ready';

import type { ToastLike } from './util';

interface SharedSubsUndoToastProps {
  t: ToastLike;
}

export function SharedSubsUndoToast({ t }: SharedSubsUndoToastProps) {
  return (
    <div
      className={clsx(
        'pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg',
        {
          'animate-enter': t.visible,
          'animate-leave': !t.visible,
        },
      )}
    >
      <div className="p-3 pr-12">
        <div className="flex items-center">
          <div className="flex w-0 flex-1 justify-between">
            <div className="w-0 flex-1 text-sm font-medium text-green-800">
              <div className="flex flex-col items-start">
                <div className="p-1">Notification dismissed.</div>
                <button
                  className="mt-1 p-1 font-bold hover:opacity-50"
                  data-testid="shared-subs-undo"
                  onClick={() =>
                    onPianoReady((tp) => {
                      const accessToken = tp.pianoId.getToken();
                      if (!accessToken) {
                        return;
                      }

                      disableSharedReminder({
                        accessToken,
                        disable: false,
                      }).catch((e) => console.error(e));

                      toast.dismiss(t.id);
                    })
                  }
                  type="button"
                >
                  Undo
                </button>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute right-4 top-4 size-5">
          <button
            className="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
            data-testid="shared-subs-undo-close"
            onClick={() => toast.dismiss(t.id)}
            type="button"
          >
            <span className="sr-only">Close</span>
            <FontAwesomeIcon
              className="fill-current stroke-current text-green-800"
              icon={faTimes}
            />
          </button>
        </div>
      </div>
    </div>
  );
}

interface SharedSubsReminderToastProps {
  firstName: string | undefined;
  remaining: number;
  showDisabledToast: () => void;
  t: ToastLike;
}

export function SharedSubsReminderToast({
  firstName,
  remaining,
  showDisabledToast,
  t,
}: SharedSubsReminderToastProps) {
  return (
    <div
      className={clsx(
        'pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg',
        {
          'animate-enter': t.visible,
          'animate-leave': !t.visible,
        },
      )}
    >
      <div className="p-4 pr-12">
        <div className="flex items-center">
          <div className="flex w-0 flex-1 justify-between">
            <div className="w-0 flex-1 text-sm font-medium text-green-800">
              <div className="flex flex-col">
                <div>Welcome back{firstName ? ` ${firstName}` : '!'}</div>
                <div>
                  You still have{' '}
                  <b>
                    {remaining} pending invite{plural(remaining)}
                  </b>{' '}
                  that haven&apos;t been redeemed.
                </div>
                <div className="mt-3 flex items-center gap-x-9 font-bold">
                  <button
                    className="hover:opacity-50"
                    data-testid="shared-subs-dont-show-again"
                    onClick={() =>
                      onPianoReady((tp) => {
                        const accessToken = tp.pianoId.getToken();
                        if (!accessToken) {
                          return;
                        }

                        disableSharedReminder({
                          accessToken,
                          disable: true,
                        })
                          .then(showDisabledToast)
                          .catch((e) => console.error(e));

                        toast.dismiss(t.id);
                      })
                    }
                    type="button"
                  >
                    Don&#8217;t show again
                  </button>
                  <Link
                    className="hover:opacity-50"
                    data-testid="shared-subs-manage-seats"
                    href="/manage-seats/"
                    noStyle
                    onClick={() => toast.dismiss(t.id)}
                  >
                    Manage Invites
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute right-4 top-4 size-5">
          <button
            className="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
            data-testid="shared-subs-close"
            onClick={() => toast.dismiss(t.id)}
            type="button"
          >
            <span className="sr-only">Close</span>
            <FontAwesomeIcon
              className="fill-current stroke-current text-green-800"
              icon={faTimes}
            />
          </button>
        </div>
      </div>
    </div>
  );
}
