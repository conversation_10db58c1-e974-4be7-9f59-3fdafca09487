// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SharedSubsReminderToast renders with a name and 1 remaining 1`] = `
<div
  class="pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg animate-enter"
>
  <div
    class="p-4 pr-12"
  >
    <div
      class="flex items-center"
    >
      <div
        class="flex w-0 flex-1 justify-between"
      >
        <div
          class="w-0 flex-1 text-sm font-medium text-green-800"
        >
          <div
            class="flex flex-col"
          >
            <div>
              Welcome back
               Test Name
            </div>
            <div>
              You still have
               
              <b>
                1
                 pending invite
              </b>
               
              that haven't been redeemed.
            </div>
            <div
              class="mt-3 flex items-center gap-x-9 font-bold"
            >
              <button
                class="hover:opacity-50"
                data-testid="shared-subs-dont-show-again"
                type="button"
              >
                Don’t show again
              </button>
              <a
                class="hover:opacity-50"
                data-testid="shared-subs-manage-seats"
                href="/manage-seats/"
              >
                Manage Invites
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="absolute right-4 top-4 size-5"
    >
      <button
        class="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
        data-testid="shared-subs-close"
        type="button"
      >
        <span
          class="sr-only"
        >
          Close
        </span>
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-xmark fill-current stroke-current text-green-800"
          data-icon="xmark"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 384 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;

exports[`SharedSubsReminderToast renders with a name and 5 remaining 1`] = `
<div
  class="pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg animate-enter"
>
  <div
    class="p-4 pr-12"
  >
    <div
      class="flex items-center"
    >
      <div
        class="flex w-0 flex-1 justify-between"
      >
        <div
          class="w-0 flex-1 text-sm font-medium text-green-800"
        >
          <div
            class="flex flex-col"
          >
            <div>
              Welcome back
               Test Name
            </div>
            <div>
              You still have
               
              <b>
                5
                 pending invite
                s
              </b>
               
              that haven't been redeemed.
            </div>
            <div
              class="mt-3 flex items-center gap-x-9 font-bold"
            >
              <button
                class="hover:opacity-50"
                data-testid="shared-subs-dont-show-again"
                type="button"
              >
                Don’t show again
              </button>
              <a
                class="hover:opacity-50"
                data-testid="shared-subs-manage-seats"
                href="/manage-seats/"
              >
                Manage Invites
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="absolute right-4 top-4 size-5"
    >
      <button
        class="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
        data-testid="shared-subs-close"
        type="button"
      >
        <span
          class="sr-only"
        >
          Close
        </span>
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-xmark fill-current stroke-current text-green-800"
          data-icon="xmark"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 384 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;

exports[`SharedSubsReminderToast renders with a non-visible toast 1`] = `
<div
  class="pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg animate-leave"
>
  <div
    class="p-4 pr-12"
  >
    <div
      class="flex items-center"
    >
      <div
        class="flex w-0 flex-1 justify-between"
      >
        <div
          class="w-0 flex-1 text-sm font-medium text-green-800"
        >
          <div
            class="flex flex-col"
          >
            <div>
              Welcome back
               Test Name
            </div>
            <div>
              You still have
               
              <b>
                5
                 pending invite
                s
              </b>
               
              that haven't been redeemed.
            </div>
            <div
              class="mt-3 flex items-center gap-x-9 font-bold"
            >
              <button
                class="hover:opacity-50"
                data-testid="shared-subs-dont-show-again"
                type="button"
              >
                Don’t show again
              </button>
              <a
                class="hover:opacity-50"
                data-testid="shared-subs-manage-seats"
                href="/manage-seats/"
              >
                Manage Invites
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="absolute right-4 top-4 size-5"
    >
      <button
        class="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
        data-testid="shared-subs-close"
        type="button"
      >
        <span
          class="sr-only"
        >
          Close
        </span>
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-xmark fill-current stroke-current text-green-800"
          data-icon="xmark"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 384 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;

exports[`SharedSubsReminderToast renders with a visible toast 1`] = `
<div
  class="pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg animate-enter"
>
  <div
    class="p-4 pr-12"
  >
    <div
      class="flex items-center"
    >
      <div
        class="flex w-0 flex-1 justify-between"
      >
        <div
          class="w-0 flex-1 text-sm font-medium text-green-800"
        >
          <div
            class="flex flex-col"
          >
            <div>
              Welcome back
               Test Name
            </div>
            <div>
              You still have
               
              <b>
                5
                 pending invite
                s
              </b>
               
              that haven't been redeemed.
            </div>
            <div
              class="mt-3 flex items-center gap-x-9 font-bold"
            >
              <button
                class="hover:opacity-50"
                data-testid="shared-subs-dont-show-again"
                type="button"
              >
                Don’t show again
              </button>
              <a
                class="hover:opacity-50"
                data-testid="shared-subs-manage-seats"
                href="/manage-seats/"
              >
                Manage Invites
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="absolute right-4 top-4 size-5"
    >
      <button
        class="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
        data-testid="shared-subs-close"
        type="button"
      >
        <span
          class="sr-only"
        >
          Close
        </span>
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-xmark fill-current stroke-current text-green-800"
          data-icon="xmark"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 384 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;

exports[`SharedSubsReminderToast renders without a name and 1 remaining 1`] = `
<div
  class="pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg animate-enter"
>
  <div
    class="p-4 pr-12"
  >
    <div
      class="flex items-center"
    >
      <div
        class="flex w-0 flex-1 justify-between"
      >
        <div
          class="w-0 flex-1 text-sm font-medium text-green-800"
        >
          <div
            class="flex flex-col"
          >
            <div>
              Welcome back
              !
            </div>
            <div>
              You still have
               
              <b>
                1
                 pending invite
              </b>
               
              that haven't been redeemed.
            </div>
            <div
              class="mt-3 flex items-center gap-x-9 font-bold"
            >
              <button
                class="hover:opacity-50"
                data-testid="shared-subs-dont-show-again"
                type="button"
              >
                Don’t show again
              </button>
              <a
                class="hover:opacity-50"
                data-testid="shared-subs-manage-seats"
                href="/manage-seats/"
              >
                Manage Invites
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="absolute right-4 top-4 size-5"
    >
      <button
        class="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
        data-testid="shared-subs-close"
        type="button"
      >
        <span
          class="sr-only"
        >
          Close
        </span>
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-xmark fill-current stroke-current text-green-800"
          data-icon="xmark"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 384 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;

exports[`SharedSubsReminderToast renders without a name and 5 remaining 1`] = `
<div
  class="pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg animate-enter"
>
  <div
    class="p-4 pr-12"
  >
    <div
      class="flex items-center"
    >
      <div
        class="flex w-0 flex-1 justify-between"
      >
        <div
          class="w-0 flex-1 text-sm font-medium text-green-800"
        >
          <div
            class="flex flex-col"
          >
            <div>
              Welcome back
              !
            </div>
            <div>
              You still have
               
              <b>
                5
                 pending invite
                s
              </b>
               
              that haven't been redeemed.
            </div>
            <div
              class="mt-3 flex items-center gap-x-9 font-bold"
            >
              <button
                class="hover:opacity-50"
                data-testid="shared-subs-dont-show-again"
                type="button"
              >
                Don’t show again
              </button>
              <a
                class="hover:opacity-50"
                data-testid="shared-subs-manage-seats"
                href="/manage-seats/"
              >
                Manage Invites
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="absolute right-4 top-4 size-5"
    >
      <button
        class="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
        data-testid="shared-subs-close"
        type="button"
      >
        <span
          class="sr-only"
        >
          Close
        </span>
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-xmark fill-current stroke-current text-green-800"
          data-icon="xmark"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 384 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;

exports[`SharedSubsUndoToast renders with a non-visible toast 1`] = `
<div
  class="pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg animate-leave"
>
  <div
    class="p-3 pr-12"
  >
    <div
      class="flex items-center"
    >
      <div
        class="flex w-0 flex-1 justify-between"
      >
        <div
          class="w-0 flex-1 text-sm font-medium text-green-800"
        >
          <div
            class="flex flex-col items-start"
          >
            <div
              class="p-1"
            >
              Notification dismissed.
            </div>
            <button
              class="mt-1 p-1 font-bold hover:opacity-50"
              data-testid="shared-subs-undo"
              type="button"
            >
              Undo
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      class="absolute right-4 top-4 size-5"
    >
      <button
        class="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
        data-testid="shared-subs-undo-close"
        type="button"
      >
        <span
          class="sr-only"
        >
          Close
        </span>
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-xmark fill-current stroke-current text-green-800"
          data-icon="xmark"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 384 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;

exports[`SharedSubsUndoToast renders with a visible toast 1`] = `
<div
  class="pointer-events-auto relative w-full max-w-344 overflow-hidden rounded-lg bg-green-50 shadow-lg animate-enter"
>
  <div
    class="p-3 pr-12"
  >
    <div
      class="flex items-center"
    >
      <div
        class="flex w-0 flex-1 justify-between"
      >
        <div
          class="w-0 flex-1 text-sm font-medium text-green-800"
        >
          <div
            class="flex flex-col items-start"
          >
            <div
              class="p-1"
            >
              Notification dismissed.
            </div>
            <button
              class="mt-1 p-1 font-bold hover:opacity-50"
              data-testid="shared-subs-undo"
              type="button"
            >
              Undo
            </button>
          </div>
        </div>
      </div>
    </div>
    <div
      class="absolute right-4 top-4 size-5"
    >
      <button
        class="inline-flex size-5 rounded-md p-1 text-green-500 hover:text-green-600 hover:opacity-50 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2"
        data-testid="shared-subs-undo-close"
        type="button"
      >
        <span
          class="sr-only"
        >
          Close
        </span>
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-xmark fill-current stroke-current text-green-800"
          data-icon="xmark"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 384 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;
