import toast, { type Toast } from 'react-hot-toast';

import { listSharedSubscriptions } from 'util/auth/shared-subs';

import {
  SharedSubsReminderToast,
  SharedSubsUndoToast,
} from './SharedSubsToasts';

import type { PhoenixSharedSubscriptionListResponse } from 'types/phoenix-types/responses';

function registerSharedSubscriptionToast() {
  let checkedToken: string | undefined;
  let sharedSubscriptions: PhoenixSharedSubscriptionListResponse | undefined;

  function handleSharedSubscriptions(
    data: PhoenixSharedSubscriptionListResponse,
    firstName: string | undefined,
  ) {
    if (
      data.status !== 'found' ||
      data.sharedAccountLimit === data.sharedAccounts.length
    ) {
      return;
    }

    const remaining = data.sharedAccountLimit - data.sharedAccounts.length;

    toast.custom(
      (t: Toast) => (
        <SharedSubsReminderToast
          firstName={firstName}
          remaining={remaining}
          showDisabledToast={() => {
            toast.custom(
              (undoToast: Toast) => <SharedSubsUndoToast t={undoToast} />,
              {
                duration: 3000,
              },
            );
          }}
          t={t}
        />
      ),
      {
        duration: Infinity,
      },
    );
  }

  let attempts = 0;
  function checkSharedSubscriptionReminder() {
    if (!window.tp || !('pianoId' in window.tp)) {
      if (attempts >= 10) {
        // eslint-disable-next-line no-console
        console.warn(`Piano was not detected after ${attempts} attempts`);
        return;
      }

      attempts += 1;
      setTimeout(checkSharedSubscriptionReminder, 1000);
      return;
    }

    const accessToken = window.tp.pianoId.getToken();
    const firstName = window.tp.pianoId.getUser()?.firstName;
    if (!accessToken) {
      return;
    }

    if (checkedToken === accessToken && sharedSubscriptions !== undefined) {
      handleSharedSubscriptions(sharedSubscriptions, firstName);
      return;
    }

    checkedToken = accessToken;
    sharedSubscriptions = undefined;

    listSharedSubscriptions({
      accessToken,
    })
      .then((res) => {
        if (res.success) {
          sharedSubscriptions = res.data;
          handleSharedSubscriptions(res.data, firstName);
          return;
        }

        console.error(
          `List shared subscriptions failed: ${res.errors
            .map((e) => `${e.field ? e.field : 'General'}: ${e.message}`)
            .join('\n')}`,
        );
      })
      .catch((e) => {
        console.error(e);
      });
  }

  window.checkSharedSubscriptionReminder = checkSharedSubscriptionReminder;
}

export default function registerToastWindowCallbacks() {
  registerSharedSubscriptionToast();
}
