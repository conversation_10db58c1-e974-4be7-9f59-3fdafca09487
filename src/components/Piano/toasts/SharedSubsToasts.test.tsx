import {
  fireEvent,
  getByTestId,
  render,
  waitFor,
} from '@testing-library/react';
import toast from 'react-hot-toast';

import * as sharedSubs from 'util/auth/shared-subs';

import {
  SharedSubsReminderToast,
  SharedSubsUndoToast,
} from './SharedSubsToasts';

import type { onPianoReady } from '../ready';
import type { PianoTinypassInterface } from 'types/Piano';

const TEST_PIANO_TOKEN = 'test-token';
const TEST_TOAST_ID = 'test-id';

jest.mock(
  '../ready',
  () =>
    ({
      onPianoReady: (cb) => {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        cb({
          pianoId: {
            getToken: () => TEST_PIANO_TOKEN,
          },
        } as PianoTinypassInterface);
      },
    }) satisfies {
      onPianoReady: typeof onPianoReady;
    },
);

jest.mock(
  'util/auth/shared-subs',
  () =>
    ({
      disableSharedReminder: jest.fn(),
    }) satisfies {
      disableSharedReminder: typeof sharedSubs.disableSharedReminder;
    },
);

const mockedSharedSubs = sharedSubs as jest.Mocked<typeof sharedSubs>;
mockedSharedSubs.disableSharedReminder.mockResolvedValue({
  data: null,
  statusCode: 200,
  success: true,
});

jest.mock('react-hot-toast');

beforeEach(() => {
  jest.clearAllMocks();
});

describe('SharedSubsReminderToast', () => {
  it('renders with a visible toast', () => {
    expect.assertions(1);
    const { container } = render(
      <SharedSubsReminderToast
        firstName="Test Name"
        remaining={5}
        showDisabledToast={() => {}}
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with a non-visible toast', () => {
    expect.assertions(1);
    const { container } = render(
      <SharedSubsReminderToast
        firstName="Test Name"
        remaining={5}
        showDisabledToast={() => {}}
        t={{
          id: TEST_TOAST_ID,
          visible: false,
        }}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with a name and 5 remaining', () => {
    expect.assertions(1);
    const { container } = render(
      <SharedSubsReminderToast
        firstName="Test Name"
        remaining={5}
        showDisabledToast={() => {}}
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders without a name and 5 remaining', () => {
    expect.assertions(1);
    const { container } = render(
      <SharedSubsReminderToast
        firstName={undefined}
        remaining={5}
        showDisabledToast={() => {}}
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with a name and 1 remaining', () => {
    expect.assertions(1);
    const { container } = render(
      <SharedSubsReminderToast
        firstName="Test Name"
        remaining={1}
        showDisabledToast={() => {}}
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders without a name and 1 remaining', () => {
    expect.assertions(1);
    const { container } = render(
      <SharedSubsReminderToast
        firstName={undefined}
        remaining={1}
        showDisabledToast={() => {}}
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('attempts to disable reminders', async () => {
    expect.assertions(3);
    const showDisabledToast = jest.fn();

    const { container } = render(
      <SharedSubsReminderToast
        firstName={undefined}
        remaining={1}
        showDisabledToast={showDisabledToast}
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    fireEvent.click(getByTestId(container, 'shared-subs-dont-show-again'));

    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(mockedSharedSubs.disableSharedReminder).toHaveBeenCalledWith({
      accessToken: TEST_PIANO_TOKEN,
      disable: true,
    });
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(toast.dismiss).toHaveBeenCalledWith(TEST_TOAST_ID);
    await waitFor(() => {
      if (showDisabledToast.mock.calls.length === 0) {
        throw new Error('mock not called yet');
      }
    });
    expect(showDisabledToast).toHaveBeenCalled();
  });

  it('dismisses toast on clicking manage seats link', () => {
    expect.assertions(1);

    const { container } = render(
      <SharedSubsReminderToast
        firstName={undefined}
        remaining={1}
        showDisabledToast={() => {}}
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    fireEvent.click(getByTestId(container, 'shared-subs-manage-seats'));

    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(toast.dismiss).toHaveBeenCalledWith(TEST_TOAST_ID);
  });

  it('dismisses toast on clicking the close icon', () => {
    expect.assertions(1);

    const { container } = render(
      <SharedSubsReminderToast
        firstName={undefined}
        remaining={1}
        showDisabledToast={() => {}}
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    fireEvent.click(getByTestId(container, 'shared-subs-close'));

    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(toast.dismiss).toHaveBeenCalledWith(TEST_TOAST_ID);
  });
});

describe('SharedSubsUndoToast', () => {
  it('renders with a visible toast', () => {
    expect.assertions(1);
    const { container } = render(
      <SharedSubsUndoToast
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with a non-visible toast', () => {
    expect.assertions(1);
    const { container } = render(
      <SharedSubsUndoToast
        t={{
          id: TEST_TOAST_ID,
          visible: false,
        }}
      />,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('attempts to disable reminders', () => {
    expect.assertions(2);

    const { container } = render(
      <SharedSubsUndoToast
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    fireEvent.click(getByTestId(container, 'shared-subs-undo'));

    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(mockedSharedSubs.disableSharedReminder).toHaveBeenCalledWith({
      accessToken: TEST_PIANO_TOKEN,
      disable: false,
    });
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(toast.dismiss).toHaveBeenCalledWith(TEST_TOAST_ID);
  });

  it('dismisses toast on clicking the close icon', () => {
    expect.assertions(1);

    const { container } = render(
      <SharedSubsUndoToast
        t={{
          id: TEST_TOAST_ID,
          visible: true,
        }}
      />,
    );

    fireEvent.click(getByTestId(container, 'shared-subs-undo-close'));

    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(toast.dismiss).toHaveBeenCalledWith(TEST_TOAST_ID);
  });
});
