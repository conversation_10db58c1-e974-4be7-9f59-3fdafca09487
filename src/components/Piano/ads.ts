'use client';

import { useAppSelector } from 'store/hooks';
import { getCookie, removeCookie, setCookie } from 'util/cookies';
import { setGtmDataLayer } from 'util/gtm/index';
import { fetchAdPublisherProvidedId } from 'util/organization/suzuka';

const PUBLISHER_PROVIDED_ID_KEY = 'publisher_provided_id';

function setAdPublisherProvidedId(id: string): void {
  const store = window.getStore();
  const { googleTagManager } = store.getState().features;
  if (googleTagManager.enabled) {
    window.googletag = window.googletag || { cmd: [] };
    googletag.cmd.push(() => {
      googletag.pubads().setPublisherProvidedId(id);
    });
    setGtmDataLayer({
      ppid: id,
    });
  }
}

function fetchAndSetAdPublisherProvidedId(id: string) {
  fetchAdPublisherProvidedId(id)
    .then((res) => {
      let { ppid } = res;
      if (id === 'anonymous') {
        ppid = `anonymous${ppid}`;
      }
      setAdPublisherProvidedId(ppid);
      setCookie({
        cookieName: PUBLISHER_PROVIDED_ID_KEY,
        cookiePath: '/',
        cookieValue: ppid,
        expireSeconds: 7 * 24 * 60 * 60,
      });
    })
    .catch(console.error);
}

function checkIfUserIsChanged(ppid: string, id: string): boolean {
  return (
    (ppid.startsWith('anonymous') && id !== 'anonymous') ||
    (!ppid.startsWith('anonymous') && id === 'anonymous')
  );
}

export function fetchAndSetAdPublisherProvidedIdProc(id: string): void {
  const ppid = getCookie(PUBLISHER_PROVIDED_ID_KEY);
  if (ppid) {
    if (checkIfUserIsChanged(ppid, id)) {
      removeCookie(PUBLISHER_PROVIDED_ID_KEY);
      fetchAndSetAdPublisherProvidedId(id);
    } else {
      setAdPublisherProvidedId(ppid);
    }
  } else {
    fetchAndSetAdPublisherProvidedId(id);
  }
}

export function useAdPublisherProvidedId(): string | null {
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  return isClientSide ? getCookie(PUBLISHER_PROVIDED_ID_KEY) : null;
}
