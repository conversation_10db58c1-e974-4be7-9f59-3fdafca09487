import clsx from 'clsx';
import toast, { type Toast } from 'react-hot-toast';

import Link from 'themes/autumn/components/generic/Link';
import { userUpdate } from 'util/auth';

import { onPianoReady } from './ready';

export default function showCompleteProfileReminder() {
  toast.custom(
    (t: Toast) => (
      <div
        className={clsx(
          'pointer-events-auto overflow-hidden rounded-lg bg-green-50 text-sm font-medium shadow-lg',
          {
            'animate-enter': t.visible,
            'animate-leave': !t.visible,
          },
        )}
      >
        <div className="flex flex-row justify-start gap-x-3 p-4">
          <svg fill="none" height={20} viewBox="0 0 20 20" width={20}>
            <path
              clipRule="evenodd"
              d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.707-9.293a1 1 0 0 0-1.414-1.414L9 10.586 7.707 9.293a1 1 0 0 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0l4-4Z"
              fill="#34D399"
              fillRule="evenodd"
            />
          </svg>

          <div className="flex flex-col gap-y-2">
            <div className="text-green-800">
              <Link
                className="text-green-900 underline decoration-green-800 underline-offset-2 visited:text-green-900 hover:opacity-50"
                href="/complete-profile/"
                noStyle
              >
                Click here
              </Link>{' '}
              to complete your profile
            </div>
            <div className="flex gap-x-4 pr-2 text-xs text-green-900">
              <button
                className="opacity-100 hover:opacity-50"
                onClick={() => toast.dismiss(t.id)}
                type="button"
              >
                Close
              </button>
              <button
                className="opacity-100 hover:opacity-50"
                onClick={() => {
                  toast.dismiss(t.id);
                  onPianoReady((tp) => {
                    const accessToken = tp.pianoId.getToken();
                    if (!accessToken) {
                      console.error('Unable to get user token');
                      return;
                    }

                    userUpdate({
                      accessToken,
                      dismissCompleteProfileReminder: true,
                    }).catch((e) => console.error(e));
                  });
                }}
                type="button"
              >
                Don&apos;t ask again
              </button>
            </div>
          </div>
        </div>
      </div>
    ),
    {
      duration: Infinity,
    },
  );
}
