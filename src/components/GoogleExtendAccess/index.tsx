'use client';

import Script from 'next/script';
import React, { useEffect, useState } from 'react';
import { useCookies } from 'react-cookie';

import { pianoLogin, pianoLoginByToken } from 'components/Piano/auth';
import { fetchSocialAuthToken } from 'components/Piano/googleExtendedAccess';
import { pianoApiOrigins } from 'components/Piano/pipeline';
import { getPianoReady, onPianoReady } from 'components/Piano/ready';
import { useAppSelector } from 'store/hooks';
import googleExtendedAccessSlice from 'store/slices/googleExtendedAccess';

import StructuredDataMarkup from './markup';
import { debugGaaLog as debugLog, getPublicationMainDomain } from './utils';

import type { PianoSocialAuthResponse } from 'types/Piano';
import type {
  GaaInit,
  GaaUser,
  LoginModalCloseRequest,
  UserState,
  UserStateProps,
} from 'types/google-extended-access';

enum CookiesType {
  GoogleExtendedAccess = '__google-extended-access',
}

const setInitialized = (state: boolean) =>
  window
    .getStore()
    .dispatch(googleExtendedAccessSlice.actions.setInitialized(state));

const getTags = (): string[] => {
  const store = window.getStore();
  const state = store.getState();
  const { tags } = state.googleExtendAccess;
  return tags.split(',');
};

const isArticleFree = (tags: string[]): boolean => tags.includes('free');

const isArticleSubscriberOnly = (tags: string[]): boolean =>
  tags.includes('subscriber-only');

const isNewOrLinkedUser = (res: PianoSocialAuthResponse): boolean =>
  // https://docs.piano.io/piano-id-api/#social_login
  // ok -> response contains "auth_token";
  //       new or linked(social account) users

  res.status === 'ok';

const isLinkingRequired = (res: PianoSocialAuthResponse): boolean =>
  // https://docs.piano.io/piano-id-api/#social_login
  // confirm -> linking of 2 account,
  //            confirmed by user by entering password
  res.status === 'confirm';

async function loginByToken(oauthCode: string): Promise<void> {
  const tp = await getPianoReady();
  if (tp.pianoId.isUserValid()) {
    return Promise.resolve();
  }
  return fetchSocialAuthToken(oauthCode)
    .then((res) => {
      if (isNewOrLinkedUser(res)) {
        debugLog('isNewOrLinkedUser');
        pianoLoginByToken(res.access_token as string);
      } else if (isLinkingRequired(res)) {
        debugLog('isLinkingRequired');
        // TODO: Link the google account and piano account
        // once Piano provides their fix
        console.error('Piano doc missing to link account');
      } else {
        throw new Error('Unexpected API Response for Piano Social Login');
      }
    })
    .catch(console.error);
}

const getUserStateProps = (): UserStateProps => {
  const store = window.getStore();
  const state = store.getState();
  const { conversions, hasActiveMeter, hasExpiredMeter, user, userProfile } =
    state.piano;
  const props: UserStateProps = {
    conversions,
    isMeterActive: hasActiveMeter,
    isMeterExpired: hasExpiredMeter,
    user,
    userProfile,
  };
  debugLog(`userStateProps : ${JSON.stringify(props)}`);
  return props;
};

const getUserStateForFreeAndMetering = (
  isMeterActive: boolean,
  isMeterExpired: boolean,
): UserState => {
  const userState: UserState = { granted: false };

  if (isArticleFree(getTags())) {
    userState.granted = true;
    userState.grantReason = 'FREE';
    return userState;
  }

  userState.granted =
    isMeterExpired || isArticleSubscriberOnly(getTags())
      ? false
      : isMeterActive;
  if (userState.granted) {
    userState.grantReason = 'METERING';
  }
  return userState;
};

const getAnonymousUserState = ({
  isMeterActive,
  isMeterExpired,
}: UserStateProps): UserState =>
  getUserStateForFreeAndMetering(isMeterActive, isMeterExpired);

const getRegisteredUserState = ({
  conversions,
  isMeterActive,
  isMeterExpired,
  user,
  userProfile,
}: UserStateProps): UserState => {
  const userState = getUserStateForFreeAndMetering(
    isMeterActive,
    isMeterExpired,
  );
  userState.id = user?.sub;

  if (userProfile !== null && userProfile.create_date) {
    userState.registrationTimestamp = userProfile.create_date;
  }
  const conversion = conversions.length
    ? conversions.filter((item) => item.user_access.granted === true)
    : [];
  if (conversion.length) {
    userState.subscriptionTimestamp = conversion[0].user_access.start_date;
    userState.granted = true;
    userState.grantReason = 'SUBSCRIBER';
  }
  return userState;
};

const getUserState = (props: UserStateProps): UserState =>
  props.user === null
    ? getAnonymousUserState(props)
    : getRegisteredUserState(props);

const showPaywall = () => {
  debugLog('showPaywall');
};

const unlockArticle = () => {
  const userState = getUserState(getUserStateProps());
  if (!userState.granted) {
    debugLog('unlockArticle');
    const cssClasses = [
      'has-metered-banner',
      'has-registered-metered-banner',
      'has-subscribe-paywall-banner',
      'overflow-hidden',
    ];
    window.afterPaywallRemoved();
    document.body.classList.remove(...cssClasses);
    onPianoReady((tp) => {
      tp.offer.closeInline('#metered-banner');
    });
  } else {
    debugLog('article already unlocked');
  }
};

// eslint-disable-next-line @stylistic/max-len
// https://docs.piano.io/faq-article/how-to-redirect-a-user-when-they-try-to-close-the-login-screen/
const handleOnLoginScreenClose = (
  data: LoginModalCloseRequest,
): React.ReactElement | null => {
  if (data.sender.startsWith('piano-id') && data.event === 'closed') {
    debugLog('handleOnLoginScreenClose');
    setInitialized(false);
    window.location.reload();
  }
  return null;
};

function setupCallbackOnClose(): void {
  window.addEventListener('message', (event) => {
    if (!pianoApiOrigins.includes(event.origin)) {
      return;
    }

    if (typeof event.data === 'string') {
      try {
        const data = JSON.parse(event.data) as LoginModalCloseRequest;
        if (data?.sender) {
          handleOnLoginScreenClose(data);
        }
      } catch (e) {
        console.error(e);
      }
    }
  });
}

const handleLoginPromise = () =>
  new Promise<void>(() => {
    debugLog('handleLoginPromise');
    window.GaaMetering.getLoginPromise()
      .then(() => {
        debugLog('getLoginPromise');
        pianoLogin(true);
        window.GaaMeteringRegwall.remove();
        setupCallbackOnClose();
        // Resolve not required as login reloads the page
      })
      .catch(console.error);
  });

const registerUserPromise = () =>
  new Promise<void>((resolve) => {
    // Get the information for the user who has just registered.
    window.GaaMetering.getGaaUserPromise()
      .then((gaaUser: GaaUser) => {
        // With 3P Authentication, Piano handles user registration
        // after Google authentication making this process irrelevant
        debugLog('registerUserPromise');
        debugLog(`gaaUser ${JSON.stringify(gaaUser)}`);
        resolve();
      })
      .catch(console.error);
  });

const publisherEntitlementPromise = () =>
  new Promise<UserState>((resolve) => {
    debugLog('publisherEntitlementPromise');
    const store = window.getStore();
    const state = store.getState();
    const { isGoogleExtendedAccessArticle } = state.story;

    if (!isGoogleExtendedAccessArticle) {
      resolve({ granted: false });
      return;
    }

    const userState = getUserState(getUserStateProps());
    debugLog(
      `publisherEntitlementPromise userState : ${JSON.stringify(userState)}`,
    );
    resolve(userState);
  });

const getInitParams = (): GaaInit | undefined => {
  debugLog('getInitParams called');
  const store = window.getStore();
  const state = store.getState();
  const { domain } = state.conf;
  const authorizationUrl = state.piano.socialAuthorizationUrl;
  const googleExtendAccessFeature = state.features.googleExtendedAccess;

  const userState = getUserState(getUserStateProps());

  debugLog('Loaded');
  debugLog(`userState : ${JSON.stringify(userState)}`);
  debugLog(`authorizationUrl : ${JSON.stringify(authorizationUrl)}`);

  if (!googleExtendAccessFeature.enabled) {
    return undefined;
  }

  const { clientId } = googleExtendAccessFeature.data;

  const initParams = {
    allowedReferrers: [domain] as [string],
    handleLoginPromise: handleLoginPromise(),
    publisherEntitlementPromise: publisherEntitlementPromise(),
    registerUserPromise: registerUserPromise(),
    showPaywall,
    unlockArticle,
    userState,
    ...(authorizationUrl
      ? { authorizationUrl }
      : { googleApiClientId: clientId }),
  };

  debugLog(`initParams : ${JSON.stringify(initParams)}`);
  return initParams;
};

const initialize = () => {
  debugLog('Initialize called');
  if (window.GaaMetering === undefined) {
    setTimeout(() => initialize(), 300);
    debugLog(' Re-initializing');
    return;
  }

  const initParams = getInitParams();
  if (initParams) {
    const initState = window.GaaMetering.init(initParams);
    debugLog(
      `GaaMetering ${
        initState === undefined ? 'is Initialized' : 'has Error'
      } `,
    );
  }
};

const boolToString = (bool: boolean) => (bool ? 'true' : 'false');

export default function GoogleExtendAccess(): React.ReactElement | null {
  const oauthCode = useAppSelector(
    (state) => state.googleExtendAccess.oauthCode,
  );
  const gooogleEAInitialized = useAppSelector(
    (state) => state.googleExtendAccess.initialized,
  );
  const socialAuthorizationUrl = useAppSelector(
    (state) => state.piano.socialAuthorizationUrl,
  );
  const { domain, name } = useAppSelector((state) => state.conf);
  const headline = useAppSelector((state) => state.story.title);
  const datePublished = useAppSelector((state) => state.story.publishFrom);
  const dateModified = useAppSelector((state) => state.story.updatedOn);

  const [cookie, setCookie] = useCookies();

  const [loaded, setLoaded] = useState(false);
  debugLog(`Initialized : ${boolToString(gooogleEAInitialized)}`);

  useEffect(() => {
    if (
      !loaded ||
      gooogleEAInitialized ||
      !(oauthCode || socialAuthorizationUrl)
    ) {
      return;
    }
    const setup = async () => {
      debugLog('setup called');
      const { search } = window.location;
      const searchParams = new URLSearchParams(search);
      const gaNonce = searchParams.get('gaa_n') as string;
      const gaTimestamp = parseInt(searchParams.get('gaa_ts') as string, 16);
      if (cookie[CookiesType.GoogleExtendedAccess] !== gaNonce) {
        setCookie(CookiesType.GoogleExtendedAccess, gaNonce, {
          expires: new Date(gaTimestamp * 1000),
        });
      }
      const store = window.getStore();
      const tp = await getPianoReady();
      store.dispatch(
        googleExtendedAccessSlice.actions.setTags(tp.tags as string),
      );
      initialize();
      setInitialized(true);
    };
    if (oauthCode) {
      debugLog('called with oauthCode');
      loginByToken(oauthCode)
        .then(() => {
          setInitialized(true);
        })
        .catch(console.error);
    } else {
      setTimeout(() => {
        setup().catch(console.error);
      }, 2000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gooogleEAInitialized, loaded, oauthCode, socialAuthorizationUrl]);

  return (
    <>
      <StructuredDataMarkup
        dateModified={dateModified}
        datePublished={datePublished}
        headline={headline}
        isAccessibleForFree={isArticleFree(getTags())}
        name={name}
        productID={getPublicationMainDomain(domain)}
      />
      <Script
        async
        id="google-swg"
        src="https://news.google.com/swg/js/v1/swg.js"
        subscriptions-control="manual"
      />
      <Script
        async
        id="google-swg-gaa"
        onReady={() => {
          debugLog(' on load ');
          setLoaded(true);
        }}
        src="https://news.google.com/swg/js/v1/swg-gaa.js"
      />
      <Script
        async
        id="google-gsi-client"
        src="https://accounts.google.com/gsi/client"
      />
    </>
  );
}
