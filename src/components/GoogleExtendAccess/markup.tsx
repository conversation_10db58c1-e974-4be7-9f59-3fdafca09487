import Script from 'next/script';

import type { MarkupProps } from 'types/google-extended-access';

export default function StructuredDataMarkup({
  dateModified,
  datePublished,
  headline,
  isAccessibleForFree,
  name,
  productID,
}: MarkupProps): React.ReactElement {
  const markup = {
    '@context': 'https://schema.org',
    '@type': 'NewsArticle',
    dateModified,
    datePublished,
    headline,
    isAccessibleForFree,
    isPartOf: {
      '@type': ['CreativeWork', 'Product'],
      name,
      productID,
    },
    publisher: {
      '@type': 'Organization',
      name,
    },
  };

  return (
    <Script
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(markup),
      }}
      id="google-extended-access-markup"
      type="application/ld+json"
    />
  );
}
