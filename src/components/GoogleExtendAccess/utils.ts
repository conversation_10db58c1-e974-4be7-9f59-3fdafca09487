// TODO: Remove it after testing and sending videos to Google
const DailyAdvertiserDomain = 'dailyadvertiser.com.au:showcase';

export const debugGaaLog = (message: string) => {
  const store = window.getStore();
  const state = store.getState();
  const isSandbox = (state.settings.pianoCdnUrl ?? '')
    .toLowerCase()
    .includes('sandbox');
  if (isSandbox) {
    // eslint-disable-next-line no-console
    console.log('[GoogleEA] ', message);
  }
};

/**
 *
 * Ignore the 'wwww', 'beta', etc designated for extended access
 * (for example 'www.example.com.au' => 'example.com.au')
 *
 * Note: ACM domains follow -> [www|beta|...].domain.[com|net|...].au
 * Any change in domain naming should require an update in the function.
 *
 * @param domain string
 * @returns string
 */
export const getPublicationMainDomain = (domain: string): string =>
  DailyAdvertiserDomain ||
  (domain.split('.').length === 4
    ? `${domain.split('.').slice(1).join('.')}:showcase`
    : `${domain}:showcase`);
