'use client';

import Script from 'next/script';

import { useAppSelector } from 'store/hooks';

export default function RoyMorganAnalytics(): React.ReactElement | null {
  const timestamp = useAppSelector((state) => state.conf.timestamp);
  const feature = useAppSelector((state) => state.features.royMorganAnalytics);

  if (!feature.enabled) return null;

  const {
    clientId,
    clientId2,
    clientId3,
    publisherId,
    publisherId2,
    publisherId3,
    websiteId,
    websiteId2,
    websiteId3,
  } = feature.data;

  const urlBase = 'https://pixel.roymorgan.com/stats_v2/Tress.php';

  const cacheBuster = timestamp;

  const royMorganUrlIndividual =
    !!clientId && !!publisherId && !!websiteId
      ? // eslint-disable-next-line @stylistic/max-len
        `${urlBase}?u=${clientId}&ca=${websiteId}&a=${publisherId}&cb=${cacheBuster}`
      : undefined;
  const royMorganUrlGroup =
    !!clientId2 && !!publisherId2 && !!websiteId2
      ? // eslint-disable-next-line @stylistic/max-len
        `${urlBase}?u=${clientId2}&ca=${websiteId2}&a=${publisherId2}&cb=${cacheBuster}`
      : undefined;

  const royMorganUrlNetwork =
    !!clientId3 && !!publisherId3 && !!websiteId3
      ? // eslint-disable-next-line @stylistic/max-len
        `${urlBase}?u=${clientId3}&ca=${websiteId3}&a=${publisherId3}&cb=${cacheBuster}`
      : undefined;

  return (
    <>
      {royMorganUrlIndividual && (
        <Script
          async
          id="roymorgan-pixel-individual"
          src={royMorganUrlIndividual}
          strategy="lazyOnload"
        />
      )}
      {royMorganUrlGroup && (
        <Script
          async
          id="roymorgan-pixel-group"
          src={royMorganUrlGroup}
          strategy="lazyOnload"
        />
      )}
      {royMorganUrlNetwork && (
        <Script
          async
          id="roymorgan-pixel-network"
          src={royMorganUrlNetwork}
          strategy="lazyOnload"
        />
      )}
    </>
  );
}
