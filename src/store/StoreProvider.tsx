'use client';

import { useRef } from 'react';
import { Provider } from 'react-redux';

import { createStore, hydrateAction } from './store';

import type { AppStore, RootState } from './store';

export default function StoreProvider({
  children,
  context,
}: {
  children: React.ReactNode;
  context: Partial<RootState>;
}) {
  const storeRef = useRef<AppStore>(undefined);
  if (!storeRef.current) {
    // Create the store instance and hydrate it the first time this renders
    storeRef.current = createStore();
    storeRef.current.dispatch(hydrateAction(context));

    // Using typeof window instead of a useEffect hook to guarantee
    // that it executes before functions that would require this fn
    // to exist for access to the store. Safe as it does not affect
    // the DOM so cannot cause SSR desync
    // eslint-disable-next-line rulesdir/no-typeof-window-outside-useeffect
    if (typeof window !== 'undefined') {
      window.getStore = () => storeRef.current as AppStore;
    }
  }

  return <Provider store={storeRef.current}>{children}</Provider>;
}
