import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';

interface AdServingState {
  hasBottomAnchorAd: boolean;
  isAmazonPublisherServicesReady: boolean;
}

const initialState: AdServingState = {
  hasBottomAnchorAd: false,
  isAmazonPublisherServicesReady: false,
};

const adServingSlice = createSlice({
  initialState,
  name: 'adServing',
  reducers: {
    setAmazonPublisherServicesReady: (state) => {
      state.isAmazonPublisherServicesReady = true;
    },
    setBottomAnchorAd: (state, action: PayloadAction<boolean>) => {
      state.hasBottomAnchorAd = action.payload;
    },
  },
});

export default adServingSlice;
