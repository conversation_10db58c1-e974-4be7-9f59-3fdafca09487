import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';

interface RuntimeState {
  isClientSide: boolean;
}

const initialState: RuntimeState = {
  isClientSide: false,
};

const runtimeSlice = createSlice({
  initialState,
  name: 'isClientSide',
  reducers: {
    setIsClientSide: (state, action: PayloadAction<boolean>) => {
      state.isClientSide = action.payload;
    },
  },
});

export default runtimeSlice;
