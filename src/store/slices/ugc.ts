import { createSlice } from '@reduxjs/toolkit';

import type { RecirculationSectionType } from 'types/Story';
import type { UGC } from 'types/ugc';

export interface UgcState {
  recirculationSections?: RecirculationSectionType[];
  ugcDetail: UGC | null;
}

const initialState: UgcState = {
  recirculationSections: undefined,
  ugcDetail: null,
};

const UgcSlice = createSlice({
  initialState,
  name: 'ugc',
  reducers: {},
});

export default UgcSlice;
