import { createSlice } from '@reduxjs/toolkit';

import { ZoneItemType } from 'types/ZoneItems';

import type { PayloadAction } from '@reduxjs/toolkit';
import type { SwapStoriesProps, SwapZoneItemProps } from 'types/DragItems';
import type { Story } from 'types/Story';
import type {
  ClusteredStoryListZoneItem,
  StoryListZoneItem,
  TitledStoryListZoneItem,
  ZoneItemBase,
  ZoneItemGroups,
} from 'types/ZoneItems';

type AnyStoryListZoneItem =
  | ClusteredStoryListZoneItem
  | StoryListZoneItem
  | TitledStoryListZoneItem;

export interface UpdatedStories {
  pinnedStoryIds: string[];
  stories: Story[];
  storyListId: number;
}

interface UpdateZoneItemAction {
  data: Record<string, unknown>;
  elementId: number;
  newId: number;
  zoneItemId: number;
}

interface DeleteZoneItemAction {
  zoneItemId: number;
}

function isStoryListZoneItem(
  zoneItem: ZoneItemBase<unknown>,
): zoneItem is AnyStoryListZoneItem {
  return (
    zoneItem.zoneItemType === ZoneItemType.ClusteredStoryList ||
    zoneItem.zoneItemType === ZoneItemType.StoryList ||
    zoneItem.zoneItemType === ZoneItemType.TitledStoryList
  );
}

const zoneItemsSlice = createSlice({
  initialState: {
    global: [],
    page: [],
  } as ZoneItemGroups,
  name: 'zoneItems',
  reducers: {
    addZoneItem: (state, action: PayloadAction<ZoneItemBase<unknown>>) => {
      state.page.push(action.payload);
    },
    deleteZoneItem: (state, action: PayloadAction<DeleteZoneItemAction>) => {
      Object.entries(state).forEach(([section, zoneItems]) => {
        state[section as keyof ZoneItemGroups] = zoneItems.filter(
          (zoneItem) => zoneItem.zoneItemId !== action.payload.zoneItemId,
        );
      });
    },
    moveStoryInStoryList: (state, action: PayloadAction<SwapStoriesProps>) => {
      Object.entries(state).forEach(([, zoneItems]) => {
        zoneItems.forEach((state_item) => {
          if (
            isStoryListZoneItem(state_item) &&
            state_item.zoneItemId === action.payload.zoneItemId
          ) {
            const dragStory =
              state_item.zoneItemData.stories[action.payload.srcIndex];
            const { stories } = state_item.zoneItemData;
            stories.splice(action.payload.srcIndex, 1);
            stories.splice(action.payload.destIndex, 0, {
              ...dragStory,
              isPinned: true,
            });
          }
        });
      });
    },
    moveZoneItems: (state, action: PayloadAction<SwapZoneItemProps>) => {
      const dragZoneItem = state.page[action.payload.srcIndex];
      const { page } = state;
      page.splice(action.payload.srcIndex, 1);
      page.splice(action.payload.destIndex, 0, dragZoneItem);
    },
    updateStoryList: (state, action: PayloadAction<UpdatedStories>) =>
      Object.entries(state).forEach(([, zoneItems]) => {
        zoneItems.forEach((state_item) => {
          if (
            (state_item.zoneItemType === ZoneItemType.StoryList ||
              state_item.zoneItemType === ZoneItemType.ClusteredStoryList ||
              state_item.zoneItemType === ZoneItemType.TitledStoryList) &&
            state_item.zoneItemData.storyListId === action.payload.storyListId
          ) {
            state_item.zoneItemData.pinnedStoryIds =
              action.payload.pinnedStoryIds;
            if (state_item.zoneItemData.pinnedStoriesOnly) {
              state_item.zoneItemData.stories = action.payload.stories.filter(
                (story) => action.payload.pinnedStoryIds.includes(story.id),
              );
            } else {
              state_item.zoneItemData.stories = action.payload.stories;
            }
          }
        });
      }),
    updateZoneItem: (state, action: PayloadAction<UpdateZoneItemAction>) => {
      Object.entries(state).forEach(([, zoneItems]) => {
        zoneItems.forEach((zoneItem) => {
          if (zoneItem.zoneItemId === action.payload.zoneItemId) {
            /* eslint-disable no-param-reassign */
            zoneItem.zoneItemId = action.payload.newId;
            zoneItem.elementId = action.payload.elementId;
            zoneItem.zoneItemData = {
              ...zoneItem.zoneItemData,
              ...action.payload.data,
            };
            /* eslint-enable no-param-reassign */
          }
        });
      });
    },
  },
});

export default zoneItemsSlice;
