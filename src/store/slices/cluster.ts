import { createSlice } from '@reduxjs/toolkit';

export interface ClusterSite {
  domain: string;
  logoSvgOnly?: string;
  logoSvgSquare?: string;
  name: string;
  promoted?: boolean;
}

export interface ClusterState {
  clusterName: string;
  content: string;
  sites: ClusterSite[];
}

const initialState: ClusterState = {
  clusterName: '',
  content: '',
  sites: [],
};

const clusterSlice = createSlice({
  initialState,
  name: 'cluster',
  reducers: {},
});

export default clusterSlice;
