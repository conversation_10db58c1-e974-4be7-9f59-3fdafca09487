import { createSlice } from '@reduxjs/toolkit';

import type { StoryAuthor } from 'types/Story';

const initialState: StoryAuthor = {
  bio: '',
  facebook: '',
  id: 0,
  instagram: '',
  mugshot: '',
  name: '',
  position: '',
  tiktok: '',
  twitter: '',
  web: '',
  youtube: '',
};

const authorSlice = createSlice({
  initialState,
  name: 'Author',
  reducers: {},
});

export default authorSlice;
