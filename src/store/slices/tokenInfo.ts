import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';

interface TokenInfoOrganization {
  membership: {
    isActive: boolean;
    isAdmin: boolean;
    isDefault: boolean;
  };
  organization: {
    external: boolean;
    id: number;
    name: string;
  };
  role: string;
}

export interface TokenInfoState {
  application: number;
  expires: string;
  id: number;
  organizations: TokenInfoOrganization[];
  scope: string;
  sourceRefreshToken: number | null;
  token: string;
  user?: {
    dateJoined: string;
    email: string;
    firstName: string;
    groups: string[];
    id: number;
    isActive: boolean;
    isStaff: boolean;
    isSuperuser: boolean;
    lastLogin: string;
    lastName: string;
    password: string;
    userPermissions: string[];
    username: string;
  };
}

const initialState: TokenInfoState = {
  application: -1,
  expires: '',
  id: -1,
  organizations: [],
  scope: '',
  sourceRefreshToken: null,
  token: '',
};

const tokenInfoSlice = createSlice({
  initialState,
  name: 'debug',
  reducers: {
    set: (state, action: PayloadAction<TokenInfoState>) => action.payload,
  },
});

export default tokenInfoSlice;
