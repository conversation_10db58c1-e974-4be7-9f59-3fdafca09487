import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';

export interface YieldIntelligenceState {
  isTimeout: boolean;
}

export const initialState: YieldIntelligenceState = {
  isTimeout: false,
};

const yieldIntelligenceSlice = createSlice({
  initialState,
  name: 'yieldIntelligence',
  reducers: {
    setTimeout: (state, action: PayloadAction<boolean>) => {
      state.isTimeout = action.payload;
    },
  },
});

export default yieldIntelligenceSlice;
