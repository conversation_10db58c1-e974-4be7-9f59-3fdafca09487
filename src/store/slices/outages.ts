import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';
import type { Outage } from 'types/ZoneItems';

export interface OutageState {
  outages?: Outage[];
  visibleOutages: Outage[];
}

const initialState: OutageState = {
  visibleOutages: [],
};

const outagesSlice = createSlice({
  initialState,
  name: 'outages',
  reducers: {
    setVisible: (state, action: PayloadAction<Outage[]>) => {
      state.visibleOutages = action.payload;
    },
  },
});

export default outagesSlice;
