import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';

export interface GoogleExtendAccessState {
  initialized: boolean;
  oauthCode: string;
  tags: string;
}

export const initialState: GoogleExtendAccessState = {
  initialized: false,
  oauthCode: '',
  tags: '',
};

const googleExtendAccessSlice = createSlice({
  initialState,
  name: 'googleExtendAccess',
  reducers: {
    setInitialized: (state, action: PayloadAction<boolean>) => {
      state.initialized = action.payload;
    },
    setOauthCode: (state, action: PayloadAction<string>) => {
      state.oauthCode = action.payload;
    },
    setTags: (state, action: PayloadAction<string>) => {
      state.tags = action.payload;
    },
  },
});

export default googleExtendAccessSlice;
