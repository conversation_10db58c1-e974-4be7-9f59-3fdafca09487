import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';
import type {
  ClassifiedAd,
  ClassifiedAdCategory,
  StorySearchResult,
} from 'types/Classified';

export interface Category extends ClassifiedAdCategory {
  description: string;
  order: number;
  parent: number | null;
  showAds: boolean;
  showClusterAds: boolean;
  showInIndexPage: boolean;
  showInNavigation: boolean;
}

export interface ClassifiedsState {
  ad: ClassifiedAd | null;
  ads: {
    currentPage: number;
    data: ClassifiedAd[];
    nextPage: number | null;
    numPages: number;
    numPerPage: number | null;
    previousPage: number | null;
    totalNum: number;
  } | null;
  categories: Category[] | null;
  category: Category | null;
  cluster: { name: string; state: string } | null;
  clusterAds: {
    currentPage: number;
    data: ClassifiedAd[];
    nextPage: number | null;
    numPages: number;
    numPerPage: number | null;
    previousPage: number | null;
    totalNum: number;
  } | null;
  clusters: Record<string, string[]> | null;
  isMasonryLayout: boolean | undefined;
  primarySimilarAdsLen: number | null;
  query: string;
  sideBarAds: ClassifiedAd[] | null;
  similarAds: ClassifiedAd[] | null;
  stories?: StorySearchResult[];
  subcategory: Category | null;
  tooltipClicked: boolean;
}

const initialState: ClassifiedsState = {
  ad: null,
  ads: null,
  categories: [],
  category: null,
  cluster: null,
  clusterAds: null,
  clusters: null,
  isMasonryLayout: undefined,
  primarySimilarAdsLen: null,
  query: '',
  sideBarAds: null,
  similarAds: null,
  subcategory: null,
  tooltipClicked: false,
};

const classifiedsSlice = createSlice({
  initialState,
  name: 'classifieds',
  reducers: {
    setMasonryLayout: (state, action: PayloadAction<boolean>) => {
      state.isMasonryLayout = action.payload;
    },
    setTooltipClicked: (state) => {
      state.tooltipClicked = true;
    },
  },
});

export default classifiedsSlice;
