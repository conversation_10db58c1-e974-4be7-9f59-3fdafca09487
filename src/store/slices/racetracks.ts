import { createSlice } from '@reduxjs/toolkit';

export interface RacetracksSlice {
  monzaUrl: string;
  phoenixUrl: string;
  sepangUrl: string;
  silverstoneUrl: string;
  sochiUrl: string;
  suzukaUrl: string;
  transformUrl: string;
  valenciaUrl: string;
}

const initialState: RacetracksSlice = {
  monzaUrl: '',
  phoenixUrl: '',
  sepangUrl: '',
  silverstoneUrl: '',
  sochiUrl: '',
  suzukaUrl: '',
  transformUrl: '',
  valenciaUrl: '',
};

const racetracksSlice = createSlice({
  initialState,
  name: 'racetracks',
  reducers: {},
});

export default racetracksSlice;
