import { createSlice } from '@reduxjs/toolkit';

import { PaymentStatus } from '../../types/Payments';

import type { PayloadAction } from '@reduxjs/toolkit';
import type {
  PianoApiConversion,
  PianoLoginUserData,
  PianoUserProfileData,
  SimpleTerm,
} from 'types/Piano';

interface PaymentInfo {
  id?: string;
  status: PaymentStatus;
}

export enum PianoABTestingVariant {
  NONE = '',
  A = 'A',
  B = 'B',
  C = 'C',
  RECIRCULATION_A = 'Recirculation - A',
  RECIRCULATION_B = 'Recirculation - B',
  PIANO_RECIRCULATION_A = 'PianoRecirculation - A',
  PIANO_RECIRCULATION_B = 'PianoRecirculation - B',
}

export interface PianoState {
  conversions: PianoApiConversion[];
  featuresBehindPaywallEnabled: boolean;
  hasAccess: boolean;
  hasActiveMeter: boolean;
  hasCorporateAccess: boolean;
  hasDPEAccess: boolean;
  hasExpiredMeter: boolean;
  hasPaywall: boolean;
  hasPuzzlesAccess: boolean;
  hasSubscription: boolean;
  hasValidatedPremium: boolean;
  initialized: boolean;
  isMagicLink: boolean;
  isPremiumExtended?: boolean;
  isPremiumSubscription?: boolean;
  loadingPaywall: boolean;
  memberOrigin?: string;
  memberRole?: string;
  offerId: string;
  paymentStatus: PaymentInfo;
  pianoABTestingVariant: PianoABTestingVariant;
  socialAuthorizationUrl: string;
  subscribeButtonHref: string | undefined;
  subscribeButtonOverride: boolean;
  subscribeButtonStyle: string | undefined;
  subscribeButtonText: string | undefined;
  terms: (SimpleTerm | undefined)[];
  user: PianoLoginUserData | null;
  userProfile: PianoUserProfileData | null;
}

export const initialState: PianoState = {
  conversions: [],
  featuresBehindPaywallEnabled: false,
  hasAccess: false,
  hasActiveMeter: false,
  hasCorporateAccess: false,
  hasDPEAccess: false,
  hasExpiredMeter: false,
  hasPaywall: false,
  hasPuzzlesAccess: false,
  hasSubscription: false,
  hasValidatedPremium: false,
  initialized: false,
  isMagicLink: false,
  isPremiumExtended: undefined,
  isPremiumSubscription: undefined,
  loadingPaywall: true,
  memberOrigin: undefined,
  memberRole: undefined,
  offerId: '',
  paymentStatus: {
    status: PaymentStatus.UNKNOWN,
  },
  pianoABTestingVariant: PianoABTestingVariant.NONE,
  socialAuthorizationUrl: '',
  subscribeButtonHref: undefined,
  subscribeButtonOverride: false,
  subscribeButtonStyle: undefined,
  subscribeButtonText: undefined,
  terms: [],
  user: null,
  userProfile: null,
};

interface SetCorporateAccessPayload {
  hasAccess: boolean;
  hasCorporateAccess?: boolean;
}

interface SetEnterpriseMembersPayload {
  memberRole: string;
}

interface SetEnterpriseMemberOriginPayload {
  memberOrigin: string;
}

const pianoSlice = createSlice({
  initialState,
  name: 'settings',
  reducers: {
    login: (state, action: PayloadAction<PianoLoginUserData | null>) => {
      state.user = action.payload;
    },
    logout: () => ({
      ...initialState,
      initialized: true,
    }),
    setABTestingVariant: (
      state,
      action: PayloadAction<PianoABTestingVariant>,
    ) => {
      state.pianoABTestingVariant = action.payload;
    },
    setConversions: (state, action: PayloadAction<PianoApiConversion[]>) => {
      state.conversions = action.payload;
    },
    setEnableFeaturesBehindPaywall: (
      state,
      action: PayloadAction<boolean>,
    ) => {
      state.featuresBehindPaywallEnabled = action.payload;
    },
    setHasAccess: (
      state,
      action: PayloadAction<SetCorporateAccessPayload>,
    ) => {
      state.hasAccess = action.payload.hasAccess;
      if (action.payload.hasCorporateAccess !== undefined) {
        state.hasCorporateAccess = action.payload.hasCorporateAccess;
      }
    },
    setHasActiveMeter: (state, action: PayloadAction<boolean>) => {
      state.hasActiveMeter = action.payload;
    },
    setHasDPEAccess: (state, action: PayloadAction<boolean>) => {
      state.hasDPEAccess = action.payload;
    },
    setHasExpiredMeter: (state, action: PayloadAction<boolean>) => {
      state.hasExpiredMeter = action.payload;
    },
    setHasPuzzlesAccess: (state, action: PayloadAction<boolean>) => {
      state.hasPuzzlesAccess = action.payload;
    },
    setHasSubscription: (state, action: PayloadAction<boolean>) => {
      state.hasSubscription = action.payload;
    },
    setHasValidatedPremium: (state, action: PayloadAction<boolean>) => {
      state.hasValidatedPremium = action.payload;
    },
    setInitialized: (state) => {
      state.initialized = true;
    },
    setIsMagicLink(state, action: PayloadAction<boolean>) {
      state.isMagicLink = action.payload;
    },
    setMemberOrigin(
      state,
      action: PayloadAction<SetEnterpriseMemberOriginPayload>,
    ) {
      state.memberOrigin = action.payload.memberOrigin;
    },
    setMemberRole(state, action: PayloadAction<SetEnterpriseMembersPayload>) {
      state.memberRole = action.payload.memberRole;
    },
    setOfferId: (state, action: PayloadAction<string>) => {
      state.offerId = action.payload;
    },
    setPaymentStatus: (state, action: PayloadAction<PaymentInfo>) => {
      state.paymentStatus = action.payload;
    },
    setPaywall: (state, action: PayloadAction<boolean>) => {
      state.loadingPaywall = false;
      state.hasPaywall = action.payload;
    },
    setPremiumExtended: (state, action: PayloadAction<boolean>) => {
      state.isPremiumExtended = action.payload;
    },
    setPremiumSubscription: (state, action: PayloadAction<boolean>) => {
      state.isPremiumSubscription = action.payload;
    },
    setSocialLogin: (state, action: PayloadAction<string>) => {
      state.socialAuthorizationUrl = action.payload;
    },
    setSubscribeButtonHref: (state, action: PayloadAction<string>) => {
      state.subscribeButtonHref = action.payload;
    },
    setSubscribeButtonOverride: (state, action: PayloadAction<boolean>) => {
      state.subscribeButtonOverride = action.payload;
    },
    setSubscribeButtonStyle: (state, action: PayloadAction<string>) => {
      state.subscribeButtonStyle = action.payload;
    },
    setSubscribeButtonText: (state, action: PayloadAction<string>) => {
      state.subscribeButtonText = action.payload;
    },
    setTerms: (state, action: PayloadAction<SimpleTerm[]>) => {
      state.terms = action.payload;
    },
    setUserProfile: (
      state,
      action: PayloadAction<PianoUserProfileData | null>,
    ) => {
      state.userProfile = action.payload;
    },
  },
});

export default pianoSlice;
