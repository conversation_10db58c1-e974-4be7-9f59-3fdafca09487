import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';

export interface NavState {
  returnUrl?: {
    text: string;
    url: string;
  };
}

const initialState: NavState = {};

const navSlice = createSlice({
  initialState,
  name: 'nav',
  reducers: {
    setReturnUrl: (state, action: PayloadAction<NavState['returnUrl']>) => {
      state.returnUrl = action.payload;
    },
  },
});

export default navSlice;
