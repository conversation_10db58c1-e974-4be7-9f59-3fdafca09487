import { createSlice } from '@reduxjs/toolkit';

import type { AbExperiment } from 'types/ab-tests';

interface AbTestData {
  endDate: string;
  id: AbExperiment;
  startDate: string;
  variant: string;
}

export interface SettingsState {
  abTest: AbTestData | null;
  authorPianoUids: string[];
  editable: boolean;
  host: string;
  isSecure: boolean;
  pageId: number;
  pianoApiUrl: string;
  pianoCdnUrl: string;
  queryString: string;
  siteId: number;
  staticSiteUrl: string;
  staticUrl: string;
  transformUrl: string;
  userId: null;
  viewType: string;
}

const initialState: SettingsState = {
  abTest: null,
  authorPianoUids: [],
  editable: false,
  host: '',
  isSecure: false,
  pageId: 0,
  pianoApiUrl: '',
  pianoCdnUrl: '',
  queryString: '',
  siteId: 0,
  staticSiteUrl: '',
  staticUrl: '',
  transformUrl: '',
  userId: null,
  viewType: '',
};

const settingsSlice = createSlice({
  initialState,
  name: 'settings',
  reducers: {},
});

export default settingsSlice;
