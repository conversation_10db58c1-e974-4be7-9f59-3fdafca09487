import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';

export enum LayoutSize {
  MOBILE = 'Mobile',
  TABLET = 'Tablet',
  DESKTOP = 'Desktop',
}

export interface EditmodeState {
  isZoneView: boolean;
  layout: LayoutSize;
  modalCount: number;
  sidebar: {
    collapsed: boolean;
  };
}

const initialState: EditmodeState = {
  isZoneView: false,
  layout: LayoutSize.DESKTOP,
  modalCount: 0,
  sidebar: {
    collapsed: false,
  },
};

const editmodeSlice = createSlice({
  initialState,
  name: 'editmode',
  reducers: {
    decrementModalCount: (state) => {
      state.modalCount -= 1;
    },
    incrementModalCount: (state) => {
      state.modalCount += 1;
    },
    setCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebar.collapsed = action.payload;
    },
    setLayout: (state, action: PayloadAction<LayoutSize>) => {
      state.layout = action.payload;
    },
    toggleCollapsed: (state) => {
      state.sidebar.collapsed = !state.sidebar.collapsed;
    },
    toggleZoneView: (state) => {
      state.isZoneView = !state.isZoneView;
    },
  },
});

export default editmodeSlice;
