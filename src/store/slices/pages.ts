import { createSlice } from '@reduxjs/toolkit';

export interface Page {
  altMenuName: string;
  doubleClickCat: string;
  id: number;
  menuName: string;
  name: string;
  showHeading: boolean;
  showSiblingsOnChildPages: boolean;
  url: string;
}

export type PagesState = Page[];

const initialState: PagesState = [];

const pagesSlice = createSlice({
  initialState,
  name: 'pages',
  reducers: {},
});

export default pagesSlice;
