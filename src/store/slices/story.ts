import { createSlice } from '@reduxjs/toolkit';

import { StoryCommentsState } from 'types/Story';

import type { Story } from 'types/Story';
import type { StoryList } from 'types/ZoneItems';

export interface StoryState extends Story {
  cartoonOfTheDayStoryList?: StoryList;
  localNewsStoryList?: StoryList;
}

const initialState: StoryState = {
  authors: [],
  bodyText: '',
  byline: '',
  canonicalUrl: '',
  comments: StoryCommentsState.DISABLED,
  contentTier: '',
  dailymotionVideoCount: 0,
  dailymotionVideoIds: [],
  elementTypes: [],
  elements: [],
  externalLink: false,
  featuredComments: [],
  id: '',
  isOpinion: false,
  isPinned: false,
  keywords: undefined,
  leadBrightcoveId: undefined,
  leadImage: undefined,
  noTransformUrl: undefined,
  orgName: undefined,
  organization: '',
  pubStatus: '',
  publishFrom: '',
  publishable: false,
  seoTitle: '',
  shortTitle: '',
  showLeadImage: true,
  showSummary: false,
  snapshot: 0,
  storySeoNoIndex: false,
  storyUrl: '',
  summary: '',
  tags: [],
  title: '',
  updatedOn: '',
  url: '',
  wordCount: undefined,
  wordCountRange: undefined,
};

const storySlice = createSlice({
  initialState,
  name: 'story',
  reducers: {},
});

export default storySlice;
