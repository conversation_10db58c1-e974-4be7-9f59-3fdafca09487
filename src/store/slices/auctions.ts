import { createSlice } from '@reduxjs/toolkit';

import type {
  AuctionStory,
  CategoryOptions,
  LocationOptions,
  SortOptions,
  SpeciesOptions,
  TypeOptions,
} from 'types/Auction';

interface PaginationBase {
  auctions: AuctionStory[];
  currentPage: number;
  endIndex: number;
  hasPreviousPage: boolean;
  itemCount: number;
  pageRange: number[];
  pageSize: number;
  previousPageNumber: number | null;
  startIndex: number;
}

interface PaginationWithNextPage {
  hasNextPage: true;
  nextPageNumber: number;
}

interface PaginationWithoutNextPage {
  hasNextPage: false;
  nextPageNumber: null;
}

interface PaginationWithPreviousPage {
  hasPreviousPage: true;
  previousPageNumber: number;
}

interface PaginationWithoutPreviousPage {
  hasPreviousPage: false;
  previousPageNumber: null;
}

type Pagination = PaginationBase &
  (PaginationWithNextPage | PaginationWithoutNextPage) &
  (PaginationWithPreviousPage | PaginationWithoutPreviousPage);

export interface AuctionResultsState {
  category: CategoryOptions;
  location: LocationOptions;
  pagination: Pagination;
  sort: SortOptions;
  species: SpeciesOptions;
  type: TypeOptions;
}

export interface AuctionHomeState {
  clearingsList: AuctionStory[];
  livestockList: AuctionStory[];
}

export interface AuctionsState {
  home?: AuctionHomeState;
  results?: AuctionResultsState;
}

const initialState: AuctionsState = {};

const auctionsSlice = createSlice({
  initialState,
  name: 'Auctions',
  reducers: {},
});

export default auctionsSlice;
