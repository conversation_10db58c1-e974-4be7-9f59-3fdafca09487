import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

import App from 'components/App';
import Providers from 'components/Providers';
import { RenderMode } from 'store/slices/conf';
import { getAccessToken } from 'util/organization/monza';
import { keysToCamel } from 'util/string';

import getMetadata from './metadata';

import type { Metadata } from 'next';
import type { RootState } from 'store/store';

export default async function Page() {
  const rawData = (await headers()).get('X-POST-Body');

  if (!rawData) {
    notFound();
  }

  const context = keysToCamel(JSON.parse(rawData)) as Partial<RootState>;

  if (
    process.env.NODE_ENV === 'production' &&
    !context.accessToken &&
    context.conf?.mode === RenderMode.EDIT
  ) {
    context.conf.mode = RenderMode.NORMAL;
  }

  if (!context.racetracks?.monzaUrl) {
    throw new Error('Monza endpoint was not provided');
  }

  try {
    const accessToken = await (context.accessToken ??
      (context.conf?.mode === RenderMode.EDIT
        ? getAccessToken(context.racetracks.monzaUrl)
        : null));

    if (accessToken) {
      context.accessToken = accessToken;
    }
  } catch {
    // Issues thrown during authentication are handled
    // by verifying token exists within App.tsx
  }

  return (
    <Providers context={context}>
      <App />
    </Providers>
  );
}

// eslint-disable-next-line react-refresh/only-export-components
export async function generateMetadata(): Promise<Metadata> {
  const rawData = (await headers()).get('X-POST-Body');

  if (!rawData) {
    return {};
  }

  const context = keysToCamel(JSON.parse(rawData)) as RootState;
  return getMetadata(context);
}
