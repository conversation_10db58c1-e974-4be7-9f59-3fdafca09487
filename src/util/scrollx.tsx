'use client';

import { type RefObject, useEffect, useState } from 'react';

import type { AdSizeType } from './ads';

export function hasAdSize(sizes: AdSizeType | undefined) {
  if (!sizes) {
    return null;
  }

  if (Array.isArray(sizes)) {
    return sizes.length !== 0;
  }

  // If it reaches here, the size is an object
  return true;
}

export function isMrec(sizes: AdSizeType | undefined) {
  if (!hasAdSize(sizes)) {
    return false;
  }

  if (Array.isArray(sizes) && sizes.length) {
    return sizes[1] === 250;
  }

  return false;
}

export function useScrollX(
  ref: RefObject<HTMLDivElement | null>,
  sizes: AdSizeType,
): boolean {
  const [isScrollX, setIsScrollX] = useState(false);
  useEffect(() => {
    // Only apply to mrecs to prevent interfering with Teads and other types
    if (!ref.current || isScrollX || !isMrec(sizes)) {
      return undefined;
    }

    const observer = new MutationObserver(() => {
      // Prevent certain ads from overriding the container height
      ref.current?.removeAttribute('style');

      // Detect Bonzai ScrollX/Celtra Interscroller ads by checking for
      // children with these classnames
      const isScrollXMutation =
        ref.current?.querySelector(
          '.bz-viewability-container, .celtra-ad-inline-host',
        ) !== null;

      setIsScrollX(isScrollXMutation);
    });

    observer.observe(ref.current, {
      attributes: true,
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [isScrollX, sizes, ref]);
  return isScrollX;
}
