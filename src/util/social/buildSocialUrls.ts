interface SocialConfig {
  facebookUrl?: string;
  instagramUsername?: string;
  twitterUsername?: string;
  youtubeUrl?: string;
}

export default function buildSocialUrls(config: SocialConfig): string[] {
  const { facebookUrl, instagramUsername, twitterUsername, youtubeUrl } =
    config;
  const socialProfileUrl = [];

  if (facebookUrl) {
    socialProfileUrl.push(facebookUrl);
  }
  if (twitterUsername) {
    socialProfileUrl.push(`https://twitter.com/${twitterUsername}/`);
  }
  if (youtubeUrl) {
    socialProfileUrl.push(youtubeUrl);
  }
  if (instagramUsername) {
    socialProfileUrl.push(`https://instagram.com/${instagramUsername}/`);
  }

  return socialProfileUrl;
}
