import { genMockStories } from './jest';
import {
  getCategorySlugFromStoryTags,
  getTopicsFromStoryTags,
  isStoryNewsletterSummary,
} from './story';

describe('story util', () => {
  describe('#getTopicsFromStoryTags', () => {
    it('should return empty array if no topic tags', () => {
      const tags = ['tag-1', 'tag-2', 'tag-3'];

      const result = getTopicsFromStoryTags(tags);

      expect(result).toEqual([]);
    });

    it('should return topics from tags', () => {
      const tags = ['tag-1', 'topic-tag-2', 'topic-tag-3'];

      const result = getTopicsFromStoryTags(tags);

      expect(result).toEqual(['Tag 2', 'Tag 3']);
    });
  });

  describe('#getCategorySlugFromStoryTags', () => {
    it('should return undefined if no category or signpost tags', () => {
      const tags = ['tag-1', 'tag-2', 'tag-3'];

      const result = getCategorySlugFromStoryTags(tags);

      expect(result).toBeUndefined();
    });

    it('should return category from category tag', () => {
      const tags = ['tag-1', 'category-tag-2', 'tag-3'];

      const result = getCategorySlugFromStoryTags(tags);

      expect(result).toBe('tag-2');
    });

    it('should return category from signpost tag', () => {
      const tags = ['tag-1', 'signpost-tag-2', 'tag-3'];

      const result = getCategorySlugFromStoryTags(tags);

      expect(result).toBe('tag-2');
    });

    // eslint-disable-next-line @stylistic/max-len
    it('should get category first if both category and signpost tags exist', () => {
      const tags = ['tag-1', 'category-tag-2', 'signpost-tag-3', 'tag-4'];

      const result = getCategorySlugFromStoryTags(tags);

      expect(result).toBe('tag-2');
    });
  });

  describe('#isStoryNewsletterSummary', () => {
    it('should return true when story has tags ending with -summary', () => {
      const story = genMockStories({ length: 1 })[0];
      story.tags = ['tag-1', 'newsletter-summary', 'tag-3'];

      const result = isStoryNewsletterSummary(story);

      expect(result).toBe(true);
    });

    // eslint-disable-next-line @stylistic/max-len
    it('should return false when story has no tags ending with -summary', () => {
      const story = genMockStories({ length: 1 })[0];
      story.tags = ['tag-1', 'tag-2', 'tag-3'];

      const result = isStoryNewsletterSummary(story);

      expect(result).toBe(false);
    });
  });
});
