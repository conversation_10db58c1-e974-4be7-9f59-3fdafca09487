import {
  faChevronLeft,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

interface ArrowProps {
  onClick?: (evt: never) => void;
}

export function CarouselNextArrow({ onClick }: ArrowProps) {
  return (
    <div
      aria-label="Next"
      className="absolute right-2 top-1/2 z-20 -mt-5 flex size-10 items-center justify-center text-white outline-none md:right-5"
      onClick={onClick}
      onKeyDown={onClick}
      role="button"
      tabIndex={0}
    >
      <FontAwesomeIcon icon={faChevronRight} />
    </div>
  );
}

export function CarouselPrevArrow({ onClick }: ArrowProps) {
  return (
    <div
      aria-label="Previous"
      className="absolute left-2 top-1/2 z-20 -mt-5 flex size-10 items-center justify-center text-white outline-none md:left-5"
      onClick={onClick}
      onKeyDown={onClick}
      role="button"
      tabIndex={0}
    >
      <FontAwesomeIcon icon={faChevronLeft} />
    </div>
  );
}
