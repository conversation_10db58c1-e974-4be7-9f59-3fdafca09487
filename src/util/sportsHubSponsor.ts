import { useAppSelector } from 'store/hooks';
import { SportPage } from 'types/SportsHub';

import { usePageParents } from './hooks';

export function useSportPage(): SportPage | null {
  const { parent: parentPage } = usePageParents();
  const page = useAppSelector((state) => state.page);

  const sportPage = Object.values(SportPage).find((sport) => {
    const pageNameTokens = page.name.toLowerCase().split(' ');
    const parentPageNameTokens = parentPage?.name.toLowerCase().split(' ');

    return (
      pageNameTokens.includes(sport) || parentPageNameTokens?.includes(sport)
    );
  });

  return sportPage ? (sportPage as SportPage) : null;
}

export default function useSportSponsorData() {
  const page = useAppSelector((state) => state.page);
  const sportPage = useSportPage();
  const sponsorData = useAppSelector(
    (state) =>
      state.features.sportsHubSponsor.enabled &&
      state.features.sportsHubSponsor.data.sponsorData,
  );
  const featuredSportSponsor = useAppSelector(
    (state) =>
      state.features.sportsHubSponsor.enabled &&
      state.features.sportsHubSponsor.data.featuredSportSponsor,
  );

  if (!sponsorData) {
    return null;
  }

  if (
    page.name.toLowerCase().includes('sport') &&
    featuredSportSponsor &&
    featuredSportSponsor in sponsorData
  ) {
    return sponsorData[featuredSportSponsor];
  }

  if (!sportPage || !(sportPage in sponsorData)) {
    return null;
  }

  return sponsorData[sportPage];
}
