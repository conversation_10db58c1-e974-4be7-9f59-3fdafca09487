import { textTruncateByWords } from '.';

describe('Text util', () => {
  describe('#textTruncateByWords', () => {
    it('should truncate text by words', () => {
      const text = 'The quick brown fox jumps over the lazy dog';
      expect(textTruncateByWords(text, 3)).toBe('The quick brown...');
    });

    // eslint-disable-next-line @stylistic/max-len
    it('should return the text if the word count is less than the specified count', () => {
      const text = 'The quick brown fox jumps over the lazy dog';
      expect(textTruncateByWords(text, 10)).toBe(text);
    });
  });
});
