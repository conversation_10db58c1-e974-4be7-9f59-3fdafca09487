import { useEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';

const MANTIS_TIMEOUT = 2000; // 2 seconds

export default function useMantisTargetingData() {
  const enabled = useAppSelector(
    (state) =>
      state.features.adServing.enabled &&
      state.features.adServing.data.useMantis,
  );
  const [isReady, setIsReady] = useState(false);
  const [targetingData, setTargetingData] = useState<
    MantisTargetingData | undefined
  >();

  useEffect(() => {
    if (isReady || !enabled) {
      return;
    }

    const setMantisData = () => {
      setIsReady(true);
      setTargetingData(window.mantis?.targetingData?.standard);
    };

    if (window.mantis?.targetingData || window.mantis?.error) {
      setMantisData();
      return;
    }

    window.addEventListener('mantis.data.ready', setMantisData, {
      once: true,
    });
    window.addEventListener('mantis.error', setMantisData, { once: true });
    const mantisTimeoutId = window.setTimeout(() => {
      if (!isReady) {
        console.error('Mantis error: Timed out');
        setMantisData();
      }
    }, MANTIS_TIMEOUT);

    // eslint-disable-next-line consistent-return
    return () => {
      window.removeEventListener('mantis.data.ready', setMantisData);
      window.removeEventListener('mantis.error', setMantisData);
      window.clearTimeout(mantisTimeoutId);
    };
  }, [enabled, isReady]);

  return { isReady, targetingData };
}
