import { hasAdSize, isMrec } from './scrollx';

import type { AdSizeType } from './ads';

describe('hasAdSize function', () => {
  const tests = [
    { expected: true, sizes: [300, 250] as AdSizeType },
    {
      expected: true,
      sizes: [
        [300, 250],
        [300, 600],
      ] as AdSizeType,
    },
    { expected: null, sizes: undefined },
    { expected: false, sizes: [] as AdSizeType },
  ];

  it('checks if variable has ad size', () => {
    expect.assertions(4);
    tests.forEach(({ expected, sizes }) => {
      expect(hasAdSize(sizes)).toStrictEqual(expected);
    });
  });
});

describe('isMrec function', () => {
  const tests = [
    { expected: true, sizes: [300, 250] as AdSizeType },
    { expected: false, sizes: undefined },
    { expected: false, sizes: [1, 1] as AdSizeType },
    {
      expected: false,
      sizes: [
        [300, 250],
        [300, 600],
      ] as AdSizeType,
    },
    { expected: false, sizes: [] as AdSizeType },
  ];

  it('checks if size is mrec', () => {
    expect.assertions(5);
    tests.forEach(({ expected, sizes }) => {
      expect(isMrec(sizes)).toStrictEqual(expected);
    });
  });
});
