import { renderHook } from '@testing-library/react';
import { Provider } from 'react-redux';

import { createStore } from 'store/store';
import { SportPage } from 'types/SportsHub';

import useSportSponsorData, { useSportPage } from './sportsHubSponsor';

import type { Store } from '@reduxjs/toolkit';

function getWrapper(store: Store): React.FC {
  // eslint-disable-next-line react/display-name
  return ({ children }: { children?: React.ReactNode }) => (
    <Provider store={store}>{children}</Provider>
  );
}

describe('useSportsPage hook', () => {
  it('checks sport page in 2nd level index page', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      page: {
        ...state.page,
        name: 'AFL',
      },
      pages: [
        {
          altMenuName: 'Top News',
          doubleClickCat: 'sport',
          id: 130701,
          menuName: '',
          name: 'Sport',
          showHeading: false,
          showSiblingsOnChildPages: true,
          url: 'sport',
        },
        {
          altMenuName: 'News',
          doubleClickCat: 'afl',
          id: 130734,
          menuName: '',
          name: 'AFL',
          showHeading: true,
          showSiblingsOnChildPages: true,
          url: 'sport/afl',
        },
      ],
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportPage(), { wrapper });

    expect(result.current).toBe(SportPage.AFL);
  });

  it('checks sport page in 3rd level index page', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      page: {
        ...state.page,
        name: 'Matches',
      },
      pages: [
        {
          ...state.pages[0],
          name: 'Sport',
        },
        {
          ...state.pages[0],
          name: 'AFL',
        },
        {
          ...state.pages[0],
          name: 'Matches',
        },
      ],
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportPage(), { wrapper });

    expect(result.current).toBe(SportPage.AFL);
  });

  it('checks sport page in 3rd level index page with complex name', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      page: {
        ...state.page,
        name: 'Matches',
      },
      pages: [
        {
          ...state.pages[0],
          name: 'Sport',
        },
        {
          ...state.pages[0],
          name: 'Local A-League (Boorowa)',
        },
        {
          ...state.pages[0],
          name: 'Matches',
        },
      ],
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportPage(), { wrapper });

    expect(result.current).toBe(SportPage.A_LEAGUE);
  });

  it('checks unknown sport page', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      page: {
        ...state.page,
        name: 'Matches',
      },
      pages: [
        {
          ...state.pages[0],
          name: 'Sport',
        },
        {
          ...state.pages[0],
          name: 'Local Table Tennis (Boorowa)',
        },
        {
          ...state.pages[0],
          name: 'Matches',
        },
      ],
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportPage(), { wrapper });

    expect(result.current).toBeNull();
  });

  it('checks index sport page', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      page: {
        ...state.page,
        name: 'Sport',
      },
      pages: [
        {
          ...state.pages[0],
          name: 'Sport',
        },
      ],
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportPage(), { wrapper });

    expect(result.current).toBeNull();
  });
});

describe('useSportSponsorData hook', () => {
  it('return corresponding sport sponsor according to current page', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        sportsHubSponsor: {
          data: {
            featuredSportSponsor: '',
            sponsorData: {
              afl: {
                logo: '',
                name: 'AFL Sponsor',
                url: '',
              },
              cricket: {
                logo: '',
                name: 'Cricket Sponsor',
                url: '',
              },
            },
          },
          enabled: true,
        },
      },
      page: {
        ...state.page,
        name: 'Local Cricket (Bendigo)',
      },
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportSponsorData(), { wrapper });

    expect(result.current?.name).toBe('Cricket Sponsor');
  });

  it('return corresponding sport sponsor according to parent page', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        sportsHubSponsor: {
          data: {
            featuredSportSponsor: '',
            sponsorData: {
              'a-league': {
                logo: '',
                name: 'A-League Sponsor',
                url: '',
              },
              afl: {
                logo: '',
                name: 'AFL Sponsor',
                url: '',
              },
            },
          },
          enabled: true,
        },
      },
      page: {
        ...state.page,
        name: 'Matches',
      },
      pages: [
        {
          ...state.pages[0],
          name: 'Sport',
        },
        {
          ...state.pages[0],
          name: 'Local A-League (Boorowa)',
        },
        {
          ...state.pages[0],
          name: 'Matches',
        },
      ],
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportSponsorData(), { wrapper });

    expect(result.current?.name).toBe('A-League Sponsor');
  });

  it('return featured sponsor for sport index page', () => {
    expect.assertions(2);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        sportsHubSponsor: {
          data: {
            featuredSportSponsor: 'afl',
            sponsorData: {
              'a-league': {
                bannerMobileUrl: '/a-l-banner-url.jpg',
                logo: '',
                name: 'A-League Sponsor',
                url: '',
              },
              afl: {
                bannerMobileUrl: '/banner-url.jpg',
                logo: '',
                name: 'AFL Sponsor',
                url: '',
              },
            },
          },
          enabled: true,
        },
      },
      page: {
        ...state.page,
        name: 'National Sport',
      },
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportSponsorData(), { wrapper });

    expect(result.current?.bannerMobileUrl).toBe('/banner-url.jpg');
    expect(result.current?.name).toBe('AFL Sponsor');
  });

  it('return null for no featured sponsor data on sport index page', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        sportsHubSponsor: {
          data: {
            featuredSportSponsor: 'netball',
            sponsorData: {
              'a-league': {
                bannerMobileUrl: '/a-l-banner-url.jpg',
                logo: '',
                name: 'A-League Sponsor',
                url: '',
              },
              afl: {
                bannerMobileUrl: '/banner-url.jpg',
                logo: '',
                name: 'AFL Sponsor',
                url: '',
              },
            },
          },
          enabled: true,
        },
      },
      page: {
        ...state.page,
        name: 'Local Sport',
      },
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportSponsorData(), { wrapper });

    expect(result.current).toBeFalsy();
  });

  it('return null unknown sports page', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        sportsHubSponsor: {
          data: {
            featuredSportSponsor: '',
            sponsorData: {
              'a-league': {
                logo: '',
                name: 'A-League Sponsor',
                url: '',
              },
              afl: {
                logo: '',
                name: 'AFL Sponsor',
                url: '',
              },
            },
          },
          enabled: true,
        },
      },
      page: {
        ...state.page,
        name: 'Matches',
      },
      pages: [
        {
          ...state.pages[0],
          name: 'Sport',
        },
        {
          ...state.pages[0],
          name: 'Local Voleyball (Boorowa)',
        },
        {
          ...state.pages[0],
          name: 'Matches',
        },
      ],
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportSponsorData(), { wrapper });

    expect(result.current).toBeFalsy();
  });

  it('return featured sponsor data on sport index page', () => {
    expect.assertions(1);

    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        sportsHubSponsor: {
          data: {
            featuredSportSponsor: 'a-league',
            sponsorData: {
              'a-league': {
                logo: '',
                name: 'A-League Sponsor',
                url: '',
              },
              afl: {
                logo: '',
                name: 'AFL Sponsor',
                url: '',
              },
            },
          },
          enabled: true,
        },
      },
      page: {
        ...state.page,
        name: 'National Sport',
      },
    }));

    const wrapper = getWrapper(store);

    const { result } = renderHook(() => useSportSponsorData(), { wrapper });

    expect(result.current?.name).toBe('A-League Sponsor');
  });
});
