import { createHash } from 'crypto';

import { TRACKING_QUERY_PARAMS, getTrackingQueryParams } from './tracking';

function mockWindowURL(url: string) {
  // eslint-disable-next-line compat/compat
  Reflect.deleteProperty(global.window, 'location');
  window.location = new URL(url) as unknown as Location & string;
}

function hash(str: string): string {
  return createHash('md5').update(str).digest('hex');
}

describe('getTrackingQueryParams', () => {
  it('returns empty object for no parameters present', () => {
    expect.assertions(1);
    mockWindowURL('https://localhost/');
    const params = getTrackingQueryParams();
    expect(params).toEqual({});
  });

  it('returns all expected tracked parameters', () => {
    const search = new URLSearchParams();
    TRACKING_QUERY_PARAMS.forEach((p) => {
      search.set(p, hash(p));
      search.set(`utm_${p}`, hash(`utm_${p}`));
    });

    expect.assertions(TRACKING_QUERY_PARAMS.length * 2);
    mockWindowURL(`https://localhost/?${search.toString()}`);

    const params = getTrackingQueryParams();
    TRACKING_QUERY_PARAMS.forEach((p) => {
      expect(params[p]).toBe(hash(p));
      expect(params[`utm_${p}`]).toBe(hash(`utm_${p}`));
    });
  });
});
