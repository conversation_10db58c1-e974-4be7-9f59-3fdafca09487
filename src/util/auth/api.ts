import { getPianoReady } from 'components/Piano/ready';

import type { PhoenixApiRequest } from 'types/phoenix-types/requests';
import type { PhoenixApiResponse } from 'types/phoenix-types/responses';

export type PhoenixParams<T extends PhoenixApiRequest> = Omit<
  T,
  keyof PhoenixApiRequest
>;

export async function callAuth<PERSON>pi<P extends PhoenixApiRequest, R>(
  endpoint: string,
  params: PhoenixParams<P>,
): Promise<PhoenixApiResponse<R>> {
  const { aid } = await getPianoReady();
  const store = window.getStore();
  const { phoenixUrl } = store.getState().racetracks;
  try {
    const res = await fetch(`${phoenixUrl}${endpoint}`, {
      body: JSON.stringify({
        aid,
        ...params,
      }),
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
      method: 'POST',
    });

    const body = (await res.json()) as PhoenixApiResponse<R>;
    return body;
  } catch {
    return {
      errors: [{ field: null, message: 'Unable to contact auth servers' }],
      statusCode: -1,
      success: false,
    };
  }
}
