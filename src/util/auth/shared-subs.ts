/**
 * List Shared Subscriptions
 */

import { type PhoenixParams, callAuth<PERSON>pi } from './api';

import type {
  PhoenixApiRequest,
  PhoenixSharedSubscriptionInviteRequest,
  PhoenixSharedSubscriptionListRequest,
  PhoenixSharedSubscriptionReminderRequest,
  PhoenixSharedSubscriptionRemoveRequest,
  PhoenixSharedSubscriptionResendRequest,
} from 'types/phoenix-types/requests';
import type {
  PhoenixApiResponse,
  PhoenixSharedSubscriptionInviteResponse,
  PhoenixSharedSubscriptionListResponse,
  PhoenixSharedSubscriptionReminderResponse,
  PhoenixSharedSubscriptionRemoveResponse,
  PhoenixSharedSubscriptionResendResponse,
} from 'types/phoenix-types/responses';

export async function listSharedSubscriptions(
  params: PhoenixParams<PhoenixSharedSubscriptionListRequest>,
): Promise<PhoenixApiResponse<PhoenixSharedSubscriptionListResponse>> {
  return callAuthApi<PhoenixApiRequest, PhoenixSharedSubscriptionListResponse>(
    'shared/list',
    params,
  );
}

export async function sendSharedInvite(
  params: PhoenixParams<PhoenixSharedSubscriptionInviteRequest>,
): Promise<PhoenixApiResponse<PhoenixSharedSubscriptionInviteResponse>> {
  return callAuthApi<
    PhoenixApiRequest,
    PhoenixSharedSubscriptionInviteResponse
  >('shared/invite', params);
}

export async function resendSharedInvite(
  params: PhoenixParams<PhoenixSharedSubscriptionResendRequest>,
): Promise<PhoenixApiResponse<PhoenixSharedSubscriptionResendResponse>> {
  return callAuthApi<
    PhoenixApiRequest,
    PhoenixSharedSubscriptionResendResponse
  >('shared/resend', params);
}

export async function disableSharedReminder(
  params: PhoenixParams<PhoenixSharedSubscriptionReminderRequest>,
): Promise<PhoenixApiResponse<PhoenixSharedSubscriptionReminderResponse>> {
  return callAuthApi<
    PhoenixApiRequest,
    PhoenixSharedSubscriptionReminderResponse
  >('shared/reminder', params);
}

export async function removeSharedInvite(
  params: PhoenixParams<PhoenixSharedSubscriptionRemoveRequest>,
): Promise<PhoenixApiResponse<PhoenixSharedSubscriptionRemoveResponse>> {
  return callAuthApi<
    PhoenixApiRequest,
    PhoenixSharedSubscriptionRemoveResponse
  >('shared/remove', params);
}
