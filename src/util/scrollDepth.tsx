import { useEffect, useState } from 'react';

export default function useScrollDepthBreakpoint(breakpointPercent: number) {
  const [hasReached, setHasReached] = useState(false);

  useEffect(() => {
    const checkScrollPercentage = () => {
      window.removeEventListener('scroll', checkScrollPercentage);
      const scrollPosition = window.scrollY;
      const documentHeight =
        document.body.offsetHeight || document.documentElement.scrollHeight;
      const viewportHeight = window.innerHeight;

      // Calculate the maximum scrollable height
      const maxScroll = documentHeight - viewportHeight;

      // Calculate the scroll percentage
      const scrollPercentage = (scrollPosition / maxScroll) * 100;

      if (scrollPercentage >= breakpointPercent) {
        setHasReached(true);
      } else {
        setTimeout(
          () => window.addEventListener('scroll', checkScrollPercentage),
          100,
        );
      }
    };

    window.addEventListener('scroll', checkScrollPercentage);

    return () => {
      window.removeEventListener('scroll', checkScrollPercentage);
    };
  }, [breakpointPercent]);

  return hasReached;
}
