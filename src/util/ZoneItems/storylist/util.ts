/* eslint-disable import/prefer-default-export */
import { type GlobalZoneName, ZoneName } from 'types/ZoneItems';

export function hideBottomBorder(
  disableBottomBorder: boolean,
  isBeforeStoryList: boolean | undefined,
  isLastBlockInZoneItem: boolean,
  isLastItemInZone: boolean,
  zoneName: ZoneName | GlobalZoneName,
): boolean {
  return (
    isLastBlockInZoneItem &&
    ((zoneName === ZoneName.NEWSWELL && isLastItemInZone) ||
      (zoneName !== ZoneName.NEWSWELL && !isBeforeStoryList) ||
      disableBottomBorder)
  );
}
