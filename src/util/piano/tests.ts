import { userDisplayName } from '.';

describe('userDisplayName', () => {
  it('first name', () => {
    expect(
      userDisplayName({
        email: '<EMAIL>',
        firstName: '<PERSON>',
        lastName: '',
      }),
    ).toStrictEqual('<PERSON>');
  });

  it('first and last name', () => {
    expect(
      userDisplayName({
        email: '<EMAIL>',
        firstName: 'Brad',
        lastName: '<PERSON>',
      }),
    ).toStrictEqual('<PERSON>');
  });

  it('No name, only email', () => {
    expect(
      userDisplayName({
        email: '<EMAIL>',
        firstName: '',
        lastName: '',
      }),
    ).toStrictEqual('test');
  });
});
