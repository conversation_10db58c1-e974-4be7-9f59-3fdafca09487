export const TRACKING_QUERY_PARAMS = [
  'source',
  'medium',
  'campaign',
  'channel',
  'term',
] as const;

type TrackingQueryParam = (typeof TRACKING_QUERY_PARAMS)[number];
type TrackingQueryParams = Partial<
  Record<TrackingQueryParam | `utm_${TrackingQueryParam}`, string>
>;

export function getTrackingQueryParams(): TrackingQueryParams {
  const trackingParams: TrackingQueryParams = {};
  const urlParams = new URLSearchParams(window.location.search);

  TRACKING_QUERY_PARAMS.forEach((param) => {
    const paramValue = urlParams.get(param);
    if (paramValue) {
      trackingParams[param] = paramValue;
    }

    const utmParamValue = urlParams.get(`utm_${param}`);
    if (utmParamValue) {
      trackingParams[`utm_${param}`] = utmParamValue;
    }
  });

  return trackingParams;
}
