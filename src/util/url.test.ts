import { stripQueryParams } from './url';

describe('stripQueryParams', () => {
  it('should remove query parameters from a URL', () => {
    expect(stripQueryParams('http://example.com/?a=1&b=2')).toBe(
      'http://example.com/',
    );
  });

  it('should return the original URL if no query parameters exist', () => {
    expect(stripQueryParams('http://example.com/')).toBe(
      'http://example.com/',
    );
  });

  it('should strip hash fragment', () => {
    expect(stripQueryParams('http://example.com/#section')).toBe(
      'http://example.com/',
    );
  });

  it('should strip hash fragment when it is just #', () => {
    expect(stripQueryParams('http://example.com/#')).toBe(
      'http://example.com/',
    );
  });

  it('should strip query params and hash fragment', () => {
    expect(stripQueryParams('http://example.com/?a=1#section')).toBe(
      'http://example.com/',
    );
  });

  it('should return an empty string for an empty input', () => {
    expect(stripQueryParams('')).toBe('');
  });

  it('should return an empty string for an invalid URL', () => {
    expect(stripQueryParams('invalid-url')).toBe('');
  });

  it('should return an empty string for undefined', () => {
    expect(stripQueryParams(undefined)).toBe('');
  });
});
