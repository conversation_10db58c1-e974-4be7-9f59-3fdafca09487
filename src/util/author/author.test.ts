import { getAuthorBioHtml } from './author';

import type { StoryAuthor } from 'types/Story';

describe('getAuthorBioHtml', () => {
  it('should return bio as is when it starts with HTML tag', () => {
    const author: StoryAuthor = {
      bio: '<p>This is a bio with HTML</p>',
      facebook: '',
      id: 1,
      instagram: '',
      mugshot: '',
      name: 'Test Author',
      position: '',
      tiktok: '',
      twitter: '',
      web: '',
      youtube: '',
    };

    const result = getAuthorBioHtml(author);

    expect(result).toBe('<p>This is a bio with HTML</p>');
  });

  it('should convert new lines to <br> for bio without HTML tags', () => {
    const author: StoryAuthor = {
      bio: 'This is a bio\nwith new lines',
      facebook: '',
      id: 2,
      instagram: '',
      mugshot: '',
      name: 'Test Author',
      position: '',
      tiktok: '',
      twitter: '',
      web: '',
      youtube: '',
    };

    const result = getAuthorBioHtml(author);

    expect(result).toBe('This is a bio<br>with new lines');
  });
});
