/* eslint-disable import/prefer-default-export */
import { convertNewLinesToBr } from '../string';

import type { StoryAuthor } from 'types/Story';

export function getAuthorBioHtml(
  author: StoryAuthor,
  wrapInParagraph: boolean = false,
): string {
  // check if author bio is has html elements
  if (author.bio.startsWith('<')) {
    return author.bio;
  }

  return wrapInParagraph
    ? `<p>${convertNewLinesToBr(author.bio)}</p>`
    : convertNewLinesToBr(author.bio);
}
