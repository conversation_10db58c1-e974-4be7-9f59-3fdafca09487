/* eslint-disable sort-keys */
import {
  buildRecurrenceRule,
  getLastDayOfMonth,
  getRecurrenceText,
  getWeekdayInstance,
  isLastDayOfMonth,
  processRecurrenceText,
} from './ugc';

import type { UGCFormInputs } from 'types/ugc';

describe('getWeekdayInstance', () => {
  it('returns correct instance for first occurrence of weekday', () => {
    // first monday of feb 2025
    const date = new Date(2025, 2, 3);
    expect(getWeekdayInstance(date)).toBe('1st Monday');
  });

  it('returns correct instance for second occurrence of weekday', () => {
    // second tuesday of feb 2025
    const date = new Date(2025, 2, 11);
    expect(getWeekdayInstance(date)).toBe('2nd Tuesday');
  });

  it('returns correct instance for third occurrence of weekday', () => {
    // third wednesday of feb 2025
    const date = new Date(2025, 2, 19);
    expect(getWeekdayInstance(date)).toBe('3rd Wednesday');
  });

  it('returns correct instance for fourth/last occurrence of weekday', () => {
    // fourth thurs of feb 2025
    const date = new Date(2025, 2, 27);
    expect(getWeekdayInstance(date)).toBe('Last Thursday');
  });

  it('returns "Last" for the last occurrence of weekday in month', () => {
    // last friday of feb 2025
    const date = new Date(2025, 1, 28);
    expect(getWeekdayInstance(date)).toBe('Last Friday');
  });
});

describe('getLastDayOfMonth', () => {
  it('returns 31 for January', () => {
    expect(getLastDayOfMonth(2025, 0)).toBe(31);
  });

  it('returns 28 for February in non-leap year', () => {
    expect(getLastDayOfMonth(2025, 1)).toBe(28);
  });

  it('returns 29 for February in leap year', () => {
    expect(getLastDayOfMonth(2024, 1)).toBe(29);
  });

  it('returns 30 for April', () => {
    expect(getLastDayOfMonth(2025, 3)).toBe(30);
  });
});

describe('isLastDayOfMonth', () => {
  it('returns true for the last day of January', () => {
    const date = new Date(2025, 0, 31);
    expect(isLastDayOfMonth(date)).toBe(true);
  });

  it('returns true for the last day of February in non-leap year', () => {
    const date = new Date(2025, 1, 28);
    expect(isLastDayOfMonth(date)).toBe(true);
  });

  it('returns true for the last day of February in leap year', () => {
    const date = new Date(2024, 1, 29);
    expect(isLastDayOfMonth(date)).toBe(true);
  });

  it('returns false for a day that is not the last day of the month', () => {
    const date = new Date(2025, 1, 15);
    expect(isLastDayOfMonth(date)).toBe(false);
  });
});

describe('getRecurrenceText', () => {
  const endDateString = '2025-02-28';

  describe('weekly recurrence', () => {
    // eslint-disable-next-line @stylistic/max-len
    it('returns correct text for weekly recurrence without selected days', () => {
      const result = getRecurrenceText(
        'WEEKLY',
        1,
        new Date(2025, 1, 24),
        {},
        null,
        endDateString,
      );
      expect(result).toBe('Repeats weekly, , until February 28 2025');
    });

    it('returns correct text for weekly recurrence with selected days', () => {
      const selectedDays = {
        MO: true,
        WE: true,
        FR: true,
      };
      const result = getRecurrenceText(
        'WEEKLY',
        1,
        new Date(2025, 1, 24),
        selectedDays,
        null,
        endDateString,
      );
      expect(result).toBe(
        'Repeats weekly,  each \nMon, Wed, Fri, until February 28 2025',
      );
    });

    it('returns correct text for bi-weekly recurrence', () => {
      const selectedDays = { MO: true };
      const result = getRecurrenceText(
        'WEEKLY',
        2,
        new Date(2025, 1, 24),
        selectedDays,
        null,
        endDateString,
      );
      expect(result).toBe(
        'Repeats every 2 weeks,  each \nMon, until February 28 2025',
      );
    });
  });

  describe('monthly recurrence', () => {
    it('returns correct text for monthly recurrence on a specific date', () => {
      const result = getRecurrenceText(
        'MONTHLY',
        1,
        new Date(2025, 1, 25),
        {},
        25,
        endDateString,
      );
      expect(result).toBe(
        'Repeats monthly, on the 25th, until February 28 2025',
      );
    });

    it('returns correct text for monthly recurrence on the last day', () => {
      const result = getRecurrenceText(
        'MONTHLY',
        1,
        new Date(2025, 1, 28),
        {},
        28,
        endDateString,
      );
      expect(result).toBe(
        'Repeats monthly, on the last day, until February 28 2025',
      );
    });

    // eslint-disable-next-line @stylistic/max-len
    it('returns correct text for monthly recurrence on a specific weekday instance', () => {
      const result = getRecurrenceText(
        'MONTHLY',
        1,
        new Date(2025, 1, 24),
        {},
        null,
        endDateString,
      );
      expect(result).toBe(
        'Repeats monthly, on the Last Monday, until February 28 2025',
      );
    });

    it('returns correct text for bi-monthly recurrence', () => {
      const result = getRecurrenceText(
        'MONTHLY',
        2,
        new Date(2025, 1, 25),
        {},
        25,
        endDateString,
      );
      expect(result).toBe(
        'Repeats every 2 months, on the 25th, until February 28 2025',
      );
    });
  });
});

describe('processRecurrenceText', () => {
  it('processes weekly recurrence text correctly', () => {
    const recurrenceString = 'weekly, each Monday, Wednesday, Friday';
    const endDateString = '2025-02-28';
    const result = processRecurrenceText(recurrenceString, endDateString);
    expect(result).toBe('Repeats weekly, each Mon, Wed, Fri');
  });

  it('processes monthly recurrence text correctly', () => {
    const recurrenceString = 'monthly, on the 25th';
    const endDateString = '2025-02-28';
    const result = processRecurrenceText(recurrenceString, endDateString);
    expect(result).toBe('Repeats monthly, on the 25th');
  });

  it('replaces date format with formatted date', () => {
    const recurrenceString = 'weekly, each Monday until 2025-02-28';
    const endDateString = '2025-02-28';
    const result = processRecurrenceText(recurrenceString, endDateString);
    expect(result).toBe('Repeats weekly, each Mon until February 28 2025');
  });
});

describe('buildRecurrenceRule', () => {
  it('builds weekly recurrence rule correctly', () => {
    const formData = {
      recurrence_frequency: '1',
      recurrence_interval: '1',
      start_datetime: '2025-02-24T10:00:00',
      end_datetime: '2025-02-28T10:00:00',
    };
    const selectedDays = {
      MO: true,
      WE: true,
      FR: true,
    };
    const result = buildRecurrenceRule(
      formData as UGCFormInputs,
      selectedDays,
    );
    expect(result).toBe(
      'RRULE:FREQ=WEEKLY;UNTIL=20250301T100000000Z;INTERVAL=1;BYDAY=MO,WE,FR',
    );
  });

  it('builds monthly recurrence rule with specific date correctly', () => {
    const formData = {
      recurrence_frequency: '2',
      recurrence_interval: '1',
      start_datetime: '2025-02-25T10:00:00',
      end_datetime: '2025-02-28T10:00:00',
      recurrence_monthly_date: '25',
    };
    const selectedDays = {};
    const result = buildRecurrenceRule(
      formData as UGCFormInputs,
      selectedDays,
    );
    expect(result).toBe(
      'RRULE:FREQ=MONTHLY;UNTIL=20250301T100000000Z;INTERVAL=1;BYMONTHDAY=25',
    );
  });

  it('builds monthly recurrence rule with last day of month correctly', () => {
    const formData = {
      recurrence_frequency: '2',
      recurrence_interval: '1',
      start_datetime: '2025-02-28T10:00:00',
      end_datetime: '2025-02-28T10:00:00',
      recurrence_monthly_date: '28',
    };
    const selectedDays = {};
    const result = buildRecurrenceRule(
      formData as UGCFormInputs,
      selectedDays,
    );
    expect(result).toBe(
      'RRULE:FREQ=MONTHLY;UNTIL=20250301T100000000Z;INTERVAL=1;BYMONTHDAY=-1',
    );
  });

  it('builds monthly recurrence rule with weekday instance correctly', () => {
    const formData = {
      recurrence_frequency: '2',
      recurrence_interval: '1',
      start_datetime: '2025-02-24T10:00:00', // last monday of feb 2025
      end_datetime: '2025-02-28T10:00:00',
      recurrence_monthly_weekday: '1',
      recurrence_weekday_instance: '4',
    };
    const selectedDays = {
      MO: true,
    };
    const result = buildRecurrenceRule(
      formData as UGCFormInputs,
      selectedDays,
    );
    expect(result).toBe(
      'RRULE:FREQ=MONTHLY;UNTIL=20250301T100000000Z;INTERVAL=1;BYDAY=-1MO',
    );
  });

  it('builds bi-monthly recurrence rule correctly', () => {
    const formData = {
      recurrence_frequency: '2',
      recurrence_interval: '2',
      start_datetime: '2025-02-25T10:00:00',
      end_datetime: '2025-02-28T10:00:00',
      recurrence_monthly_date: '25',
    };
    const selectedDays = {};
    const result = buildRecurrenceRule(
      formData as UGCFormInputs,
      selectedDays,
    );
    expect(result).toBe(
      'RRULE:FREQ=MONTHLY;UNTIL=20250301T100000000Z;INTERVAL=2;BYMONTHDAY=25',
    );
  });
});
