/* eslint-disable import/prefer-default-export */
import slugify from 'slugify';

import { titleCase } from './string';

import type { StoryState } from 'store/slices/story';

export const getTopicsFromStoryTags = (tags: string[]): string[] =>
  tags
    .filter((it) => it.startsWith('topic-'))
    .map((rawTopicTag) =>
      titleCase(
        slugify(rawTopicTag).replace('topic-', '').replaceAll('-', ' '),
      ),
    );

export const getCategorySlugFromStoryTags = (
  tags: string[],
): string | undefined => {
  // check for category tag first
  let category = tags.find((tag) => tag.startsWith('category-'));

  if (category) {
    return category.replace('category-', '');
  }

  // if no category tag, check for signpost tag
  category = tags.find((tag) => tag.startsWith('signpost-'));

  if (category) {
    category = category.replace('signpost-', '');
  }

  return category;
};

export const isStoryNewsletterSummary = (story: StoryState): boolean => {
  const { tags } = story;

  return tags.some((tag) => tag.endsWith('-summary'));
};
