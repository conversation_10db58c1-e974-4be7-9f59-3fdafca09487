export interface BookmarkMetadata {
  description?: string;
  site_id?: number;
  thumbnail?: Record<string, unknown> | string;
  title?: string;
  url?: string;
}

export enum UserBookmarkResourceType {
  STORY = 'story',
  PAGE = 'page',
  AUTHOR = 'author',
}

export interface UserBookmark<Metadata = BookmarkMetadata> {
  added_on: string;
  domain: string;
  metadata?: Metadata;
  resource_id: string;
  resource_type: UserBookmarkResourceType;
  resource_type_and_id: string;
  user_id: string;
}
