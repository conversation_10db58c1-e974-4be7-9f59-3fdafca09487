export enum Order {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Create a user's bookmark for a specific resource
 * Path:
 *  - POST /api/v1/users/:user_id/bookmarks
 */

export interface CreateUserBookmarkRequest {
  metadata?: {
    [x: string]: unknown;
  };
  resource_id: string;
  resource_type: string;
}

/**
 * Get a user's bookmarks for a specific resource
 * Path:
 *  - GET /api/v1/users/:user_id/bookmarks/:resource_type
 */
export interface UserBookmarkResourceQueryRequest {
  limit: number;
  next_token?: string;
  resource_type: string;
}

/**
 * Batch fetch user bookmarks
 * Path:
 *  - GET /api/v1/users/:user_id/bookmarks/batch-fetch
 */

export interface BatchFetchUserBookmarksRequest {
  items: {
    resource_id: string;
    resource_type: string;
  }[];
}

/**
 * Get a user's bookmarks
 * Path:
 *  - GET /api/v1/users/:user_id/bookmarks
 */

export interface GetUserBookmarksRequest {
  limit?: string;
  next_token?: string;
  order?: Order;
}

/**
 * Delete a user's bookmark for a specific resource
 * Path:
 *  - DELETE /api/v1/users/:user_id/bookmarks/:resource_type/:resource_id
 */

export type DeleteUserBookmarkRequest = void;

/**
 * Get a user's bookmark for a specific resource
 * Path:
 *  - GET /api/v1/users/:user_id/bookmarks/:resource_type/:resource_id
 */

export type GetUserBookmarkRequest = void;
