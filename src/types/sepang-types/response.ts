// eslint-disable-next-line import/extensions
import type { UserBookmark } from './bookmark.ts';

/**
 * Base issue type copied from valibot types
 * https://github.com/fabian-hiller/valibot/blob/main/library/src/types/issue.ts
 */
interface BaseIssue<TInput> {
  expected: string | null;
  input: TInput;
  issues?: [BaseIssue<TInput>, ...BaseIssue<TInput>[]] | undefined;
  kind: 'schema' | 'validation' | 'transformation';
  message: string;
  path?: unknown[];
  received: string;
  requirement?: unknown;
  type: string;
}

export enum ApiErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
}

export type ApiSuccessResponse<T> = {
  data: T;
  success: true;
};

export type PaginatedMeta = {
  nextToken?: string | null;
};

export type ApiSuccessInput<T> = {
  data: T;
  meta?: PaginatedMeta;
  status?: number;
};

export type ApiSuccessPaginatedResponse<T> = ApiSuccessResponse<T> & {
  meta: PaginatedMeta;
};

export type ApiErrorInput = {
  code: ApiErrorCode;
  customMessage?: string;
  details?: BaseIssue<unknown>[] | Record<string, unknown>[];
  status: number;
};

/**
 * User bookmarks JSON error response
 */

export type ApiErrorResponse = {
  code: ApiErrorCode;
  details: BaseIssue<unknown>[] | Record<string, unknown>[];
  message: string;
  success: false;
};

/**
 * Get a user's bookmarks JSON success response
 * Path:
 *  - GET /api/v1/users/:user_id/bookmarks
 */

export type GetUserBookmarksResponse = ApiSuccessPaginatedResponse<
  UserBookmark[]
>;

/**
 * Get a user's bookmarks for a specific resource JSON success response
 * Path:
 *  - GET /api/v1/users/:user_id/bookmarks/:resource_type
 */

export type GetUserBookmarksForResourceResponse = ApiSuccessPaginatedResponse<
  UserBookmark[]
>;

/**
 * Get a user's bookmark for a specific resource JSON success response
 * Path:
 *  - GET /api/v1/users/:user_id/bookmarks/:resource_type/:resource_id
 */

export type GetUserBookmarkResponse = ApiSuccessResponse<UserBookmark>;

/**
 * Create a user's bookmark for a specific resource JSON success response
 * Path:
 *  - POST /api/v1/users/:user_id/bookmarks
 */

export type CreateUserBookmarkResponse = ApiSuccessResponse<UserBookmark>;

/**
 * Delete a user's bookmark for a specific resource PLAIN success response
 * Path:
 *  - DELETE /api/v1/users/:user_id/bookmarks/:resource_type/:resource_id
 */

export type DeleteUserBookmarkResponse = void;

/**
 * Batch fetch user bookmarks JSON success response
 * Path:
 *  - GET /api/v1/users/:user_id/bookmarks/batch-fetch
 */

export type BatchFetchUserBookmarksResponse = ApiSuccessResponse<
  UserBookmark[]
>;
