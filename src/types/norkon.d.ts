interface Renderer {
  factory(options): void;
  name: string;
}

interface NcLiveCenterExtensions {
  channelRenderer: Array<Renderer>;
  fullowLoaded: boolean;
  inlineRendere: Array<Renderer>;
}

interface PostParams {
  baseUrl: string | null;
  channelId: string;
  container: HTMLElement | null;
  extensionContainer: NcLiveCenterExtensions;
  showMoreElement: HTMLElement | null;
  tenantKey: string;
  wsBaseUrl: string;
}

interface Window {
  NcLiveCenterExtensions: NcLiveCenterExtensions;
  NcPosts: {
    start(params: PostParams): void;
  };
}
