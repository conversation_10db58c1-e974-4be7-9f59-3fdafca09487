# Google Extended Access

[[TOC]]

## Overview

> The goal of Extended Access is to provide publishers with qualified leads to deepen user engagement and build brand loyalty. It achieves this by giving limited access to curated, paywalled content to readers and aims to increase their liklelihood to subscribe.

[Google Extend Access Developer Documentation](https://developers.google.com/news/subscribe/extended-access/overview)

### How Extended Access works

Extended Access has the following general distinctions:

- Extended Access only applies to users from [Google News Showcase surfaces](https://support.google.com/news/publisher-center/answer/10018494).
- If a user logs in and subscribes with the publisher, Extended Access isn't necessary. Subscribed users do not benefit from Extended Access.
- Users who previously registered with the publisher are directly eligible for Extended Access after they deplete a publisher meter.
- For users who are unknown to the publisher, the publisher can ask Google to show a Google-operated registration intervention:

![Registration intervention](./img/google-registration-prompt.png 'Registration intervention')

Figure 1. Registration intervention

In the Extended Access flow, a user who clicks on a Showcase panel will be redirected to the publisher's site and, in the case where this user is not already identified, presented with the registration intervention displayed in Figure 1.

- The user can click Continue with Google to register with the publisher through Sign In with Google.
- In case they're already a registered user who wasn't logged in, the user can click Already have an account? to log in with an existing publisher account.
- Once the publisher identifies the user, either as a newly registered user or an existing user, the user is eligible for Extended Access through Google.
- If Google grants Extended Access, the user sees an overlay that indicates it's active and a prominent Subscribe button as an upsell option:

![Extended Access prompt](./img/extended-access.png 'Extended Access prompt')

Figure 2. Extended Access prompt

- If Google doesn't grant Extended Access, the user sees the publisher's paywall.

Unsubscribed users who benefit from Extended Access are not given unlimited access to paywalled content. In order for Google to determine whether a user should be given access to content under Extended Access, Google uses the userState object that the publisher shares with Google. The userState uniquely identifies a user in an opaque way for Google. If the publisher doesn't share a userState object with Google, we assume the user isn't registered, and therefore Extended Access isn't available to the user.

## Integration Steps

### Extended Access decision logic

The following diagram outlines this logic for when Google offers Extended Access to a user:

![Extended Access decision logic](./img/ea-decision-logic.png 'Extended Access decision logic')

Figure 1. Extended Access decision logic

### Integrate with a third-party Identity Management System

Most Extended Access implementations can be supported with a Sign-in with Google client library. However, there are exceptional cases where publishers need to directly interact with a third-party Identity Management System (IDMS) to handle sign-in and registration with a Google OAuth 2.0 flow. This document describes how to achieve such implementations in Extended Access.

![Third Party Integration Overview](./img/third-party-integration-overview.png)

1. Initialize the Extended Access library with the authorizationUrl parameter.
2. Launch a third-party sign-in flow from an authorizationUrl. This enables an IDMS to redirect a user to Sign-in with Google through OAuth 2.0 and handles sign-in or registration on the publisher's behalf.
3. Redirect to the article page upon a successful sign-in or registration.

The following is an example of a third-party sign-in flow:

![Third Party Sign In Flow](./img/third-party-sign-in-flow.jpeg)

- [3P Authentication System](https://docs.google.com/document/d/1JhPOdGFdLEuLwo3U6pAmwHhRIUfF7-o2/edit#)

## Prepare for Launch

After you complete your integration, Google needs to review and approve your launch of Extended Access.

Submit use case videos
To prepare for your launch, complete the following steps:

1. Capture videos of each use case.
2. Upload the videos to Google Drive and share <NAME_EMAIL>.
3. Request a review. To do so, use the Extended Access QA form to submit video links for all the use cases.

### Use cases

A valid Extended Access implementation must successfully test all the use cases. To test them, first complete the following tasks:

1. [Create five panels in the Publisher Center](https://support.google.com/news/publisher-center/answer/10042612) with five different URLs and leave them in Draft. We refer to the panels in this section as Test panel 1 through Test panel 5.
2. Make sure that you [manage your user's access](https://support.google.com/news/publisher-center/answer/9603340) so that you're an "owner" of the publication in the Publisher Center.
3. [Enable Designer Mode](https://support.google.com/news/publisher-center/answer/9603948) in your Google News app.
4. Make sure you can [Preview your panels](https://support.google.com/news/publisher-center/answer/10042612) in the Publisher Center tool in the Google News app.

### Test use cases

#### Use case 1: Identify Google News Showcase users

**Scenario:** _When users don't come from a Showcase article and the registration intervention and Extended Access isn't shown._

**How to test:**

1. Read non-Showcase articles until you deplete the available publisher meter for anonymous users.
2. Copy the article URL from Test panel 1 in Publisher Center and open the article in a standard web browser.
3. Verify that the registration intervention isn't shown.
4. Sign in to an existing publisher account without a subscription.
5. Read non-Showcase articles until you deplete the available publisher meter for registered users.
6. Open the article URL from Test panel 1 in a standard browser.
7. Verify that the Extended Access prompt isn't shown.

**What to provide to us:** One video capture of the test case.

#### Use case 2: Handle anonymous Showcase users

**Scenario:** _Extended Access isn't shown on articles that are free for all users._

**How to test:**

1. Open the test panel, which links to a free article in the preview mode of the Google News app as an anonymous user.
2. Verify that the registration prompt isn't shown.

**What to provide to us:** One video capture of the test case.

#### Use case 3: Handle registration for new users and Sync entitlements with Google

**Scenario:** _When non-registered users reach the paywall, they're shown the registration intervention, and new users can register to get Extended Access._

**How to test:**

1. Read non-Showcase articles in Google News until you deplete the available publisher meter for anonymous users.
2. Open Test panel 1 in the preview mode of the Google News app.
3. Verify that the registration wall is shown.
4. Click Continue with Google.
5. Choose a Google Account that hasn't previously been registered with the publisher.
6. Verify that a publisher account was created for the Google user.
7. Read non-Showcase articles in Google News until you deplete the available publisher meter for registered users.
8. Open Test panel 2 in the preview mode of the Google News app.
9. Verify that the Extended Access prompt is shown.
10. Dismiss the Extended Access prompt and verify that the user can read the article and doesn't see a paywall.
11. Open Test panel 3 in the preview mode of the Google News app.
12. Verify that the Extended Access prompt is shown.
13. Dismiss the Extended Access prompt, and verify that the user can read the article and doesn't see a paywall.
14. Open Test panel 4 in the preview mode of the Google News app.
15. Verify that the Extended Access prompt is shown.
16. Dismiss the Extended Access prompt, and verify that the user can read the article and doesn't see a paywall.
17. Open Test panel 5 in the preview mode of the Google News app.
18. Verify that the Extended Access prompt isn't shown and that the user sees the publisher paywall.

**What to provide to us:** One video capture of the test case.

### Use case 4; Handle login for existing users and Sync entitlements with Google

**Scenario:** _Existing registered users without subscriptions can log in and get Extended Access._

**How to test:**

1. Read non-Showcase articles in Google News until you deplete the available publisher meter for anonymous users.
2. Open Test panel 1 in the preview mode of the Google News app.
3. Verify that the registration wall is shown.
4. Click Already registered? Sign in.
5. Log in with an existing publisher account that was created more than 30 days ago and doesn't have a subscription.
6. Read non-Showcase articles in Google News until you deplete the available publisher meter for registered users.
7. Open Test panel 2 in the preview mode of the Google News app.
8. Verify that the Extended Access prompt is shown.
9. Dismiss the Extended Access prompt and verify that the user can read the article and doesn't see a paywall.
10. Open Test panel 3 in the preview mode of the Google News app.
11. Verify that the Extended Access prompt isn't shown and that the user sees the publisher paywall.

**What to provide to us:** One video capture of the test case.

#### Use case 5: Sync entitlements with Google

**Scenario:** _Users can purchase a publisher subscription._

**How to test:**

1. Read non-Showcase articles in Google News until you deplete the available publisher meter for anonymous users.
2. Open Test panel 1 in the preview mode of the Google News app.
3. Verify that the registration wall is shown.
4. Click Continue with Google.
5. Choose a Google Account that hasn't previously been registered with the publisher.
6. Verify that a publisher account was created for the Google user.
7. Read non-Showcase articles in Google News until you deplete the available publisher meter for registered users.
8. Open Test panel 2 in the preview mode of the Google News app.
9. Verify that the Extended Access prompt is shown.
10. Verify that if users click Subscribe on the Extended Access prompt they can purchase a publisher subscription.
11. Open Test panel 3 in the preview mode of the Google News app.
12. Verify that the Extended Access prompt isn't shown for the subscribed user.

**What to provide to us:** One video capture of the test case.
