import type { Configuration } from 'lint-staged';

export default {
  // Use a function so tsc runs without files passed as arguments
  '*': () => 'tsc',
  '*.@(js|jsx|cjs|mjs|ts|tsx|cts|mts)': [
    'npm run -s lint:prettier -- --check',
    'npm run -s lint:eslint',
  ],
  '*.@(yaml|yml)': 'npm run -s lint:prettier -- --check',
  '*.css': 'npm run -s lint:prettier -- --check',
  '*.json': 'npm run -s lint:prettier -- --check',
} satisfies Configuration;
