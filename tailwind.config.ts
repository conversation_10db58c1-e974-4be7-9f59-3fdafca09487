import gradientMaskImage from '@storipress/tailwind-gradient-mask-image';
import containerQueries from '@tailwindcss/container-queries';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';
import tailwindColors from 'tailwindcss/colors';
import defaultTheme from 'tailwindcss/defaultTheme';
import plugin from 'tailwindcss/plugin';
import empty from 'tailwindcss-empty';
import scopedGroups from 'tailwindcss-scoped-groups';

import type { Config } from 'tailwindcss';
import type { PluginAPI } from 'tailwindcss/types/config';

// eslint-disable-next-line @typescript-eslint/unbound-method
function decorationPlugin({ addUtilities, e, theme }: PluginAPI) {
  const colors = theme('colors');

  if (colors === undefined) {
    return;
  }

  const utilities = Object.keys(colors).map((name) => {
    const nameValue = colors[name] as string | Record<string, string>;

    if (typeof nameValue === 'string') {
      return {
        [`.${e(`decoration-${name}`)}`]: {
          textDecorationColor: nameValue,
        },
      };
    }

    return Object.entries(nameValue).reduce(
      (all, [current, value]) => ({
        ...all,
        [`.${e(`decoration-${name}-${current}`)}`]: {
          textDecorationColor: value,
        },
      }),
      {},
    );
  });

  addUtilities(utilities);
}

// eslint-disable-next-line @typescript-eslint/unbound-method
function masonryLayoutPlugin({ addUtilities }: PluginAPI) {
  const newUtilities = {
    '.break-inside': {
      '-webkit-column-break-inside': 'avoid',
      'break-inside': 'avoid',
      'page-break-inside': 'avoid',
    },
    '.masonry': {
      'column-count': '3',
    },
  };
  addUtilities(newUtilities);
}

// eslint-disable-next-line @typescript-eslint/unbound-method
function scrollbarHidePlugin({ addUtilities }: PluginAPI) {
  addUtilities({
    '.scrollbar-hide': {
      // Safari/Chrome
      '&::-webkit-scrollbar': {
        display: 'none',
      },
      '-ms-overflow-style': 'none', // IE/Edge
      'scrollbar-width': 'none', // Firefox
    },
  });
}

const config: Config = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  experimental: {
    optimizeUniversalDefaults: true,
  },
  future: {
    hoverOnlyWhenSupported: true,
  },
  plugins: [
    containerQueries,
    forms,
    typography,
    scopedGroups,
    empty,
    gradientMaskImage,
    plugin(decorationPlugin),
    plugin(masonryLayoutPlugin),
    plugin(scrollbarHidePlugin),
  ],
  theme: {
    extend: {
      animation: {
        enter: 'enter .2s ease-out',
        'fade-in-down': 'fade-in-down 0.5s ease-out',
        leave: 'leave .15s ease-in forwards',
      },
      borderWidth: {
        1: '1px',
        3: '3px',
        6: '6px',
      },
      boxShadow: {
        '3xl': '0 35px 60px -15px rgba(0, 0, 0, 0.3)',
      },
      colors: {
        blue: {
          350: '#deebf3',
          450: '#def0fc',
          850: '#224E88',
        },
        'ct-blue': '#163262',
        echidna: '#DB593F',
        gray: {
          250: '#EDEFF7',
          350: '#E4EBEC',
          450: '#5F8386',
          550: '#1D1D1D',
          650: '#1E1E1E',
          850: '#09113A',
        },
        green: {
          550: '#058C42',
          650: '#018B45',
        },
        neutral: tailwindColors.neutral,
        'notification-danger': '#EBC8C4',
        'notification-info': '#CCE8F4',
        'notification-success': '#DEF2D6',
        'notification-warning': '#F8F3D6',
        orange: {
          650: '#F6511D',
        },
        red: {
          650: '#EC1B42',
        },
        slate: {
          750: '#1E2019',
        },
        teal: {
          150: '#F4FFF8',
        },
      },
      fontSize: {
        '4xs': '0.375rem',
        'heading-mobile': '1.625rem',
        heading1: [
          '48px',
          {
            lineHeight: '64px',
          },
        ],
        heading2: [
          '32px',
          {
            lineHeight: '39px',
          },
        ],
        heading3: [
          '28px',
          {
            lineHeight: '36px',
          },
        ],
        heading4: [
          '22px',
          {
            lineHeight: '26px',
          },
        ],
        heading5: [
          '21px',
          {
            lineHeight: '29px',
          },
        ],
        xxs: '0.625rem',
      },
      height: {
        10.5: '2.625rem', // 42px
        88: '22rem', // 352px
        104: '26rem', // 416px
        112: '28rem', // 448px
        120: '30rem', // 480px
        128: '32rem', // 512px
      },
      keyframes: {
        enter: {
          '0%': {
            opacity: '0',
            transform: 'scale(.9)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)',
          },
        },
        'fade-in-down': {
          '0%': {
            opacity: '0',
            transform: 'translateY(-10px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        leave: {
          '0%': {
            opacity: '1',
            transform: 'scale(1)',
          },
          '100%': {
            opacity: '0',
            transform: 'scale(.9)',
          },
        },
      },
      lineHeight: {
        2: '0.625rem',
        12: '3rem',
        20: '5rem',
      },
      margin: {
        7: '1.75rem',
        13: '3.25rem',
      },
      maxHeight: {
        '60vh': '60vh',
      },
      maxWidth: {
        150: '150px',
        344: '344px',
        375: '375px',
        container: '1220px',
        lg: '1220px',
        md: '820px',
        mrec: '300px',
        sm: '640px',
      },
      minWidth: {
        mrec: '300px',
        side: '328px',
      },
      outline: {
        blue: '2px dashed #3B82F6',
        gray: '2px dashed #6B7280',
      },
      size: {
        30: '7.5rem',
      },
      spacing: {
        4.5: '1.125rem',
        5.5: '1.375rem',
        18: '4.5rem',
      },
      transitionDuration: {
        600: '600ms',
      },
      transitionTimingFunction: {
        default: 'cubic-bezier(0.25,0.1,0.25,1)',
      },
      typography: (theme: PluginAPI['theme']) => ({
        DEFAULT: {
          css: {
            a: {
              '&:hover': {
                textDecorationColor: theme('colors.blue.600'),
              },
              '&:visited': {
                color: theme('colors.gray.500'),
                textDecorationColor: theme('colors.gray.500'),
              },
              color: theme('colors.blue.600'),
              textDecoration: 'underline',
              textDecorationColor: theme('colors.blue.300'),
            },
            color: theme('colors.gray.500'),
            h1: {
              color: theme('colors.black'),
              textAlign: 'center',
            },
            'h1 strong': {
              color: theme('colors.black'),
              fontWeight: '900',
            },
            h2: {
              color: theme('colors.black'),
            },
            'h2 strong': {
              color: theme('colors.black'),
              fontWeight: '800',
            },
            h3: {
              color: theme('colors.black'),
            },
            'h3 strong': {
              color: theme('colors.black'),
              fontWeight: '700',
            },
            h4: {
              color: theme('colors.black'),
            },
            'h4 strong': {
              color: theme('colors.black'),
              fontWeight: '700',
            },
            strong: {
              color: theme('colors.gray.500'),
              fontWeight: '700',
            },
          },
        },
      }),
      width: {
        '3/10': '30%',
        '7/10': '70%',
        18: '4.5rem',
        83.5: '20.875rem',
        120: '120px',
        161: '161px',
        174: '174px',
        180: '720px',
        192: '48rem',
        container: '1200px',
        side: '366px',
      },
      zIndex: {
        'above-piano': '1000000',
      },
    },
    fontFamily: {
      inter: ['Inter', ...defaultTheme.fontFamily.sans],
      lora: ['Lora', ...defaultTheme.fontFamily.serif],
      merriweather: ['Merriweather', ...defaultTheme.fontFamily.serif],
      opensans: ['Open Sans', ...defaultTheme.fontFamily.sans],
      playfair: ['Playfair Display', ...defaultTheme.fontFamily.sans],
      rubik: ['Rubik', ...defaultTheme.fontFamily.sans],
      sans: ['Inter', ...defaultTheme.fontFamily.sans],
      serif: ['Merriweather', ...defaultTheme.fontFamily.serif],
    },
    gradientMaskImage: {
      0: '0', // Plugin seems to have missed the 0, so adding here
      10: '10',
      20: '20',
      30: '30',
      40: '40',
      50: '50',
      60: '60',
      70: '70',
      80: '80',
      90: '90',
      100: '100',
    },
  },
} satisfies Config;

export default config;
