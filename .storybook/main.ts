import svgr from 'vite-plugin-svgr';

import type { StorybookConfig } from '@storybook/nextjs-vite';

const config: StorybookConfig = {
  addons: ['@storybook/addon-links', '@storybook/addon-docs'],
  core: {
    disableTelemetry: true,
  },
  framework: '@storybook/nextjs-vite',
  stories: ['../src/**/*.stories.@(js|jsx|cjs|mjs|ts|tsx)'],
  // SVGR support from https://github.com/ygkn/storybook-nextjs-vite-svgr
  viteFinal: (viteConfig) => {
    /* eslint-disable no-param-reassign */
    viteConfig.plugins = [
      ...viteConfig.plugins!,
      svgr({
        include: /\.svg$/,
      }),
    ];

    viteConfig.plugins = viteConfig.plugins.flat().map((plugin) => {
      if (
        typeof plugin === 'object' &&
        plugin !== null &&
        'name' in plugin &&
        plugin.name === 'vite-plugin-storybook-nextjs-image'
      ) {
        return {
          ...plugin,
          resolveId(id, importer) {
            // Skip .svg imports
            if (id.endsWith('.svg')) {
              return null;
            }

            if (typeof plugin.resolveId === 'function') {
              // @ts-expect-error this is a function in the Storybook plugin
              return plugin.resolveId(id, importer);
            }

            return null;
          },
        };
      }

      return plugin;
    });
    /* eslint-enable no-param-reassign */

    return viteConfig;
  },
};

export default config;
