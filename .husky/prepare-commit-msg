#!/usr/bin/env bash
# Generate an initial commit message with the ticket number parsed from the
# current branch, looking for branch names containing `/ABC-123`
COMMIT_MSG_FILE=$1
COMMIT_SOURCE=$2

if [[ -z "$COMMIT_SOURCE" ]]; then
  ref=$(git rev-parse --abbrev-ref HEAD)
  if [[ $ref =~ '/([A-Z]+-[0-9]+).*' ]]; then
    hint=$(cat "$COMMIT_MSG_FILE")
    ticket="${BASH_REMATCH[1]}"
    echo -e "\n\n${ticket}" > "$COMMIT_MSG_FILE"
    echo "$hint" >> "$COMMIT_MSG_FILE"
  fi
fi
