#!/usr/bin/env sh
changed_files="$(git diff-tree -r --name-only --no-commit-id ORIG_HEAD HEAD)"

banner() {
  msg="* $* *"
  border="$(echo "$msg" | sed 's/./*/g')"
  echo "$border"
  echo "$msg"
  echo "$border"
}

on_change() {
  echo "$changed_files" | grep -q "$1" && banner "$2"
}

on_change package-lock.json "package-lock.json has changed. Run npm install to update your dependencies"
on_change Dockerfile "Dockerfile has changed. Run docker compose build --pull app to rebuild the container."
